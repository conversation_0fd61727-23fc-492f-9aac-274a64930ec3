#!/bin/bash

# 批量替换 this.$message.error 为 this.$showFriendlyError
# 这个脚本会处理剩余的文件

echo "开始批量替换错误处理..."

# 查找所有包含 this.$message.error 的文件
files=$(grep -r "this\.\$message\.error" src/ --include="*.vue" --include="*.js" -l)

for file in $files; do
    echo "处理文件: $file"
    
    # 使用 sed 进行替换
    # 注意：这是一个简单的替换，可能需要手动调整一些复杂的情况
    sed -i.bak 's/this\.\$message\.error(/this.$showFriendlyError(null, /g' "$file"
    
    # 删除备份文件
    rm "${file}.bak" 2>/dev/null || true
done

echo "批量替换完成！"
echo "请检查替换结果，某些复杂情况可能需要手动调整。"
