# REM 适配使用指南

## 概述

本项目已经统一使用rem作为单位进行响应式适配，基于1920px设计稿，设置1rem = 37.5px的基准值。

## 基本设置

### 基准值设置
- **设计稿宽度**: 1920px
- **基准字体大小**: 37.5px (即 1rem = 37.5px)
- **设计稿总宽度**: 51.2rem (1920px ÷ 37.5px)

### 响应式断点
- **小屏幕** (≤1440px): 动态计算字体大小，最小20px
- **标准屏幕** (1441px-1920px): 37.5px
- **大屏幕** (>1920px): 最大45px

## 使用方法

### 1. 自动适配
项目已在 `main.js` 中初始化rem适配，会自动根据屏幕尺寸调整根字体大小。

### 2. px转rem转换

#### 使用转换工具
```javascript
import { pxToRem } from '@/utils/px-to-rem-converter'

// 单个值转换
const width = pxToRem(510) // "13.6rem"

// 批量转换
const sizes = pxArrayToRem([20, 40, 60]) // ["0.53rem", "1.07rem", "1.6rem"]

// CSS字符串转换
const css = convertCSSPxToRem('width: 510px; height: 190px;')
// 结果: "width: 13.6rem; height: 5.07rem;"
```

#### 常用转换值
```scss
// 间距
4px  → 0.11rem
8px  → 0.21rem
12px → 0.32rem
16px → 0.43rem
20px → 0.53rem
24px → 0.64rem
32px → 0.85rem
40px → 1.07rem

// 常用尺寸
36px  → 0.96rem  (按钮高度)
160px → 4.27rem  (选择器宽度)
240px → 6.4rem   (搜索框宽度)
510px → 13.6rem  (卡片宽度)
```

### 3. 在Vue组件中使用

#### 样式中使用rem
```scss
.agent-card {
  width: 13.6rem;   // 510px
  height: 5.07rem;  // 190px
  padding: 0.53rem 0.43rem; // 20px 16px
  margin-bottom: 0.53rem;   // 20px
}

.search-box {
  width: 6.4rem;    // 240px
  height: 0.96rem;  // 36px
  padding: 0 0.35rem; // 0 13px
}
```

#### 响应式网格
```scss
.agents-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.53rem; // 20px

  @media (max-width: 1600px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}
```

## 修复的问题

### 1. 卡片宽度超出问题
- **原因**: 使用固定的510px宽度，在小屏幕上会超出
- **解决**: 改为13.6rem，会根据屏幕大小自动缩放

### 2. 间距不统一问题
- **原因**: 混用px和rem单位
- **解决**: 统一使用rem单位，保持比例一致

### 3. 响应式适配问题
- **原因**: 没有动态调整根字体大小
- **解决**: 添加JavaScript动态计算和监听窗口变化

## 开发建议

### 1. 设计稿转换
当拿到设计稿时，按以下步骤转换：
1. 确认设计稿宽度（通常1920px）
2. 将所有px值除以37.5得到rem值
3. 使用转换工具快速计算

### 2. 调试工具
在浏览器控制台中使用：
```javascript
// 查看当前根字体大小
console.log(document.documentElement.style.fontSize)

// 查看转换表格
import converter from '@/utils/px-to-rem-converter'
converter.printConversionTable()
```

### 3. 最佳实践
- 优先使用常用转换值中的标准尺寸
- 保持设计的比例关系
- 在不同屏幕尺寸下测试效果
- 避免混用px和rem单位

## 故障排除

### 1. 页面显示过大或过小
检查根字体大小设置是否正确：
```javascript
// 手动设置根字体大小
document.documentElement.style.fontSize = '37.5px'
```

### 2. 响应式不生效
确保已正确引入和初始化rem适配：
```javascript
import { initRem } from '@/utils/rem'
initRem()
```

### 3. 某些元素仍使用px
搜索并替换项目中的px单位：
```bash
# 搜索px使用情况
grep -r "px" src/ --include="*.vue" --include="*.scss"
```

## 与Demo项目对比

| 项目 | 单位 | 基准值 | 适配方式 |
|------|------|--------|----------|
| Demo | px | 固定像素 | 无响应式 |
| 当前项目 | rem | 37.5px | 动态响应式 |

通过使用rem适配，你的项目现在可以：
- 在不同屏幕尺寸下保持比例
- 自动适配各种设备
- 避免内容超出屏幕
- 保持与设计稿的一致性
