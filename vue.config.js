const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  // 根据环境配置source map
  productionSourceMap: process.env.NODE_ENV === 'production' && process.env.VUE_APP_ENV !== 'local' ? false : true,

  // 开发服务器配置
  devServer: {
    port: 8080,
    open: true,
    // 根据环境配置HTTPS
    https: process.env.NODE_ENV === 'development' ? false : true,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_BASE_API || 'http://localhost:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },

  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  },

  // 配置webpack
  configureWebpack: config => {
    // 本地开发生产环境模式 - 支持debugger且编译较快
    if (process.env.NODE_ENV === 'production' && process.env.VUE_APP_ENV === 'local') {
      // 启用source map以支持debugger
      config.devtool = 'eval-cheap-module-source-map'

      // 关闭代码压缩以提高编译速度
      config.optimization = {
        ...config.optimization,
        minimize: false
      }

      // 性能配置
      config.performance = {
        maxEntrypointSize: 10000000,
        maxAssetSize: 30000000
      }
    } else if (process.env.NODE_ENV === 'production') {
      // 真正的生产环境配置
      config.performance = {
        maxEntrypointSize: 10000000,
        maxAssetSize: 30000000
      }
    } else {
      // 开发环境配置 - 最快的编译速度
      config.devtool = 'eval-cheap-module-source-map'
    }
  }
})
