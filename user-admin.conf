server {
    listen       3002;
    server_name  localhost;
    root         /wwwroot/user-admin/dist;
    index        index.html;

    # 处理单页应用路由
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control no-cache;
    }

    # 专门处理 JavaScript 文件
    location ~* \.(js|mjs)$ {
        add_header Content-Type application/javascript;
        add_header X-Content-Type-Options nosniff;
        add_header Cache-Control no-cache;
    }

    # 处理静态资源
    location /assets {
        expires 1d;
        add_header Cache-Control "public, no-transform";
    }

    # 启用 gzip 压缩
    gzip on;
    gzip_types text/plain application/javascript text/css;
    gzip_min_length 1k;
    gzip_comp_level 6;
}