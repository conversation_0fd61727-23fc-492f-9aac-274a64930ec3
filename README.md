# User Admin - 用户管理后台系统

基于Vue.js 2.x + Element UI构建的现代化用户管理后台系统，提供完整的用户管理功能和权限控制。

## 🚀 项目特性

- **现代化技术栈**：Vue 2.x + Element UI + Vuex + Vue Router
- **响应式设计**：支持多种设备和屏幕尺寸
- **模块化架构**：清晰的项目结构，易于维护和扩展
- **完整的权限系统**：基于角色的访问控制
- **丰富的组件库**：基于Element UI的自定义组件
- **统一的API管理**：标准化的接口调用和错误处理

## 📦 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Vue.js | 2.6.14 | 渐进式JavaScript框架 |
| Element UI | 2.15.14 | 基于Vue的桌面端组件库 |
| Vuex | 3.6.2 | Vue.js的状态管理模式 |
| Vue Router | 3.5.1 | Vue.js官方路由管理器 |
| Axios | 1.8.4 | 基于Promise的HTTP库 |
| SCSS | 1.32.7 | CSS预处理器 |
| ECharts | 5.6.0 | 数据可视化图表库 |

## 🛠️ 开发环境设置

### 环境要求
- Node.js >= 14.x
- npm >= 6.x 或 yarn >= 1.x
- 现代浏览器（支持ES6+）

### 安装依赖
```bash
# 使用yarn（推荐）
yarn install

# 或使用npm
npm install
```

### 开发服务器
```bash
# 开发环境（默认端口8080）
yarn dev
# 或
npm run dev

# 本地环境
yarn local
# 或
npm run local

# 生产环境预览
yarn pro
# 或
npm run pro
```

### 构建项目
```bash
# 开发环境构建
yarn build
# 或
npm run build

# 生产环境构建
yarn build-pro
# 或
npm run build-pro

# 本地环境构建
yarn build-local
# 或
npm run build-local
```

### 代码检查和修复
```bash
# ESLint检查和自动修复
yarn lint
# 或
npm run lint
```

### 预览构建结果
```bash
# 预览生产环境构建
yarn preview-pro
# 或
npm run preview-pro

# 预览本地环境构建
yarn preview-local
# 或
npm run preview-local
```

## 📁 项目结构

```
user-admin/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   ├── assets/            # 项目资源文件
│   ├── components/        # 可复用组件
│   ├── layouts/           # 布局组件
│   ├── mixins/            # Vue混入
│   ├── router/            # 路由配置
│   ├── store/             # Vuex状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .cursor/               # Cursor AI配置
│   └── rules/             # 项目开发规则
├── dist/                  # 构建输出目录
├── node_modules/          # 依赖包
├── .eslintignore          # ESLint忽略配置
├── .gitignore             # Git忽略配置
├── babel.config.js        # Babel配置
├── jsconfig.json          # JavaScript配置
├── package.json           # 项目配置
├── vue.config.js          # Vue CLI配置
└── yarn.lock              # 依赖锁定文件
```

## 🎯 Cursor AI 开发规则

本项目配置了完整的Cursor AI开发规则，位于`.cursor/rules/`目录中。Cursor支持四种规则类型：

### 规则类型说明

#### 1. Always（始终应用）
- **作用**：规则始终生效，在所有代码编写过程中都会应用
- **适用场景**：基础代码规范、项目架构规则
- **本项目应用**：Vue组件规范、代码风格规范

#### 2. Auto Attached（自动附加）
- **作用**：当匹配特定文件模式（globs）的文件被引用时自动附加规则
- **适用场景**：特定文件类型的专门规则
- **本项目应用**：
  - `*.vue` 文件：Vue组件开发规范
  - `src/api/*.js` 文件：API接口规范
  - `src/store/*.js` 文件：Vuex状态管理规范

#### 3. Agent Requested（AI代理请求）
- **作用**：根据AI代理的智能判断决定是否应用规则
- **适用场景**：复杂业务逻辑、性能优化建议
- **本项目应用**：性能优化规则、安全规范检查

#### 4. Manual（手动调用）
- **作用**：仅在提示中显式使用 `@规则名` 时附加规则
- **适用场景**：特殊场景的专门规则、调试规则
- **本项目应用**：
  - `@vue-debug`：Vue调试规则
  - `@api-test`：API测试规则
  - `@performance`：性能分析规则

### 规则文件列表

| 文件名 | 类型 | 说明 |
|--------|------|------|
| `vue-project-rules.mdc` | Always | Vue.js项目主要开发规则 |
| `code-style-rules.mdc` | Always | 代码风格和格式规范 |
| `api-data-rules.mdc` | Auto Attached | API调用和数据处理规范 |
| `README.mdc` | Manual | 规则使用说明文档 |

### 使用方式

1. **自动应用**：大部分规则会自动生效，无需手动操作
2. **手动调用**：在Cursor中输入 `@规则名` 来调用特定规则
3. **文件关联**：编辑特定类型文件时会自动加载相关规则
4. **智能建议**：AI会根据上下文智能推荐适用的规则

## 🔧 开发指南

### 代码规范
- 遵循ESLint配置的代码规范
- 使用Prettier进行代码格式化
- 组件命名使用PascalCase
- 文件命名使用kebab-case

### 提交规范
```
类型: 简短描述

详细描述（可选）

类型包括：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

## 🚀 部署

### 环境配置
项目支持多环境配置：
- **development**: 开发环境
- **local**: 本地环境
- **production**: 生产环境

### 构建部署
```bash
# 生产环境构建
yarn build-pro

# 部署到服务器
# 将dist目录内容上传到Web服务器
```

## 📖 相关文档

- [Vue.js官方文档](https://cn.vuejs.org/)
- [Element UI组件库](https://element.eleme.cn/)
- [Vuex状态管理](https://vuex.vuejs.org/zh/)
- [Vue Router路由](https://router.vuejs.org/zh/)
- [Axios HTTP库](https://axios-http.com/)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: 添加某个功能'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 维护者

- **开发团队**: XPlan开发组
- **项目负责人**: [项目负责人姓名]
- **技术支持**: [技术支持联系方式]

---

**注意**: 本项目使用Cursor AI辅助开发，遵循项目规则可以获得更好的AI编程体验。
