import MainLayout from '@/layouts/MainLayout.vue'
import Login from '@/views/Login.vue'
import Error404 from '@/views/error/404.vue'
import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录'
    }
  },
  {
    path: '/',
    component: MainLayout,
    redirect: '/create/app',
    children: [
      {
        path: 'create',
        name: 'Create',
        component: () => import('@/views/create/Index.vue'),
        meta: {
          title: '创造'
        },
        redirect: '/create/app',
        children: [
          {
            path: 'app',
            name: 'CreateApp',
            component: () => import('@/views/create/app/Index.vue'),
            meta: {
              title: '应用'
            },
            children: [
              {
                path: 'new',
                name: 'NewApp',
                component: () => import('@/views/create/app/new/index.vue'),
                meta: {
                  title: '创建智能体'
                }
              },
              {
                path: 'settings/:id',
                name: 'AppSettings',
                component: () => import('@/views/create/app/settings/Index.vue'),
                meta: {
                  title: '应用设置',
                  hideSidebar: true,
                  hideTopBar: true
                }
              },
              {
                path: 'chat/:id',
                name: 'AppChat',
                component: () => import('@/views/create/app/chat/Index.vue'),
                meta: {
                  title: '应用对话',
                  hideSecondaryMenu: true
                }
              }
            ]
          },
          {
            path: 'knowledge',
            name: 'CreateKnowledge',
            component: () => import('@/views/create/knowledge/Index.vue'),
            meta: {
              title: '知识库'
            },
            children: [
              {
                path: ':id/detail',
                name: 'KnowledgeDetail',
                component: () => import('@/views/create/knowledge/components/KnowledgeDetail.vue'),
                meta: {
                  title: '知识库详情'
                }
              },
              {
                path: ':id/setting',
                name: 'KnowledgeSetting',
                component: () => import('@/views/create/knowledge/components/KnowledgeSetting.vue'),
                meta: {
                  title: '知识库设置'
                }
              }
            ]
          },
          {
            path: 'database',
            name: 'CreateDatabase',
            component: () => import('@/views/create/database/Index.vue'),
            meta: {
              title: '数据库'
            },
            children: [
              {
                path: 'setting/:id',
                name: 'DatabaseSetting',
                component: () => import('@/views/create/database/setting/Index.vue'),
                meta: {
                  title: '数据库设置'
                }
              }
            ]
          },
          {
            path: 'plugin',
            name: 'CreatePlugin',
            component: () => import('@/views/create/plugin/Index.vue'),
            meta: {
              title: '插件'
            },
            children: [
              {
                path: 'new',
                name: 'NewPlugin',
                component: () => import('@/views/create/plugin/new/Index.vue'),
                meta: {
                  title: '创建插件'
                }
              },
              {
                path: 'edit/:id',
                name: 'EditPlugin',
                component: () => import('@/views/create/plugin/new/Index.vue'),
                meta: {
                  title: '编辑插件'
                }
              },
              {
                path: 'view/:id',
                name: 'ViewPlugin',
                component: () => import('@/views/create/plugin/new/Index.vue'),
                meta: {
                  title: '查看插件'
                }
              }
            ]
          },
          {
            path: 'workflow',
            name: 'CreateWorkflow',
            component: () => import('@/views/create/workflow/Index.vue'),
            meta: {
              title: '工作流'
            },
            children: [
              {
                path: 'execute/:workflowId',
                name: 'ExecuteWorkflow',
                component: () => import('@/views/create/workflow/ExecuteWorkflow.vue'),
                meta: {
                  title: '执行工作流',
                  hideSecondaryMenu: true
                }
              }
            ]
          }
        ]
      },
      {
        path: 'access',
        name: 'Access',
        component: () => import('@/views/access/Index.vue'),
        meta: {
          title: '接入'
        },
        redirect: '/access/channel',
        children: [
          {
            path: 'channel',
            name: 'AccessChannel',
            component: () => import('@/views/access/channel/Index.vue'),
            meta: {
              title: '渠道接入'
            }
          },
          {
            path: 'client',
            name: 'AccessClient',
            component: () => import('@/views/access/client/Index.vue'),
            meta: {
              title: '客户端'
            }
          },
          {
            path: 'web-embed/:id',
            name: 'WebEmbedConfig',
            component: () => import('@/views/access/web-embed/Index.vue'),
            meta: {
              title: '网页嵌入配置'
            }
          },
          {
            path: 'chat',
            name: 'AccessChat',
            component: () => import('@/views/access/chat/Index.vue'),
            meta: {
              title: '对话管理'
            }
          },
          {
            path: 'temporary-chat',
            name: 'TemporaryChat',
            component: () => import('@/views/access/temporaryChat/index.vue'),
            meta: {
              title: '用户对话'
            }
          }

        ]
      },
      {
        path: 'manage',
        name: 'Manage',
        component: () => import(/* webpackChunkName: "manage" */ '@/views/manage/Index.vue'),
        meta: {
          title: '管理'
        },
        redirect: '/manage/account',
        children: [
          {
            path: 'account',
            name: 'ManageAccount',
            component: () => import(/* webpackChunkName: "manage-account" */ '@/views/manage/account/Index.vue'),
            meta: {
              title: '我的账户'
            }
          },
          {
            path: 'dashboard',
            name: 'ManageDashboard',
            component: () => import(/* webpackChunkName: "manage-dashboard" */ '@/views/manage/dashboard/Index.vue'),
            meta: {
              title: '数据看板'
            }
          },
          {
            path: 'team',
            name: 'ManageTeam',
            component: () => import(/* webpackChunkName: "manage-team" */ '@/views/manage/team/Index.vue'),
            meta: {
              title: '团队空间'
            },
            children: [
              {
                path: 'upgrade',
                name: 'TeamUpgrade',
                component: () => import(/* webpackChunkName: "manage-team-upgrade" */ '@/views/manage/team/upgrade/Index.vue'),
                meta: {
                  title: '版本升级'
                }
              }
            ]
          }
        ]
      },
      {
        path: 'discover',
        name: 'Discover',
        component: () => import('@/views/discover/Index.vue'),
        meta: {
          title: '发现'
        },
        redirect: '/discover/model',
        children: [
          {
            path: 'model',
            name: 'DiscoverModel',
            component: () => import('@/views/discover/TemplateCenter.vue'),
            meta: {
              title: '模型仓库'
            }
          },
          {
            path: 'template-center',
            name: 'DiscoverTemplateCenter',
            component: () => import('@/views/discover/template/Index.vue'),
            meta: {
              title: '模板中心'
            }
          },
          {
            path: 'marketplace',
            name: 'DiscoverMarketplace',
            component: () => import('@/views/discover/marketplace/Index.vue'),
            meta: {
              title: '应用广场'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/404',
    name: '404',
    component: Error404
  },
  {
    path: '*',
    redirect: '/404'
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
// eslint-disable-next-line no-unused-vars
router.beforeEach((to, from, next) => {
  // 检测URL中的邀请码
  if (to.query.inviteCode) {
    console.log('检测到邀请码:', to.query.inviteCode)
    // 保存邀请码到localStorage
    localStorage.setItem('pendingInviteCode', to.query.inviteCode)
  }

  const token = localStorage.getItem('token')
  const tenantId = localStorage.getItem('tenantId')
  const isAuthenticated = token && tenantId // 需要同时有token和租户ID

  if (to.path !== '/login' && !isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    next('/create/app')
  } else {
    // 添加刷新控制
    if (to.params.forceRefresh) {
      to.meta.forceRefresh = true
    }
    next()
  }
})

// 全局路由后置钩子
// eslint-disable-next-line no-unused-vars
router.afterEach((to, from) => {
  // 处理刷新逻辑
  if (to.meta.forceRefresh) {
    // 清除强制刷新标记，以防止无限循环
    delete to.meta.forceRefresh
  }
})

export default router
