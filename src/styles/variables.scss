// 颜色变量 - 使用蓝湖设计规范
$color-primary: #1890ff;
$color-primary-light: #40a9ff;
$color-primary-dark: #096dd9;
$color-primary-bg: rgba(24, 144, 255, 0.1);

$color-success: #52c41a;
$color-warning: #faad14;
$color-danger: #f5222d;
$color-info: #1890ff;

$text-color-primary: #333333;
$text-color-regular: #666666;
$text-color-secondary: #999999;
$text-color-placeholder: #cccccc;
$text-color-white: #ffffff;

$border-color: #e8e8e8;
$border-color-light: #f0f0f0;
$background-color: #f3f4f5;
$background-color-light: #f7f7f7;

// 布局变量
$header-height: 60px;
$sidebar-width: 80px;
$sidebar-expanded-width: 80px;
$submenu-width: 194px;
$content-padding: 24px;

// 断点变量
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
$screen-xxl: 1600px;

// 字体变量
$font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-sm: 12px;
$font-size-xs: 10px;

// 间距变量
$spacing-unit: 4px;
$spacing-1: $spacing-unit;
$spacing-2: $spacing-unit * 2;
$spacing-3: $spacing-unit * 3;
$spacing-4: $spacing-unit * 4;
$spacing-5: $spacing-unit * 5;
$spacing-6: $spacing-unit * 6;

// 圆角变量
$border-radius-base: 4px;
$border-radius-sm: 2px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-circle: 50%;

// 阴影变量
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.08);
$box-shadow-light: 0 1px 4px rgba(0, 0, 0, 0.05);
$box-shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.12);
$box-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.15);

// 动画变量
$transition-duration: 0.3s;
$transition-timing-function: ease;
