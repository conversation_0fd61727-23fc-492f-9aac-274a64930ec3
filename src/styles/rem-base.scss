// rem基础样式设置
// 基于1920px设计稿，设置合适的rem基准值
// Demo项目页面总宽度为1920px，我们设置1rem = 37.5px，这样设计稿51.2rem = 1920px
html {
  font-size: 37.5px; // 1rem = 37.5px，基于1920px设计稿
}

// 针对不同屏幕尺寸的适配
@media screen and (max-width: 1440px) {
  html {
    font-size: 28px; // 1440px屏幕适配
  }
}

@media screen and (min-width: 1441px) and (max-width: 1920px) {
  html {
    font-size: 37.5px; // 标准1920px
  }
}

@media screen and (min-width: 1921px) {
  html {
    font-size: 40px; // 大屏幕适配
  }
}

// 重置一些基础样式
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
