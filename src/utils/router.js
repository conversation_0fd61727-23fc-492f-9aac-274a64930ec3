/**
 * 路由跳转工具函数，支持强制刷新页面
 * @param {Object} router - Vue Router实例
 * @param {String|Object} location - 目标路由路径或对象
 * @param {Boolean} forceRefresh - 是否强制刷新组件
 */
export function routerPush(router, location, forceRefresh = true) {
  // 如果传入的是字符串路径，将其转换为路由对象
  const route = typeof location === 'string'
    ? { path: location }
    : location

  // 添加强制刷新参数
  if (forceRefresh) {
    if (!route.params) {
      route.params = {}
    }
    route.params.forceRefresh = true
  }

  // 执行路由跳转
  return router.push(route)
}

/**
 * 使用replace方式进行路由跳转，支持强制刷新页面
 * @param {Object} router - Vue Router实例
 * @param {String|Object} location - 目标路由路径或对象
 * @param {Boolean} forceRefresh - 是否强制刷新组件
 */
export function routerReplace(router, location, forceRefresh = true) {
  // 如果传入的是字符串路径，将其转换为路由对象
  const route = typeof location === 'string'
    ? { path: location }
    : location

  // 添加强制刷新参数
  if (forceRefresh) {
    if (!route.params) {
      route.params = {}
    }
    route.params.forceRefresh = true
  }

  // 使用replace方式进行路由跳转
  return router.replace(route)
}
