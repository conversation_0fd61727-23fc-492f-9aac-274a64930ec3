/**
 * 文档格式转换工具
 * 用于处理文档格式转换，特别是 docx 到 doc 的转换
 */

/**
 * 检查文件是否为 docx 格式
 * @param {File} file - 文件对象
 * @returns {boolean} 是否为 docx 格式
 */
export function isDocxFile(file) {
  if (!file) return false;

  // 检查文件扩展名
  const fileName = file.name.toLowerCase();
  const isDocxExtension = fileName.endsWith('.docx');

  // 检查 MIME 类型
  const isDocxMimeType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

  return isDocxExtension || isDocxMimeType;
}

/**
 * 将 docx 文件转换为 doc 格式
 * 通过修改文件名和MIME类型来实现转换
 * @param {File} file - 原始 docx 文件
 * @returns {File} 转换后的文件对象
 */
export function convertDocxToDoc(file) {
  if (!isDocxFile(file)) {
    return file; // 如果不是 docx 文件，直接返回原文件
  }

  try {
    // 创建新的文件名（将 .docx 替换为 .doc）
    const originalName = file.name;
    const newFileName = originalName.replace(/\.docx$/i, '.doc');

    console.log(`文档格式转换: ${originalName} -> ${newFileName}`);

    // 创建新的 File 对象，保持原始内容但修改名称和类型
    const convertedFile = new File([file], newFileName, {
      type: 'application/msword', // 设置为 doc 的 MIME 类型
      lastModified: file.lastModified
    });

    // 添加转换标记，便于后续处理识别
    convertedFile._originalFormat = 'docx';
    convertedFile._convertedToDoc = true;
    convertedFile._originalName = originalName;

    return convertedFile;
  } catch (error) {
    console.error('文档格式转换失败:', error);
    // 转换失败时返回原文件
    return file;
  }
}

/**
 * 处理文件上传前的转换
 * @param {File} file - 原始文件
 * @returns {Object} 处理结果 { file: File, converted: boolean, originalName: string, convertedName: string }
 */
export function processFileForUpload(file) {
  // 如果是 docx 文件，进行转换
  if (isDocxFile(file)) {
    const convertedFile = convertDocxToDoc(file);
    return {
      file: convertedFile,
      converted: true,
      originalName: file.name,
      convertedName: convertedFile.name
    };
  }

  // 其他格式的文件直接返回
  return {
    file: file,
    converted: false,
    originalName: file.name,
    convertedName: file.name
  };
}
