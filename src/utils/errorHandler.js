import { Message } from 'element-ui'

/**
 * 错误消息映射表 - 将技术错误转换为用户友好的提示
 */
const ERROR_MESSAGE_MAP = {
  // 网络相关错误
  'Network Error': '网络连接异常，请检查网络后重试',
  'timeout': '请求超时，请稍后重试',
  'ECONNREFUSED': '服务暂时不可用，请稍后重试',
  'ENOTFOUND': '网络连接失败，请检查网络设置',

  // 认证相关错误
  'Unauthorized': '登录已过期，请重新登录',
  'token expired': '登录已过期，请重新登录',
  'invalid token': '登录状态异常，请重新登录',
  'authentication failed': '身份验证失败，请重新登录',

  // 权限相关错误
  'Forbidden': '权限不足，无法执行此操作',
  'Access denied': '权限不足，请联系管理员',
  'insufficient permissions': '权限不足，请联系管理员',

  // 资源相关错误
  'Not Found': '请求的资源不存在',
  'Resource not found': '资源不存在或已被删除',
  'File not found': '文件不存在',

  // 服务器相关错误
  'Internal Server Error': '服务暂时繁忙，请稍后重试',
  'Bad Gateway': '服务暂时不可用，请稍后重试',
  'Service Unavailable': '服务暂时不可用，请稍后重试',
  'Gateway Timeout': '服务响应超时，请稍后重试',

  // 文件上传相关错误
  'file too large': '文件大小超出限制',
  'unsupported file type': '不支持的文件格式',
  'upload failed': '文件上传失败，请重试',

  // 业务相关错误关键词
  'validation failed': '输入信息有误，请检查后重试',
  'invalid request': '请求参数有误，请重试',
  'duplicate': '信息重复，请检查后重试',
  'conflict': '操作冲突，请刷新页面后重试',

  // 数据库相关错误
  'database error': '数据处理异常，请稍后重试',
  'connection failed': '连接失败，请检查网络后重试',

  // 流式响应相关错误
  'stream error': '数据传输异常，请重试',
  'connection lost': '连接中断，请重新操作',
  'BodyStreamBuffer was aborted': '操作已取消',
  'aborted': '操作已取消',

  // AI相关错误
  'model error': 'AI服务暂时不可用，请稍后重试',
  'generation failed': 'AI生成失败，请重试',
  'rate limit': '请求过于频繁，请稍后重试'
}

/**
 * 将技术错误消息转换为用户友好的提示
 * @param {string} errorMessage - 原始错误消息
 * @returns {string} - 用户友好的错误消息，如果没有匹配则返回原始消息
 */
function translateErrorMessage(errorMessage) {
  if (!errorMessage || typeof errorMessage !== 'string') {
    return '操作失败，请重试'
  }

  // 转换为小写进行匹配
  const lowerMessage = errorMessage.toLowerCase()

  // 遍历错误映射表，查找匹配的关键词
  for (const [keyword, friendlyMessage] of Object.entries(ERROR_MESSAGE_MAP)) {
    if (lowerMessage.includes(keyword.toLowerCase())) {
      return friendlyMessage
    }
  }

  // 如果没有匹配到ERROR_MESSAGE_MAP中的任何项，直接返回原始错误消息
  // 这样可以逐步完善错误映射表，而不会丢失原始错误信息
  return errorMessage
}

/**
 * 统一的错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @param {Object} vue - Vue实例（可选，如果传入则使用$message）
 */
export function handleError(error, defaultMessage = '操作失败，请重试', vue = null) {
  console.error('操作失败:', error)

  // 检查是否已在拦截器中处理过错误
  if (error && error.alreadyHandled) {
    return // 已经处理过，直接返回
  }

  // 确定显示的错误消息
  let errorMessage = defaultMessage

  if (error) {
    let rawMessage = ''

    if (error.message) {
      rawMessage = error.message
    } else if (error.response && error.response.data && error.response.data.message) {
      rawMessage = error.response.data.message
    } else if (typeof error === 'string') {
      rawMessage = error
    }

    // 转换为用户友好的错误消息
    if (rawMessage) {
      errorMessage = translateErrorMessage(rawMessage)
    }
  }

  // 显示错误消息
  if (vue && vue.$message) {
    vue.$message.error(errorMessage)
  } else {
    Message({
      message: errorMessage,
      type: 'error',
      duration: 3000
    })
  }
}

/**
 * 网络错误处理函数
 * @param {Error} error - 网络错误对象
 * @param {Object} vue - Vue实例（可选）
 */
export function handleNetworkError(error, vue = null) {
  console.error('网络错误:', error)

  if (error && error.alreadyHandled) {
    return
  }

  let errorMessage = '网络连接失败，请检查网络后重试'

  if (error && error.response) {
    switch (error.response.status) {
      case 401:
        errorMessage = '登录已过期，请重新登录'
        break
      case 403:
        errorMessage = '权限不足，无法执行该操作'
        break
      case 404:
        errorMessage = '请求的资源不存在'
        break
      case 500:
        errorMessage = '服务暂时繁忙，请稍后重试'
        break
      case 502:
      case 503:
      case 504:
        errorMessage = '服务暂时不可用，请稍后重试'
        break
      default:
        if (error.response.data && error.response.data.message) {
          // 对服务器返回的错误消息也进行转换
          errorMessage = translateErrorMessage(error.response.data.message)
        }
    }
  } else if (error && error.message) {
    // 对网络错误消息进行转换
    errorMessage = translateErrorMessage(error.message)
  }

  if (vue && vue.$message) {
    vue.$message.error(errorMessage)
  } else {
    Message({
      message: errorMessage,
      type: 'error',
      duration: 3000
    })
  }
}

/**
 * 业务错误处理函数（用于处理接口返回的业务错误）
 * @param {Object} response - 接口响应对象
 * @param {string} defaultMessage - 默认错误消息
 * @param {Object} vue - Vue实例（可选）
 */
export function handleBusinessError(response, defaultMessage = '操作失败', vue = null) {
  if (!response) {
    handleError(null, defaultMessage, vue)
    return
  }

  // 如果是成功响应，直接返回
  if (response.isSuccess || response.success || response.is_success) {
    return
  }

  // 提取错误消息并转换为用户友好的提示
  const rawMessage = response.message || response.msg || defaultMessage
  const errorMessage = translateErrorMessage(rawMessage)

  if (vue && vue.$message) {
    vue.$message.error(errorMessage)
  } else {
    Message({
      message: errorMessage,
      type: 'error',
      duration: 3000
    })
  }
}

/**
 * 简化的异步操作错误处理装饰器
 * @param {Function} asyncFunc - 异步函数
 * @param {string} defaultMessage - 默认错误消息
 * @param {Object} vue - Vue实例
 */
export function withErrorHandling(asyncFunc, defaultMessage, vue) {
  return async (...args) => {
    try {
      return await asyncFunc(...args)
    } catch (error) {
      handleError(error, defaultMessage, vue)
      throw error // 重新抛出错误，让调用者可以做进一步处理
    }
  }
}

/**
 * Vue插件 - 为所有组件提供友好的错误处理方法
 */
export const ErrorHandlerPlugin = {
  install(Vue) {
    // 全局混入，为所有组件添加友好的错误处理方法
    Vue.mixin({
      methods: {
        /**
         * 显示友好的错误消息
         * @param {string|Error} error - 错误消息或错误对象
         * @param {string} defaultMessage - 默认错误消息
         */
        $showFriendlyError(error, defaultMessage = '操作失败，请重试') {
          // 检查是否已在拦截器中处理过错误
          if (error && error.alreadyHandled) {
            return // 已经处理过，直接返回，避免重复显示
          }

          let errorMessage = defaultMessage

          if (error) {
            if (typeof error === 'string') {
              errorMessage = translateErrorMessage(error)
            } else if (error.message) {
              errorMessage = translateErrorMessage(error.message)
            } else if (error.response && error.response.data && error.response.data.message) {
              errorMessage = translateErrorMessage(error.response.data.message)
            }
          }

          this.$message.error(errorMessage)
        },

        /**
         * 转换错误消息为用户友好的提示
         * @param {string} errorMessage - 原始错误消息
         * @returns {string} - 用户友好的错误消息
         */
        $translateError(errorMessage) {
          return translateErrorMessage(errorMessage)
        }
      }
    })
  }
}

/**
 * 导出错误消息转换函数，供其他模块使用
 */
export { translateErrorMessage }

/**
 * 使用说明和示例：
 *
 * 1. 在组件中使用全局方法（推荐）：
 *    this.$showFriendlyError(error, '操作失败，请重试')
 *    this.$showFriendlyError('Network Error', '网络连接失败')
 *
 * 2. 直接使用转换函数：
 *    import { translateErrorMessage } from '@/utils/errorHandler'
 *    this.$message.error(translateErrorMessage(error.message))
 *
 * 3. 使用统一的错误处理函数：
 *    import { handleError } from '@/utils/errorHandler'
 *    handleError(error, '自定义默认消息', this)
 *
 * 4. 替换现有错误处理的建议：
 *    原来：this.$message.error(error.message)
 *    改为：this.$showFriendlyError(error)
 *
 *    原来：this.$message.error('操作失败: ' + error.message)
 *    改为：this.$showFriendlyError(error, '操作失败，请重试')
 *
 * 5. 错误映射表会自动处理常见的技术错误，包括：
 *    - 网络错误（Network Error, timeout等）
 *    - 认证错误（Unauthorized, token expired等）
 *    - 权限错误（Forbidden, Access denied等）
 *    - 服务器错误（Internal Server Error, Bad Gateway等）
 *    - 文件错误（file too large, unsupported file type等）
 *    - AI相关错误（model error, generation failed等）
 */
