import OSS from 'ali-oss'

// OSS配置
const ossConfig = {
  development: {
    region: 'oss-cn-hangzhou',
    accessKeyId: 'your-dev-access-key',
    accessKeySecret: 'your-dev-access-secret',
    bucket: 'your-dev-bucket'
  },
  test: {
    region: 'oss-cn-hangzhou',
    accessKeyId: 'your-test-access-key',
    accessKeySecret: 'your-test-access-secret',
    bucket: 'your-test-bucket'
  },
  production: {
    region: 'oss-cn-hangzhou',
    accessKeyId: 'your-prod-access-key',
    accessKeySecret: 'your-prod-access-secret',
    bucket: 'your-prod-bucket'
  }
}

// 获取当前环境配置
const currentConfig = ossConfig[process.env.NODE_ENV] || ossConfig.development

class OSSUtil {
  constructor() {
    this.client = new OSS(currentConfig)
  }

  /**
   * 上传文件
   * @param {File} file 文件对象
   * @param {String} dir 存储目录
   * @returns {Promise<String>} 文件访问URL
   */
  async uploadFile(file, dir = 'uploads') {
    try {
      const fileName = `${dir}/${Date.now()}-${file.name}`
      const result = await this.client.put(fileName, file)
      return result.url
    } catch (error) {
      console.error('OSS上传失败:', error)
      throw new Error('文件上传失败')
    }
  }

  /**
   * 上传Base64图片
   * @param {String} base64 base64字符串
   * @param {String} dir 存储目录
   * @returns {Promise<String>} 文件访问URL
   */
  async uploadBase64(base64, dir = 'images') {
    try {
      const buffer = Buffer.from(base64.split(',')[1], 'base64')
      const fileName = `${dir}/${Date.now()}.png`
      const result = await this.client.put(fileName, buffer)
      return result.url
    } catch (error) {
      console.error('OSS上传失败:', error)
      throw new Error('图片上传失败')
    }
  }

  /**
   * 删除文件
   * @param {String} url 文件URL或路径
   * @returns {Promise<void>}
   */
  async deleteFile(url) {
    try {
      const fileName = url.split('/').pop()
      await this.client.delete(fileName)
    } catch (error) {
      console.error('OSS删除失败:', error)
      throw new Error('文件删除失败')
    }
  }

  /**
   * 获取文件临时访问URL
   * @param {String} fileName 文件名
   * @param {Number} expires 过期时间(秒)
   * @returns {Promise<String>} 临时访问URL
   */
  async getSignedUrl(fileName, expires = 3600) {
    try {
      const url = await this.client.signatureUrl(fileName, {
        expires
      })
      return url
    } catch (error) {
      console.error('获取签名URL失败:', error)
      throw new Error('获取文件访问地址失败')
    }
  }
}

export default new OSSUtil()
