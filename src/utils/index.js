/*
 * @Description: haolin.hu Create File
 * @Version: 2.0
 * @Autor: haolin.hu
 * @Date: 2025-06-05 10:30:46
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-07-19 14:56:59
 */
export { default as oss } from './oss';

// 可以在这里导出其他工具类
export const formatDate = (date, /* eslint-disable-line no-unused-vars */ format = 'YYYY-MM-DD HH:mm:ss') => {
  // 日期格式化方法
}

export const formatFileSize = (/* eslint-disable-line no-unused-vars */ bytes) => {
  // 文件大小格式化方法
}

/**
 * 根据文件扩展名获取文件图标
 * @param {string} ext - 文件扩展名
 * @returns {string} - 文件图标的HTML字符串
 */
const getFileIcon = (ext) => {
  const iconMap = {
    'doc': 'DOC',
    'docx': 'DOCX',
    'pdf': 'PDF',
    'txt': 'TXT',
    'xls': 'XLS',
    'xlsx': 'XLSX',
    'ppt': 'PPT',
    'pptx': 'PPTX',
    'zip': 'ZIP',
    'rar': 'RAR',
    '7z': '7Z'
  };
  return iconMap[ext.toLowerCase()] || 'FILE';
}



/**
 * 将Markdown格式文本转换为HTML
 * @param {string} outputText - Markdown格式的文本
 * @returns {string} - 格式化后的HTML文本
 */
export const formatOutputContent = (outputText) => {
  if (!outputText) return '';

  // 检查是否已经是格式化过的HTML内容
  // 如果已经有markdown-content容器，则认为已经完全格式化过
  if (outputText.includes('<div class="markdown-content">')) {
    return outputText;
  }

  // 智能换行处理 - 专门处理健康档案格式
  let formattedText = outputText;

  // 1. 处理 **标签：** 后面紧跟内容的情况，确保标签和内容分行
  formattedText = formattedText.replace(/(\*\*[^*]+：\*\*)([^*\n])/g, '$1\n$2');

  // 2. 处理分隔线前后的换行问题
  // 确保分隔线前有换行符（如果前面不是换行符）
  formattedText = formattedText.replace(/([^\n])---/g, '$1\n---');
  // 确保分隔线后有换行符（如果后面不是换行符）
  formattedText = formattedText.replace(/---([^\n])/g, '---\n$1');

  // 3. 处理连续的分隔线，确保它们独占一行
  formattedText = formattedText.replace(/---+/g, '---');

  // 将Markdown格式转换为HTML
  formattedText = formattedText
    // 处理标题 - 添加更丰富的样式类
    .replace(/### (.*?)\n/g, '<h3 class="md-title md-h3"><span class="md-title-icon">§</span>$1</h3>\n')
    .replace(/## (.*?)\n/g, '<h2 class="md-title md-h2"><span class="md-title-icon">§</span>$1</h2>\n')
    .replace(/# (.*?)\n/g, '<h1 class="md-title md-h1"><span class="md-title-icon">§</span>$1</h1>\n')

    // 处理粗体和斜体
    .replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>') // 粗斜体
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
    .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体

    // 处理代码段 - 添加语法高亮容器
    .replace(/```(\w*)\n([\s\S]*?)```/g, (match, lang, code) => {
      const language = lang ? ` language-${lang}` : '';
      return `<div class="code-container"><div class="code-header">${lang || 'code'}</div><pre class="code-block${language}"><code>${code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre></div>`;
    })

    // 处理行内代码
    .replace(/`(.*?)`/g, '<code class="inline-code">$1</code>')

                // 处理链接和图片
    // 处理 Markdown 图片语法 ![alt](url) - 显示图片
    .replace(/!\[(.*?)\]\((.*?)\)/g, '<div class="md-image-container"><div class="md-image-loading" style="display:none;">加载中...</div><img src="$2" alt="$1" class="md-image" onload="this.previousElementSibling.style.display=\'none\';" onerror="this.style.display=\'none\';this.previousElementSibling.style.display=\'none\';this.nextElementSibling.style.display=\'block\';" /><a href="$2" target="_blank" class="md-link image-fallback" style="display:none;">图片加载失败，点击查看原图: $2</a></div>')
    // 处理标准 Markdown 链接格式
    .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" class="md-link">$1</a>');

  // 先处理图片链接，再处理普通链接
  // 1. 处理base64图片 - data:image/ 开头的图片数据
  formattedText = formattedText.replace(/(^|[^">])(data:image\/[^;]+;base64,[A-Za-z0-9+/=\s]+)/gi, (match, prefix, base64Data) => {
    // 清理base64数据中的空格和换行符
    const cleanBase64 = base64Data.replace(/\s+/g, '');
    return `${prefix}<div class="md-image-container"><img src="${cleanBase64}" alt="base64图片" class="md-image" onerror="this.style.display='none';this.nextElementSibling.style.display='block';" /><div class="md-link image-fallback" style="display:none;">图片格式错误，无法显示</div></div>`;
  });

  // 2. 处理blob图片链接
  formattedText = formattedText.replace(/(^|[^">])(blob:[^\s<"]+)/gi, (match, prefix, blobUrl) => {
    return `${prefix}<div class="md-image-container"><img src="${blobUrl}" alt="blob图片" class="md-image" onerror="this.style.display='none';this.nextElementSibling.style.display='block';" /><div class="md-link image-fallback" style="display:none;">图片链接已失效</div></div>`;
  });

  // 3. 处理图片链接 - 替换整个链接文本，不保留原链接
  formattedText = formattedText.replace(/(^|[^">])(https?:\/\/[^\s<"]+\.(jpg|jpeg|png|gif|webp|svg|bmp)(\?[^\s]*)?)/gi, (match, prefix, url) => {
    return `${prefix}<div class="md-image-container"><div class="md-image-loading" style="display:none;">加载中...</div><img src="${url}" alt="图片" class="md-image" onload="this.previousElementSibling.style.display='none';" onerror="this.style.display='none';this.previousElementSibling.style.display='none';this.nextElementSibling.style.display='block';" /><a href="${url}" target="_blank" class="md-link image-fallback" style="display:none;">图片加载失败，点击查看原图</a></div>`;
  });

  // 4. 处理云存储图片链接 - 只处理图片格式的文件
  formattedText = formattedText.replace(/(^|[^">])(https?:\/\/[^\s<"]*\.(?:obs\.[\w-]+\.myhuaweicloud\.com|oss\.[\w-]+\.aliyuncs\.com|cos\.[\w-]+\.myqcloud\.com|[\w-]+\.qiniu\.com|[\w-]+\.amazonaws\.com)[^\s<"]*\.(jpg|jpeg|png|gif|webp|svg|bmp)(\?[^\s]*)?)/gi, (match, prefix, url) => {
    return `${prefix}<div class="md-image-container"><div class="md-image-loading" style="display:none;">加载中...</div><img src="${url}" alt="图片" class="md-image" onload="this.previousElementSibling.style.display='none';" onerror="this.style.display='none';this.previousElementSibling.style.display='none';this.nextElementSibling.style.display='block';" /><a href="${url}" target="_blank" class="md-link image-fallback" style="display:none;">图片加载失败，点击查看原图</a></div>`;
  });

  // 5. 处理云存储文档文件链接 - 创建下载链接，特别处理文档格式
  formattedText = formattedText.replace(/(https?:\/\/[^\s<"]*\.(?:obs\.[\w-]+\.myhuaweicloud\.com|oss\.[\w-]+\.aliyuncs\.com|cos\.[\w-]+\.myqcloud\.com|[\w-]+\.qiniu\.com|[\w-]+\.amazonaws\.com)[^\s<"]*\.(?:docx|doc|pdf|txt|xlsx|xls|pptx|ppt|zip|rar|7z)(?:\?[^\s]*)?)/gi, (match) => {
    const fileName = match.split('/').pop().split('?')[0]; // 提取文件名，去掉查询参数
    const ext = fileName.split('.').pop().toLowerCase(); // 获取文件扩展名
    const fileIcon = getFileIcon(ext);

    return '<div style="display: flex; align-items: center; padding: 8px 12px; margin: 4px 0; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px;">' +
      '<div style="width: 32px; height: 32px; background: #4285f4; border-radius: 4px; display: flex; align-items: center; justify-content: center; margin-right: 8px;">' +
        '<span style="color: white; font-size: 8px; font-weight: bold;">' + fileIcon + '</span>' +
      '</div>' +
      '<div style="flex: 1; overflow: hidden;">' +
        '<div style="color: #1a73e8; font-size: 13px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">' + fileName + '</div>' +
        '<div style="color: #6c757d; font-size: 11px;">8.00B</div>' +
      '</div>' +
      '<a href="' + match + '" target="_blank" style="margin-left: 8px; cursor: pointer; padding: 4px; text-decoration: none;" title="下载文件">' +
        '<svg width="16" height="16" viewBox="0 0 24 24" fill="none">' +
          '<path d="M12 16L7 11H10V4H14V11H17L12 16Z" fill="#6c757d"/>' +
          '<path d="M5 20H19" stroke="#6c757d" stroke-width="2" stroke-linecap="round"/>' +
        '</svg>' +
      '</a>' +
    '</div>';
  });

  // 6. 处理其他HTTP(S)链接
  formattedText = formattedText.replace(/(^|[^">])(https?:\/\/[^\s<"]+)/g, (match, prefix, url) => {
    // 避免重复处理已经在HTML标签中的链接
    if (match.includes('<') || match.includes('>')) {
      return match;
    }

    // 避免处理云存储文档文件（应该由前面的步骤处理）
    if (url.match(/\.(?:obs\.[\w-]+\.myhuaweicloud\.com|oss\.[\w-]+\.aliyuncs\.com|cos\.[\w-]+\.myqcloud\.com|[\w-]+\.qiniu\.com|[\w-]+\.amazonaws\.com).*\.(?:docx|doc|pdf|txt|xlsx|xls|pptx|ppt|zip|rar|7z)(?:\?.*)?$/i)) {
      return match;
    }

    return `${prefix}<a href="${url}" target="_blank" class="md-link">${url}</a>`;
  });

  formattedText = formattedText
    // 处理分隔线 - 添加更美观的分隔线
    .replace(/\n---+\n/g, '\n<div class="md-divider"></div>\n')

    // 处理有序列表
    .replace(/^\d+\.\s+(.*?)(?:\n|$)/gm, '<li class="md-list-item ordered">$1</li>')

    // 处理无序列表 - 支持多种符号
    .replace(/^[*\-•]\s+(.*?)(?:\n|$)/gm, '<li class="md-list-item">$1</li>')

    // 处理引用块 - 添加更美观的引用样式
    .replace(/^>\s+(.*?)(?:\n|$)/gm, '<blockquote class="md-blockquote">$1</blockquote>')

    // 处理表格 (简化版)
    .replace(/\|(.+)\|/g, '<tr><td>$1</td></tr>'.replace(/\|/g, '</td><td>'))

    // 将换行符转换为<br>标签
    .replace(/\n/g, '<br>');

  // 处理列表：将连续的列表项包装在ul或ol中
  if (formattedText.includes('<li class="md-list-item ordered">')) {
    formattedText = formattedText.replace(/(<li class="md-list-item ordered">.*?<\/li>)+/g, match => {
      return `<ol class="md-list md-list-ordered">${match}</ol>`;
    });
  }

  if (formattedText.includes('<li class="md-list-item">')) {
    formattedText = formattedText.replace(/(<li class="md-list-item">.*?<\/li>)+/g, match => {
      return `<ul class="md-list">${match}</ul>`;
    });
  }

  // 处理表格：包装在table标签中
  if (formattedText.includes('<tr>')) {
    formattedText = formattedText.replace(/(<tr>.*?<\/tr>)+/g, match => {
      return `<table class="md-table">${match}</table>`;
    });
  }

  // 修复嵌套的问题
  formattedText = formattedText
    // 连续的blockquote合并
    .replace(/(<\/blockquote><br><blockquote[^>]*>)/g, '')
    // 移除blockquote内的<br>
    .replace(/<blockquote([^>]*)>(.*?)<\/blockquote>/gs, (match, attrs, content) => {
      return `<blockquote${attrs}>${content.replace(/<br>/g, ' ')}</blockquote>`;
    });

  // 添加主容器，便于样式隔离
  return `<div class="markdown-content">${formattedText}</div>`;
};

/**
 * 生成智能体分享链接
 * @param {string} appId - 应用ID
 * @returns {string} - 格式化的分享链接
 */
export const generateShareLink = (appId) => {
  // 获取当前环境的base URL
  const baseUrl = process.env.NODE_ENV === "production" ? "http://www.yuanzhiqi.com:3002" : "http://************:3002/";
  return `${baseUrl}/create/app/chat/${appId}?sourceType=1`;
};

/**
 * 将文本复制到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} - 是否复制成功
 */
export const copyToClipboard = async (text) => {
  if (!text) {
    return false;
  }

  try {
    // 首先尝试使用现代API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      return true;
    }

    // 回退到传统方法
    const textArea = document.createElement('textarea');
    textArea.value = text;
    // 防止scrolling
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);

    return successful;
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    return false;
  }
}

/**
 * 清除聊天记忆的公共方法
 * @param {Object} options - 配置参数
 * @param {string} options.sessionId - 会话ID
 * @param {Object} options.api - API对象
 * @param {Object} options.messageHandler - 消息处理器，包含 confirm, success, error 方法
 * @param {Function} options.clearLocalMessages - 清除本地消息的回调函数
 * @param {Function} options.stopStreaming - 停止流式输出的回调函数（可选）
 * @param {Function} options.stopReading - 停止语音朗读的回调函数（可选）
 * @param {Function} options.addWelcomeMessage - 添加欢迎消息的回调函数（可选）
 * @returns {Promise<boolean>} - 是否成功清除
 */
export const clearChatMemory = async (options) => {
  const {
    sessionId,
    api,
    messageHandler,
    clearLocalMessages,
    stopStreaming,
    stopReading,
    addWelcomeMessage
  } = options;

  try {
    // 显示确认对话框
    const confirmResult = await messageHandler.confirm(
      '确定要清除所有聊天记录和会话记忆吗？此操作不可恢复。',
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    if (!confirmResult) {
      return false;
    }

    // 如果有会话ID，调用清除记忆接口
    if (sessionId && api && api.llmService && api.llmService.clearMemory) {
      try {
        console.log('正在调用清除记忆接口，sessionId:', sessionId);
        const response = await api.llmService.clearMemory({
          sessionId: sessionId
        });

        console.log('清除记忆接口响应:', response);

        if (response && response.isSuccess) {
          if (response.data && response.data.success) {
            console.log('服务端记忆清除成功');
          } else {
            console.warn('服务端记忆清除失败:', response.data?.message || '未知错误');
            // 即使服务端失败，也继续清理本地数据
          }
        } else {
          console.warn('清除记忆接口调用失败:', response?.message || '未知错误');
          // 即使服务端失败，也继续清理本地数据
        }
      } catch (error) {
        console.error('调用清除记忆接口时出错:', error);
        // 即使服务端失败，也继续清理本地数据
      }
    }

    // 执行本地清理操作
    try {
      // 停止当前的语音朗读
      if (stopReading && typeof stopReading === 'function') {
        stopReading();
      }

      // 停止流式输出（如果有）
      if (stopStreaming && typeof stopStreaming === 'function') {
        stopStreaming();
      }

      // 清除本地消息
      if (clearLocalMessages && typeof clearLocalMessages === 'function') {
        clearLocalMessages();
      }

      // 添加新的欢迎消息（如果有）
      if (addWelcomeMessage && typeof addWelcomeMessage === 'function') {
        addWelcomeMessage();
      }

      messageHandler.success('聊天记录和会话记忆已清除');
      return true;

    } catch (error) {
      console.error('清理本地数据时出错:', error);
      messageHandler.error('清理本地数据失败');
      return false;
    }

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消，不显示错误
      return false;
    }
    console.error('清除聊天记忆失败:', error);
    messageHandler.error('清除聊天记忆失败');
    return false;
  }
};

/**
 * 去除字符串中的所有空格
 * @param {string} str - 要处理的字符串
 * @returns {string} - 去除空格后的字符串
 */
export const removeSpaces = (str) => {
  if (!str || typeof str !== 'string') {
    return '';
  }
  return str.replace(/\s+/g, '');
};

/**
 * 生成二维码的Base64数据URL
 * @param {string} text - 要生成二维码的文本
 * @param {Object} options - 二维码配置选项
 * @param {number} options.width - 二维码宽度，默认200
 * @param {string} options.color - 二维码颜色，默认'#000000'
 * @param {string} options.backgroundColor - 背景颜色，默认'#FFFFFF'
 * @param {number} options.margin - 边距，默认4
 * @returns {Promise<string>} - 返回Base64格式的二维码图片URL
 */
export const generateQRCode = async (text, options = {}) => {
  const {
    width = 200,
    color = '#000000',
    backgroundColor = '#FFFFFF',
    margin = 4
  } = options;

  try {
    // 动态导入qrcode库
    const QRCode = await import('qrcode');

    // 生成二维码的配置
    const qrOptions = {
      width: width,
      margin: margin,
      color: {
        dark: color,
        light: backgroundColor
      },
      errorCorrectionLevel: 'M' // 中等纠错级别
    };

    // 生成二维码的Data URL
    const dataURL = await QRCode.toDataURL(text, qrOptions);
    return dataURL;
  } catch (error) {
    console.error('生成二维码失败:', error);
    // 返回一个默认的错误占位符
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaJp+ihjOaJp+ihjOaJp+ihjTwvdGV4dD48L3N2Zz4=';
  }
};

/**
 * 生成二维码并返回Canvas元素
 * @param {string} text - 要生成二维码的文本
 * @param {Object} options - 二维码配置选项
 * @returns {Promise<HTMLCanvasElement>} - 返回Canvas元素
 */
export const generateQRCodeCanvas = async (text, options = {}) => {
  try {
    const QRCode = await import('qrcode');

    const qrOptions = {
      width: options.width || 200,
      margin: options.margin || 4,
      color: {
        dark: options.color || '#000000',
        light: options.backgroundColor || '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    };

    // 创建canvas元素
    const canvas = document.createElement('canvas');
    await QRCode.toCanvas(canvas, text, qrOptions);
    return canvas;
  } catch (error) {
    console.error('生成二维码Canvas失败:', error);
    throw error;
  }
};

// ... 其他通用工具方法
