/**
 * 语音录制工具
 * 支持长按录音、语音识别、音频处理等功能
 */

class VoiceRecorder {
  constructor(options = {}) {
    this.options = {
      sampleRate: 16000, // 采样率
      channels: 1, // 声道数
      bitDepth: 16, // 位深度
      maxDuration: 60000, // 最大录制时长(ms)
      minDuration: 1000, // 最小录制时长(ms)
      ...options
    };

    this.mediaRecorder = null;
    this.audioChunks = [];
    this.stream = null;
    this.isRecording = false;
    this.startTime = null;
    this.recordingTimer = null;

    // 回调函数
    this.onStart = options.onStart || (() => {});
    this.onStop = options.onStop || (() => {});
    this.onData = options.onData || (() => {});
    this.onError = options.onError || (() => {});
    this.onProgress = options.onProgress || (() => {});
  }

  /**
   * 检查浏览器是否支持录音功能
   */
  static isSupported() {
    return !!(navigator.mediaDevices &&
              navigator.mediaDevices.getUserMedia &&
              window.MediaRecorder);
  }

  /**
   * 检查麦克风权限
   */
  async checkPermissions() {
    try {
      if (!navigator.permissions) {
        // 在不支持permissions API的浏览器中，直接尝试获取媒体流
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach(track => track.stop());
        return { state: 'granted' };
      }

      const permission = await navigator.permissions.query({ name: 'microphone' });
      return permission;
    } catch (error) {
      console.error('检查麦克风权限失败:', error);
      throw new Error('无法检查麦克风权限');
    }
  }

  /**
   * 开始录音
   */
  async startRecording() {
    try {
      if (this.isRecording) {
        console.warn('已经在录音中');
        return;
      }

      // 检查浏览器支持
      if (!VoiceRecorder.isSupported()) {
        throw new Error('当前浏览器不支持录音功能');
      }

      // 获取麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.options.sampleRate,
          channelCount: this.options.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // 创建MediaRecorder
      const mimeTypes = [
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/mp4',
        'audio/wav'
      ];

      let mimeType = '';
      for (const type of mimeTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          mimeType = type;
          break;
        }
      }

      if (!mimeType) {
        throw new Error('浏览器不支持任何可用的音频格式');
      }

      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: mimeType
      });

      // 清空之前的录音数据
      this.audioChunks = [];

      // 设置事件监听
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.handleRecordingStop();
      };

      this.mediaRecorder.onerror = (event) => {
        console.error('录音错误:', event.error);
        this.onError(new Error(`录音错误: ${event.error}`));
        this.cleanup();
      };

      // 开始录音
      this.mediaRecorder.start(100); // 每100ms收集一次数据
      this.isRecording = true;
      this.startTime = Date.now();

      // 设置最大录制时长定时器
      this.recordingTimer = setTimeout(() => {
        if (this.isRecording) {
          this.stopRecording();
        }
      }, this.options.maxDuration);

      // 开始进度更新
      this.updateProgress();

      console.log('开始录音');
      this.onStart();

    } catch (error) {
      console.error('开始录音失败:', error);
      this.cleanup();
      this.onError(error);
      throw error;
    }
  }

  /**
   * 停止录音
   */
  async stopRecording() {
    try {
      if (!this.isRecording || !this.mediaRecorder) {
        console.warn('当前没有在录音');
        return null;
      }

      const duration = Date.now() - this.startTime;

      // 检查最小录制时长
      if (duration < this.options.minDuration) {
        this.cleanup();
        throw new Error(`录音时长不能少于${this.options.minDuration / 1000}秒`);
      }

      // 停止录音
      this.mediaRecorder.stop();
      this.isRecording = false;

      // 清除定时器
      if (this.recordingTimer) {
        clearTimeout(this.recordingTimer);
        this.recordingTimer = null;
      }

      console.log(`录音结束，时长: ${duration}ms`);

      // 返回一个Promise，等待录音数据处理完成
      return new Promise((resolve) => {
        const originalOnData = this.onData;
        this.onData = (audioBlob, duration) => {
          originalOnData(audioBlob, duration);
          resolve({ audioBlob, duration });
        };
      });

    } catch (error) {
      console.error('停止录音失败:', error);
      this.cleanup();
      this.onError(error);
      throw error;
    }
  }

  /**
   * 处理录音停止事件
   */
  handleRecordingStop() {
    try {
      if (this.audioChunks.length === 0) {
        console.warn('没有录音数据');
        this.cleanup();
        return;
      }

      // 创建音频Blob
      const audioBlob = new Blob(this.audioChunks, {
        type: this.mediaRecorder.mimeType || 'audio/webm'
      });

      const duration = Date.now() - this.startTime;

      console.log('录音数据处理完成:', {
        size: audioBlob.size,
        duration: duration,
        type: audioBlob.type
      });

      this.onData(audioBlob, duration);
      this.onStop(audioBlob, duration);

    } catch (error) {
      console.error('处理录音数据失败:', error);
      this.onError(error);
    } finally {
      this.cleanup();
    }
  }

  /**
   * 更新录音进度
   */
  updateProgress() {
    if (!this.isRecording) return;

    const duration = Date.now() - this.startTime;
    const progress = Math.min(duration / this.options.maxDuration, 1);

    this.onProgress(duration, progress);

    if (this.isRecording) {
      setTimeout(() => this.updateProgress(), 100);
    }
  }

  /**
   * 取消录音
   */
  cancelRecording() {
    try {
      if (this.isRecording && this.mediaRecorder) {
        this.mediaRecorder.stop();
      }
      this.isRecording = false;
      this.cleanup();
      console.log('录音已取消');
    } catch (error) {
      console.error('取消录音失败:', error);
      this.onError(error);
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    try {
      // 停止所有音频轨道
      if (this.stream) {
        this.stream.getTracks().forEach(track => {
          track.stop();
        });
        this.stream = null;
      }

      // 清除定时器
      if (this.recordingTimer) {
        clearTimeout(this.recordingTimer);
        this.recordingTimer = null;
      }

      // 重置状态
      this.mediaRecorder = null;
      this.audioChunks = [];
      this.isRecording = false;
      this.startTime = null;

    } catch (error) {
      console.error('清理资源失败:', error);
    }
  }

  /**
   * 获取录音状态
   */
  getRecordingState() {
    return {
      isRecording: this.isRecording,
      duration: this.startTime ? Date.now() - this.startTime : 0,
      isSupported: VoiceRecorder.isSupported()
    };
  }
}

/**
 * 语音识别工具
 */
class VoiceRecognition {
  constructor(options = {}) {
    this.options = {
      language: 'zh-CN', // 识别语言
      continuous: false, // 是否连续识别
      interimResults: true, // 是否返回临时结果
      ...options
    };

    this.recognition = null;
    this.isListening = false;

    // 回调函数
    this.onResult = options.onResult || (() => {});
    this.onError = options.onError || (() => {});
    this.onStart = options.onStart || (() => {});
    this.onEnd = options.onEnd || (() => {});
  }

  /**
   * 检查浏览器是否支持语音识别
   */
  static isSupported() {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition);
  }

  /**
   * 开始语音识别
   */
  startRecognition() {
    try {
      if (!VoiceRecognition.isSupported()) {
        throw new Error('当前浏览器不支持语音识别功能');
      }

      if (this.isListening) {
        console.warn('语音识别已在进行中');
        return;
      }

      // 创建语音识别对象
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();

      // 配置识别参数
      this.recognition.lang = this.options.language;
      this.recognition.continuous = this.options.continuous;
      this.recognition.interimResults = this.options.interimResults;

      // 设置事件监听
      this.recognition.onstart = () => {
        this.isListening = true;
        console.log('语音识别开始');
        this.onStart();
      };

      this.recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        this.onResult({
          finalTranscript,
          interimTranscript,
          isFinal: finalTranscript !== ''
        });
      };

      this.recognition.onerror = (event) => {
        console.error('语音识别错误:', event.error);
        this.isListening = false;
        this.onError(new Error(`语音识别错误: ${event.error}`));
      };

      this.recognition.onend = () => {
        this.isListening = false;
        console.log('语音识别结束');
        this.onEnd();
      };

      // 开始识别
      this.recognition.start();

    } catch (error) {
      console.error('开始语音识别失败:', error);
      this.onError(error);
      throw error;
    }
  }

  /**
   * 停止语音识别
   */
  stopRecognition() {
    try {
      if (this.recognition && this.isListening) {
        this.recognition.stop();
        this.isListening = false;
        console.log('语音识别已停止');
      }
    } catch (error) {
      console.error('停止语音识别失败:', error);
      this.onError(error);
    }
  }

  /**
   * 取消语音识别
   */
  cancelRecognition() {
    try {
      if (this.recognition && this.isListening) {
        this.recognition.abort();
        this.isListening = false;
        console.log('语音识别已取消');
      }
    } catch (error) {
      console.error('取消语音识别失败:', error);
      this.onError(error);
    }
  }
}

/**
 * 语音播报工具
 * 支持服务器TTS和本地语音合成
 */
class VoiceTTS {
  constructor(options = {}) {
    this.options = {
      preferServer: true, // 优先使用服务器TTS
      fallbackToLocal: true, // 服务器失败时降级到本地
      ...options
    };

    // 状态管理
    this.isReading = false;
    this.currentReadingMessageIndex = -1;
    this.isLoadingAudio = false;
    this.currentAudio = null;
    this.speechSynthesis = null;

    // 回调函数
    this.onStart = options.onStart || (() => {});
    this.onStop = options.onStop || (() => {});
    this.onError = options.onError || (() => {});
    this.onProgress = options.onProgress || (() => {});

    // 初始化本地语音合成
    this.initSpeechSynthesis();
  }

  /**
   * 初始化本地语音合成
   */
  initSpeechSynthesis() {
    try {
      if ('speechSynthesis' in window) {
        this.speechSynthesis = window.speechSynthesis;
        console.log('本地语音合成功能已初始化');
      } else {
        console.warn('浏览器不支持本地语音合成功能');
      }
    } catch (error) {
      console.error('初始化本地语音合成失败:', error);
    }
  }

  /**
   * 检查浏览器支持情况
   */
  static checkSupport() {
    return {
      speechSynthesis: 'speechSynthesis' in window,
      audioPlayback: 'Audio' in window
    };
  }

  /**
   * 语音朗读消息
   * @param {string} content - 要朗读的内容（可以是HTML）
   * @param {number} messageIndex - 消息索引（用于状态管理）
   * @param {Object} ttsParams - TTS参数
   * @param {Function} api - API对象（用于调用服务器TTS）
   * @param {Function} messageHandler - 消息处理函数（用于显示提示）
   */
  async readMessage(content, messageIndex, ttsParams = {}, api = null, messageHandler = null) {
    try {
      // 如果正在朗读同一条消息，则停止朗读
      if (this.isReading && this.currentReadingMessageIndex === messageIndex) {
        this.stopReading();
        return;
      }

      // 停止当前朗读（如果有）
      this.stopReading();

      // 提取纯文本内容（去除HTML标签）
      const textContent = this.extractTextContent(content);

      if (!textContent.trim()) {
        if (messageHandler && messageHandler.warning) {
          messageHandler.warning('该消息没有可朗读的文本内容');
        }
        return;
      }

      // 设置朗读状态
      this.isReading = true;
      this.currentReadingMessageIndex = messageIndex;
      this.isLoadingAudio = true;

      this.onStart(messageIndex);

      try {
        // 优先使用服务器文字转语音接口
        if (this.options.preferServer && api && api.voice && api.voice.textToSpeech) {
          const ttsResult = await this.callServerTTS(textContent, ttsParams, api);

          if (ttsResult && ttsResult.data && ttsResult.data.obsUrl) {
            // 播放服务器返回的音频
            await this.playAudioFromUrl(ttsResult.data.obsUrl);
            return;
          }
        }

        // 服务器接口失败或不可用，降级到本地语音合成
        if (this.options.fallbackToLocal) {
          console.warn('服务器文字转语音失败或不可用，降级到本地语音合成');
          this.fallbackToLocalTTS(textContent, messageHandler);
        } else {
          throw new Error('语音朗读服务不可用');
        }
      } catch (error) {
        console.error('服务器文字转语音失败:', error);
        // 降级到本地语音合成
        if (this.options.fallbackToLocal) {
          this.fallbackToLocalTTS(textContent, messageHandler);
        } else {
          throw error;
        }
      } finally {
        this.isLoadingAudio = false;
      }

    } catch (error) {
      console.error('语音朗读失败:', error);
      if (messageHandler && messageHandler.error) {
        messageHandler.error('语音朗读功能出现错误');
      }
      this.stopReading();
      this.onError(error, messageIndex);
    }
  }

  /**
   * 提取纯文本内容
   */
  extractTextContent(htmlContent) {
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      return tempDiv.textContent || tempDiv.innerText || '';
    } catch (error) {
      console.error('提取文本内容失败:', error);
      return htmlContent; // 如果失败，返回原始内容
    }
  }

  /**
   * 调用服务器TTS接口
   */
  async callServerTTS(text, ttsParams, api) {
    try {
      const params = {
        text: text,
        ...ttsParams
      };

      console.log('调用服务器文字转语音接口:', params);
      const result = await api.voice.textToSpeech(params);
      console.log('服务器文字转语音结果:', result);

      if (result && result.isSuccess && result.data) {
        return result;
      } else {
        throw new Error(result?.message || '服务器文字转语音失败');
      }
    } catch (error) {
      console.error('调用服务器文字转语音接口失败:', error);
      throw error;
    }
  }

  /**
   * 播放服务器返回的音频URL
   */
  async playAudioFromUrl(audioUrl) {
    return new Promise((resolve, reject) => {
      try {
        // 创建音频对象
        const audio = new Audio(audioUrl);
        this.currentAudio = audio;

        // 设置音频事件监听
        audio.oncanplaythrough = () => {
          console.log('音频可以播放');
        };

        audio.onplay = () => {
          console.log('音频开始播放');
        };

        audio.onended = () => {
          console.log('音频播放结束');
          this.isReading = false;
          this.currentReadingMessageIndex = -1;
          this.currentAudio = null;
          this.onStop();
          resolve();
        };

        audio.onerror = (error) => {
          console.error('音频播放错误:', error);
          this.isReading = false;
          this.currentReadingMessageIndex = -1;
          this.currentAudio = null;
          this.onError(new Error('音频播放失败'));
          reject(new Error('音频播放失败'));
        };

        // 开始播放
        audio.play().catch(error => {
          console.error('音频播放启动失败:', error);
          this.isReading = false;
          this.currentReadingMessageIndex = -1;
          this.currentAudio = null;
          this.onError(error);
          reject(error);
        });

      } catch (error) {
        console.error('创建音频播放器失败:', error);
        this.isReading = false;
        this.currentReadingMessageIndex = -1;
        this.currentAudio = null;
        this.onError(error);
        reject(error);
      }
    });
  }

  /**
   * 降级到本地语音合成
   */
  fallbackToLocalTTS(textContent, messageHandler) {
    try {
      if (!this.speechSynthesis) {
        if (messageHandler && messageHandler.warning) {
          messageHandler.warning('当前浏览器不支持语音朗读功能');
        }
        this.stopReading();
        return;
      }

      console.log('使用本地语音合成');

      // 创建语音合成实例
      const utterance = new SpeechSynthesisUtterance(textContent);

      // 设置语音参数
      utterance.lang = 'zh-CN'; // 中文
      utterance.rate = 1; // 语速
      utterance.pitch = 1; // 音调
      utterance.volume = 1; // 音量

      // 朗读开始事件
      utterance.onstart = () => {
        console.log('本地语音合成开始朗读');
      };

      // 朗读结束事件
      utterance.onend = () => {
        this.isReading = false;
        this.currentReadingMessageIndex = -1;
        this.onStop();
        console.log('本地语音合成朗读结束');
      };

      // 朗读错误事件
      utterance.onerror = (event) => {
        console.error('本地语音合成朗读出错:', event);
        this.isReading = false;
        this.currentReadingMessageIndex = -1;
        if (messageHandler && messageHandler.error) {
          messageHandler.error('语音朗读失败');
        }
        this.onError(event);
      };

      // 开始朗读
      this.speechSynthesis.speak(utterance);

    } catch (error) {
      console.error('本地语音合成失败:', error);
      this.stopReading();
      if (messageHandler && messageHandler.error) {
        messageHandler.error('语音朗读失败');
      }
      this.onError(error);
    }
  }

  /**
   * 停止语音朗读
   */
  stopReading() {
    try {
      // 停止音频播放
      if (this.currentAudio) {
        this.currentAudio.pause();
        this.currentAudio.currentTime = 0;
        this.currentAudio = null;
      }

      // 停止本地语音合成
      if (this.speechSynthesis && this.speechSynthesis.speaking) {
        this.speechSynthesis.cancel();
      }

      this.isReading = false;
      this.currentReadingMessageIndex = -1;
      this.isLoadingAudio = false;

      this.onStop();
    } catch (error) {
      console.error('停止朗读失败:', error);
      this.onError(error);
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      isReading: this.isReading,
      currentReadingMessageIndex: this.currentReadingMessageIndex,
      isLoadingAudio: this.isLoadingAudio
    };
  }

  /**
   * 获取语音朗读按钮的图标
   */
  getVoiceReadButtonIcon(messageIndex) {
    if (this.isLoadingAudio && this.currentReadingMessageIndex === messageIndex) {
      return 'el-icon-loading';
    } else if (this.isReading && this.currentReadingMessageIndex === messageIndex) {
      return 'el-icon-video-pause';
    } else {
      return 'el-icon-video-play';
    }
  }

  /**
   * 获取语音朗读按钮的提示文字
   */
  getVoiceReadButtonTitle(messageIndex) {
    if (this.isLoadingAudio && this.currentReadingMessageIndex === messageIndex) {
      return '正在加载音频...';
    } else if (this.isReading && this.currentReadingMessageIndex === messageIndex) {
      return '停止朗读';
    } else {
      return '语音朗读';
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.stopReading();
    this.speechSynthesis = null;
    this.currentAudio = null;
  }
}

/**
 * 语音输入管理器
 * 提供按住录音、语音识别、自动发送消息的完整功能
 */
export class VoiceInputManager {
  constructor(options = {}) {
    this.options = {
      sessionId: null,
      appId: null,
      api: null, // API实例
      onStatusChange: () => {}, // 状态变化回调
      onTextResult: () => {}, // 识别文本结果回调
      onAutoSend: null, // 自动发送回调，如果为null则不自动发送
      ...options
    };

    // 录音相关
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
    this.canRecord = false;
    this.shouldIgnoreResult = false; // 标志是否应该忽略录音结果

    // 状态管理
    this.voiceStatus = '';
    this.voiceStatusType = '';
    this.voiceStatusIcon = '';
  }

  /**
   * 初始化语音输入功能
   */
  async initialize() {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('当前浏览器不支持录音功能');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log('获取音频流成功');

      // 检查是否支持指定的MIME类型
      const mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        console.warn('不支持 audio/webm;codecs=opus，尝试使用默认格式');
        this.mediaRecorder = new MediaRecorder(stream);
      } else {
        this.mediaRecorder = new MediaRecorder(stream, {
          mimeType: mimeType
        });
      }

      console.log('MediaRecorder 创建成功，支持的MIME类型:', this.mediaRecorder.mimeType);

      this.mediaRecorder.ondataavailable = (event) => {
        console.log('录音数据可用，大小:', event.data.size);
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = async () => {
        console.log('录音停止，音频块数量:', this.audioChunks.length);
        console.log('音频块大小:', this.audioChunks.map(chunk => chunk.size));

        // 检查是否应该忽略这次录音结果（录音时间太短）
        if (this.shouldIgnoreResult) {
          console.log('忽略录音结果：录音时间太短');
          this.audioChunks = [];
          this.shouldIgnoreResult = false; // 重置标志
          return;
        }

        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        console.log('最终音频Blob大小:', audioBlob.size);

        this.audioChunks = [];

        // 检查音频大小，如果太小则不发送
        if (audioBlob.size < 100) {
          console.warn('录音时间太短或音频为空');
          this.setStatus('录音时间太短，请重试', 'error', 'el-icon-warning');
          setTimeout(() => {
            this.setStatus('', '', '');
          }, 3000);
          return;
        }

        await this.sendAudioToAPI(audioBlob);
      };

      this.canRecord = true;
      this.setStatus('', '', '');
      return true;
    } catch (error) {
      console.error('无法访问麦克风:', error);
      this.canRecord = false;
      this.setStatus('无法访问麦克风，请确保浏览器有麦克风权限', 'error', 'el-icon-warning');
      throw error;
    }
  }

  /**
   * 开始录音
   */
  startRecording() {
    if (!this.mediaRecorder || this.isRecording) return false;

    console.log('开始录音...');
    this.isRecording = true;
    this.audioChunks = [];

    try {
      if (this.mediaRecorder.state === 'inactive') {
        this.mediaRecorder.start();
        console.log('MediaRecorder 已启动，状态:', this.mediaRecorder.state);
        this.setStatus('正在录音...', 'loading', 'el-icon-microphone');
        return true;
      } else {
        console.warn('MediaRecorder 状态异常:', this.mediaRecorder.state);
        this.isRecording = false;
        return false;
      }
    } catch (error) {
      console.error('录音失败:', error);
      this.isRecording = false;
      this.setStatus('录音失败', 'error', 'el-icon-warning');
      return false;
    }
  }

  /**
   * 停止录音
   */
  stopRecording() {
    if (!this.mediaRecorder || !this.isRecording) return false;

    console.log('停止录音...');
    this.isRecording = false;

    try {
      if (this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
        console.log('MediaRecorder 已停止，状态:', this.mediaRecorder.state);
        this.setStatus('正在处理音频...', 'loading', 'el-icon-loading');
        return true;
      } else {
        console.warn('MediaRecorder 状态异常，无法停止:', this.mediaRecorder.state);
        this.setStatus('录音状态异常', 'error', 'el-icon-warning');
        return false;
      }
    } catch (error) {
      console.error('停止录音失败:', error);
      this.setStatus('停止录音失败', 'error', 'el-icon-warning');
      return false;
    }
  }

  /**
   * 发送音频到API进行语音识别
   */
  async sendAudioToAPI(audioBlob) {
    try {
      if (!this.options.api || !this.options.api.voice || !this.options.api.voice.speechToText) {
        throw new Error('API接口未配置');
      }

      this.setStatus('正在识别语音...', 'loading', 'el-icon-loading');

      const params = {
        sessionID: this.options.sessionId || 'temp-session',
        flowType: 'SessionFlow',
        appId: this.options.appId || 'temp-app',
        audioFormat: 'webm',
        language: 'zh-CN'
      };

      console.log('语音识别参数:', params);

      const response = await this.options.api.voice.speechToText(audioBlob, params);

      if (response.isSuccess && response.data) {
        const recognizedText = response.data.text;
        if (recognizedText && recognizedText.trim()) {
          // 调用文本结果回调
          this.options.onTextResult(recognizedText);

          if (this.options.onAutoSend) {
            this.setStatus('识别成功，正在发送...', 'success', 'el-icon-check');
            // 等待一小段时间让用户看到识别结果，然后自动发送
            setTimeout(async () => {
              this.setStatus('', '', '');
              // 调用自动发送回调
              await this.options.onAutoSend(recognizedText);
            }, 1000);
          } else {
            this.setStatus('识别成功', 'success', 'el-icon-check');
            setTimeout(() => {
              this.setStatus('', '', '');
            }, 2000);
          }
        } else {
          this.setStatus('未识别到语音内容', 'error', 'el-icon-warning');
          setTimeout(() => {
            this.setStatus('', '', '');
          }, 3000);
        }
      } else {
        this.setStatus('语音识别失败: ' + (response.message || '未知错误'), 'error', 'el-icon-warning');
        setTimeout(() => {
          this.setStatus('', '', '');
        }, 3000);
      }
    } catch (error) {
      console.error('语音识别请求失败:', error);
      this.setStatus('语音识别失败: ' + error.message, 'error', 'el-icon-warning');
      setTimeout(() => {
        this.setStatus('', '', '');
      }, 3000);
    }
  }

  /**
   * 设置状态并触发回调
   */
  setStatus(status, type, icon) {
    this.voiceStatus = status;
    this.voiceStatusType = type;
    this.voiceStatusIcon = icon;
    this.options.onStatusChange({
      status,
      type,
      icon
    });
  }

  /**
   * 获取语音输入按钮标题
   */
  getInputTitle(isDisabled = false) {
    if (!this.canRecord) {
      return '麦克风权限未授权';
    }
    if (isDisabled) {
      return '正在处理中，请稍候';
    }
    if (this.isRecording) {
      return '松开结束录音';
    }
    return '按住开始录音';
  }

  /**
   * 获取语音输入按钮图标
   */
  getInputIcon() {
    if (!this.canRecord) {
      return 'el-icon-microphone-off';
    }
    if (this.isRecording) {
      return 'el-icon-video-pause';
    }
    return 'el-icon-microphone';
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    if (this.mediaRecorder && this.mediaRecorder.stream) {
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    }
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
    this.canRecord = false;
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      isRecording: this.isRecording,
      canRecord: this.canRecord,
      voiceStatus: this.voiceStatus,
      voiceStatusType: this.voiceStatusType,
      voiceStatusIcon: this.voiceStatusIcon
    };
  }

  /**
   * 更新配置
   */
  updateOptions(options) {
    this.options = { ...this.options, ...options };
  }
}

/**
 * 语音工具的统一接口
 */
export class VoiceUtils {
  constructor() {
    this.recorder = null;
    this.recognition = null;
    this.tts = null;
    this.voiceInputManager = null;
  }

  /**
   * 检查浏览器支持情况
   */
  static checkSupport() {
    return {
      recording: VoiceRecorder.isSupported(),
      recognition: VoiceRecognition.isSupported(),
      synthesis: VoiceTTS.checkSupport().speechSynthesis,
      audioPlayback: VoiceTTS.checkSupport().audioPlayback
    };
  }

  /**
   * 创建录音器
   */
  createRecorder(options = {}) {
    this.recorder = new VoiceRecorder(options);
    return this.recorder;
  }

  /**
   * 创建语音识别器
   */
  createRecognition(options = {}) {
    this.recognition = new VoiceRecognition(options);
    return this.recognition;
  }

  /**
   * 创建语音播报器
   */
  createTTS(options = {}) {
    this.tts = new VoiceTTS(options);
    return this.tts;
  }

  /**
   * 创建语音输入管理器
   */
  createVoiceInputManager(options = {}) {
    this.voiceInputManager = new VoiceInputManager(options);
    return this.voiceInputManager;
  }

  /**
   * 将音频Blob转换为Base64
   */
  static async blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * 将音频Blob转换为ArrayBuffer
   */
  static async blobToArrayBuffer(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsArrayBuffer(blob);
    });
  }

  /**
   * 格式化时长
   */
  static formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${remainingSeconds}秒`;
    }
  }

  /**
   * 播放音频
   */
  static async playAudio(audioSrc) {
    return new Promise((resolve, reject) => {
      const audio = new Audio();

      audio.oncanplaythrough = () => {
        audio.play().then(resolve).catch(reject);
      };

      audio.onerror = () => {
        reject(new Error('音频加载失败'));
      };

      if (audioSrc instanceof Blob) {
        audio.src = URL.createObjectURL(audioSrc);
      } else {
        audio.src = audioSrc;
      }
    });
  }

  /**
   * 提取文本内容（去除HTML标签）
   */
  static extractTextContent(htmlContent) {
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      return tempDiv.textContent || tempDiv.innerText || '';
    } catch (error) {
      console.error('提取文本内容失败:', error);
      return htmlContent;
    }
  }

  /**
   * 复制文本到剪贴板
   */
  static async copyToClipboard(text, messageHandler = null) {
    try {
      // 尝试使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        if (messageHandler && messageHandler.success) {
          messageHandler.success('内容已复制到剪贴板');
        }
        return true;
      } else {
        // 使用备用复制方法
        return VoiceUtils.fallbackCopyTextToClipboard(text, messageHandler);
      }
    } catch (error) {
      console.error('复制失败:', error);
      return VoiceUtils.fallbackCopyTextToClipboard(text, messageHandler);
    }
  }

  /**
   * 备用的复制方法
   */
  static fallbackCopyTextToClipboard(text, messageHandler = null) {
    try {
      // 创建临时DOM元素
      const textArea = document.createElement("textarea");
      textArea.value = text;

      // 确保元素不可见
      textArea.style.position = 'fixed';
      textArea.style.left = '-9999px';
      textArea.style.top = '-9999px';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      // 尝试执行复制命令
      const successful = document.execCommand('copy');
      if (successful) {
        if (messageHandler && messageHandler.success) {
          messageHandler.success("复制成功");
        }
      } else {
        if (messageHandler && messageHandler.warning) {
          messageHandler.warning("复制失败，请手动复制");
        }
      }

      // 清理
      document.body.removeChild(textArea);
      return successful;
    } catch (err) {
      if (messageHandler && messageHandler.error) {
        messageHandler.error("复制失败，请手动复制");
      }
      console.error('复制失败:', err);
      return false;
    }
  }
}

export default VoiceUtils;
