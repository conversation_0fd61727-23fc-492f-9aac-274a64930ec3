/**
 * 错误处理白名单配置
 * 在此列表中的接口将跳过统一错误处理，直接将后端错误抛出给组件处理
 */
export const ERROR_HANDLING_WHITELIST = {
  // URL路径匹配（支持正则表达式字符串）
  urlPatterns: [
    '/api/v1/SchemaStore/data-record',
    '/api/v1/SchemaStore/create-table',
    '/api/v1/SchemaStore/column',
    '/api/v1/SchemaStore/import-data-records-from-excel',
  ],
  
  // 特定HTTP方法的接口（可选）
  methodPatterns: {
    // 'POST': ['/api/special/endpoint'],  // 示例：仅POST方法的特定接口
    // 'PUT': ['/api/update/sensitive'],   // 示例：仅PUT方法的特定接口
  },
  
  // 错误码白名单（这些错误码不进行统一处理）
  errorCodes: /** @type {number[]} */ ([
    // 示例：可以添加特定的错误码
    // 1001,  // 自定义业务错误码
    // 1002,  // 验证失败错误码
  ]),
  
  // 响应状态码白名单
  statusCodes: /** @type {number[]} */ ([
    // 示例：可以添加特定的HTTP状态码
    // 422,   // 数据验证失败
    // 409,   // 资源冲突
  ])
}

/**
 * 检查接口是否在错误处理白名单中
 * @param {string} url - 请求URL
 * @param {string} method - 请求方法
 * @param {number|null} errorCode - 错误码
 * @param {number|null} statusCode - HTTP状态码
 * @returns {boolean} - 是否在白名单中
 */
export function isInErrorHandlingWhitelist(url, method = 'GET', errorCode = null, statusCode = null) {
  if (!url) return false
  
  // 检查URL路径匹配
  const isUrlMatched = ERROR_HANDLING_WHITELIST.urlPatterns.some(pattern => {
    if (pattern.startsWith('/') && pattern.endsWith('/')) {
      // 正则表达式模式（去掉首尾的斜杠）
      try {
        const regex = new RegExp(pattern.slice(1, -1))
        return regex.test(url)
      } catch (e) {
        console.warn(`Invalid regex pattern in whitelist: ${pattern}`)
        return false
      }
    } else {
      // 简单字符串包含匹配
      return url.includes(pattern)
    }
  })
  
  if (isUrlMatched) {
    return true
  }
  
  // 检查特定方法的接口
  const methodPatterns = ERROR_HANDLING_WHITELIST.methodPatterns[method.toUpperCase()]
  if (methodPatterns && methodPatterns.some(pattern => url.includes(pattern))) {
    return true
  }
  
  // 检查错误码白名单
  if (errorCode !== null && ERROR_HANDLING_WHITELIST.errorCodes.includes(errorCode)) {
    return true
  }
  
  // 检查状态码白名单
  if (statusCode !== null && ERROR_HANDLING_WHITELIST.statusCodes.includes(statusCode)) {
    return true
  }
  
  return false
}

/**
 * 添加新的URL模式到白名单
 * @param {string} pattern - URL模式
 */
export function addUrlToWhitelist(pattern) {
  if (!ERROR_HANDLING_WHITELIST.urlPatterns.includes(pattern)) {
    ERROR_HANDLING_WHITELIST.urlPatterns.push(pattern)
  }
}

/**
 * 从白名单中移除URL模式
 * @param {string} pattern - URL模式
 */
export function removeUrlFromWhitelist(pattern) {
  const index = ERROR_HANDLING_WHITELIST.urlPatterns.indexOf(pattern)
  if (index > -1) {
    ERROR_HANDLING_WHITELIST.urlPatterns.splice(index, 1)
  }
}

/**
 * 添加错误码到白名单
 * @param {number} errorCode - 错误码
 */
export function addErrorCodeToWhitelist(errorCode) {
  if (!ERROR_HANDLING_WHITELIST.errorCodes.includes(errorCode)) {
    ERROR_HANDLING_WHITELIST.errorCodes.push(errorCode)
  }
}

/**
 * 从白名单中移除错误码
 * @param {number} errorCode - 错误码
 */
export function removeErrorCodeFromWhitelist(errorCode) {
  const index = ERROR_HANDLING_WHITELIST.errorCodes.indexOf(errorCode)
  if (index > -1) {
    ERROR_HANDLING_WHITELIST.errorCodes.splice(index, 1)
  }
}

/**
 * 获取当前白名单配置（只读）
 * @returns {Object} - 白名单配置的副本
 */
export function getWhitelistConfig() {
  return JSON.parse(JSON.stringify(ERROR_HANDLING_WHITELIST))
} 