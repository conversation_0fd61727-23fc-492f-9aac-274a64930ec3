/**
 * 应用创建的结构模板
 * 包含默认的节点和连线配置
 */
export const appFlowTemplate = {
  "createFlowDetailInput": {
    "name": "",
    "startNodes": [
      {
        "id": "node-1745543052475",
        "type": "start",
        "position": { "x": -483.4596868289366, "y": 92.15187764475483 },
        "data": {
          "label": "开始",
          "type": "start",
          "name": "开始",
          "description": "开始节点描述",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "开始节点配置",
            "memory": false,
            "memoryTurns": 1,
            "inputs": [],
            "content": "",
            "structuredOutput": true,
            "outputs": [],
            "inputSettings": {
              "enableTextInput": true,
              "textInputName": "文字输入",
              "textInputRequired": true,
              "textInputPlaceholder": "请输入你想咨询的",
              "enableImageInput": true,
              "imageInputName": "上传的图片",
              "imageInputRequired": true,
              "imageInputPlaceholder": "上传的图片提示-输入提示",
              "enableFileInput": true,
              "fileInputName": "上传的文件",
              "fileInputRequired": true,
              "fileInputPlaceholder": "上传的文件-输入提示",
              "customVariables": [
                {
                  "displayName": "性别",
                  "name": "sex",
                  "type": 1,
                  "required": true,
                  "placeholder": "请输入你的性别",
                  "options": [],
                  "defaultValue": ""
                },
                {
                  "displayName": "职业",
                  "name": "job",
                  "type": 2,
                  "required": true,
                  "placeholder": "请选择你的职业",
                  "options": [
                    { "label": "工人", "value": "worker" },
                    { "label": "老师", "value": "techer" },
                    { "label": "其他", "value": "other" }
                  ],
                  "defaultValue": "worker"
                },
                {
                  "displayName": "currentTime",
                  "name": "sys_current_time",
                  "type": 3,
                  "required": true,
                  "placeholder": "获取当前时间",
                  "options": [],
                  "defaultValue": ""
                }
              ]
            }
          }
        }
      }
    ],
    "largeModelNodes": [
      {
        "id": "node-1745544192305",
        "type": "largeModel",
        "position": { "x": -252.6513248515168, "y": 305.2711780801074 },
        "data": {
          "label": "大模型",
          "type": "largeModel",
          "name": "大模型",
          "description": "大模型节点描述",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "大模型配置",
            "memory": true,
            "memoryTurns": 2,
            "memoryTime": 0,
            "contextSizes": 0,
            "inputs": [
              {
                "name": "文字输入",
                "sourceType": 1,
                "sourceNodeId": "node-1745543052475",
                "field": "textInput",
                "dataType": 1,
                "description": "文字输入",
                "isParameter": false,
                "inputOutputType": 1
              },
              {
                "name": "上传的图片",
                "sourceType": 1,
                "sourceNodeId": "node-1745543052475",
                "field": "imageInput",
                "dataType": 1,
                "description": "上传的图片",
                "isParameter": false,
                "inputOutputType": 1
              },
              {
                "name": "上传的文件",
                "sourceType": 1,
                "sourceNodeId": "node-1745543052475",
                "field": "fileInput",
                "dataType": 1,
                "description": "上传的文件",
                "isParameter": false,
                "inputOutputType": 1
              },
              {
                "name": "性别",
                "sourceType": 1,
                "sourceNodeId": "node-1745543052475",
                "field": "sex",
                "dataType": 1,
                "description": "性别",
                "isParameter": false,
                "inputOutputType": 1
              },
              {
                "name": "职业",
                "sourceType": 1,
                "sourceNodeId": "node-1745543052475",
                "field": "job",
                "dataType": 1,
                "description": "职业",
                "isParameter": false,
                "inputOutputType": 1
              },
              {
                "name": "currentTime",
                "sourceType": 1,
                "sourceNodeId": "node-1745543052475",
                "field": "sys_current_time",
                "dataType": 1,
                "description": "currentTime",
                "isParameter": false,
                "inputOutputType": 1
              }
            ],
            "content": "{\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"textInput\",\"dataType\":1,\"description\":\"文字输入\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"文字输入\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"imageInput\",\"dataType\":1,\"description\":\"上传的图片\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"上传的图片\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"fileInput\",\"dataType\":1,\"description\":\"上传的文件\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"上传的文件\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"sex\",\"dataType\":1,\"description\":\"性别\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"性别\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"job\",\"dataType\":1,\"description\":\"职业\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"职业\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"sys_current_time\",\"dataType\":1,\"description\":\"currentTime\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"currentTime\"}根据你输入的进行查询？",
            "systemPrompt": "我现在 {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"textInput\",\"dataType\":1,\"description\":\"文字输入\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"文字输入\"} 岁了，应该喝哪些茶保养身体 {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"imageInput\",\"dataType\":1,\"description\":\"上传的图片\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"上传的图片\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"fileInput\",\"dataType\":1,\"description\":\"上传的文件\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"上传的文件\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"sex\",\"dataType\":1,\"description\":\"性别\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"性别\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"job\",\"dataType\":1,\"description\":\"职业\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"职业\"}  {\"sourceType\":1,\"sourceNodeId\":\"node-1745543052475\",\"field\":\"sys_current_time\",\"dataType\":1,\"description\":\"currentTime\",\"isParameter\":false,\"inputOutputType\":1,\"name\":\"currentTime\"}，输入的提示词",
            "temperature": 0.4000000059604645,
            "model": {
              "code": "qwen-turbo",
              "name": "通义千问-Turbo",
              "type": ""
            },
            "chooseModel": [
              {
                "code": "qwen-turbo",
                "name": "通义千问-Turbo",
                "type": ""
              }
            ],
            "structuredOutput": false,
            "outputs": []
          }
        }
      }
    ],
    "knowledgeNodes": [
      {
        "id": "node-1745547812905",
        "type": "knowledge",
        "position": { "x": -75.63801467714092, "y": 76.09565851692616 },
        "data": {
          "label": "知识库",
          "type": "knowledge",
          "name": "知识库",
          "description": "知识库的节点描述",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "知识库配置",
            "memory": false,
            "memoryTurns": 1,
            "inputs": [],
            "content": "",
            "structuredOutput": true,
            "outputs": [],
            "knowledgeBase": "KB-20250423-738852，KB-20250416-716935，KB-20250422-585767，KB-20250419-163654",
            "searchType": 2,
            "similarity": 0.3,
            "semanticCount": 7,
            "fullTextCount": 3,
            "noHitStrategy": 1,
            "fixedText": "",
            "showReference": true,
            "referencethreshold": 0
          }
        }
      }
    ],
    "intentBranchNodes": [],
    "logicBranchNodes": [],
    "applicationNodes": [],
    "endNodes": [
      {
        "id": "node-1745543060444",
        "type": "end",
        "position": { "x": 720.8132989172176, "y": 280.39849543825284 },
        "data": {
          "label": "结束",
          "type": "end",
          "name": "结束",
          "description": "节点描述",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "结束节点配置",
            "memory": false,
            "memoryTurns": 1,
            "inputs": [],
            "content": "",
            "structuredOutput": true,
            "outputs": [],
            "responseConfig": {
              "enableSegmentedResponse": false,
              "enableMergeResponse": true,
              "responseDelay": 0
            }
          }
        }
      }
    ],
    "pluginNodes": [
      {
        "id": "node-1745547826762",
        "type": "plugin",
        "enabled": true,
        "position": { "x": 174.60654305421474, "y": 310.1809926614295 },
        "data": {
          "label": "",
          "type": "plugin",
          "name": "",
          "description": "",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "",
            "memory": false,
            "memoryTurns": 1,
            "inputs": [],
            "content": "",
            "structuredOutput": true,
            "outputs": [],
            "pluginId": "",
            "pluginName": "",
            "structuredParams": false,
            "pluginInputs": []
          }
        }
      }
    ],
    "newPluginNodes": [
      {
        "id": "node-1751868870019",
        "type": "newPlugin",
        "enabled": true,
        "position": { "x": 174.60654305421474, "y": 310.1809926614295 },
        "data": {
          "label": "",
          "type": "newPlugin",
          "name": "",
          "description": "",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "",
            "memory": false,
            "memoryTurns": 1,
            "inputConfig": {},
            "inputConfigJson": {},
            "defaultInput": "",
            "defaultInputJson": [],
            "structuredOutput": true,
            "outputConfig": {},
            "pluginConfig": {},
            "pluginId": "",
            "pluginName": "",
            "structuredParams": false
          }
        }
      }
    ],
    "humanTransferNodes": [
      {
        "id": "node-1745548386080",
        "type": "humanTransfer",
        "position": { "x": 441.3214329756577, "y": 72.28150245253013 },
        "data": {
          "label": "转人工",
          "type": "humanTransfer",
          "name": "转人工",
          "description": "转人工节点描述",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "转人工配置",
            "memory": false,
            "memoryTurns": 1,
            "inputs": [],
            "content": "",
            "structuredOutput": true,
            "outputs": [],
            "defaultReply": "转人工的回复",
            "notifyPerson": "staff1",
            "notifyMethods": { "sms": true, "wechat": true },
            "replyMode": 3,
            "delayMinutes": 5,
            "triggerConditions": []
          }
        }
      }
    ],
    "fixedContentNodes": [],
    "codeNodes": [],
    "dbNodes": [
      {
        "id": "node-1752198219662",
        "type": "database",
        "position": { "x": 219, "y": 264 },
        "data": {
          "label": "",
          "type": "database",
          "name": "",
          "description": "",
          "activeTab": "config",
          "executionStatus": {},
          "nodeTypeConfig": {
            "name": "",
            "inputs": [],
            "outputs": [],
            "modelCode": "",
            "structuredOutput": false,
            "dbConfigs": [
              {
                "dbId": "",
                "dbName": ""
              }
            ],
          }
        }
      }
    ],
    "edges": [
      {
        "id": "enode-1745543052475-node-1745544192305",
        "source": "node-1745543052475",
        "target": "node-1745544192305",
        "type": "simple-bezier",
        "sourceHandle": "node-1745543052475-source",
        "targetHandle": "node-1745544192305-target",
        "data": { "selected": false },
        "label": "",
        "style": { "stroke": "#64748b" }
      },
      {
        "id": "enode-1745544192305-node-1745547812905",
        "source": "node-1745544192305",
        "target": "node-1745547812905",
        "type": "simple-bezier",
        "sourceHandle": "node-1745544192305-source",
        "targetHandle": "node-1745547812905-target",
        "data": { "selected": false },
        "label": "",
        "style": { "stroke": "#64748b" }
      },
      {
        "id": "enode-1745547812905-node-1745547826762",
        "source": "node-1745547812905",
        "target": "node-1745547826762",
        "type": "simple-bezier",
        "sourceHandle": "node-1745547812905-source",
        "targetHandle": "node-1745547826762-target",
        "data": { "selected": false },
        "label": "",
        "style": { "stroke": "#64748b" }
      },
      {
        "id": "enode-1745547826762-node-1745548386080",
        "source": "node-1745547826762",
        "target": "node-1745548386080",
        "type": "simple-bezier",
        "sourceHandle": "node-1745547826762-source",
        "targetHandle": "node-1745548386080-target",
        "data": { "selected": false },
        "label": "",
        "style": { "stroke": "#64748b" }
      },
      {
        "id": "enode-1745548386080-node-1745543060444",
        "source": "node-1745548386080",
        "target": "node-1745543060444",
        "type": "simple-bezier",
        "sourceHandle": "node-1745548386080-source",
        "targetHandle": "node-1745543060444-target",
        "data": { "selected": false },
        "label": "",
        "style": { "stroke": "#64748b" }
      }
    ],
    "version": "1745548746392"
  }
};

/**
 * 根据智能体类型获取模板中需要的节点
 * @param {string} appType - 智能体类型, 'light' 或 'knowledge'
 * @returns {Object} - 适合该智能体类型的节点结构
 */
export function getNodesByAppType(appType) {
  const template = JSON.parse(JSON.stringify(appFlowTemplate.createFlowDetailInput));

  // 根据智能体类型过滤节点
  if (appType === 'light') {
    // 轻量智能体不需要知识库节点
    template.knowledgeNodes = [];

    // 更新边缘连接，跳过知识库节点
    template.edges = template.edges.filter(edge =>
      !edge.source.includes('node-1745547812905') &&
      !edge.target.includes('node-1745547812905')
    );

    // 添加大模型到插件的直接连接
    template.edges.push({
      "id": "enode-1745544192305-node-1745547826762",
      "source": "node-1745544192305",
      "target": "node-1745547826762",
      "type": "simple-bezier",
      "sourceHandle": "node-1745544192305-source",
      "targetHandle": "node-1745547826762-target",
      "data": { "selected": false },
      "label": "",
      "style": { "stroke": "#64748b" }
    });

    // 更新插件节点，移除对知识库节点的引用
    if (template.pluginNodes.length > 0) {
      const pluginNode = template.pluginNodes[0];
      pluginNode.data.nodeTypeConfig.inputs = pluginNode.data.nodeTypeConfig.inputs.filter(
        input => input.sourceNodeId !== 'node-1745547812905'
      );

      // 更新content字段，移除对知识库的引用
      pluginNode.data.nodeTypeConfig.content = pluginNode.data.nodeTypeConfig.content.replace(
        /{"sourceType":1,"sourceNodeId":"node-1745547812905".*?}/g, ''
      ).replace(/\s\s+/g, ' ');
    }
  }

  return template;
}
