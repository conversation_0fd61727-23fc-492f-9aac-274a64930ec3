/*
 * @Description: haolin.hu Create File
 * @Version: 2.0
 * @Autor: haolin.hu
 * @Date: 2025-06-27 09:02:58
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-07-15 20:49:25
 */
/**
 * 枚举定义文件
 * 统一管理系统中的枚举值
 */

// 智能体分类枚举
export const EnumApplicationType = [
  { value: 1, label: "客服", code: "CUSTOMER_SERVICE" },
  { value: 3, label: "销售", code: "SALES" },
  { value: 4, label: "采购", code: "PROCUREMENT" },
  { value: 5, label: "物流", code: "LOGISTICS" },
  { value: 6, label: "财务", code: "FINANCE" },
  { value: 7, label: "法务", code: "LEGAL" },
  { value: 8, label: "质管", code: "QUALITY" },
  { value: 2, label: "其他", code: "OTHER" },
];

// 智能体类型枚举
export const EnumAgentType = [
  { value: 1, label: "轻量智能体", code: "LIGHT" },
  { value: 2, label: "知识智能体", code: "KNOWLEDGE" }
];

// 根据值获取标签
export const getApplicationTypeLabel = (value) => {
  const item = EnumApplicationType.find(item => item.value === value);
  return item ? item.label : "未知";
};

// 根据值获取代码
export const getApplicationTypeCode = (value) => {
  const item = EnumApplicationType.find(item => item.value === value);
  return item ? item.code : "";
};

// 根据代码获取标签
export const getApplicationTypeLabelByCode = (code) => {
  const item = EnumApplicationType.find(item => item.code === code);
  return item ? item.label : "未知";
};

// 根据值获取智能体类型标签
export const getAgentTypeLabel = (value) => {
  const item = EnumAgentType.find(item => item.value === value);
  return item ? item.label : "未知";
};

// 根据值获取智能体类型代码
export const getAgentTypeCode = (value) => {
  const item = EnumAgentType.find(item => item.value === value);
  return item ? item.code : "";
};
