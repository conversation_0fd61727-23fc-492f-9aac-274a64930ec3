/**
 * px到rem转换工具
 * 基于37.5px的基准值进行转换
 */

const BASE_FONT_SIZE = 37.5;

/**
 * 批量转换常用的像素值
 */
const commonConversions = {
  // 间距
  4: '0.11rem',   // 4px
  8: '0.21rem',   // 8px
  12: '0.32rem',  // 12px
  16: '0.43rem',  // 16px
  20: '0.53rem',  // 20px
  24: '0.64rem',  // 24px
  32: '0.85rem',  // 32px
  40: '1.07rem',  // 40px
  48: '1.28rem',  // 48px

  // 字体大小
  10: '0.27rem',  // 10px
  12: '0.32rem',  // 12px
  14: '0.37rem',  // 14px
  16: '0.43rem',  // 16px
  18: '0.48rem',  // 18px
  20: '0.53rem',  // 20px
  24: '0.64rem',  // 24px

  // 常用宽高
  36: '0.96rem',   // 36px
  44: '1.17rem',   // 44px
  60: '1.6rem',    // 60px
  80: '2.13rem',   // 80px
  100: '2.67rem',  // 100px
  120: '3.2rem',   // 120px
  160: '4.27rem',  // 160px
  200: '5.33rem',  // 200px
  240: '6.4rem',   // 240px
  280: '7.47rem',  // 280px
  320: '8.53rem',  // 320px
  400: '10.67rem', // 400px
  480: '12.8rem',  // 480px
  510: '13.6rem',  // 510px (智能体卡片宽度)
  600: '16rem',    // 600px
  800: '21.33rem', // 800px
  1000: '26.67rem', // 1000px
  1200: '32rem',   // 1200px
  1440: '38.4rem', // 1440px
  1920: '51.2rem', // 1920px (设计稿宽度)
};

/**
 * 将px转换为rem
 * @param {number} px - 像素值
 * @param {number} baseSize - 基准字体大小，默认37.5
 * @returns {string} rem值字符串
 */
function pxToRem(px, baseSize = BASE_FONT_SIZE) {
  if (typeof px !== 'number') {
    console.warn('pxToRem: 输入值必须是数字');
    return '0rem';
  }

  const rem = (px / baseSize).toFixed(2);
  return `${rem}rem`;
}

/**
 * 批量转换px数组为rem数组
 * @param {number[]} pxArray - 像素值数组
 * @param {number} baseSize - 基准字体大小
 * @returns {string[]} rem值字符串数组
 */
function pxArrayToRem(pxArray, baseSize = BASE_FONT_SIZE) {
  return pxArray.map(px => pxToRem(px, baseSize));
}

/**
 * 转换CSS样式字符串中的px为rem
 * @param {string} cssString - CSS样式字符串
 * @param {number} baseSize - 基准字体大小
 * @returns {string} 转换后的CSS字符串
 */
function convertCSSPxToRem(cssString, baseSize = BASE_FONT_SIZE) {
  return cssString.replace(/(\d+(?:\.\d+)?)px/g, (match, px) => {
    return pxToRem(parseFloat(px), baseSize);
  });
}

/**
 * 获取常用转换值
 * @param {number} px - 像素值
 * @returns {string} rem值或提示信息
 */
function getCommonConversion(px) {
  return commonConversions[px] || pxToRem(px);
}

/**
 * 打印转换表格（开发调试用）
 */
function printConversionTable() {
  console.table(commonConversions);
}

/**
 * 生成CSS变量定义
 * @returns {string} CSS变量定义字符串
 */
function generateCSSVariables() {
  let cssVars = ':root {\n';
  Object.entries(commonConversions).forEach(([px, rem]) => {
    cssVars += `  --size-${px}: ${rem};\n`;
  });
  cssVars += '}';
  return cssVars;
}

export {
    BASE_FONT_SIZE, commonConversions, convertCSSPxToRem, generateCSSVariables, getCommonConversion,
    printConversionTable, pxArrayToRem, pxToRem
};

export default {
  pxToRem,
  pxArrayToRem,
  convertCSSPxToRem,
  getCommonConversion,
  printConversionTable,
  generateCSSVariables,
  commonConversions,
  BASE_FONT_SIZE
};
