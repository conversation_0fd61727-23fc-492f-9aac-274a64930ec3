import { api } from '@/api/request'

/**
 * 登录成功后的处理逻辑
 * @param {string} token - 登录成功返回的token
 * @returns {Promise<Object>} 包含是否需要选择租户和租户列表的对象
 */
export async function handleLoginSuccess(token) {
  try {
    // 首先保存token
    localStorage.setItem('token', token)

    // 获取租户列表
    const tenantResponse = await api.auth.getTenants()

    if (tenantResponse.isSuccess && tenantResponse.data && tenantResponse.data.length > 0) {
      if (tenantResponse.data.length === 1) {
        // 只有一个租户，直接选择
        const defaultTenant = tenantResponse.data[0]
        const tenantId = defaultTenant.tenantId

        // 保存租户ID到localStorage
        localStorage.setItem('tenantId', tenantId)

        // 保存完整的租户信息（可选，用于后续使用）
        localStorage.setItem('tenantInfo', JSON.stringify(defaultTenant))

        console.log('租户信息获取成功:', defaultTenant)

        // 检查是否有待处理的邀请码
        const pendingInviteCode = localStorage.getItem('pendingInviteCode')
        if (pendingInviteCode) {
          console.log('登录成功后检测到待处理的邀请码:', pendingInviteCode)
          // 设置标记，提示应用需要处理邀请码
          localStorage.setItem('needProcessInvite', 'true')
        }

        return { needSelectTenant: false, tenants: null }
      } else {
        // 多个租户，需要用户选择
        return { needSelectTenant: true, tenants: tenantResponse.data }
      }
    } else {
      console.warn('未获取到租户信息或租户列表为空')
      return { needSelectTenant: false, tenants: null }
    }
  } catch (error) {
    console.error('获取租户信息失败:', error)
    throw error
  }
}

/**
 * 选择租户后的处理逻辑
 * @param {Object} selectedTenant - 选择的租户对象
 */
export function handleTenantSelection(selectedTenant) {
  try {
    const tenantId = selectedTenant.tenantId

    // 保存租户ID到localStorage
    localStorage.setItem('tenantId', tenantId)

    // 保存完整的租户信息
    localStorage.setItem('tenantInfo', JSON.stringify(selectedTenant))

    console.log('租户选择成功:', selectedTenant)

    // 检查是否有待处理的邀请码
    const pendingInviteCode = localStorage.getItem('pendingInviteCode')
    if (pendingInviteCode) {
      console.log('租户选择后检测到待处理的邀请码:', pendingInviteCode)
      // 设置标记，提示应用需要处理邀请码
      localStorage.setItem('needProcessInvite', 'true')
    }
  } catch (error) {
    console.error('租户选择处理失败:', error)
    throw error
  }
}

/**
 * 登出时清理认证相关信息
 */
export function handleLogout() {
  localStorage.removeItem('token')
  localStorage.removeItem('tenantId')
  localStorage.removeItem('tenantInfo')
  localStorage.removeItem('user')
  localStorage.removeItem('workflowSessionData')
  // 清除邀请相关数据
  localStorage.removeItem('pendingInviteCode')
  localStorage.removeItem('needProcessInvite')
}

/**
 * 检查是否已登录且有租户信息
 * @returns {boolean}
 */
export function isAuthenticatedWithTenant() {
  const token = localStorage.getItem('token')
  const tenantId = localStorage.getItem('tenantId')
  return !!(token && tenantId)
}

/**
 * 获取当前租户ID
 * @returns {string|null}
 */
export function getCurrentTenantId() {
  return localStorage.getItem('tenantId')
}

/**
 * 获取当前租户信息
 * @returns {object|null}
 */
export function getCurrentTenantInfo() {
  const tenantInfo = localStorage.getItem('tenantInfo')
  try {
    return tenantInfo ? JSON.parse(tenantInfo) : null
  } catch (error) {
    console.error('解析租户信息失败:', error)
    return null
  }
}
