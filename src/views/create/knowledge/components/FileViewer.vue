<template>
  <div class="file-viewer">
    <div class="viewer-header">
      <div class="file-info">
        <img :src="getFileIcon(fileData.type)" class="file-icon" alt="" />
        <h2 class="file-name">{{ fileData.name || "知识库文件" }}</h2>
        <el-tag type="success" class="file-type">{{
          getFileTypeName(fileData.type)
        }}</el-tag>
      </div>
      <div class="header-actions">
        <!-- <el-button type="text" icon="el-icon-download" @click="downloadFile">导出</el-button> -->
        <!-- <el-button type="text" icon="el-icon-edit" @click="editFile">编辑</el-button> -->
        <el-button type="text" icon="el-icon-back" @click="closeViewer"
          >返回</el-button
        >
      </div>
    </div>

    <div class="viewer-content">
      <div class="table-header">
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            placeholder="请输入搜索内容"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"
              >搜索</el-button
            >
          </el-input>
        </div>
        <!-- <el-button type="primary" icon="el-icon-plus" @click="handleAddRow">新增</el-button> -->
      </div>

      <!-- 表格类型显示 -->
      <div v-if="isTableType" class="table-content">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          v-loading="loading"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '5px 10px' }"
        >
          <el-table-column type="index" label="序号" width="80" align="center">
          </el-table-column>

          <el-table-column
            v-for="(column, index) in tableColumns"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            min-width="150"
          >
            <template slot-scope="scope">
              <el-tooltip
                :content="scope.row[column.prop]"
                placement="top"
                effect="dark"
                :open-delay="300"
                :disabled="
                  !scope.row[column.prop] ||
                  String(scope.row[column.prop]).length < 30
                "
                popper-class="table-tooltip"
              >
                <div class="cell-content single-line">
                  {{ scope.row[column.prop] }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="handleView(scope.$index, scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleDelete(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 文档类型显示 (包括网页) -->
      <div v-else-if="isDocType" class="document-content">
        <div class="doc-stats">
          <el-card shadow="never" class="stats-card">
            <div class="stats-info">
              <div class="stats-item">
                <span class="stats-label">分段数量：</span>
                <span class="stats-value">{{ total }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">Token数量：</span>
                <span class="stats-value">{{ fileData.tokens || 0 }}</span>
              </div>
              <div class="stats-item">
                <span class="stats-label">文件大小：</span>
                <span class="stats-value">{{ formatFileSize(fileData.size) }}</span>
              </div>
            </div>
          </el-card>
        </div>

        <div class="doc-segments">
          <div v-for="(segment, index) in tableData" :key="segment.id" class="doc-segment">
            <div class="segment-header">
              <div class="segment-title">
                <i class="el-icon-document"></i>
                分段 {{ index + 1 }}
                <el-tag size="mini" :type="getContentTypeTagType(segment.contentType)">
                  {{ segment.contentType }}
                </el-tag>
              </div>
              <div class="segment-actions">
                <el-button type="text" size="mini" @click="handleView(index, segment)">
                  <i class="el-icon-view"></i> 查看详情
                </el-button>
                <el-button type="text" size="mini" @click="handleDelete(index)">
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </div>
            </div>
            <div class="segment-content">
              <div v-if="segment.contentType === 'Markdown'" v-html="formatMarkdown(segment.previewContent)" class="markdown-preview"></div>
              <div v-else-if="segment.contentType === 'HTML'" v-html="segment.previewContent" class="html-preview"></div>
              <div v-else class="text-preview">{{ segment.previewContent }}</div>

              <div v-if="segment.isTruncated" class="view-more">
                <el-button type="text" @click="handleView(index, segment)">
                  查看完整内容 <i class="el-icon-arrow-right"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 问答类型显示 -->
      <div v-else class="qa-content">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          v-loading="loading"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '5px 10px' }"
        >
          <el-table-column type="index" label="序号" width="80" align="center">
          </el-table-column>

          <el-table-column prop="question" label="问题/键" min-width="300">
            <template slot-scope="scope">
              <el-tooltip
                :content="scope.row.question"
                placement="top"
                effect="dark"
                :open-delay="300"
                :disabled="
                  !scope.row.question || scope.row.question.length < 30
                "
                popper-class="table-tooltip"
              >
                <div class="cell-content single-line">
                  {{ scope.row.question }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column prop="answer" label="答案/内容" min-width="500">
            <template slot-scope="scope">
              <el-tooltip
                :content="scope.row.answer"
                placement="top"
                effect="dark"
                :open-delay="300"
                :disabled="!scope.row.answer || scope.row.answer.length < 30"
                popper-class="table-tooltip"
              >
                <div class="cell-content single-line">
                  {{ scope.row.answer }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="handleView(scope.$index, scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleDelete(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 - 仅在表格和问答类型时显示 -->
      <div v-if="!isDocType" class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>
    </div>

    <!-- 编辑行对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="650px"
      :close-on-click-modal="false"
    >
      <el-form :model="currentRow" label-width="100px" ref="rowForm">
        <el-form-item
          label="问题/键"
          prop="question"
          :rules="[
            { required: true, message: '请输入问题或键名', trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="currentRow.question"
            type="textarea"
            :rows="3"
            placeholder="请输入问题或键名"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="答案/内容"
          prop="answer"
          :rules="[
            { required: true, message: '请输入答案或内容', trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="currentRow.answer"
            type="textarea"
            :rows="6"
            placeholder="请输入答案或内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRow" :loading="saveLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <el-dialog
      title="查看详情"
      :visible.sync="viewDialogVisible"
      :close-on-click-modal="false"
      width="1000px"
      class="detail-dialog"
    >
      <div class="detail-content" v-if="currentRow">
        <!-- 文档类型详情 -->
        <template v-if="isDocType">
          <div class="detail-header">
            <div class="detail-title">
              <i class="el-icon-document"></i>
              分段详情
            </div>
            <el-tag :type="getContentTypeTagType(currentRow.contentType)">
              {{ currentRow.contentType }}
            </el-tag>
          </div>
          <div class="detail-body">
            <div v-if="currentRow.contentType === 'Markdown'" v-html="formatMarkdown(currentRow.originalContent)" class="markdown-content"></div>
            <div v-else-if="currentRow.contentType === 'HTML'" v-html="currentRow.originalContent" class="html-content"></div>
            <div v-else class="text-content">{{ currentRow.originalContent }}</div>
          </div>
        </template>

        <!-- 表格类型详情 -->
        <template v-else-if="isTableType">
          <div
            v-for="(column, index) in tableColumns"
            :key="index"
            class="detail-item"
          >
            <div class="detail-label">{{ column.label }}：</div>
            <div class="detail-value">
              {{ currentRow[column.prop] }}
            </div>
          </div>
        </template>

        <!-- 问答类型详情 -->
        <template v-else>
          <div class="detail-item">
            <div class="detail-label">问题/键：</div>
            <div class="detail-value">{{ currentRow.question }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">答案/内容：</div>
            <div class="detail-value content-wrap">{{ currentRow.answer }}</div>
          </div>
        </template>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="viewDialogVisible = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"

export default {
  name: "FileViewer",
  props: {
    fileId: {
      type: [String, Number],
      required: true,
    },
    knowledgeBaseId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      saveLoading: false,
      fileData: {},
      tableData: [],
      tableColumns: [],
      searchQuery: "",
      currentPage: 0,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      viewDialogVisible: false,
      dialogTitle: "编辑行",
      currentRow: {
        question: "",
        answer: "",
      },
      currentIndex: -1,
      isEditing: false,
    };
  },
  computed: {
    isTableType() {
      // 优先根据文件扩展名判断
      if (this.fileData.name &&
          (this.fileData.name.endsWith(".xlsx") ||
           this.fileData.name.endsWith(".xls"))) {
        return true;
      }
      // 再根据type字段判断
      return this.fileData.type === 2;
    },
    isDocType() {
      // 优先根据文件扩展名判断，排除表格文件
      if (this.fileData.name) {
        if (this.fileData.name.endsWith(".xlsx") || this.fileData.name.endsWith(".xls")) {
          return false; // Excel文件不是文档类型
        }
        if (this.fileData.name.endsWith(".txt") ||
            this.fileData.name.endsWith(".doc") ||
            this.fileData.name.endsWith(".docx") ||
            this.fileData.name.endsWith(".pdf") ||
            this.fileData.name.includes("www") ||
            this.fileData.name.endsWith(".md")) {
          return true;
        }
      }
      // 再根据type字段判断
      return this.fileData.type === 0 || this.fileData.type === 3;
    },
    isQAType() {
      // 优先根据文件扩展名判断，排除其他类型
      if (this.fileData.name &&
          (this.fileData.name.endsWith(".xlsx") ||
           this.fileData.name.endsWith(".xls") ||
           this.fileData.name.endsWith(".txt") ||
           this.fileData.name.endsWith(".doc") ||
           this.fileData.name.endsWith(".docx") ||
           this.fileData.name.endsWith(".pdf") ||
           this.fileData.name.includes("www") ||
           this.fileData.name.endsWith(".md"))) {
        return false;
      }
      return this.fileData.type === 1;
    },
  },
  created() {
    this.fetchFileData();
  },
  methods: {
    // 获取文件数据
    fetchFileData() {
      this.loading = true;
      this.fetchFileContent();
    },

    // 获取文件内容
    fetchFileContent() {
      this.loading = true;

      const params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        contentQuery: this.searchQuery,
      };

      // 使用封装好的API调用
      api.fileService
        .getFileContent(this.fileId, params)
        .then((res) => {
          if (res.code === 200 || res.isSuccess) {
            // 保存文件信息
            if (res.data && res.data.fileInfo) {
              this.fileData = res.data.fileInfo;
            }

            // 处理所有类型的内容为表格数据
            if (res.data && res.data.chunks) {
              // 判断是否为表格类型
              if (this.isTableType) {
                this.processTableContent(res.data.chunks);
              }
              // 判断是否为文档类型 (包括网页)
              else if (this.isDocType) {
                this.processDocContent(res.data.chunks);
              } else {
                // 处理问答类型数据
                this.tableData = this.processQAContent(res.data.chunks);
              }

              // 更新总数
              this.total = res.data.totalChunks || 0;
            }
          } else {
            this.$showFriendlyError(null, res.message || "获取文件内容失败");
          }
        })
        .catch((err) => {
          this.$showFriendlyError(null,
            "获取文件内容失败：" + (err.message || "未知错误")
          );
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理表格类型的内容
    processTableContent(chunks) {
      // 清空表格列和数据
      this.tableColumns = [];
      this.tableData = [];

      if (chunks.length === 0) {
        return;
      }

      // 使用第一个chunk的columns作为表头定义
      const firstChunk = chunks[0];

      if (firstChunk && firstChunk.columns) {
        // 使用API返回的columns作为表头
        this.tableColumns = firstChunk.columns.map((columnName, index) => ({
          prop: `col${index}`,
          label: columnName === "nan" ? `列${index + 1}` : columnName,
        }));
      } else {
        // 备用方案：解析content中的列名
        const headerContent = firstChunk.content || "";
        if (headerContent.startsWith("Row")) {
          const headerParts = headerContent.split(", ");
          this.tableColumns = headerParts.map((part, index) => {
            let label = part;
            if (part.includes(":")) {
              const colonParts = part.split(":");
              label = colonParts[colonParts.length - 1].trim();
            }
            return {
              prop: `col${index}`,
              label: label === "nan" ? `列${index + 1}` : label,
            };
          });
        }
      }

      // 处理所有数据行，不跳过任何数据
      this.tableData = chunks.map((chunk) => {
        const rowData = {
          id: chunk.id,
          chunkIndex: chunk.chunkIndex,
        };

        try {
          // 优先使用previewRows中的格式化数据
          if (chunk.previewRows && chunk.previewRows.length > 0) {
            const previewRow = chunk.previewRows[0];
            if (previewRow.values && Array.isArray(previewRow.values)) {
              previewRow.values.forEach((value, index) => {
                const prop = `col${index}`;
                // 处理 nan 值
                rowData[prop] = value === "nan" ? "" : (value || "");
              });
            }
          } else {
            // 备用方案：解析content中的数据
            const content = chunk.content || "";
            if (content.startsWith("Row")) {
              const parts = content.split(", ");

              // 将每一列的值映射到对应的列属性
              parts.forEach((part, index) => {
                let value = part;
                if (part.includes(":")) {
                  const colonParts = part.split(":");
                  value = colonParts[colonParts.length - 1].trim();
                }

                // 处理 nan 值
                if (value === "nan") {
                  value = "";
                }

                const prop = `col${index}`;
                rowData[prop] = value;
              });
            } else {
              // 备选格式处理，这里简单拆分
              const values = content.split(",");
              values.forEach((value, index) => {
                rowData[`col${index}`] = value.trim();
              });
            }
          }
        } catch (error) {
          console.error("解析表格行出错:", error);
          // 如果解析失败，至少保留原始内容
          rowData.col0 = chunk.content || "";
        }

        return rowData;
      });

      // 确保有表格列（如果上面的解析都失败了）
      if (this.tableColumns.length === 0) {
        // 通过检查第一行数据，确定列数
        if (this.tableData.length > 0) {
          const firstRow = this.tableData[0];
          this.tableColumns = Object.keys(firstRow)
            .filter((key) => key.startsWith("col"))
            .map((key, index) => ({
              prop: key,
              label: `列${index + 1}`,
            }));
        } else {
          // 默认至少显示两列
          this.tableColumns = [
            { prop: "col0", label: "列1" },
            { prop: "col1", label: "列2" },
          ];
        }
      }
    },

    // 处理文档类型内容 (包括网页)
    processDocContent(chunks) {
      console.log("处理文档内容:", chunks);

      this.tableData = chunks.map((chunk) => {
        const content = chunk.content || "";
        const originalContent = content;

        // 判断内容类型
        let contentType = "文本";
        let previewContent = content;
        let isTruncated = false;

        // 检测Markdown内容 (您的示例数据是Markdown格式)
        if (this.isMarkdown(content)) {
          contentType = "Markdown";
          // 对于Markdown，提取前500个字符作为预览
          if (content.length > 500) {
            previewContent = content.substring(0, 500) + "...";
            isTruncated = true;
          }
        }
        // 检测HTML内容
        else if (this.isHtml(content)) {
          contentType = "HTML";
          // 对于HTML，提取前500个字符作为预览
          if (content.length > 500) {
            previewContent = content.substring(0, 500) + "...";
            isTruncated = true;
          }
        }
        // 普通文本处理
        else {
          contentType = "文本";
          // 统一换行符
          previewContent = content.replace(/\r\n|\r/g, "\n");

          // 如果内容太长，截取前500个字符用于预览
          if (previewContent.length > 500) {
            previewContent = previewContent.substring(0, 500) + "...";
            isTruncated = true;
          }
        }

        return {
          id: chunk.id,
          chunkIndex: chunk.chunkIndex,
          content: previewContent,
          originalContent: originalContent,
          previewContent: previewContent,
          contentType: contentType,
          isTruncated: isTruncated,
          tokenNum: chunk.tokenNum || 0,
          metadata: chunk.metadata || {}
        };
      });

      console.log("文档内容处理结果:", this.tableData);
    },

    // 判断是否为HTML内容
    isHtml(content) {
      if (!content) return false;
      return /<[^>]+>/.test(content);
    },

    // 判断是否为Markdown内容
    isMarkdown(content) {
      if (!content) return false;
      const markdownPatterns = [
        /^#\s+.+$/m,           // 标题
        /\*\*.+\*\*/,           // 粗体
        /\*.+\*/,               // 斜体
        /\[.+\]\(.+\)/,         // 链接
        /```[\s\S]*?```/,       // 代码块
        /^\s*[-*+]\s+.+$/m,     // 无序列表
        /^\s*\d+\.\s+.+$/m,     // 有序列表
        /^\s*>.+$/m,            // 引用
        /\|.+\|.+\|/            // 表格
      ];
      return markdownPatterns.some(pattern => pattern.test(content));
    },

    // 格式化Markdown内容
    formatMarkdown(content) {
      if (!content) return '';

      // 简单的Markdown转HTML实现
      let html = content
        // 标题
        .replace(/^### (.+)$/gm, '<h3>$1</h3>')
        .replace(/^## (.+)$/gm, '<h2>$1</h2>')
        .replace(/^# (.+)$/gm, '<h1>$1</h1>')
        // 粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 斜体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 代码块
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        // 行内代码
        .replace(/`([^`]+)`/g, '<code>$1</code>')
        // 无序列表
        .replace(/^\* (.+)$/gm, '<li>$1</li>')
        .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
        // 换行
        .replace(/\n/g, '<br>');

      return html;
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size && size !== 0) return '-';
      if (size < 1024) return size + ' B';
      if (size < 1024 * 1024) return Math.round(size / 1024) + ' KB';
      return Math.round(size / (1024 * 1024)) + ' MB';
    },

    // 获取内容类型标签样式
    getContentTypeTagType(contentType) {
      const typeMap = {
        'HTML': 'warning',
        'Markdown': 'success',
        '文本': 'info'
      };
      return typeMap[contentType] || 'info';
    },

    // 处理问答类型内容
    processQAContent(chunks) {
      return chunks.map((chunk, index) => {
        let questionAnswer = {};

        try {
          // 优先从metadata中获取question和answer
          if (chunk.metadata) {
            let metadata = chunk.metadata;

            // 如果metadata是字符串，尝试解析为JSON
            if (typeof metadata === "string") {
              try {
                metadata = JSON.parse(metadata);
              } catch (parseError) {
                console.warn("解析metadata JSON失败:", parseError);
                metadata = {};
              }
            }

            // 从metadata中提取question和answer
            if (metadata.question && metadata.answer) {
              questionAnswer = {
                question: metadata.question,
                answer: metadata.answer,
              };
            } else {
              // 如果metadata中没有question和answer，使用备选逻辑
              const content = chunk.content || "";
              if (content.includes(",")) {
                const [question, answer] = content.split(",", 2);
                questionAnswer = {
                  question: question.trim(),
                  answer: answer.trim(),
                };
              } else {
                questionAnswer = {
                  question: `条目 ${index + 1}`,
                  answer: content,
                };
              }
            }
          } else {
            // 如果没有metadata，使用原有逻辑处理content
            const content = chunk.content || "";
            if (content.includes(",")) {
              const [question, answer] = content.split(",", 2);
              questionAnswer = {
                question: question.trim(),
                answer: answer.trim(),
              };
            } else {
              questionAnswer = {
                question: `条目 ${index + 1}`,
                answer: content,
              };
            }
          }
        } catch (error) {
          console.error("处理chunk数据出错:", error);
          questionAnswer = {
            question: `条目 ${index + 1}`,
            answer: chunk.content || "",
          };
        }

        return {
          id: chunk.id,
          chunkIndex: chunk.chunkIndex,
          ...questionAnswer,
        };
      });
    },

    // 根据文件类型获取图标
    getFileIcon(type) {
      const iconMap = {
        0: "/images/fileTypes/doc.png",
        1: "/images/fileTypes/qa.png",
        2: "/images/fileTypes/table.png",
        3: "/images/fileTypes/web.png",
        DOC: "/images/fileTypes/doc.png",
        QA: "/images/fileTypes/qa.png",
        TABLE: "/images/fileTypes/table.png",
        WEB: "/images/fileTypes/web.png",
      };
      return iconMap[type] || "/images/fileTypes/doc.png";
    },

    // 根据文件类型获取类型名称
    getFileTypeName(type) {
      const typeMap = {
        0: "文档",
        1: "问答对",
        2: "表格",
        3: "网站",
        DOC: "文档",
        QA: "问答",
        TABLE: "表格",
        WEB: "网站",
      };
      return typeMap[type] || "文档";
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 0;
      this.loading = true;

      // 使用api.fileService进行调用
      const params = {
        pageIndex: this.currentPage,
        pageSize: this.pageSize,
        contentQuery: this.searchQuery,
      };

      api.fileService
        .getFileContent(this.fileId, params)
        .then((res) => {
          if (res.code === 200 || res.isSuccess) {
            // 保存文件信息
            if (res.data && res.data.fileInfo) {
              this.fileData = res.data.fileInfo;
            }

            // 处理所有类型的内容为表格数据
            if (res.data && res.data.chunks) {
              // 判断是否为表格类型
              if (this.isTableType) {
                this.processTableContent(res.data.chunks);
              }
              // 判断是否为文档类型
              else if (this.isDocType) {
                this.processDocContent(res.data.chunks);
              } else {
                // 处理问答类型数据
                this.tableData = this.processQAContent(res.data.chunks);
              }

              // 更新总数
              this.total = res.data.totalChunks || 0;
            }
          } else {
            this.$showFriendlyError(null, res.message || "获取文件内容失败");
          }
        })
        .catch((err) => {
          this.$showFriendlyError(null,
            "获取文件内容失败：" + (err.message || "未知错误")
          );
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理编辑行
    handleEdit(index, row) {
      this.dialogTitle = "编辑条目";
      this.currentRow = JSON.parse(JSON.stringify(row));
      this.currentIndex = index;
      this.isEditing = true;
      this.dialogVisible = true;
    },

    // 处理删除行
    handleDelete(index) {
      this.$confirm("确认删除该条记录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const chunkId = this.tableData[index].id;

          api.fileService
            .deleteFileContent(this.fileId, chunkId)
            .then((res) => {
              if (res.code === 200 || res.isSuccess) {
                this.tableData.splice(index, 1);
                this.$message.success("删除成功");
              } else {
                this.$showFriendlyError(null, res.message || "删除失败");
              }
            })
            .catch((err) => {
              this.$showFriendlyError(null, "删除失败：" + (err.message || "未知错误"));
            });
        })
        .catch(() => {});
    },

    // 处理添加行
    handleAddRow() {
      this.dialogTitle = "新增条目";
      this.currentRow = {
        question: "",
        answer: "",
      };
      this.isEditing = false;
      this.dialogVisible = true;
    },

    // 保存行数据
    saveRow() {
      this.$refs.rowForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;

          if (this.isEditing) {
            // 更新已有行
            const chunkId = this.tableData[this.currentIndex].id;
            const data = {
              content: `${this.currentRow.question},${this.currentRow.answer}`,
            };

            api.fileService
              .updateFileContent(this.fileId, chunkId, data)
              .then((res) => {
                if (res.code === 200 || res.isSuccess) {
                  this.tableData[this.currentIndex] = {
                    ...this.tableData[this.currentIndex],
                    ...this.currentRow,
                  };
                  this.$message.success("更新成功");
                  this.dialogVisible = false;
                } else {
                  this.$showFriendlyError(null, res.message || "更新失败");
                }
              })
              .finally(() => {
                this.saveLoading = false;
              });
          } else {
            // 添加新行
            const data = {
              content: `${this.currentRow.question},${this.currentRow.answer}`,
            };

            api.fileService
              .addFileContent(this.fileId, data)
              .then((res) => {
                if (res.code === 200 || res.isSuccess) {
                  // 如果后端返回了新创建的内容ID
                  if (res.data && res.data.id) {
                    this.tableData.unshift({
                      id: res.data.id,
                      ...this.currentRow,
                    });
                  } else {
                    // 使用临时ID
                    this.tableData.unshift({
                      id: `new_${Date.now()}`,
                      ...this.currentRow,
                    });
                  }

                  this.$message.success("添加成功");
                  this.dialogVisible = false;

                  // 刷新列表
                  this.fetchFileContent();
                } else {
                  this.$showFriendlyError(null, res.message || "添加失败");
                }
              })
              .catch((err) => {
                this.$showFriendlyError(null, "添加失败：" + (err.message || "未知错误"));
              })
              .finally(() => {
                this.saveLoading = false;
              });
          }
        }
      });
    },

    // 下载文件
    downloadFile() {
      // 检查文件是否有可用的URL
      if (this.fileData.url) {
        window.open(this.fileData.url, "_blank");
      } else if (this.fileData.ossUrl) {
        window.open(this.fileData.ossUrl, "_blank");
      } else {
        this.$message.warning("没有找到可下载的文件链接");
      }
    },

    // 编辑文件
    editFile() {
      this.$emit("edit-file", this.fileData.id);
    },

    // 关闭查看器
    closeViewer() {
      this.$emit("close");
    },

    // 处理每页数量变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchFileContent();
    },

    // 处理页码变化
    handleCurrentChange(val) {
      // Element UI分页从1开始，转换为从0开始的索引
      this.currentPage = val - 1;
      this.fetchFileContent();
    },

    // 处理查看详情
    handleView(index, row) {
      // 深拷贝行数据
      this.currentRow = JSON.parse(JSON.stringify(row));
      this.viewDialogVisible = true;
    },
  },
};
</script>

<style lang="scss">
/* 全局样式，不使用scoped */
.table-tooltip {
  max-width: 400px !important;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
  padding: 12px !important;
}
</style>

<style lang="scss" scoped>
.file-viewer {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .viewer-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #eaeaea;

    .file-info {
      display: flex;
      align-items: center;

      .file-icon {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }

      .file-name {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        margin-right: 12px;
      }

      .file-type {
        margin-left: 8px;
      }
    }

    .header-actions {
      display: flex;
      gap: 16px;
    }
  }

  .viewer-content {
    flex: 1;
    overflow: auto;
    padding: 20px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .search-box {
        width: 300px;
      }
    }

    .cell-content {
      white-space: pre-wrap;
      word-break: break-word;
      line-height: 1.5;

      &.single-line {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 50px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

/* 文档类型特有样式 */
.document-content {
  .doc-stats {
    margin-bottom: 20px;

    .stats-card {
      border: 1px solid #ebeef5;

      .stats-info {
        display: flex;
        gap: 30px;

        .stats-item {
          display: flex;
          align-items: center;

          .stats-label {
            color: #666;
            margin-right: 8px;
          }

          .stats-value {
            font-weight: 600;
            color: #333;
          }
        }
      }
    }
  }

  .doc-segments {
    .doc-segment {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      overflow: hidden;

      .segment-header {
        background: #f8f9fa;
        padding: 12px 16px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .segment-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
          color: #333;

          i {
            color: #409eff;
          }
        }

        .segment-actions {
          display: flex;
          gap: 8px;
        }
      }

      .segment-content {
        padding: 16px;
        background: #fff;

        .markdown-preview, .html-preview, .text-preview {
          margin-bottom: 12px;
        }

        .view-more {
          text-align: right;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;
        }
      }
    }
  }
}

/* 详情弹窗样式 */
.detail-dialog {
  .detail-content {
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0 16px 0;
      margin-bottom: 16px;
      border-bottom: 1px solid #ebeef5;

      .detail-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #333;

        i {
          color: #409eff;
        }
      }
    }

    .detail-body {
      max-height: 600px;
      overflow-y: auto;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
    }

    .detail-item {
      margin-bottom: 20px;

      .detail-label {
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 15px;
        color: #333;
      }

      .detail-value {
        padding: 16px;
        background-color: #f7f9fc;
        border-radius: 4px;
        border: 1px solid #ebeef5;
        min-height: 40px;
        font-size: 14px;
        line-height: 1.6;

        &.content-wrap {
          white-space: pre-wrap;
          word-break: break-word;
          min-height: 120px;
          max-height: 500px;
          overflow-y: auto;
        }
      }
    }
  }
}

/* Markdown内容样式 */
.markdown-content, .markdown-preview {
  :deep(h1), :deep(h2), :deep(h3) {
    color: #333;
    margin: 16px 0 12px 0;
    font-weight: 600;
  }

  :deep(h1) {
    font-size: 24px;
    border-bottom: 2px solid #ebeef5;
    padding-bottom: 8px;
  }

  :deep(h2) {
    font-size: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 6px;
  }

  :deep(h3) {
    font-size: 18px;
  }

  :deep(strong) {
    font-weight: 600;
    color: #333;
  }

  :deep(em) {
    font-style: italic;
    color: #666;
  }

  :deep(code) {
    background-color: #f7f7f7;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
  }

  :deep(pre) {
    background-color: #f7f7f7;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 12px 0;

    code {
      background: none;
      padding: 0;
    }
  }

  :deep(ul) {
    margin: 12px 0;
    padding-left: 20px;

    li {
      margin: 6px 0;
      list-style-type: disc;
    }
  }
}

/* HTML内容样式 */
.html-content, .html-preview {
  line-height: 1.6;
  color: #333;

  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  :deep(th), :deep(td) {
    border: 1px solid #e8e8e8;
    padding: 12px 16px;
    text-align: left;
    word-break: break-word;
  }

  :deep(th) {
    background-color: #f7f7f7;
    font-weight: 500;
    color: #333;
    border-bottom: 2px solid #ddd;
  }

  :deep(tr:nth-child(even)) {
    background-color: #fafafa;
  }

  :deep(tr:hover) {
    background-color: #f0f7ff;
  }
}

/* 文本内容样式 */
.text-content, .text-preview {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  color: #333;
}
</style>
