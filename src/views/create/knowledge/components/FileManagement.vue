<template>
  <div class="file-management">
    <!-- 文件列表视图 -->
    <div v-if="!showFileViewer" class="file-list-view">
      <div class="operation-bar">
        <div class="left-actions">
          <el-radio-group v-model="fileType" @change="handleFileTypeChange">
            <el-radio-button label="10">全部</el-radio-button>
            <el-radio-button label="0">文档</el-radio-button>
            <el-radio-button label="1">问答</el-radio-button>
            <el-radio-button label="2">表格</el-radio-button>
            <el-radio-button label="3">网页</el-radio-button>
          </el-radio-group>
        </div>
        <div class="right-actions">
          <el-input
            v-model="searchQuery"
            placeholder="请输入文件名搜索"
            prefix-icon="el-icon-search"
            clearable
            style="width: 200px; margin-right: 16px;"
            @input="handleSearch"
          ></el-input>
          <el-dropdown @command="handleCreateCommand">
            <el-button type="primary" icon="el-icon-plus">
              创建<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="manual">手动创建</el-dropdown-item>
              <el-dropdown-item command="import">导入文件</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <!-- 文件列表表格 -->
      <el-table
        v-loading="loading"
        :data="fileList"
        style="width: 100%; margin-top: 16px; background: transparent;"
        border
        :header-cell-style="{ background: 'transparent', backgroundColor: 'transparent' }"
        :header-row-style="{ background: 'transparent', backgroundColor: 'transparent' }"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        ></el-table-column>

        <el-table-column
          prop="name"
          label="文件名"
          min-width="250"
        >
          <template slot-scope="scope">
            <div class="file-name">
              <img :src="getFileIcon(scope.row.type)" class="file-icon" alt="" />
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="type"
          label="文件类型"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            {{ getFileTypeName(scope.row.type) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="size"
          label="文件大小(KB)"
          width="140"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.size || 30 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="tokens"
          label="tokens"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.tokens || 10942 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="chunkCount"
          label="知识库条数"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.chunkCount || 65 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1? '生效' : '未生效' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="200"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" size="mini" plain @click="handleCommand('edit', scope.row)">重命名</el-button>
            <el-button type="primary" size="mini" plain @click="handleCommand('delete', scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background
        >
        </el-pagination>
      </div>

      <!-- 手动创建文件弹窗 -->
      <el-dialog
        title="手动创建"
        :visible.sync="createDialogVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <div class="manual-create-dialog">
          <div class="file-type-selector">
            <div
              class="file-type-option"
              :class="{ 'active': createFileType === 'doc' }"
              @click="createFileType = 'doc'"
            >
              <div class="option-content">
                <img src="/images/fileTypes/doc.png" class="file-icon" alt="" />
                <span>文档</span>
              </div>
            </div>
            <div
              class="file-type-option"
              :class="{ 'active': createFileType === 'qa' }"
              @click="createFileType = 'qa'"
            >
              <div class="option-content">
                <img src="/images/fileTypes/qa.png" class="file-icon" alt="" />
                <span>问答</span>
              </div>
            </div>
          </div>
          <el-input v-model="createFileName" placeholder="请输入文件名称" class="file-name-input"></el-input>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="createDialogVisible = false" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="confirmCreate" :loading="loading">确定</el-button>
        </span>
      </el-dialog>

      <!-- 重命名文件弹窗 -->
      <el-dialog
        title="重命名文件"
        :visible.sync="renameDialogVisible"
        :close-on-click-modal="false"
        width="30%"
        center
      >
        <el-form :model="renameForm" label-width="80px">
          <el-form-item label="文件名称">
            <el-input v-model="renameForm.name" maxlength="20" style="width: 80%;" placeholder="请输入文件名称"></el-input>
            <span v-if="renameForm.extension" style="margin-left: 5px; color: #909399;">{{ renameForm.extension }}</span>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="renameDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRename" :loading="renaming">确定</el-button>
        </div>
      </el-dialog>
    </div>

    <!-- 文件查看器视图 -->
    <div v-else class="file-viewer-container">
      <FileViewer
        :fileId="currentFileId"
        :knowledgeBaseId="knowledgeBaseId"
        @close="closeFileViewer"
        @edit-file="handleEdit"
      />
    </div>
  </div>
</template>

<script>
import { api } from '@/api/request'
import FileViewer from './FileViewer.vue'

export default {
  name: 'FileManagement',
  components: {
    FileViewer
  },
  props: {
    knowledgeBaseId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      fileType: '10',
      searchQuery: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      fileList: [],
      createDialogVisible: false,
      createFileType: 'doc',
      createFileName: '',
      showFileViewer: false,
      currentFileId: null,
      renameDialogVisible: false,
      renaming: false,
      renameForm: {
        id: '',
        name: '',
        extension: ''
      }
    }
  },
  created() {
    this.fetchFileList()
  },
  methods: {
    // 获取文件列表
    fetchFileList() {
      this.loading = true

      const params = {
        filename: this.searchQuery,
        filetype: Number(this.fileType),
        knowledgebaseid: this.knowledgeBaseId,
        pageindex: this.currentPage - 1, // API从0开始计数
        pagesize: this.pageSize
      }

      api.rag.getRagFileList(params)
        .then(res => {
          if (res.code==200) {
            // 处理文件列表数据
            this.fileList = (res.data.items || []).map(item => {
              // 确保每个文件项都有正确的类型字段
              if (!item.type && item.file_type) {
                item.type = item.file_type; // 兼容旧的API返回格式
              }
              return item;
            });
            this.total = res.data.total || 0
          } else {
            this.$showFriendlyError(null, res.status && res.status.message || '获取文件列表失败')
          }
        })
        .catch(err => {
          this.$showFriendlyError(null, '获取文件列表失败：' + (err.message || '未知错误'))
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 根据文件类型获取图标
    getFileIcon(type) {
      const iconMap = {
        '0': '/images/fileTypes/doc.png', // 文档
        '1': '/images/fileTypes/qa.png', // 问答
        '2': '/images/fileTypes/table.png', // 表格
        '3': '/images/fileTypes/web.png', // 网站
        'DOC': '/images/fileTypes/doc.png', // 文档
        'QA': '/images/fileTypes/qa.png', // 问答
        'TABLE': '/images/fileTypes/table.png', // 表格
        'WEB': '/images/fileTypes/web.png' // 网站
      }
      return iconMap[type] || '/images/fileTypes/doc.png'
    },

    // 根据文件类型ID获取类型名称
    getFileTypeName(type) {
      const typeMap = {
        '0': '文档',
        '1': '问答',
        '2': '表格',
        '3': '网站',
        'DOC': '文档',
        'QA': '问答',
        'TABLE': '表格',
        'WEB': '网站'
      }
      return typeMap[type] || '未知'
    },

    // 处理文件类型变更
    handleFileTypeChange() {
      this.currentPage = 1 // 重置为第一页
      this.fetchFileList()
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1 // 重置为第一页
      this.fetchFileList()
    },

    // 处理创建命令
    handleCreateCommand(command) {
      if (command === 'manual') {
        this.showCreateDialog();
      } else if (command === 'import') {
        this.goToImportPage();
      }
    },

    // 显示创建对话框
    showCreateDialog() {
      this.createDialogVisible = true;
      this.createFileType = 'doc';
      this.createFileName = '';
    },

    // 跳转到导入页面
    goToImportPage() {
      // 通过事件通知父组件（KnowledgeSetting）切换到导入菜单
      this.$emit('switch-menu', 'import');
    },

    // 确认创建文件
    confirmCreate() {
      if (!this.createFileName.trim()) {
        this.$message.warning('请输入文件名称');
        return;
      }

      this.loading = true;
      // 创建FormData对象
      const formData = new FormData();
      // 添加知识库ID
      formData.append('knowledgeBaseId', this.knowledgeBaseId);

      // 创建一个空文件内容
      const emptyContent = this.createFileType === 'doc' ? '这是一个空文档' : '问题,答案\n示例问题,示例答案';
      const fileName = `${this.createFileName.trim()}.${this.createFileType === 'doc' ? 'txt' : 'csv'}`;

      // 创建Blob对象
      const blob = new Blob([emptyContent], {
        type: this.createFileType === 'doc' ? 'text/plain' : 'text/csv'
      });

      // 将文件添加到FormData
      formData.append('files', blob, fileName);

      // 根据文件类型选择不同的API
      const apiRequest = this.createFileType === 'doc'
        ? api.rag.uploadForm(formData)
        : api.rag.qaPreview(formData);

      apiRequest
        .then(res => {
          if (res.code === 200 || res.isSuccess) {
            // 获取文件ID
            const fileData = res.data.items && res.data.items[0];
            if (fileData && fileData.fileId) {
              // 确认导入到知识库
              const confirmData = {
                knowledgeBaseId: this.knowledgeBaseId,
                fileIds: [fileData.fileId]
              };

              // 根据文件类型选择确认方法
              const confirmRequest = this.createFileType === 'doc'
                ? api.rag.confirm(confirmData)
                : api.rag.qaConfirm(confirmData);

              return confirmRequest.then(confirmRes => {
                if (confirmRes.code === 200 || confirmRes.isSuccess) {
                  this.$message.success(`创建${this.createFileType === 'doc' ? '文档' : '问答'}：${this.createFileName} 成功`);
                  this.createDialogVisible = false;
                  // 刷新文件列表
                  this.fetchFileList();
                } else {
                  throw new Error(confirmRes.message || '确认导入失败');
                }
              });
            } else {
              throw new Error('文件创建成功但未获取到ID');
            }
          } else {
            throw new Error(res.message || '创建失败');
          }
        })
        .catch(error => {
          this.$showFriendlyError(error, '创建失败，请重试');
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 查看文件
    handleView(row) {
      this.currentFileId = row.id || row.fileId
      this.showFileViewer = true
    },

    // 通过ID查看文件
    handleViewById(fileId) {
      if (!fileId) {
        this.$message.warning('文件ID不可用')
        return
      }
      this.currentFileId = fileId
      this.showFileViewer = true
    },

    // 关闭文件查看器
    closeFileViewer() {
      this.showFileViewer = false
      this.currentFileId = null
    },

    // 处理下拉菜单命令
    handleCommand(command, row) {
      switch (command) {
        case 'edit':
          this.handleRename(row);
          break
        case 'export':
          this.exportFile(row)
          break
        case 'delete':
          this.$confirm('确认删除该文件吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 调用删除文件API
            api.rag.deleteFile(row.id).then(() => {
              this.$message.success('删除成功')
              this.currentPage = 1 // 重置为第一页
              this.fetchFileList() // 刷新文件列表
            }).catch(error => {
              this.$showFriendlyError(error, '删除失败，请重试')
            })
          }).catch(() => {})
          break
      }
    },

    // 处理重命名文件
    handleRename(row) {
      this.renameForm.id = row.id;

      // 从完整文件名中提取名称部分（不含后缀）
      const fullName = row.name;
      const lastDotIndex = fullName.lastIndexOf('.');

      // 如果找到了后缀，则分离名称和后缀
      if (lastDotIndex !== -1) {
        this.renameForm.name = fullName.substring(0, lastDotIndex);
        this.renameForm.extension = fullName.substring(lastDotIndex); // 保存后缀
      } else {
        // 如果没有后缀，则使用完整名称
        this.renameForm.name = fullName;
        this.renameForm.extension = '';
      }

      this.renameDialogVisible = true;
    },

    // 提交重命名
    async submitRename() {
      if (!this.renameForm.name.trim()) {
        this.$message.warning('请输入文件名称');
        return;
      }
      if(this.renameForm.name.length > 20){
        this.$message.warning("文件名称不能超过20个字符");
        return;
      }
      this.renaming = true;
      try {
        // 组合新的完整文件名（名称+原后缀）
        const newFullName = this.renameForm.name.trim() + this.renameForm.extension;

        const params = {
          id: this.renameForm.id,
          name: newFullName
        };

        const result = await api.fileService.rename(params);

        if (result && (result.code === 200 || result.isSuccess)) {
          this.$message.success(result.message || "重命名成功");
          this.renameDialogVisible = false;
          this.fetchFileList(); // 重新加载列表
        } else {
          this.$showFriendlyError(null, result && result.message || "重命名失败");
        }
      } catch (error) {
        console.error("重命名失败:", error);
        this.$showFriendlyError(error, "重命名失败，请重试");
      } finally {
        this.renaming = false;
      }
    },

    // 处理每页数量变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchFileList()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchFileList()
    },

    // 刷新列表
    handleRefresh() {
      this.fetchFileList()
      this.$message.success('刷新成功')
    },

    // 导出文件
    exportFile(file) {
      // 检查文件是否有可用的URL
      if (file.url) {
        // 使用文件的URL直接在新标签页打开
        window.open(file.url, '_blank');
        this.$message.success(`文件已在新标签页打开`);
      } else if (file.ossUrl) {
        // 使用文件的ossUrl在新标签页打开
        window.open(file.ossUrl, '_blank');
        this.$message.success(`文件已在新标签页打开`);
      } else {
        this.$message.warning('没有找到可打开的文件链接');
      }
    },

    // 处理编辑文件
    handleEdit(file) {
      // 实现编辑文件的逻辑
      console.log('编辑文件:', file);
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) {
        return '-';
      }

      try {
        // 处理ISO 8601格式的时间字符串，例如：2025-07-23T10:12:34
        const date = new Date(dateTimeStr);

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return dateTimeStr; // 如果不是有效日期，返回原字符串
        }

        // 格式化为 YYYY-MM-DD HH:mm:ss
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error('时间格式化失败:', error);
        return dateTimeStr; // 格式化失败时返回原字符串
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.file-management {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 4px;

    .left-actions {
      .el-radio-group {
        .el-radio-button__inner {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }

    .right-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .file-name {
    display: flex;
    align-items: center;
    gap: 12px;

    img {
      width: 24px;
      height: 24px;
    }

    span {
      font-size: 14px;
      line-height: 1.5;
      font-weight: 400;
      color: #303133;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.el-table) {
    background: transparent !important;

    .el-button--mini {
      padding: 5px 8px;
      margin: 0 2px;
      font-size: 12px;
    }

    .el-table__row {
      height: 60px;
    }

    .el-table__header-wrapper {
      background: transparent !important;
    }

    .el-table__header {
      background: transparent !important;
    }

    .el-table thead {
      background: transparent !important;
    }

    .el-table th {
      background: transparent !important;
      background-color: transparent !important;
      color: #606266;
      font-weight: 500;
    }

    .el-table th.is-leaf {
      background: transparent !important;
      background-color: transparent !important;
    }

    .el-table th.gutter {
      background: transparent !important;
      background-color: transparent !important;
    }

    .el-table__body-wrapper {
      background: transparent !important;
    }

    .el-table__row {
      background: transparent !important;
    }

    .el-table__row:hover {
      background: rgba(64, 158, 255, 0.1) !important;
    }

    .el-table__body {
      background: transparent !important;
    }

    .el-table__empty-block {
      background: transparent !important;
    }

    .el-table__append-wrapper {
      background: transparent !important;
    }
  }

  .manual-create-dialog {
    .file-type-selector {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin: 10px 0 30px;

      .file-type-option {
        cursor: pointer;
        padding: 15px 20px;
        border-radius: 8px;
        border: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          border-color: #409EFF;
          background-color: #ecf5ff;
        }

        .option-content {
          display: flex;
          align-items: center;

          img {
            width: 36px;
            height: 36px;
            margin-right: 12px;
          }

          span {
            font-size: 15px;
            font-weight: 500;
          }
        }
      }
    }

    .file-name-input {
      margin-top: 20px;
    }
  }

  .file-viewer-container {
    height: 100%;
    width: 100%;
  }
}

// 全局样式覆盖，确保表头透明
:deep(.el-table) {
  background: transparent !important;

  .el-table__header-wrapper {
    background: transparent !important;
  }

  .el-table__header {
    background: transparent !important;
  }

  thead {
    background: transparent !important;
  }

  th {
    background: transparent !important;
    background-color: transparent !important;
  }

  th.is-leaf {
    background: transparent !important;
    background-color: transparent !important;
  }

  th.gutter {
    background: transparent !important;
    background-color: transparent !important;
  }
}
</style>
