<template>
  <div class="file-import">
    <div v-if="$route.name === 'KnowledgeImport'" class="back-bar">
      <el-button type="text" icon="el-icon-arrow-left" @click="goBack">返回知识库</el-button>
    </div>
    <!-- 顶部类型选择卡片 -->
    <div class="import-type-cards">
      <div
        v-for="(item, index) in importTypes"
        :key="index"
        :class="['type-card', { active: importType === item.value }]"
        @click="handleImportTypeChange(item.value)"
      >
        <img :src="item.icon" class="type-icon" alt="" />
        <div class="card-content">
          <div class="title">{{ item.title }}</div>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧操作区 -->
      <div class="operation-area" v-if="importType !== 'table' || !hasFiles">
        <!-- 文档导入 -->
        <div v-if="importType === 'doc'" class="import-settings">
          <div class="setting-item import-method">
            <div class="label">
              导入方式
              <span class="required">*</span>
            </div>
            <div class="content">
              <el-radio-group v-model="importMethod" size="small">
                <el-radio label="local">导入本地文档</el-radio>
                <el-radio label="remote">导入飞书文档</el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="setting-item">
            <div class="label">
              上传文件
              <span class="required">*</span>
            </div>
            <div class="content">
              <div class="file-tips">
                <i class="el-icon-info"></i>
                <div class="capacity-info" v-if="capacityInfo">
                  <span>支持 txt, pdf, md, docx格式文档，当前知识库：{{ capacityInfo.fileCount }}个文件，{{ capacityInfo.chunkCount }}个段落，{{ (capacityInfo.totalSize / 1024).toFixed(2) }}KB</span>
                </div>
              </div>
              <el-upload
                class="file-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileList"
                multiple
                drag
                accept=".txt,.pdf,.md,.docx,.doc"
              >
                <div class="upload-trigger">
                  <i class="el-icon-upload"></i>
                  <div class="upload-text">将文件拖到此处，或<em>点击选择文件</em></div>
                </div>
              </el-upload>
            </div>
          </div>

          <div class="setting-item">
            <div class="label">
              增强解析
              <el-tooltip content="开启后将使用更强大的解析能力" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <div class="content">
              <el-switch v-model="enhancedParsing"></el-switch>
            </div>
          </div>
        </div>

        <!-- 问答导入 -->
        <div v-if="importType === 'qa'" class="import-settings">
          <div class="setting-item">
            <div class="label">
              上传文件
              <span class="required">*</span>
            </div>
            <div class="content">
              <div class="file-tips">
                <i class="el-icon-info"></i>
                支持 csv(双列) 格式文件，当前知识库：{{ capacityInfo.fileCount }}个文件，{{ capacityInfo.chunkCount }}个段落，{{ (capacityInfo.totalSize / 1024).toFixed(2) }}KB
              </div>
              <div class="csv-template">
                上传文件需和模板文件格式一致（CSV UTF-8格式）
                <el-link type="primary" @click="downloadTemplate">下载CSV模板</el-link>
              </div>
              <el-upload
                class="file-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleQAFileChange"
                :on-remove="handleQAFileRemove"
                :file-list="qaFileList"
                multiple
                drag
                accept=".csv"
              >
                <div class="upload-trigger">
                  <i class="el-icon-upload"></i>
                  <div class="upload-text">
                    <span>拖入文件或</span>
                    <em>点击选择文件</em>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>

        <!-- 表格导入 -->
        <div v-if="importType === 'table'" class="import-settings">
          <div class="setting-item import-method">
            <div class="label">
              导入方式
              <span class="required">*</span>
            </div>
            <div class="content">
              <el-radio-group v-model="tableImportMethod" size="small">
                <el-radio label="local">导入本地表格</el-radio>
                <el-radio label="remote">导入飞书表格</el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="setting-item">
            <div class="label">
              上传文件
              <span class="required">*</span>
            </div>
            <div class="content">
              <div class="file-tips">
                <i class="el-icon-info"></i>
                支持 csv (UTF-8) 格式, xlsx 格式文件，当前知识库：{{ capacityInfo.fileCount }}个文件，{{ capacityInfo.chunkCount }}个段落，{{ (capacityInfo.totalSize / 1024).toFixed(2) }}KB
              </div>
              <el-upload
                class="file-uploader"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleTableFileChange"
                :on-remove="handleTableFileRemove"
                :file-list="tableFileList"
                :limit="1"
                drag
                accept=".csv,.xlsx"
              >
                <div class="upload-trigger">
                  <i class="el-icon-upload"></i>
                  <div class="upload-text">
                    <span>拖入文件或</span>
                    <em>点击选择文件</em>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>

        <!-- 网页导入 -->
        <div v-if="importType === 'web'" class="import-settings">
          <div class="setting-item import-method">
            <div class="label">
              导入方式
              <span class="required">*</span>
            </div>
            <div class="content">
              <el-radio-group v-model="webImportMethod" size="small">
                <el-radio label="url">导入独立网址</el-radio>
                <el-radio label="sitemap">基于网站地图导入</el-radio>
              </el-radio-group>
            </div>
          </div>

          <div class="setting-item">
            <div class="label">
              网页地址
              <span class="required">*</span>
            </div>
            <div class="content">
              <div class="web-input">
                <div class="url-tips">当前版本单次可添加30个网址（含深度解析的子页面）</div>
                <el-input
                  v-model="webUrl"
                  type="textarea"
                  :rows="4"
                  placeholder="可批量输入网址（以http或https开头），用换行分隔"
                ></el-input>
                <div class="web-options">
                  <div class="option-item">
                    <span>提取子页面</span>
                    <el-tooltip content="开启后将自动提取网页中的链接" placement="top">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                    <el-switch v-model="extractSubPages" class="option-switch"></el-switch>
                  </div>
                  <div class="option-item">
                    <span>提取图片</span>
                    <el-tooltip content="开启后将提取网页中的图片" placement="top">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                    <el-switch v-model="extractImages" class="option-switch"></el-switch>
                  </div>
                </div>
                <el-button type="primary" @click="handleWebImport">导入</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 文档列表 -->
        <div class="document-list" v-if="allDocuments.length > 0">
          <div class="list-header">
            <div class="list-title">
              <i class="el-icon-document"></i>
              已导入文档 ({{ allDocuments.length }})
            </div>
          </div>
          <el-table
            :data="allDocuments"
            style="width: 100%"
            @row-click="handleDocumentClick"
            :highlight-current-row="true"
            :row-class-name="tableRowClassName"
          >
            <el-table-column prop="filename" label="文档名称" min-width="250">
              <template #default="scope">
                <div class="filename-cell">
                  <i :class="getFileIconClass(scope.row.document_type)"></i>
                  <span>{{ scope.row.filename }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="document_type" label="类型" width="80"></el-table-column>
            <el-table-column prop="total_chunks" label="分段数" width="80" align="center"></el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="scope">
                <el-button @click.stop="deleteDocument(scope.row.index)" type="text" icon="el-icon-delete" class="danger-text">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧预览区 -->
      <div class="preview-container" :class="{'full-width': importType === 'table' && hasFiles}">
        <!-- 无内容时的预览区 -->
        <div class="preview-area" v-if="!hasFiles">
          <el-empty :description="getPreviewText">
            <template #image>
              <i class="el-icon-document preview-icon"></i>
            </template>
          </el-empty>
        </div>

        <!-- 有内容时的预览区 -->
        <div class="preview-area" v-if="hasFiles && previewData">
          <div class="preview-content">
            <div class="preview-header">
              <div class="preview-title">
                <i class="el-icon-info"></i>
                分段预览
                <span class="preview-subtitle">{{ previewData.filename }}</span>
              </div>
            </div>

            <!-- 非表格和非QA类型的分段预览 -->
            <el-tabs v-model="activeChunkTab" type="card" class="preview-tabs" v-if="previewData.document_type !== 'table' && previewData.document_type !== 'qa'">
              <!-- 其他文档类型 -->
              <el-tab-pane
                v-for="(chunk, index) in (previewData.chunks || previewData.preview_chunks || [])"
                :key="index"
                :label="`分段${(chunk.sequence || index) + 1}`"
                :name="String(chunk.sequence || index)"
              >
                <div class="chunk-content">
                  <pre v-if="!isMarkdownOrHtml(chunk.text)">{{ chunk.text }}</pre>
                  <div v-else-if="isMarkdown(chunk.text)" v-html="formatMarkdown(chunk.text)" class="markdown-content"></div>
                  <div v-else v-html="chunk.text" class="html-content"></div>
                </div>
              </el-tab-pane>
            </el-tabs>

            <!-- 表格类型特殊处理 - 完全独立于标签页结构 -->
            <div v-if="previewData.document_type === 'table'" class="table-preview-container">
              <div class="table-preview">
                <table class="preview-table">
                  <thead>
                    <tr>
                      <th v-for="(column, colIndex) in previewData.columns" :key="colIndex">
                        {{ column }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, rowIndex) in previewData.preview_rows" :key="rowIndex">
                      <td v-for="(value, cellIndex) in row.values" :key="cellIndex">
                        <el-tooltip :content="value === 'nan' ? '' : value" placement="top" :disabled="!value || value === 'nan'">
                          <div class="cell-content">{{ value === 'nan' ? '' : value }}</div>
                        </el-tooltip>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- QA类型特殊处理 - 完全独立于标签页结构 -->
            <div v-if="previewData.document_type === 'qa'" class="qa-preview-container">
              <div class="qa-preview">
                <div v-for="(chunk, index) in (previewData.chunks || previewData.preview_chunks || [])"
                  :key="index"
                  class="qa-segment">
                  <div class="qa-segment-header">分段{{ (chunk.sequence || index) + 1 }} {{ previewData.filename }}</div>
                  <div class="qa-item">
                    <div class="qa-question">
                      <span class="qa-label">问题：</span>
                      <div class="qa-text">{{ chunk.text.split('\n答：')[0].replace('问：', '') }}</div>
                    </div>
                    <div class="qa-answer">
                      <span class="qa-label">答案：</span>
                      <div class="qa-text">{{ chunk.text.split('\n答：')[1] || '' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部固定区域 -->
    <div class="footer-area" v-if="hasFiles && previewData">
      <div class="footer-container">
        <div class="info-items">
          <div class="info-item">
            <span class="label">单文件段落数：</span>
            <span class="value">{{ previewData.chunks ? previewData.chunks.length : previewData.total_chunks || 0 }}</span>
          </div>
          <!-- <div class="info-item">
            <span class="label">单文件价格：</span>
            <span class="value">4 积分</span>
          </div>
          <div class="info-item">
            <span class="label">预估总价：</span>
            <span class="value">{{ allDocuments.length * 4 }} 积分</span>
          </div> -->
        </div>
        <div class="action-buttons">
          <el-button @click="handleCancel">取消导入</el-button>
          <el-button type="primary" @click="handleImport">确定导入</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { api } from '@/api/request'
import { formatOutputContent } from '@/utils'; // 导入格式化函数
import { processFileForUpload } from '@/utils/documentConverter'; // 导入文档转换工具

export default {
  name: 'FileImport',
  props: {
    knowledgeBaseId: {
      type: String,
      default: 'kb_123' // 默认知识库ID
    }
  },
  data() {
    return {
      importType: 'doc',
      importMethod: 'local',
      qaImportMethod: 'local',
      tableImportMethod: 'local',
      webImportMethod: 'url',
      enhancedParsing: false,
      hasFiles: false,
      webUrl: '',
      extractSubPages: false,
      extractImages: false,
      fileList: [], // 文档文件列表
      qaFileList: [], // 问答文件列表
      tableFileList: [], // 表格文件列表
      uploading: false, // 上传状态
      uploadResults: [], // 上传结果
      // 存储各类型的文档数据
      docDocuments: [],
      qaDocuments: [],
      tableDocuments: [],
      webDocuments: [],
      // 存储各类型的预览数据
      docPreviewData: null,
      qaPreviewData: null,
      tablePreviewData: null,
      webPreviewData: null,
      // 存储各类型的文件状态
      docHasFiles: false,
      qaHasFiles: false,
      tableHasFiles: false,
      webHasFiles: false,
      // 存储各类型的活动文档索引
      docActiveIndex: -1,
      qaActiveIndex: -1,
      tableActiveIndex: -1,
      webActiveIndex: -1,
      // 存储各类型的文件列表
      docFileList: [],
      qaStoredFileList: [],
      tableStoredFileList: [],
      importTypes: [
        {
          value: 'doc',
          icon: '/images/fileTypes/doc.png',
          title: '文档',
          desc: '自动解析文档，使用方便'
        },
        {
          value: 'qa',
          icon: '/images/fileTypes/qa.png',
          title: '问答',
          desc: '一问一答导入，准确性更佳'
        },
        {
          value: 'table',
          icon: '/images/fileTypes/table.png',
          title: '表格',
          desc: '结构化表格导入，支持多列检索'
        },
        {
          value: 'web',
          icon: '/images/fileTypes/web.png',
          title: '网页',
          desc: '自动获取网页内容导入'
        }
      ],
      previewData: null, // 当前预览的文档数据
      activeChunkTab: '0',
      allDocuments: [], // 存储所有上传的文档数据
      activeDocumentIndex: -1, // 当前选中的文档索引
      internalKnowledgeBaseId: '', // 内部使用的知识库ID
      capacityInfo: {
        fileCount: 0,
        chunkCount: 0,
        totalSize: 0,
        totalTokenNum: 0
      },
    }
  },
  computed: {
    getPreviewText() {
      const textMap = {
        doc: '导入文档后预览',
        qa: '导入问答后预览',
        table: '导入表格后预览',
        web: '导入网页后预览'
      }
      return textMap[this.importType]
    },
    // 使用计算属性获取实际使用的知识库ID
    actualKnowledgeBaseId() {
      return this.internalKnowledgeBaseId || this.knowledgeBaseId;
    }
  },
  created() {
    // 如果是通过路由参数传入知识库ID
    if (this.$route.params.id) {
      this.internalKnowledgeBaseId = this.$route.params.id;
    } else {
      this.internalKnowledgeBaseId = this.knowledgeBaseId;
    }
    // 获取知识库容量信息
    this.getCapacityInfo();
  },
  methods: {
    handleImportTypeChange(type) {
      if (this.importType === type) return;

      // 保存当前类型的状态
      this.saveCurrentTypeState();

      // 切换导入类型
      this.importType = type;

      // 恢复目标类型的状态
      this.restoreTypeState();
    },

    saveCurrentTypeState() {
      const currentType = this.importType;

      // 保存当前类型的文档数据
      if (currentType === 'doc') {
        this.docDocuments = this.allDocuments.filter(doc => !doc.document_type || doc.document_type === 'doc');
        this.docPreviewData = this.previewData;
        this.docHasFiles = this.hasFiles;
        this.docActiveIndex = this.activeDocumentIndex;
        this.docFileList = [...this.fileList];
      } else if (currentType === 'qa') {
        this.qaDocuments = this.allDocuments.filter(doc => doc.document_type === 'qa');
        this.qaPreviewData = this.previewData;
        this.qaHasFiles = this.hasFiles;
        this.qaActiveIndex = this.activeDocumentIndex;
        this.qaStoredFileList = [...this.qaFileList];
      } else if (currentType === 'table') {
        this.tableDocuments = this.allDocuments.filter(doc => doc.document_type === 'table');
        this.tablePreviewData = this.previewData;
        this.tableHasFiles = this.hasFiles;
        this.tableActiveIndex = this.activeDocumentIndex;
        this.tableStoredFileList = [...this.tableFileList];
      } else if (currentType === 'web') {
        this.webDocuments = this.allDocuments.filter(doc => doc.document_type === 'web');
        this.webPreviewData = this.previewData;
        this.webHasFiles = this.hasFiles;
        this.webActiveIndex = this.activeDocumentIndex;
      }
    },

    restoreTypeState() {
      // 恢复对应类型的状态
      if (this.importType === 'doc') {
        this.allDocuments = [...this.docDocuments];
        this.previewData = this.docPreviewData;
        this.hasFiles = this.docHasFiles;
        this.activeDocumentIndex = this.docActiveIndex;
        this.fileList = [...this.docFileList];
      } else if (this.importType === 'qa') {
        this.allDocuments = [...this.qaDocuments];
        this.previewData = this.qaPreviewData;
        this.hasFiles = this.qaHasFiles;
        this.activeDocumentIndex = this.qaActiveIndex;
        this.qaFileList = [...this.qaStoredFileList];
      } else if (this.importType === 'table') {
        this.allDocuments = [...this.tableDocuments];
        this.previewData = this.tablePreviewData;
        this.hasFiles = this.tableHasFiles;
        this.activeDocumentIndex = this.tableActiveIndex;
        this.tableFileList = [...this.tableStoredFileList];
      } else if (this.importType === 'web') {
        this.allDocuments = [...this.webDocuments];
        this.previewData = this.webPreviewData;
        this.hasFiles = this.webHasFiles;
        this.activeDocumentIndex = this.webActiveIndex;
      }

      // 如果有预览数据，则重置分段索引为第一个（但表格类型不使用tabs）
      if (this.previewData && this.previewData.document_type !== 'table') {
        this.activeChunkTab = '0';
      }
    },
    handleFileChange(file, fileList) {
      console.log('Selected doc file:', file)

      // 验证文件类型和大小
      const validTypes = ['text/plain', 'application/pdf', 'text/markdown', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      const fileExtensions = ['.txt', '.pdf', '.md', '.docx', '.doc']

      const isValidType = validTypes.includes(file.raw.type) ||
                         fileExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
      const isValidSize = file.size / 1024 / 1024 <= 30 // 30MB

      if (!isValidType || !isValidSize) {
        this.$showFriendlyError(null, '请上传符合要求的文件：txt, pdf, md, docx格式，单个文件不超过30M')
        return;
      }

      // 自动上传新添加的文件
      if (file.status === 'ready') {
        // 处理docx到doc的转换
        let fileToUpload = file.raw;
        try {
          const processResult = processFileForUpload(file.raw);
          if (processResult.converted) {
            fileToUpload = processResult.file;
            // 显示转换提示
            this.$message.success(`已将 ${processResult.originalName} 自动转换为 ${processResult.convertedName}`);
          }
        } catch (error) {
          console.error('文件转换失败:', error);
          // 转换失败继续使用原文件
        }

        // 先设置文件列表以显示选择的文件，但这会在上传失败时被重置
        this.fileList = fileList.slice(0, 30);
        // 处理文件列表，添加类型标识和格式化大小
        this.fileList = this.fileList.map((item) => {
          return {
            ...item,
            // 将字节大小转换为KB显示
            size: this.formatFileSize(item.size)
          };
        });
        this.uploadSingleFile(fileToUpload);
      }
    },

    handleFileRemove(file, fileList) {
      this.fileList = fileList
      // 移除文件时，如果没有成功上传的文档，重置hasFiles状态
      if (this.fileList.length === 0 && this.allDocuments.length === 0) {
        this.hasFiles = false;
        this.previewData = null;
        this.activeDocumentIndex = -1;
      }
    },

    async uploadFiles() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true
      this.uploadResults = []

      try {
        for (let i = 0; i < this.fileList.length; i++) {
          const file = this.fileList[i].raw
          await this.uploadFile(file)
        }

        this.$message.success('文件上传成功')
        this.hasFiles = true
      } catch (error) {
        console.error('文件上传失败:', error)
        this.$showFriendlyError(null, '文件上传失败，请重试')
      } finally {
        this.uploading = false
      }
    },

    async uploadFile(file) {
      try {
        // 创建FormData对象
        const formData = new FormData();

        // 添加API所需参数
        formData.append('knowledgeBaseId', this.actualKnowledgeBaseId);
        formData.append('files', file);
        formData.append('enhanceParsing', this.enhancedParsing.toString());
        formData.append('segmentLength', '1000');

        // 调用RAG上传接口 (使用form请求)
        console.log('正在调用文档预览接口...');
        const ragResult = await api.rag.uploadForm(formData);
        console.log('文档预览接口返回结果:', ragResult);

        // 处理RAG接口返回结果
        if (ragResult && (ragResult.isSuccess || ragResult.code === 200)) {
          const responseData = ragResult.data;
          // 获取第一个文件的信息
          const fileData = responseData.items[0];
          console.log('文档预览接口调用成功，文档ID:', fileData.fileId);

          // 创建文档数据结构
          const ragData = {
            document_id: fileData.fileId,
            filename: file.name,
            document_type: this.importType,
            // 将segments转换为chunks格式，保持兼容性
            chunks: fileData.segments.map((segment, index) => ({
              text: segment.text,
              sequence: segment.sort || index
            })),
            total_chunks: fileData.totalSegments,
            // 添加索引以便在表格中标识
            index: this.allDocuments.length
          };

          // 添加到文档列表
          this.allDocuments.push(ragData);

          // 更新当前预览的文档
          this.activeDocumentIndex = ragData.index;
          this.previewData = ragData;
          this.activeChunkTab = '0'; // 默认选中第一个分段

          // 标记为已有文件
          this.hasFiles = true;

          // 保存当前类型的状态
          this.saveCurrentTypeState();

          return {
            ragResult: ragResult
          };
        } else {
          console.error('文档预览接口返回异常:', ragResult);
          this.uploadResults.push({
            fileName: file.name,
            fileSize: file.size,
            status: 'warning',
            message: '文件处理失败'
          });
          // 接口失败时重置状态，恢复上传界面
          this.resetUploadState();
          return { error: '文件处理失败' };
        }
      } catch (error) {
        console.error('文件上传错误:', error);
        this.uploadResults.push({
          fileName: file.name,
          fileSize: file.size,
          status: 'error',
          message: error.message || '上传失败'
        });
        // 接口失败时重置状态，恢复上传界面
        this.resetUploadState();
        throw error;
      }
    },
    handleQAFileChange(file, fileList) {
      console.log('Selected QA file:', file)

      // 验证文件类型和大小
      const validTypes = ['text/csv']
      const fileExtensions = ['.csv']

      const isValidType = validTypes.includes(file.raw.type) ||
                         fileExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
      const isValidSize = file.size / 1024 / 1024 <= 30 // 30MB

      if (!isValidType || !isValidSize) {
        this.$showFriendlyError(null, '请上传符合要求的文件：CSV格式，单个文件不超过30M')
        return;
      }

      // 自动上传新添加的文件
      if (file.status === 'ready') {
        // 先设置文件列表以显示选择的文件，但这会在上传失败时被重置
        this.qaFileList = fileList.slice(0, 30);
        this.uploadSingleQAFile(file.raw);
      }
    },

    handleQAFileRemove(file, fileList) {
      this.qaFileList = fileList
      // 移除文件时，如果没有成功上传的文档，重置hasFiles状态
      if (this.qaFileList.length === 0 && this.allDocuments.length === 0) {
        this.hasFiles = false;
        this.previewData = null;
        this.activeDocumentIndex = -1;
      }
    },

    async uploadQAFiles() {
      if (this.qaFileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true
      this.uploadResults = []

      try {
        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < this.qaFileList.length; i++) {
          const file = this.qaFileList[i].raw
          try {
            await this.uploadSingleQAFile(file);
            successCount++;
          } catch (error) {
            console.error(`问答文件 ${file.name} 预览失败:`, error);
            failCount++;
          }
        }

        if (successCount > 0 && failCount === 0) {
          this.$message.success(`${successCount}个问答文件预览成功`);
        } else if (successCount > 0 && failCount > 0) {
          this.$message.warning(`${successCount}个文件预览成功，${failCount}个预览失败`);
        } else {
          this.$showFriendlyError(null, '所有问答文件预览失败');
        }

        this.hasFiles = this.allDocuments.length > 0;
      } catch (error) {
        console.error('问答文件批量预览失败:', error)
        this.$showFriendlyError(null, '文件预览失败，请重试')
      } finally {
        this.uploading = false
      }
    },

    handleTableFileChange(file, fileList) {
      console.log('Selected table file:', file)

      // 验证文件类型和大小
      const validTypes = ['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
      const fileExtensions = ['.csv', '.xlsx']

      const isValidType = validTypes.includes(file.raw.type) ||
                         fileExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
      const isValidSize = file.size / 1024 / 1024 <= 30 // 30MB

      if (!isValidType || !isValidSize) {
        this.$showFriendlyError(null, '请上传符合要求的文件：CSV或XLSX格式，单个文件不超过30M')
        return;
      }

      // 自动上传新添加的文件 - 只有文件验证通过且不在上传中时才上传
      if (file.status === 'ready' && !this.uploading) {
        // 先临时设置文件列表以显示选择的文件，但这会在上传失败时被重置
        this.tableFileList = fileList.slice(0, 1);
        this.uploadSingleTableFile(file.raw);
      }
    },

    handleTableFileRemove(file, fileList) {
      this.tableFileList = fileList
      // 移除文件时，如果没有成功上传的文档，重置hasFiles状态
      if (this.tableFileList.length === 0 && this.allDocuments.length === 0) {
        this.hasFiles = false;
        this.previewData = null;
        this.activeDocumentIndex = -1;
      }
    },

    async uploadTableFiles() {
      if (this.tableFileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true
      this.uploadResults = []

      try {
        const file = this.tableFileList[0].raw
        await this.uploadSingleTableFile(file);
        this.hasFiles = this.allDocuments.length > 0;
      } catch (error) {
        console.error('表格文件预览失败:', error)
        this.$showFriendlyError(null, '文件预览失败，请重试')
      } finally {
        this.uploading = false
      }
    },
    async handleWebImport() {
      if (!this.webUrl) {
        this.$message.warning('请输入网页地址')
        return
      }

      try {
        // 处理URL列表
        const urls = this.webUrl.split('\n').filter(url => url.trim().startsWith('http'))
        if (urls.length === 0) {
          this.$message.warning('请输入有效的网页地址')
          return
        }

        // 准备请求参数
        const params = {
          knowledgeBaseId: this.actualKnowledgeBaseId,
          importMethod: this.webImportMethod === 'url' ? 0 : 1, // 0: 独立网址, 1: 网站地图
          urls: urls,
          extractSubpages: this.extractSubPages,
          extractImages: this.extractImages
        }

        // 调用预览接口
        const res = await api.rag.webPreview(params)

        if (res.code === 200) {
          this.$message.success('预览成功')
          // 处理返回的数据
          if (res.data) {
            // 创建网页文档数据结构
            const webData = {
              document_id: res.data.fileId,
              filename: res.data.fileName,
              document_type: 'web',
              chunks: res.data.segments ? res.data.segments.map((segment, index) => ({
                text: segment.text,
                sequence: index
              })) : [],
              total_chunks: res.data.splitCount,
              index: this.allDocuments.length
            }

            // 添加到文档列表
            this.allDocuments.push(webData)

            // 更新当前预览的文档
            this.activeDocumentIndex = webData.index
            this.previewData = webData
            this.activeChunkTab = '0'

            // 标记为已有文件
            this.hasFiles = true

            // 保存当前类型的状态
            this.saveCurrentTypeState()
          }
        } else {
          this.$showFriendlyError(null, res.message || '预览失败')
        }
      } catch (error) {
        console.error('网页导入失败:', error)
        this.$showFriendlyError(null, '网页导入失败')
      }
    },
    downloadTemplate() {
      const templateUrl = 'https://yuanzhiqi-test.obs.cn-southwest-2.myhuaweicloud.com/fileDemo/qa_template.csv';
      const link = document.createElement('a');
      link.href = templateUrl;
      link.download = 'qa_template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    // 重置上传状态，恢复上传界面
    resetUploadState() {
      // 根据当前导入类型清空对应的文件列表
      if (this.importType === 'doc') {
        this.fileList = [];
      } else if (this.importType === 'qa') {
        this.qaFileList = [];
      } else if (this.importType === 'table') {
        this.tableFileList = [];
      }

      // 重置文件状态
      this.hasFiles = false;
      // 清空预览数据
      this.previewData = null;
      this.activeDocumentIndex = -1;
      this.activeChunkTab = '0';
      // 清空所有文档数据
      this.allDocuments = [];
      // 保存当前类型的状态
      this.saveCurrentTypeState();
    },
    // 添加新方法来上传单个文件
    async uploadSingleFile(file) {
      if (!file) return;

      // 设置正在上传状态
      this.uploading = true;
      try {
        const result = await this.uploadFile(file);

        // 检查返回结果中是否包含文档预览结果
        if (result && result.ragResult && (result.ragResult.isSuccess || result.ragResult.code === 200)) {
          const fileData = result.ragResult.data.items && result.ragResult.data.items[0];
          const documentId = fileData && fileData.fileId ? fileData.fileId : (result.ragResult.data.document_id || '');
          const totalChunks = fileData && fileData.totalSegments ? fileData.totalSegments : (result.ragResult.data.total_chunks || 0);
          this.$message.success(`文件 ${file.name} 上传成功，已生成文档ID: ${documentId}，分割为${totalChunks}个片段`);
        } else {
          this.$message.warning(`文件 ${file.name} 已上传，但内容处理可能有问题，请检查`);
        }
      } catch (error) {
        console.error('文件上传失败:', error);
                  this.$showFriendlyError(error, `文件 ${file.name} 上传失败，请重试`);
      } finally {
        this.uploading = false;
      }
    },
    handleDocumentClick(row) {
      this.activeDocumentIndex = row.index;
      this.previewData = row;
      this.activeChunkTab = '0';
    },
    previewDocument(document) {
      this.activeDocumentIndex = document.index;
      this.previewData = document;
      this.activeChunkTab = '0';
    },
    deleteDocument(index) {
      this.allDocuments.splice(index, 1);

      if (this.activeDocumentIndex === index) {
        this.activeDocumentIndex = -1;
        this.previewData = null;
        this.activeChunkTab = '0';
      } else if (this.activeDocumentIndex > index) {
        // 如果当前活跃文档在删除的文档后面，需要调整其索引
        this.activeDocumentIndex--;
      }

      // 删除后重新为所有文档分配索引
      this.allDocuments.forEach((doc, idx) => {
        doc.index = idx;
      });

      // 如果当前已无文档，重置hasFiles
      if (this.allDocuments.length === 0) {
        this.hasFiles = false;
      }

      // 更新当前类型的状态
      this.saveCurrentTypeState();
    },
    getFileIconClass(documentType) {
      const iconMap = {
        doc: 'el-icon-document',
        qa: 'el-icon-chat-dot-round',
        table: 'el-icon-tickets',
        web: 'el-icon-link'
      }
      return iconMap[documentType] || 'el-icon-document';
    },
    tableRowClassName({ row }) {
      return row.index === this.activeDocumentIndex ? 'active-row' : '';
    },
    handleCancel() {
      // 确认对话框
      this.$confirm('确定要取消导入吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置当前导入类型的相关状态
        this.allDocuments = [];
        this.previewData = null;
        this.activeDocumentIndex = -1;
        this.activeChunkTab = '0';
        this.hasFiles = false;

        // 重置各个导入类型的文件列表
        this.fileList = [];
        this.qaFileList = [];
        this.tableFileList = [];

        // 重置存储的文件列表
        this.docFileList = [];
        this.qaStoredFileList = [];
        this.tableStoredFileList = [];

        // 重置上传状态
        this.uploading = false;
        this.uploadResults = [];

        // 更新当前类型的状态
        this.saveCurrentTypeState();

        this.$message({
          type: 'info',
          message: '已取消导入'
        });
      }).catch(() => {
        // 取消操作，不做任何事
      });
    },

    handleImport() {
      // 确认导入操作
      if (!this.hasFiles || !this.allDocuments.length) {
        this.$message.warning('请先上传文件并处理成功后再导入');
        return;
      }

      this.$confirm('确定要导入这些文档到知识库吗?', '确认导入', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        // 显示加载中
        const loading = this.$loading({
          lock: true,
          text: '正在确认导入...',
          spinner: 'el-icon-loading',
          background: 'rgba(255, 255, 255, 0.7)'
        });

        try {
          // 获取所有待导入文档的ID
          const fileIds = this.allDocuments.map(doc => doc.document_id || doc.file_id);
          let result;

          // 根据不同的导入类型，使用不同的确认接口
          if (this.importType === 'table') {
            console.log('使用表格专用接口确认导入');
            // 调用表格确认导入接口
            result = await api.rag.tableConfirm({
              knowledgeBaseId: this.actualKnowledgeBaseId,
              fileIds: fileIds
            });
          } else if (this.importType === 'qa') {
            console.log('使用问答专用接口确认导入');
            // 调用问答确认导入接口
            result = await api.rag.qaConfirm({
              knowledgeBaseId: this.actualKnowledgeBaseId,
              fileIds: fileIds
            });
          } else {
            // 其他类型使用通用确认导入接口
            result = await api.rag.confirm({
              knowledgeBaseId: this.actualKnowledgeBaseId,
              fileIds: fileIds
            });
          }

          // 检查是否有失败的文件
          if (result && result.code === 200) {
            // 即使有失败的文件，code仍然可能是200，所以需要检查message中的失败数量
            this.$message({
                type: 'success',
                message: result.message || `成功导入到知识库`
              })
            // 导入成功后获取知识库容量信息
            this.getCapacityInfo();
            // 导入成功后重置状态
            this.allDocuments = [];
            this.previewData = null;
            this.activeDocumentIndex = -1;
            this.activeChunkTab = '0';
            this.hasFiles = false;

            // 清空当前导入类型的文件列表
            if (this.importType === 'doc') {
              this.fileList = [];
            } else if (this.importType === 'qa') {
              this.qaFileList = [];
            } else if (this.importType === 'table') {
              this.tableFileList = [];
            } else if (this.importType === 'web') {
              this.webUrl = ''; // 清空网页URL输入
            }

            // 更新当前类型的状态
            this.saveCurrentTypeState();
          }
        } catch (error) {
          console.error('确认导入失败:', error);
        } finally {
          // 确保无论是成功还是失败，都关闭loading状态
          loading.close();
        }
      }).catch(() => {
        // 用户取消导入，不执行任何操作
      });
    },
    goBack() {
      // 返回知识库设置页面
      this.$router.push(`/create/knowledge/${this.actualKnowledgeBaseId}/setting`);
    },
    // 添加单个问答文件预览方法
    async uploadSingleQAFile(file) {
      if (!file) return;

      // 设置正在上传状态
      this.uploading = true;
      try {
        // 创建FormData对象
        const formData = new FormData();

        // 添加API所需参数
        formData.append('knowledgeBaseId', this.actualKnowledgeBaseId);
        formData.append('files', file);

        // 调用问答预览接口
        console.log('正在调用问答预览接口...');
        const qaResult = await api.rag.qaPreview(formData);
        console.log('问答预览接口返回结果:', qaResult);

        // 处理预览接口返回结果
        if (qaResult && (qaResult.isSuccess || qaResult.code === 200)) {
          const responseData = qaResult.data;
          // 获取第一个文件的信息
          const fileData = responseData.items[0];
          console.log('问答预览接口调用成功，文档ID:', fileData.fileId);

          if (!fileData.isValidFormat) {
            this.$showFriendlyError(null, `文件 ${file.name} 格式错误，请确保CSV文件至少有两列（问题和答案）`);
            // 格式错误时重置状态，恢复上传界面
            this.resetUploadState();
            this.uploading = false;
            return;
          }

          // 创建问答文档数据结构
          const qaData = {
            document_id: fileData.fileId,
            filename: file.name,
            document_type: 'qa',
            // 将 qaPairs 转换为 chunks 格式，保持兼容性
            chunks: fileData.qaPairs.map((pair, index) => ({
              text: `问：${pair.question}\n答：${pair.answer}`,
              sequence: pair.sort || index
            })),
            // 保存原始的问答，便于可能的后续处理
            qa_pairs: fileData.qaPairs,
            total_chunks: fileData.totalPairs,
            // 添加索引以便在表格中标识
            index: this.allDocuments.length
          };

          // 添加到文档列表
          this.allDocuments.push(qaData);

          // 更新当前预览的文档
          this.activeDocumentIndex = qaData.index;
          this.previewData = qaData;
          this.activeChunkTab = '0'; // 默认选中第一个分段

          // 标记为已有文件
          this.hasFiles = true;

          // 保存当前类型的状态
          this.saveCurrentTypeState();

          this.$message.success(`文件 ${file.name} 预览成功，共有 ${fileData.totalPairs} 个问答`);
        } else {
          console.error('问答预览接口返回异常:', qaResult);
          this.$showFriendlyError(null, qaResult?.message || `文件 ${file.name} 预览失败`);
          // 接口失败时重置状态，恢复上传界面
          this.resetUploadState();
        }
      } catch (error) {
        console.error('问答文件预览错误:', error);
        this.$showFriendlyError(error, `文件 ${file.name} 预览失败，请重试`);
        // 接口失败时重置状态，恢复上传界面
        this.resetUploadState();
      } finally {
        this.uploading = false;
      }
    },
    // 添加单个表格文件预览方法
    async uploadSingleTableFile(file) {
      if (!file) return;

      // 设置正在上传状态
      this.uploading = true;
      try {
        // 创建FormData对象
        const formData = new FormData();

        // 添加API所需参数
        formData.append('knowledgeBaseId', this.actualKnowledgeBaseId);
        formData.append('file', file);

        // 调用表格预览接口
        console.log('正在调用表格预览接口...');
        const tableResult = await api.rag.tablePreview(formData);
        console.log('表格预览接口返回结果:', tableResult);

        // 处理预览接口返回结果
        if (tableResult && (tableResult.isSuccess || tableResult.code === 200)) {
          const fileData = tableResult.data;
          console.log('表格预览接口调用成功，文档ID:', fileData.fileId);
          console.log('表格列信息:', fileData.columns);
          console.log('表格预览行数据:', fileData.previewRows);

          // 创建表格文档数据结构
          const tableData = {
            document_id: fileData.fileId,
            filename: fileData.fileName,
            document_type: 'table',
            // 保留一个空的chunks数组，确保兼容性
            chunks: [],
            // 保存列信息
            columns: fileData.columns,
            // 保存原始预览行数据
            preview_rows: fileData.previewRows,
            total_chunks: fileData.rowCount || fileData.splitCount || 0,
            // 添加索引以便在表格中标识
            index: this.allDocuments.length
          };

          // 添加到文档列表
          this.allDocuments.push(tableData);

          // 更新当前预览的文档
          this.activeDocumentIndex = tableData.index;
          this.previewData = tableData;
          console.log('设置表格预览数据:', this.previewData);
          // 表格类型不使用tabs，所以不需要设置activeChunkTab

          // 标记为已有文件
          this.hasFiles = true;

          // 保存当前类型的状态
          this.saveCurrentTypeState();

          this.$message.success(`表格文件 ${fileData.fileName} 预览成功，共有 ${tableData.total_chunks} 行数据`);
        } else {
          console.error('表格预览接口返回异常:', tableResult);
          this.$showFriendlyError(null, tableResult?.message || `文件 ${file.name} 预览失败`);
          // 接口失败时重置状态，恢复上传界面
          this.resetUploadState();
        }
      } catch (error) {
        console.error('表格文件预览错误:', error);
        this.$showFriendlyError(error, `文件 ${file.name} 预览失败，请重试`);
        // 接口失败时重置状态，恢复上传界面
        this.resetUploadState();
      } finally {
        this.uploading = false;
      }
    },
    // 格式化文件大小 - 确保小于1KB的显示为1KB
    formatFileSize(size) {
      if (!size && size !== 0) return '-';

      const kbSize = Math.round(size / 1024);
      return kbSize < 1 ? 1 : kbSize; // 小于1KB的显示为1KB
    },
    // 获取知识库容量信息
    async getCapacityInfo() {
      try {
        const res = await api.rag.getCapacity(this.actualKnowledgeBaseId);
        if (res.isSuccess && res.data) {
          this.capacityInfo = res.data;
        }
      } catch (error) {
        console.error('获取知识库容量信息失败:', error);
      }
    },
    isHtml(text) {
      // 判断文本是否包含HTML标签
      if (!text) return false;
      // 更全面地检测HTML表格和其他HTML元素
      const htmlPatterns = ['<table', '<div', '<p', '<ul', '<ol', '<tr', '<td', '<th', '<thead', '<tbody'];
      return htmlPatterns.some(pattern => text.includes(pattern));
    },

    isMarkdown(text) {
      if (!text) return false;
      // 检测常见的Markdown标记
      const markdownPatterns = [
        /^#\s+.+$/m,           // 标题
        /\*\*.+\*\*/,           // 粗体
        /\*.+\*/,               // 斜体
        /\[.+\]\(.+\)/,         // 链接
        /```[\s\S]*?```/,       // 代码块
        /^\s*[-*+]\s+.+$/m,     // 无序列表
        /^\s*\d+\.\s+.+$/m,     // 有序列表
        /^\s*>.+$/m,            // 引用
        /\|.+\|.+\|/            // 表格
      ];
      return markdownPatterns.some(pattern => pattern.test(text));
    },

    isMarkdownOrHtml(text) {
      return this.isMarkdown(text) || this.isHtml(text);
    },

    // 添加一个辅助方法，确保formatOutputContent被识别为已使用
    formatMarkdown(text) {
      return formatOutputContent(text);
    },
  }
}
</script>

<style lang="scss" scoped>
.file-import {
  background: #f5f7fa;
  height:100%;
  display: flex;
  flex-direction: column;

  box-sizing: border-box;
  position: relative;

  .back-bar {
    padding: 12px 0;
    background: #fff;
    margin-bottom: 16px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .import-type-cards {
    background: #fff;
    border-radius: 8px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .main-content {
    display: flex;

    flex: 1;
    min-height: calc(100vh - 285px); /* 确保主内容区域占满剩余空间 */

    .operation-area {
      flex: 1;
      min-width: 0;
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh );
      overflow-y: auto;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }

    .preview-container {
      width: 50%;
      background: #fff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh );
      overflow-y: hidden;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

      &.full-width {
        width: 100%;
      }
    }
  }

  .footer-area {
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-top: 1px solid #ebeef5;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;

    .footer-container {
      max-width: 100%;
      margin: 0 auto;
      padding: 12px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .info-items {
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
    column-gap: 32px;

    .info-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 14px;
        color: #909399;
      }

      .value {
        font-size: 14px;
        color: #333;
        margin-left: 4px;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }

  .preview-area {
    display: flex;
    flex-direction: column;
    height: 100%;

    .preview-content {
      padding: 20px;
      display: flex;
      flex-direction: column;
    }

    .preview-header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 15px;
      margin-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      .preview-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: var(--el-color-primary);
        }

        .preview-subtitle {
          margin-left: 10px;
          font-size: 14px;
          color: #606266;
          font-weight: normal;
        }
      }

      .preview-info {
        font-size: 14px;
        color: #606266;

        .info-separator {
          margin: 0 8px;
          color: #dcdfe6;
        }

        .info-id {
          color: #909399;
          font-size: 13px;
        }
      }
    }

    .preview-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #e0e0e0;

      :deep(.el-tabs__content) {
        flex: 1;
        padding: 20px 0;

        .el-tab-pane {
          height: 100%;
        }
      }
    }

    .chunk-content {
      background: #fff;
      padding: 16px;
      height: 100%;
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      max-height: calc(100vh - 430px);
      overflow-y: auto;

      pre {
        white-space: pre-wrap;       /* 保留空格，但文本换行 */
        word-wrap: break-word;       /* 允许长单词换行 */
        overflow-wrap: break-word;   /* 现代浏览器的属性 */
        word-break: break-all;       /* 在任意字符间断行 */
      }
    }
  }
}

.type-card {
  position: relative;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  padding: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.08);
  }

  &.active {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }

  .type-icon {
    width: 44px;
    height: 44px;
    margin-right: 16px;
    object-fit: contain;
  }

  .card-content {
    flex: 1;

    .title {
      font-weight: 500;
      font-size: 16px;
      color: #333;
      margin-bottom: 4px;
    }

    .desc {
      font-size: 14px;
      color: #909399;
    }
  }
}

.setting-item {
  margin-bottom: 24px;

  .label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;

    .required {
      color: #f56c6c;
      margin-left: 4px;
      font-size: 14px;
    }

    .el-icon-question {
      margin-left: 4px;
      color: #909399;
      cursor: help;
      font-size: 14px;
    }
  }

  .content {
    :deep(.el-radio-group) {
      .el-radio {
        margin-right: 24px;
        .el-radio__label {
          color: #333;
          font-size: 14px;
        }
      }
    }
    :deep(.el-upload) {
      width: 100%;
    }
    /* 添加对上传区域的深度选择器样式覆盖 */
    :deep(.el-upload-dragger) {
      padding: 8px 0;
      min-height: 60px !important;
      height: auto !important;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    :deep(.el-icon-upload) {
      font-size: 20px !important;
      margin: 0 0 1px 0 !important;
    }

    :deep(.el-upload__text) {
      font-size: 13px;
      line-height: 1.4;
    }

    .file-tips {
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      padding: 12px;
      border-radius: 4px;
      margin-bottom: 16px;
      font-size: 12px;
      color: #909399;

      i {
        margin-right: 8px;
        color: var(--el-color-primary);
        font-size: 14px;
      }
    }
  }
}

/* 添加导入方式横向布局样式 */
.setting-item.import-method {
  display: flex;
  align-items: center;

  .label {
    margin-bottom: 0;
    margin-right: 16px;
    white-space: nowrap;
  }

  .content {
    flex: 1;
  }
}

.upload-area {
  .file-tips {
    display: flex;
    align-items: center;
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 12px;
    color: #909399;

    i {
      margin-right: 8px;
      color: var(--el-color-primary);
      font-size: 14px;
    }
  }

  .file-uploader {
    width: 100%;

    .upload-trigger {
      padding: 8px 0;
      background: #fff;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 60px;
      width: 100%;

      &:hover {
        border-color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }

      i {
        font-size: 20px;
        color: #909399;
        margin-bottom: 1px;
      }

      .upload-text {
        color: #606266;
        font-size: 13px;
        text-align: center;

        em {
          color: var(--el-color-primary);
          font-style: normal;
          margin-left: 4px;
          cursor: pointer;
        }
      }
    }
  }
}

.csv-template {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 13px;
  color: #606266;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.web-input {
  .url-tips {
    font-size: 13px;
    color: #909399;
    margin-bottom: 8px;
  }

  .web-options {
    margin: 16px 0;
    display: flex;
    gap: 24px;

    .option-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #606266;

      .el-icon-question {
        color: #909399;
        cursor: help;
      }

      .option-switch {
        margin-left: 8px;
      }
    }
  }
}

.document-list {
  margin-top: 24px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;

  .list-header {
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;

    .list-title {
      font-size: 14px;
      color: #333;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: var(--el-color-primary);
        font-size: 16px;
      }
    }
  }

  :deep(.el-table) {
    .el-table__header {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
        font-size: 13px;
      }
    }

    .el-table__row {
      td {
        padding: 8px 0;
        font-size: 13px;
        color: #606266;
      }
    }

    .active-row {
      background-color: var(--el-color-primary-light-9);
    }

    .el-button--text {
      padding: 0 8px;
      font-size: 13px;
    }

    .danger-text {
      color: #f56c6c;
    }
  }

  .filename-cell {
    display: flex;
    align-items: center;
    font-size: 13px;

    i {
      margin-right: 8px;
      font-size: 14px;
      color: var(--el-color-primary);
    }
  }
}

.import-settings {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-tips {
  .capacity-info {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }
}

/* 表格预览样式 */
.table-preview-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  padding: 12px;
  max-height: calc(100vh - 430px);
  overflow-y: auto;
  flex: 1;
}

.table-preview {
  width: 100%;
  overflow-x: auto;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ddd;
  font-size: 14px;
}

.preview-table th {
  background-color: #f7f7f7;
  font-weight: 500;
  color: #333;
  border: 1px solid #e8e8e8;
  padding: 12px 16px;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 1;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-table td {
  border: 1px solid #e8e8e8;
  padding: 12px 16px;
  word-break: break-word;
  max-width: 250px;
}

.preview-table td .cell-content {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-table tr:first-child {
  background-color: #efefef;
}

.preview-table tr:nth-child(even) {
  background-color: #fafafa;
}

.preview-table tr:hover {
  background-color: #f0f7ff;
}

.qa-preview-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.qa-preview {
  background: #f5f7fa !important;
  padding: 12px !important;
  max-height: calc(100vh - 430px);
  overflow-y: auto;
  flex: 1;
}

.qa-segment {
  margin-bottom: 16px;
}

.qa-segment-header {
  padding: 8px 16px;
  background: #ebeef5;
  font-weight: 500;
  color: #333;
  border-radius: 4px 4px 0 0;
  font-size: 14px;
}

.qa-item {
  padding: 0;
  border: 1px solid #ebeef5;
  border-radius: 0 0 4px 4px;
  background: #fff;
}

.qa-question, .qa-answer {
  padding: 16px;
}

.qa-question {
  background: #f6f9fc;
  border-bottom: 1px solid #ebeef5;
}

.qa-label {
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.qa-text {
  display: block;
  width: 100%;
}
</style>

<!-- 添加全局样式 -->
<style>
.html-content table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 16px 0 !important;
  border: 1px solid #ddd !important;
  border-radius: 2px !important;
  font-size: 14px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  table-layout: fixed !important;
}

.html-content th,
.html-content td {
  border: 1px solid #e8e8e8 !important;
  padding: 12px 16px !important;
  text-align: left !important;
  min-width: 80px !important;
  word-break: break-word !important;
  font-size: 14px !important;
}

.html-content th {
  background-color: #f7f7f7 !important;
  font-weight: 500 !important;
  color: #333 !important;
  border-bottom: 2px solid #ddd !important;
  position: relative !important;
  white-space: normal !important;
}

.html-content tr {
  transition: background 0.2s ease-in-out !important;
  min-width: 80px !important;
}

.html-content tr:first-child {
  background-color: #efefef!important;
}

.html-content tr:nth-child(even) {
  background-color: #fafafa !important;
}

.html-content tr:hover {
  background-color: #f0f7ff !important;
}

/* 解决职级等表格标题显示问题 */
.html-content td blockquote {
  margin: 0 !important;
  padding: 0 !important;
  border-left: none !important;
  font-weight: bold !important;
}

.html-content td blockquote p {
  margin: 0 !important;
  padding: 0 !important;
  font-weight: bold !important;
}

.html-content blockquote {
  border-left: 4px solid #e0e0e0 !important;
  margin: 8px 0 !important;
  padding: 0 8px !important;
  color: #606266 !important;
  background-color: #f9f9f9 !important;
}
</style>

