<template>
  <div class="knowledge-config">
    <el-form :model="configForm" label-width="120px" class="config-form">
      <el-form-item label="知识库名称:" required>
        <el-input
          v-model="configForm.name"
          placeholder="商城【客户】知识库"
          class="form-input"
        ></el-input>
      </el-form-item>

      <el-form-item label="知识库Code:" required>
        <el-input
          v-model="configForm.code"
          placeholder="UHFirptw"
          class="form-input"
          disabled
        ></el-input>
      </el-form-item>

      <el-form-item label="知识库描述:">
        <el-input
          v-model="configForm.description"
          type="textarea"
          :rows="6"
          placeholder="请输入知识库描述"
          class="form-input"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <div class="button-group">
          <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
          <el-button @click="handleDelete">删除</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
// 引入 api
import { api } from '@/api/request'
import EventBus from '@/utils/eventBus'

export default {
  name: 'KnowledgeConfig',
  props: {
    knowledgeInfo: {
      type: Object,
      default: () => ({
        id: '',
        name: '',
        code: '',
        description: '',
        createTime: '',
        updateTime: ''
      })
    }
  },
  data() {
    return {
      configForm: {
        id: '',
        name: '',
        code: '',
        description: ''
      },
      loading: false
    }
  },
  watch: {
    knowledgeInfo: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.updateFormData(newVal)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    updateFormData(data) {
      // 更新表单数据
      this.configForm = {
        id: data.id || '',
        name: data.name || '',
        code: data.code || '',
        description: data.description || ''
      }
    },
    async handleSave() {
      if (!this.configForm.name.trim()) {
        this.$message.warning('请输入知识库名称')
        return
      }

      if (!this.configForm.id) {
        this.$message.warning('知识库ID不能为空')
        return
      }

      this.loading = true
      try {
        // 保存知识库配置
        const params = {
          name: this.configForm.name,
          description: this.configForm.description
        }

        // 调用更新知识库API
        const res = await api.rag.updateKnowledge(this.configForm.id, params)
        if (res.isSuccess || res.code === 200) {
          this.$message.success('知识库更新成功')
          // 通知父组件刷新数据
          this.$emit('refresh')
          // 触发事件总线，通知列表页面刷新数据
          EventBus.$emit('refresh-knowledge-list')
          // 跳转到知识库列表页面
          setTimeout(() => {
            this.$router.push('/create/knowledge')
          }, 500)
        } else {
          this.$showFriendlyError(null, res.message || '更新知识库失败')
        }
      } catch (err) {
        this.$showFriendlyError(null, '更新知识库失败：' + (err.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    handleDelete() {
      if (!this.configForm.id) {
        this.$message.warning('知识库ID不能为空')
        return
      }

      this.$confirm('确认删除该知识库吗？删除后数据无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        try {
          // 调用删除知识库API
          const res = await api.rag.deleteKnowledge(this.configForm.id)

          if (res.isSuccess || res.code === 200) {
            this.$message.success('删除成功')
          } else {
            this.$showFriendlyError(null, res.message || '删除知识库失败')
            return
          }
          // 触发事件总线，通知列表页面刷新数据
          EventBus.$emit('refresh-knowledge-list')
          // 跳转到知识库列表页面
          setTimeout(() => {
            this.$router.push('/create/knowledge')
          }, 500)
        } catch (err) {
          this.$showFriendlyError(null, '删除知识库失败：' + (err.message || '未知错误'))
        } finally {
          this.loading = false
        }
      }).catch(() => {
        // 用户取消删除操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.knowledge-config {
  padding: 20px;

  .config-form {
    max-width: 800px;
    margin: 0 auto;

    .form-input {
      width: 100%;
    }

    .button-group {
      display: flex;
      gap: 16px;
    }

    ::v-deep .el-form-item__label {
      font-weight: normal;
      color: #606266;
    }

    ::v-deep .el-input.is-disabled .el-input__inner {
      color: #606266;
      background-color: #f5f7fa;
    }
  }
}
</style>
