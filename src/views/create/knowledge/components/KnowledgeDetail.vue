<template>
  <div class="knowledge-detail">
    <div class="page-header">
      <div class="header-left">
        <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        <h2>{{ knowledgeData.name }}</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <div class="page-content">
      <el-card class="info-card">
        <div class="info-header">
          <h3>基本信息</h3>
        </div>
        <div class="info-content">
          <div class="info-item">
            <span class="label">知识库名称：</span>
            <span class="value">{{ knowledgeData.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">描述：</span>
            <span class="value">{{ knowledgeData.description }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ knowledgeData.createTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后更新：</span>
            <span class="value">{{ knowledgeData.updateTime }}</span>
          </div>
        </div>
      </el-card>

      <el-card class="content-card">
        <div class="content-header">
          <h3>知识库内容</h3>
          <el-button type="primary" plain icon="el-icon-plus">添加内容</el-button>
        </div>
        <div class="content-list">
          <!-- 这里可以根据实际需求添加知识库内容列表 -->
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KnowledgeDetail',
  data() {
    return {
      knowledgeData: {
        id: null,
        name: '',
        description: '',
        createTime: '',
        updateTime: ''
      }
    }
  },
  created() {
    // 从路由参数获取知识库ID
    const knowledgeId = this.$route.params.id
    // TODO: 根据ID获取知识库详情
    this.getKnowledgeDetail(knowledgeId)
  },
  methods: {
    goBack() {
      this.$router.push('/create/knowledge')
    },
    handleEdit() {
      this.$router.push(`/create/knowledge/${this.knowledgeData.id}/edit`)
    },
    getKnowledgeDetail(id) {
      // TODO: 调用API获取知识库详情
      // 这里暂时使用模拟数据
      this.knowledgeData = {
        id,
        name: '商城【客服】知识库',
        description: '用于处理商城订单、售后、发票、物流相关问题',
        createTime: '2024-03-21 16:43',
        updateTime: '2024-03-21 16:43'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.knowledge-detail {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }

  .info-card {
    margin-bottom: 24px;

    .info-header {
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .info-content {
      .info-item {
        display: flex;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          width: 100px;
          color: #6b7280;
        }

        .value {
          color: #1f2937;
        }
      }
    }
  }

  .content-card {
    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .content-list {
      min-height: 200px;
    }
  }
}
</style>
