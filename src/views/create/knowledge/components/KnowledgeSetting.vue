<template>
  <div class="knowledge-setting">
    <!-- 顶部标题栏 -->
    <div class="setting-header">
      <div class="header-left">
        <el-button
          icon="el-icon-back"
          type="text"
          @click="handleBack"
          class="back-btn"
        >
          知识库配置
        </el-button>
      </div>
    </div>

    <!-- 顶部菜单 -->
    <div class="setting-menu">
      <el-menu
        :default-active="activeMenu"
        class="setting-menu-list"
        mode="horizontal"
        @select="handleMenuSelect"
      >
        <el-menu-item index="file">
          <span>文件管理</span>
        </el-menu-item>
        <el-menu-item index="import">
          <span>文件导入</span>
        </el-menu-item>
        <el-menu-item index="material">
          <span>素材管理</span>
        </el-menu-item>
        <el-menu-item index="test">
          <span>检索测试</span>
        </el-menu-item>
        <el-menu-item index="config">
          <span>配置</span>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 内容区域 -->
    <div class="setting-content">
      <file-management
        v-if="activeMenu === 'file'"
        :knowledge-base-id="knowledgeBaseId"
        @switch-menu="handleMenuSelect"
      />
      <file-import
        v-if="activeMenu === 'import'"
        :knowledge-base-id="knowledgeBaseId"
      />
      <material-management
        v-if="activeMenu === 'material'"
        :knowledge-base-id="knowledgeBaseId"
      />
      <search-test
        v-if="activeMenu === 'test'"
        :knowledge-base-id="knowledgeBaseId"
        @view-file="handleViewFile"
      />
      <knowledge-config
        v-if="activeMenu === 'config'"
        :knowledge-info="knowledgeInfo"
        @refresh="fetchKnowledgeInfo"
      />
    </div>
  </div>
</template>

<script>
import FileManagement from "./FileManagement.vue";
import FileImport from "./FileImport.vue";
import MaterialManagement from "./MaterialManagement.vue";
import SearchTest from "./SearchTest.vue";
import KnowledgeConfig from "./KnowledgeConfig.vue";
import { api } from "@/api/request";

export default {
  name: "KnowledgeSetting",
  components: {
    FileManagement,
    FileImport,
    MaterialManagement,
    SearchTest,
    KnowledgeConfig,
  },
  data() {
    return {
      activeMenu: "file",
      knowledgeBaseId: "",
      knowledgeInfo: {
        id: "",
        name: "",
        code: "",
        description: "",
        createTime: "",
        updateTime: "",
      },
      loading: false,
    };
  },
  created() {
    // 从路由参数获取知识库ID
    this.knowledgeBaseId = this.$route.params.id;
    // 获取知识库详情
    this.fetchKnowledgeInfo();
  },
  methods: {
    handleMenuSelect(index) {
      this.activeMenu = index;
    },
    // 处理返回按钮点击
    handleBack() {
      this.$router.push("/create/knowledge");
    },
    // 处理查看文件事件
    handleViewFile(fileId) {
      // 先切换到文件管理页面
      this.activeMenu = "file";
      // 然后通过$nextTick确保DOM已更新
      this.$nextTick(() => {
        // 向FileManagement组件发送查看文件的指令
        const fileManagementComponent = this.$children.find(
          (c) => c.$options.name === "FileManagement"
        );
        if (fileManagementComponent) {
          fileManagementComponent.handleViewById(fileId);
        } else {
          this.$showFriendlyError(null, "无法找到文件管理组件");
        }
      });
    },
    async fetchKnowledgeInfo() {
      if (!this.knowledgeBaseId) return;

      this.loading = true;
      try {
        // 查询参数 - 通过知识库列表API筛选出当前知识库
        const params = {
          keyword: "",
          pageIndex: 0,
          pageSize: 100,
        };

        const res = await api.rag.getKnowledgeList(params);
        if (res.code === 200) {
          // 从列表中找到匹配当前ID的知识库
          const knowledgeItem = (res.data.items || []).find(
            (item) => item.id === this.knowledgeBaseId
          );

          if (knowledgeItem) {
            // 更新知识库信息
            this.knowledgeInfo = {
              id: knowledgeItem.id,
              name: knowledgeItem.name,
              code: knowledgeItem.code,
              description: knowledgeItem.description,
              createTime: knowledgeItem.create_time,
              updateTime: knowledgeItem.update_time,
            };
          } else {
            this.$message.warning("未找到当前知识库信息");
          }
        } else {
          this.$showFriendlyError(null, res.status?.message || "获取知识库信息失败");
        }
      } catch (err) {
        this.$showFriendlyError(null, 
          "获取知识库信息失败：" + (err.message || "未知错误")
        );
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.knowledge-setting {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 55px);
  background: transparent;

  .setting-header {
    padding: 16px 24px;
    background: transparent;

    .header-left {
      .back-btn {
        font-size: 16px;
        font-weight: 500;
        color: #606266;
        padding: 0;

        &:hover {
          color: var(--el-color-primary);
        }

        .el-icon-arrow-left {
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  .setting-menu {
    border-bottom: 1px solid #e4e7ed;
    background: transparent;

    .setting-menu-list {
      border-bottom: none;
      background: transparent;
      .el-menu-item {
        height: 56px;
        line-height: 56px;
        padding: 0 24px;
        border-bottom: none !important;
        font-size: 16px;
        font-weight: 500;
        color: #606266;

        &.is-active {
          color: var(--el-color-primary);
          border-bottom: 2px solid var(--el-color-primary) !important;
          background-color: transparent;
        }

        &:hover {
          color: var(--el-color-primary);
          background-color: transparent;
        }

        span {
          font-weight: inherit;
        }
      }
    }
  }

  .setting-content {
    flex: 1;
    padding: 20px;
    background: transparent;
    overflow-y: auto;
  }
}

// 强制覆盖表格表头背景
:deep(.el-table) {
  background: transparent !important;
}

:deep(.el-table .el-table__header-wrapper) {
  background: transparent !important;
}

:deep(.el-table .el-table__header) {
  background: transparent !important;
}

:deep(.el-table thead) {
  background: transparent !important;
}

:deep(.el-table th) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.el-table th.is-leaf) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.el-table th.gutter) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(.el-table__body-wrapper) {
  background: transparent !important;
}

:deep(.el-table__body) {
  background: transparent !important;
}

// 使用更高特异性的选择器
.knowledge-setting :deep(.el-table) {
  background: transparent !important;
}

.knowledge-setting :deep(.el-table .el-table__header-wrapper) {
  background: transparent !important;
}

.knowledge-setting :deep(.el-table .el-table__header) {
  background: transparent !important;
}

.knowledge-setting :deep(.el-table thead) {
  background: transparent !important;
}

.knowledge-setting :deep(.el-table th) {
  background: transparent !important;
  background-color: transparent !important;
}

.knowledge-setting :deep(.el-table th.is-leaf) {
  background: transparent !important;
  background-color: transparent !important;
}

.knowledge-setting :deep(.el-table th.gutter) {
  background: transparent !important;
  background-color: transparent !important;
}
:deep(.el-icon-back) {
  background: #fff;
  padding: 5px;
  border-radius: 50%;
  border: 1px solid #e1e1e1;
}
</style>
