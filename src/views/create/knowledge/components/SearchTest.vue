<template>
  <div class="search-test">
    <!-- 顶部提示信息 -->
    <!-- <div class="tips-box">
      <div class="tip-item">
        <span>1. 此处展示语义检索与全文检索的结果各10条，实际使用时将根据知识库中增强检索的策略配置叠加两种结果（去重）输出</span>
      </div>
      <div class="tip-item">
        <span>2. 若内容中无匹配的关键词，则全文检索结果可能不足10条；若匹配到的内容有限，则可能不足10条</span>
      </div>
    </div> -->

    <div class="search-layout">
      <!-- 左侧检索设置 -->
      <div class="search-left">
        <div class="section-header">检索方式</div>
        <div class="search-section">
          <el-select v-model="searchType" placeholder="请选择检索方式" class="search-type-select">
            <el-option label="语义检索" value="semantic"></el-option>
            <el-option label="增强检索" value="fulltext"></el-option>
          </el-select>
        </div>

        <div class="section-header">测试文本</div>
        <div class="search-section">
          <el-input
            type="textarea"
            v-model="searchText"
            :rows="6"
            placeholder="请输入需要检索的问题"
            class="search-input"
          ></el-input>
        </div>

        <div class="search-button-container">
          <el-button type="primary" @click="handleSearch" :loading="searching">检索</el-button>
        </div>
      </div>

      <!-- 右侧检索结果 -->
      <div class="search-right">
        <div class="section-header">
          检索结果
          <el-tooltip content="检索结果说明" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>

        <div class="results-container" v-if="hasResults">
          <div v-if="searchResults.length > 0" class="results-grid">
            <div v-for="(result, index) in searchResults" :key="index" class="result-card">
              <div class="result-header">
                <span class="relevance-label">相似度：</span>
                <span class="relevance-value">{{ formatScore(result.score) }}</span>
                <el-button size="mini" type="text" class="view-detail-btn" @click="previewFile(result.fileId)">
                  <i class="el-icon-document"></i> 查看文件
                </el-button>
              </div>
              <div class="result-content">
                <p class="question-text"><strong>内容：</strong>{{ result.text }}</p>
                <p class="file-info"><strong>文件名：</strong>{{ result.fileName }}</p>
              </div>
            </div>
          </div>

          <div v-else class="no-results">
            <el-empty description="未找到匹配的结果"></el-empty>
          </div>
        </div>

        <div v-else class="empty-results">
          <el-empty description="输入问题并点击检索按钮查看结果"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { api } from '@/api/request'

export default {
  name: 'SearchTest',
  props: {
    knowledgeBaseId: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      searchType: 'semantic',
      searchText: '',
      searching: false,
      hasResults: false,
      searchResults: []
    }
  },
  computed: {
    // 检索方法映射
    methodValue: function() {
      if (this.searchType === 'semantic') {
        return 0;
      } else {
        return 1;
      }
    }
  },
  methods: {
    // 格式化分数显示
    formatScore: function(score) {
      if (score === null || score === undefined) return '0.0000';
      return score.toFixed(4);
    },

    handleSearch: function() {
      var self = this;

      if (!self.searchText.trim()) {
        self.$message.warning('请输入检索内容');
        return;
      }

      if (!self.knowledgeBaseId) {
        self.$message.warning('请先选择知识库');
        return;
      }

      self.searching = true;
      self.hasResults = false;
      self.searchResults = [];

      // 准备请求参数
      var params = {
        KnowledgeBaseId: self.knowledgeBaseId,
        Query: self.searchText.trim(),
        Method: self.methodValue, // 0: 语义检索, 1: 增强检索
        MinScore: 0.3, // 最低相关度分数
        K: 10, // 返回10条结果
        FullTextK: self.searchType === 'semantic' ? 0 : 10 // 是否增强检索
      };

      // 调用检索接口
      api.fileService.retrieve(params)
        .then(function(res) {
          self.searching = false;
          self.hasResults = true;

          if (res && res.isSuccess && res.data && res.data.results) {
            self.searchResults = res.data.results;
            if (self.searchResults.length === 0) {
              self.$message.info('未找到相关内容');
            }
          } else {
            var msg = '检索失败';
            if (res && res.message) {
              msg = res.message || '检索失败';
            }
            self.$message.error(msg);
            self.searchResults = [];
          }
        })
        .catch(function(err) {
          self.searching = false;
          self.hasResults = true;
          self.searchResults = [];
          var errMsg = '未知错误';
          if (err && err.message) {
            errMsg = err.message;
          }
          self.$message.error('检索请求失败: ' + errMsg);
        });
    },

    // 文件预览
    previewFile: function(fileId) {
      var self = this;

      if (!fileId) {
        self.$message.warning('文件ID不可用');
        return;
      }

      // 发送查看文件的事件，传递文件ID
      this.$emit('view-file', fileId);
    }
  }
}
</script>

<style lang="scss" scoped>
.search-test {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .tips-box {
    background-color: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 24px;
    flex-shrink: 0;

    .tip-item {
      color: #606266;
      font-size: 14px;
      line-height: 1.8;
      display: flex;
      align-items: flex-start;

      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }

  .search-layout {
    display: flex;
    gap: 20px;
    height: calc(100vh - 180px);
    flex: 1;
    min-height: 0;

    .search-left {
      width: 35%;
      min-width: 300px;
      display: flex;
      flex-direction: column;
      max-height: 100%;
      overflow-y: auto;
    }

    .search-right {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      max-height: 100%;
    }
  }

  .section-header {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    flex-shrink: 0;

    .el-icon-question {
      margin-left: 8px;
      color: #909399;
      cursor: help;
    }
  }

  .search-section {
    margin-bottom: 24px;
    flex-shrink: 0;

    .search-type-select {
      width: 100%;
    }

    .search-input {
      width: 100%;
    }
  }

  .search-button-container {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
    flex-shrink: 0;
  }

  .results-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    flex: 1;
    overflow-y: auto;
    background: #fff;
    min-height: 0;

    .results-grid {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .result-card {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background: #fff;
        padding: 16px;
        transition: box-shadow 0.3s;

        &:hover {
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .result-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .relevance-label {
            color: #606266;
            font-size: 14px;
          }

          .relevance-value {
            color: #409EFF;
            font-weight: 500;
            margin-right: auto;
          }

          .view-detail-btn {
            color: #409EFF;
          }
        }

        .result-content {
          margin-bottom: 12px;

          .question-text, .answer-text, .file-info {
            margin: 0 0 8px;
            font-size: 14px;
            line-height: 1.6;
            color: #303133;
            word-break: break-word;
          }

          .file-info {
            color: #606266;
            font-size: 13px;
          }
        }

        .result-footer {
          display: flex;
          justify-content: flex-end;
          color: #909399;
          font-size: 13px;

          .result-score {
            color: #67c23a;
          }
        }
      }
    }

    .no-results {
      padding: 40px 0;
    }
  }

  .empty-results {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #fff;
    min-height: 200px;
  }
}
</style>
