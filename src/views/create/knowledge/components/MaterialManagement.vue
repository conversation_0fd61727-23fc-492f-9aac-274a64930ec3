<template>
  <div class="material-management">
    <!-- 文件预览组件 -->
    <FilePreview ref="filePreview" />
    <div class="operation-bar">
      <div class="left-actions">
        <el-radio-group v-model="fileType">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="image">图片</el-radio-button>
          <el-radio-button label="video">视频</el-radio-button>
          <el-radio-button label="file">文件</el-radio-button>
        </el-radio-group>
      </div>
      <div class="right-actions">
        <el-button
          icon="el-icon-refresh"
          @click="handleRefresh"
          style="margin-right: 8px"
          >刷新</el-button
        >
        <el-input
          v-model="searchQuery"
          placeholder="请输入文件名搜索"
          prefix-icon="el-icon-search"
          clearable
          style="width: 200px; margin-right: 16px"
        ></el-input>
        <el-button type="primary" icon="el-icon-plus" @click="handleUpload"
          >上传</el-button
        >
      </div>
    </div>

    <!-- 文件列表表格 -->
    <el-table
      :data="fileList"
      style="width: 100%; margin-top: 16px; background: transparent"
      border
      :header-cell-style="{
        background: 'transparent',
        backgroundColor: 'transparent',
      }"
      :header-row-style="{
        background: 'transparent',
        backgroundColor: 'transparent',
      }"
    >
      <el-table-column
        type="index"
        label="序号"
        width="80"
        align="center"
      ></el-table-column>

      <el-table-column prop="name" label="名称" min-width="250">
        <template slot-scope="scope">
          <div class="file-name">
            <i :class="getFileIcon(scope.row.type)"></i>
            <span>{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="type"
        label="类型"
        width="120"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="size"
        label="大小(KB)"
        width="140"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="updateTime"
        label="创建时间"
        width="180"
        align="center"
      ></el-table-column>

      <el-table-column label="操作" width="260" align="center">
        <template slot-scope="scope">
          <el-button
            type="success"
            size="mini"
            @click="handlePreview(scope.row)"
            >预览</el-button
          >
          <el-button type="primary" size="mini" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="primary"
            size="mini"
            plain
            @click="handleCopy(scope.row)"
            >复制链接</el-button
          >
          <el-button
            type="primary"
            size="mini"
            plain
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
      >
      </el-pagination>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog
      :visible.sync="editVisible"
      :close-on-click-modal="false"
      title="编辑文件"
      width="30%"
      center
    >
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="文件名称">
          <el-input
            v-model="editForm.name"
            maxlength="20"
            style="width: 80%"
            placeholder="请输入文件名称"
          ></el-input>
          <span
            v-if="editForm.extension"
            style="margin-left: 5px; color: #909399"
            >{{ editForm.extension }}</span
          >
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit" :loading="editing"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 上传弹窗 -->
    <el-dialog
      :visible.sync="uploadVisible"
      title="上传文件"
      :close-on-click-modal="false"
      width="30%"
      center
    >
      <el-upload
        class="upload-container"
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        :file-list="uploadFileList"
        multiple
        drag
        accept=".txt,.pdf,.md,.docx,.png,.jpg,.jpeg,.gif,.bmp,.webp,.mp4,.mp3,.mov"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击选择多个文件</em></div>
        <div class="el-upload__tip" slot="tip">
          支持同时上传多个图片、视频、音频、文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploading"
          >上传</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";
import FilePreview from "@/components/FilePreview.vue";
import { copyToClipboard } from "@/utils";
// 素材类型枚举
const MaterialType = {
  AUTO: 0, // 全部
  IMAGE: 1, // 图片类型
  FILE: 2, // 文件类型
  VIDEO: 3, // 视频类型
  AUDIO: 4, // 音频类型
};

export default {
  name: "MaterialManagement",
  components: {
    FilePreview
  },
  props: {
    knowledgeBaseId: {
      type: String,
      default: "kb_123", // 默认知识库ID
    },
  },
  data() {
    return {
      fileType: "all",
      searchQuery: "",
      currentPage: 0,
      pageSize: 10,
      total: 0,
      uploadVisible: false,
      uploadFiles: [], // 改为数组存储多个文件
      uploadFileList: [],
      uploading: false,
      fileList: [],
      editVisible: false,
      editing: false,
      editForm: {
        id: "",
        name: "",
        extension: "",
      },
    };
  },
  created() {
    // 组件创建时加载数据
    this.loadData();
  },
  watch: {
    fileType() {
      this.currentPage = 0; // 重置为第一页
      this.loadData();
    },
    searchQuery() {
      this.currentPage = 0; // 重置为第一页
      this.loadData();
    },
  },
  methods: {
    // 加载素材列表数据
    async loadData() {
      try {
        const loading = this.$loading({
          lock: true,
          text: "加载中...",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.7)",
        });

        // 根据fileType转换为接口所需的type参数 (枚举值)
        let typeParam = MaterialType.AUTO; // 默认为全部类型
        if (this.fileType === "image") {
          typeParam = MaterialType.IMAGE;
        } else if (this.fileType === "video") {
          typeParam = MaterialType.VIDEO;
        } else if (this.fileType === "file") {
          typeParam = MaterialType.FILE;
        }

        // 构建查询参数
        const params = {
          knowledgeBaseId: this.knowledgeBaseId,
          keyword: this.searchQuery || "",
          pageIndex: this.currentPage,
          pageSize: this.pageSize,
          type: typeParam, // 使用枚举值
        };

        // 调用列表接口
        const result = await api.material.getList(params);

        loading.close();

        if (result && result.code == 200 && result.data) {
          // 更新数据列表和总数
          this.fileList = result.data.items || [];
          this.total = result.data.total || 0;

          // 处理文件列表，添加类型标识和格式化大小
          this.fileList = this.fileList.map((item) => {
            // 转换类型值为数字类型的枚举值
            let typeValue = item.type;
            // 根据后端返回的字符串类型转换为对应的数字枚举
            if (typeof item.type === "string") {
              const upperType = item.type.toUpperCase();
              if (upperType === "IMAGE") {
                typeValue = MaterialType.IMAGE;
              } else if (upperType === "VIDEO") {
                typeValue = MaterialType.VIDEO;
              } else if (upperType === "FILE") {
                typeValue = MaterialType.FILE;
              } else if (upperType === "AUDIO") {
                typeValue = MaterialType.AUDIO;
              }
            }

            // 获取类型的显示文本
            let typeStr = "文件";
            switch (typeValue) {
              case MaterialType.IMAGE:
                typeStr = "图片";
                break;
              case MaterialType.VIDEO:
                typeStr = "视频";
                break;
              case MaterialType.AUDIO:
                typeStr = "音频";
                break;
              case MaterialType.FILE:
              default:
                typeStr = "文件";
                break;
            }

            return {
              ...item,
              type: typeStr,
              typeValue: typeValue, // 保存原始类型值
              // 将字节大小转换为KB显示
              size: Math.round(item.size / 1024),
            };
          });
        } else {
          this.$message.warning(result?.message || "获取素材列表失败");
        }
      } catch (error) {
        console.error("加载素材列表失败:", error);
        this.$showFriendlyError(null, 
          "加载素材列表失败: " + (error.message || "未知错误")
        );
      }
    },
    getFileIcon(type) {
      const iconMap = {
        图片: "iconfont icon-tuxianghuihua",
        视频: "iconfont icon-yulan",
        音频: "iconfont icon-wendang audio-icon", // 使用文档图标并添加特殊样式类
        文件: "iconfont icon-wendang",
      };
      return iconMap[type] || "iconfont icon-wendang";
    },
    handleUpload() {
      this.uploadVisible = true;
      this.uploadFiles = [];
      this.uploadFileList = [];
    },
    handleCommand(command, row) {
      switch (command) {
        case "edit":
          this.handleEdit(row);
          break;
        case "copy":
          navigator.clipboard.writeText(row.url).then(() => {
            this.$message.success("链接已复制到剪贴板");
          });
          break;
        case "delete":
          this.$confirm("确认删除该文件吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.deleteFile(row.id);
            })
            .catch(() => {});
          break;
      }
    },
    // 处理编辑按钮点击
    handleEdit(row) {
      this.editForm.id = row.id;

      // 从完整文件名中提取名称部分（不含后缀）
      const fullName = row.name;
      const lastDotIndex = fullName.lastIndexOf(".");

      // 如果找到了后缀，则分离名称和后缀
      if (lastDotIndex !== -1) {
        this.editForm.name = fullName.substring(0, lastDotIndex);
        this.editForm.extension = fullName.substring(lastDotIndex); // 保存后缀
      } else {
        // 如果没有后缀，则使用完整名称
        this.editForm.name = fullName;
        this.editForm.extension = "";
      }

      this.editVisible = true;
    },
    // 提交编辑
    async submitEdit() {
      if (!this.editForm.name.trim()) {
        this.$message.warning("请输入文件名称");
        return;
      }
      if (this.editForm.name.length > 20) {
        this.$message.warning("文件名称不能超过20个字符");
        return;
      }
      this.editing = true;
      try {
        // 组合新的完整文件名（名称+原后缀）
        const newFullName = this.editForm.name.trim() + this.editForm.extension;

        const params = {
          id: this.editForm.id,
          name: newFullName,
          knowledgeBaseId: this.knowledgeBaseId,
        };

        const result = await api.material.update(params);

        if (result && (result.code === 200 || result.isSuccess)) {
          this.$message.success(result.message || "更新成功");
          this.editVisible = false;
          this.loadData(); // 重新加载列表
        } else {
          this.$showFriendlyError(null, result?.message || "更新失败");
        }
      } catch (error) {
        console.error("更新失败:", error);
        this.$showFriendlyError(error, "更新失败，请重试");
      } finally {
        this.editing = false;
      }
    },
    // 删除文件
    async deleteFile(fileId) {
      try {
        const params = {
          id: fileId,
          knowledgeBaseId: this.knowledgeBaseId,
        };

        const result = await api.material.delete(params);

        if (result && (result.code == 200 || result.isSuccess)) {
          this.$message.success(result.message || "删除成功");
          this.loadData(); // 重新加载数据
        } else {
          this.$showFriendlyError(null, result?.message || "删除失败");
        }
      } catch (error) {
        console.error("删除文件失败:", error);
        this.$showFriendlyError(error, "删除失败，请重试");
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 0; // 改变每页显示数量时，重置为第一页
      this.loadData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadData();
    },
    handleRefresh() {
      this.loadData();
      this.$message.success("刷新成功");
    },
    // 文件选择变化时的处理函数
    handleFileChange(file, fileList) {
      // 更新文件列表
      this.uploadFileList = fileList;
      // 提取所有文件的raw对象
      this.uploadFiles = fileList.map(item => item.raw).filter(Boolean);
      console.log(`已选择 ${this.uploadFiles.length} 个文件:`, this.uploadFiles.map(f => f.name));
    },
    // 文件移除时的处理函数
    handleFileRemove(file, fileList) {
      // 更新文件列表
      this.uploadFileList = fileList;
      // 提取所有文件的raw对象
      this.uploadFiles = fileList.map(item => item.raw).filter(Boolean);
      console.log(`移除文件后剩余 ${this.uploadFiles.length} 个文件:`, this.uploadFiles.map(f => f.name));
    },
    // 文件上传前的检查
    beforeUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isVideo = file.type.startsWith("video/");
      const isAudio = file.type.startsWith("audio/");
      const isAcceptedFormat = isImage || isVideo || isAudio;

      if (!isAcceptedFormat) {
        this.$showFriendlyError(null, "只能上传图片、视频或音频文件!");
        return false;
      }

      // 限制不同文件类型的大小
      let maxSize = 30; // 默认限制30MB
      let typeStr = "文件";

      if (isImage) {
        maxSize = 10; // 图片限制10MB
        typeStr = "图片";
      } else if (isVideo) {
        maxSize = 100; // 视频限制100MB
        typeStr = "视频";
      } else if (isAudio) {
        maxSize = 20; // 音频限制20MB
        typeStr = "音频";
      }

      const isLtSize = file.size / 1024 / 1024 < maxSize;
      if (!isLtSize) {
        this.$showFriendlyError(null, `${typeStr}大小不能超过 ${maxSize}MB!`);
        return false;
      }

      return true;
    },
    // 提交上传
    async submitUpload() {
      if (!this.uploadFiles || this.uploadFiles.length === 0) {
        this.$message.warning("请先选择文件");
        return;
      }

      this.uploading = true;

      try {
        // 显示加载中
        const loading = this.$loading({
          lock: true,
          text: `正在上传 ${this.uploadFiles.length} 个文件...`,
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.7)",
        });

        let successCount = 0;
        let failedCount = 0;
        const failedFiles = [];

        // 逐个上传文件
        for (let i = 0; i < this.uploadFiles.length; i++) {
          const file = this.uploadFiles[i];
          try {
            // 确定上传文件类型
            let fileType = MaterialType.FILE; // 默认为文件类型
            if (file.type.startsWith("image/")) {
              fileType = MaterialType.IMAGE;
            } else if (file.type.startsWith("video/")) {
              fileType = MaterialType.VIDEO;
            } else if (file.type.startsWith("audio/")) {
              fileType = MaterialType.AUDIO;
            }

            // 构建上传参数
            const params = {
              knowledgeBaseId: this.knowledgeBaseId,
              type: fileType, // 使用枚举值
              file: file, // 直接传递文件对象
              fileName: file.name, // 传递文件名
            };

            // 调用上传接口
            const result = await api.material.upload(params);

            if (result && result.code == 200) {
              successCount++;
              console.log(`文件 ${file.name} 上传成功`);
            } else {
              failedCount++;
              failedFiles.push(file.name);
              console.error(`文件 ${file.name} 上传失败:`, result?.msg || "未知错误");
            }
          } catch (error) {
            failedCount++;
            failedFiles.push(file.name);
            console.error(`文件 ${file.name} 上传失败:`, error);
          }
        }

        loading.close();

        // 显示上传结果
        if (successCount > 0 && failedCount === 0) {
          this.$message.success(`所有 ${successCount} 个文件上传成功`);
        } else if (successCount > 0 && failedCount > 0) {
          this.$message.warning(`${successCount} 个文件上传成功，${failedCount} 个文件上传失败：${failedFiles.join(', ')}`);
        } else {
          this.$showFriendlyError(null, `所有文件上传失败：${failedFiles.join(', ')}`);
        }

        // 如果有成功上传的文件，关闭弹窗并刷新列表
        if (successCount > 0) {
          this.uploadVisible = false;
          this.loadData(); // 重新加载数据
        }

      } catch (error) {
        console.error("批量上传失败:", error);
        this.$showFriendlyError(error, "批量上传失败，请重试");
      } finally {
        this.uploading = false;
      }
    },
    // 处理复制链接按钮点击
    async handleCopy(row) {
      const success = await copyToClipboard(row.url);
      if (success) {
        this.linkCopied = true;
        this.$message.success("链接已复制到剪贴板");
      } else {
        this.$showFriendlyError(null, "复制失败，请手动复制");
      }
    },
    // 处理删除按钮点击
    handleDelete(row) {
      this.$confirm("确认删除该文件吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deleteFile(row.id);
        })
        .catch(() => {});
    },
        // 处理预览按钮点击
    handlePreview(row) {
      this.$refs.filePreview.preview({
        name: row.name,
        url: row.url,
        type: row.type
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.material-management {
  background: transparent;

  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 4px;

    .left-actions {
      .el-radio-group {
        .el-radio-button__inner {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }

    .right-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .file-name {
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 24px;
      color: #409eff;
      width: 24px;
      height: 24px;
    }

    span {
      font-size: 14px;
      line-height: 1.5;
      font-weight: 400;
      color: #303133;
    }

    .iconfont {
      color: #409eff;
    }

    .audio-icon {
      color: #e6a23c !important; // 使用橙色来区分音频文件
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.el-table) {
    background: transparent !important;

    .el-button--mini {
      padding: 5px 8px;
      margin: 0 2px;
      font-size: 12px;
    }

    .el-table__row {
      height: 60px;
      background: transparent !important;
    }

    .el-table__row:hover {
      background: rgba(64, 158, 255, 0.1) !important;
    }

    .el-table__header-wrapper {
      background: transparent !important;
    }

    .el-table__header {
      background: transparent !important;
    }

    .el-table thead {
      background: transparent !important;
    }

    .el-table th {
      background: transparent !important;
      background-color: transparent !important;
      color: #606266;
      font-weight: 500;
    }

    .el-table th.is-leaf {
      background: transparent !important;
      background-color: transparent !important;
    }

    .el-table th.gutter {
      background: transparent !important;
      background-color: transparent !important;
    }

    .el-table__body-wrapper {
      background: transparent !important;
    }

    .el-table__body {
      background: transparent !important;
    }

    .el-table__empty-block {
      background: transparent !important;
    }

    .el-table__append-wrapper {
      background: transparent !important;
    }
  }

  .upload-container {
    width: 100%;
    text-align: center;
  }
}

// 全局样式覆盖，确保表头透明
:deep(.el-table) {
  background: transparent !important;

  .el-table__header-wrapper {
    background: transparent !important;
  }

  .el-table__header {
    background: transparent !important;
  }

  thead {
    background: transparent !important;
  }

  th {
    background: transparent !important;
    background-color: transparent !important;
  }

  th.is-leaf {
    background: transparent !important;
    background-color: transparent !important;
  }

  th.gutter {
    background: transparent !important;
    background-color: transparent !important;
  }
}


</style>
