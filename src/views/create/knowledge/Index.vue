<template>
  <div class="create-knowledge-page">
    <router-view v-if="$route.params.id"></router-view>
    <div class="page-content" v-else>
      <div
        v-if="loading"
        class="loading-container"
        v-loading="loading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="加载知识库中..."
      >
        <!-- 移除el-loading组件，改用v-loading指令 -->
      </div>
      <div v-else class="knowledge-container">
        <div
          v-for="(knowledge, index) in knowledgeList"
          :key="knowledge.id"
          class="knowledge-card"
          :style="{ '--card-index': index }"
          @click="handleSettingClick(knowledge)"
        >
          <div class="knowledge-card-header">
            <div class="knowledge-info">
              <img
                src="@/assets/knowledge/knowledge.png"
                class="knowledge-icon"
                alt="知识库"
              />
              <div class="knowledge-title">
                <h3>{{ knowledge.name }}</h3>
              </div>
            </div>
          </div>
          <p class="knowledge-desc">{{ knowledge.description }}</p>
          <div class="knowledge-actions">
            <div
              class="action-icon"
              @click.stop="handleSettingClick(knowledge)"
            >
              <i class="el-icon-setting"></i>
            </div>
          </div>
        </div>

        <!-- 创建知识库卡片 -->
        <div class="create-card" @click="handleCreateKnowledge">
          <i class="el-icon-plus"></i>
          <span>创建知识库</span>
        </div>
      </div>
    </div>

    <!-- 创建知识库弹窗 -->
    <el-dialog
      title="创建知识库"
      :visible.sync="createDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        :model="createForm"
        ref="createForm"
        :rules="rules"
        label-position="top"
        @submit.native.prevent
      >
        <el-form-item label="知识库名称" prop="name" class="form-item">
          <el-input
            v-model="createForm.name"
            placeholder="给你的知识库取一个名字吧"
            maxlength="20"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="知识库描述" prop="description" class="form-item">
          <el-input
            type="textarea"
            v-model="createForm.description"
            placeholder="请输入知识库描述"
            :rows="4"
          ></el-input>
        </el-form-item>

        <div class="dialog-tip">
          <i class="el-icon-info"></i>
          知识库需绑定到知识智能体才可生效
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleCreateConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";
import EventBus from "@/utils/eventBus";

export default {
  name: "CreateKnowledge",
  data() {
    return {
      loading: true,
      createDialogVisible: false,
      createForm: {
        name: "",
        description: "",
        userid: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入知识库名称", trigger: "blur" },
          { max: 20, message: "长度不能超过20个字符", trigger: "blur" },
        ],
      },
      knowledgeList: [],
    };
  },
  created() {
    this.fetchKnowledgeList();
    // 监听刷新事件
    EventBus.$on("refresh-knowledge-list", this.fetchKnowledgeList);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    EventBus.$off("refresh-knowledge-list", this.fetchKnowledgeList);
  },
  methods: {
    fetchKnowledgeList() {
      // 加载状态
      this.loading = true;

      // 查询参数 - 对于GET请求，这些会作为URL参数传递
      const params = {
        keyword: "",
        pageIndex: 0,
        pageSize: 100,
      };

      // 调用API获取知识库列表
      api.rag
        .getKnowledgeList(params)
        .then((res) => {
          if (res.code === 200) {
            // 处理返回数据
            if (res.data && res.data.items && res.data.items.length > 0) {
              // 将API返回的数据映射为组件需要的结构
              this.knowledgeList = res.data.items.map((item) => ({
                id: item.id,
                name: item.name,
                description: item.description,
                code: item.code,
                createTime: item.create_time,
                updateTime: item.update_time,
              }));
            } else {
              this.knowledgeList = []; // 没有数据时设为空数组
            }
          } else {
            this.$showFriendlyError({ message: res.status?.message }, "获取知识库列表失败");
            // 保持默认示例数据用于展示
          }
        })
        .catch((err) => {
          this.$showFriendlyError(err, "获取知识库列表失败，请重试");
          // 保持默认示例数据用于展示
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCreateKnowledge() {
      this.createDialogVisible = true;
    },
    handleSettingClick(knowledge) {
      this.$router.push(`/create/knowledge/${knowledge.id}/setting`);
    },
    handleDialogClose() {
      this.createDialogVisible = false;
      this.$refs.createForm?.resetFields();
    },
    handleCreateConfirm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 创建知识库请求参数
          const params = {
            name: this.createForm.name,
            description: this.createForm.description,
          };

          // 发起创建知识库请求
          this.loading = true;
          api.rag
            .createKnowledge(params)
            .then((res) => {
              if (res.isSuccess || res.success) {
                this.$message.success("知识库创建成功");
                // 刷新知识库列表
                this.fetchKnowledgeList();
              } else {
                this.$showFriendlyError({ message: res.message }, "创建知识库失败");
              }
            })
            .catch((err) => {
              this.$showFriendlyError(null, 
                "创建知识库失败：" + (err.message || "未知错误")
              );
            })
            .finally(() => {
              this.loading = false;
              this.createDialogVisible = false;
              this.$refs.createForm.resetFields();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.create-knowledge-page {
  width: 100%;
  height: 100%;
  padding: 24px;
  box-sizing: border-box;
  background-color: #f5f7fa;
}

.page-content {
  width: 100%;
}

.knowledge-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.knowledge-card {
  position: relative;
  width: 100%;
  height: 195px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  border: 1px solid #eaeaea;
  box-sizing: border-box;
  padding: 16px 20px;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
  transform-origin: center;

  &:hover {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }

  .knowledge-card-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .knowledge-info {
      display: flex;
      align-items: center;
      gap: 12px;

      img {
        width: 44px;
        height: 44px;
        display: inline-block;
        vertical-align: middle;
      }

      .knowledge-title {
        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }

  .knowledge-actions {
    position: absolute;
    top: 16px;
    right: 20px;
    display: flex;
    gap: 12px;
    z-index: 1;

    .action-icon {
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.2s;

      &:hover {
        opacity: 0.8;
      }

      i {
        font-size: 16px;
        color: #666;
      }
    }
  }

  .knowledge-desc {
    margin: 0;
    color: #4b5563;
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}

.create-card {
  position: relative;
  width: 100%;
  height: 195px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  border: 1px solid #eaeaea;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: 0.3s;
  opacity: 0;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    background: var(--el-color-primary-light-9);

    i,
    span {
      color: var(--el-color-primary);
    }

    i {
      transform: rotate(90deg);
    }
  }

  &:active {
    transform: scale(0.98);
  }

  i {
    font-size: 32px;
    margin-bottom: 12px;
    color: #9ca3af;
    transition: all 0.3s ease;
  }

  span {
    font-size: 16px;
    font-weight: 500;
    color: #9ca3af;
    transition: color 0.3s ease;
  }
}

// 创建知识库弹窗样式
::v-deep .el-dialog {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 24px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

.form-item {
  margin-bottom: 24px;

  ::v-deep .el-form-item__label {
    padding-bottom: 8px;
    line-height: 1;
    color: #606266;
  }
}

.dialog-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 14px;

  i {
    color: var(--el-color-primary);
  }
}

.loading-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 120px;
    font-size: 14px;
  }
}

.knowledge-icon {
  width: 44px;
  height: 44px;
  display: inline-block;
  vertical-align: middle;
}
</style>
