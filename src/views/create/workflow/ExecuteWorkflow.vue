<template>
  <div class="execute-workflow-page">
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    <el-row v-else :gutter="10" class="full-height">
      <!-- 左侧问题列表 - 仅在非简化版时显示 -->
      <el-col v-if="isSimpleMode !== true" :span="4" class="full-height">
        <div class="left-panel">
          <div class="list-container">
            <div class="search-box">
              <el-input
                v-model="searchQuery"
                placeholder="搜索历史记录..."
                prefix-icon="el-icon-search"
                clearable
              ></el-input>
            </div>
            <div v-if="isLoadingChatList" class="loading-list">
              <el-skeleton :rows="5" animated />
            </div>
            <template v-else>
              <div
                v-for="(item, index) in filteredSessions"
                :key="index"
                class="chat-list-item"
                :class="{ 'active': currentSessionIndex === index }"
                @click="selectSession(index)"
              >
              <div class="chat-preview">
                <div class="chat-avatar">
                  <el-avatar :size="32" :src="item.profilePhoto || ''">
                    {{item.appName ? item.appName.charAt(0) : "W"}}
                  </el-avatar>
                </div>
                <div class="chat-info">
                  <div class="chat-name">{{item.appName || "工作流" }}</div>
                  <div class="chat-last-message">
                    {{ item.description || "无描述" }}
                  </div>
                  <div class="chat-app-info" v-if="item.appName">
                    <el-tag size="mini" type="info">{{ item.appName }}</el-tag>
                    <el-tag size="mini" v-if="item.channelName" type="success">{{ item.channelName }}</el-tag>
                  </div>
                </div>
                <div class="chat-time">{{ item.timeDisplay }}</div>
              </div>
            </div>
            </template>
            <div v-if="!isLoadingChatList && filteredSessions.length === 0" class="empty-list">
              <i class="el-icon-warning-outline"></i>
              <p>暂无历史记录</p>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 中间表单区域和右侧结果区域 - 仅在左侧列表加载完成后显示 -->
      <template v-if="isSimpleMode === true || !isLoadingChatList">
        <!-- 中间表单区域 - 仅在非简化版时显示 -->
        <el-col v-if="isSimpleMode !== true" :span="10" class="full-height">
          <div class="form-panel">
            <div class="panel-header">
              <h3>
                {{ workflowDetail ? workflowDetail.name : '问题描述' }}
                <el-tag v-if="workflowDetail" type="info">ID: {{ workflowDetail.id }}</el-tag>
              </h3>
            </div>
            <div class="form-container">
              <el-form :model="formData" ref="formRef" label-width="120px" :disabled="loading" :rules="formRules">
                <!-- 文本输入字段 - 如果配置中启用 -->
                <el-form-item v-if="enableTextInput === true" :label="textInputName" prop="question" :required="startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.textInputRequired === true">
                  <el-input
                    type="textarea"
                    :rows="4"
                    :placeholder="(startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.textInputPlaceholder) || '请输入问题描述'"
                    v-model="formData.question"
                  ></el-input>
                </el-form-item>

                <!-- 图片上传 - 如果配置中启用 -->
                <el-form-item v-if="enableImageInput === true" :label="imageInputName" prop="imageFile" :required="startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.imageInputRequired === true">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    :file-list="formData.imageFileList || []"
                    :on-change="handleImageChange"
                    accept="image/*"
                    :disabled="loading"
                  >
                    <el-button type="primary" :disabled="loading">点击上传图片</el-button>
                    <div slot="tip" class="el-upload__tip">
                      {{ (startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.imageInputPlaceholder) || '只能上传jpg/png文件，且不超过10MB' }}
                    </div>
                  </el-upload>
                </el-form-item>

                <!-- 文件上传 - 如果配置中启用 -->
                <el-form-item v-if="enableFileInput === true" :label="fileInputName" prop="fileList" :required="startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.fileInputRequired === true">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    :file-list="formData.fileList"
                    :on-change="handleFileChange"
                    :disabled="loading"
                  >
                    <el-button type="primary" :disabled="loading">点击上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">
                      {{ startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.fileInputPlaceholder || '上传文件大小不超过10MB' }}
                    </div>
                  </el-upload>
                </el-form-item>

                <!-- 自定义变量字段 - 根据工作流配置动态生成 -->
                <template v-if="customVariables && customVariables.length > 0">
                  <template v-for="variable in customVariables">
                    <el-form-item
                      v-if="variable.name !== 'sys_current_time'"
                      :key="variable.name"
                      :label="variable.displayName"
                      :prop="'customVariables.' + variable.name"
                      :required="variable.required"
                    >
                      <!-- 文本类型输入框 -->
                      <el-input
                        v-if="variable.type === 1"
                        v-model="formData.customVariables[variable.name]"
                        :placeholder="variable.placeholder || '请输入' + variable.displayName"
                        :disabled="loading"
                      ></el-input>

                      <!-- 选择类型 -->
                      <el-select
                        v-else-if="variable.type === 2"
                        v-model="formData.customVariables[variable.name]"
                        :placeholder="variable.placeholder || '请选择' + variable.displayName"
                        style="width: 100%"
                        :disabled="loading"
                      >
                        <el-option
                          v-for="option in variable.options"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </template>
              </el-form>
            </div>

            <!-- 表单底部执行按钮区域 -->
            <div class="form-bottom-actions">
              <div class="action-buttons">
                <el-button icon="el-icon-delete" circle :disabled="loading"></el-button>
                <el-button icon="el-icon-refresh" circle @click="resetForm" :disabled="loading"></el-button>
              </div>
              <el-button type="primary" class="execute-btn" @click="executeWorkflow" :loading="loading">开始运行</el-button>
            </div>
          </div>
        </el-col>

        <!-- 简化版输入区域 - 仅在简化版时显示 -->
        <el-col v-if="isSimpleMode === true" :span="14" class="full-height">
          <div class="form-panel simple-mode">
            <div class="panel-header">
              <h3>
                {{ workflowDetail ? workflowDetail.name : '问题描述' }}
                <el-tag v-if="workflowDetail" type="info">ID: {{ workflowDetail.id }}</el-tag>
              </h3>
            </div>
            <div class="form-container">
              <el-form :model="formData" ref="formRef" :disabled="loading" :rules="formRules">
                <el-form-item prop="question">
                  <el-input
                    type="textarea"
                    :rows="4"
                    placeholder="请输入问题描述"
                    v-model="formData.question"
                    :disabled="loading"
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>
            <div class="form-bottom-actions">
              <el-button type="primary" class="execute-btn" @click="executeWorkflow" :loading="loading">开始运行</el-button>
            </div>
          </div>
        </el-col>

        <!-- 右侧结果区域 -->
        <el-col :span="isSimpleMode === true ? 10 : 10" class="full-height">
          <div class="result-panel">
            <div class="panel-header">
              <h3>执行结果</h3>
              <div class="header-actions">
                <el-button size="mini" icon="el-icon-refresh" circle @click="refreshResult"></el-button>
              </div>
            </div>
            <div class="result-container" ref="resultContainer">
              <div v-if="!hasResult" class="empty-result">
                <i class="el-icon-warning-outline"></i>
                <p>请点击"开始运行"按钮执行工作流</p>
              </div>
              <div v-else>
                <!-- 右上角时间戳 -->
                <div class="result-time">{{ resultData.time }}</div>
                <!-- 用户输入 -->
                <div class="chat-message user-message">
                  <div class="message-content">
                    <div class="message-text">
                      <!-- 显示文本输入 -->
                      <div v-if="resultData.textInput">{{ resultData.textInput }}</div>

                      <!-- 显示自定义变量 -->
                      <div v-if="resultData.customVariables && Object.keys(resultData.customVariables).length > 0" class="custom-variables">
                        <template v-for="(value, key) in resultData.customVariables">
                          <div v-if="key !== 'currentTime'" :key="key" class="variable-item">
                            <span class="variable-name">{{ getVariableDisplayName(key) }}:</span>
                            <span class="variable-value">{{ value }}</span>
                          </div>
                        </template>
                      </div>

                      <!-- 显示文件和图片上传情况 -->
                      <div v-if="resultData.hasImage" class="uploaded-files">
                        <div>已上传图片: 1个</div>
                      </div>
                      <div v-if="resultData.hasFile" class="uploaded-files">
                        <div>已上传文件: 1个</div>
                      </div>
                    </div>
                  </div>
                  <div class="avatar user-avatar">
                    <img src="/user-avatar.png" @error="handleUserAvatarError" alt="用户">
                  </div>
                </div>

                <!-- 系统回复 -->
                <div class="chat-message system-message">
                  <div class="avatar system-avatar">
                    <img src="/system-avatar.png" @error="handleSystemAvatarError" alt="系统">
                  </div>
                  <div class="message-content">
                    <div class="message-flow">
                      <div class="flow-node" v-for="(node, index) in ['开始', '固定节点', '插件', '结束']" :key="index">
                        <div class="node-icon" :class="{'node-active': index <= 2}">
                          <i :class="getNodeIcon(node)"></i>
                        </div>
                        <div class="node-connector" v-if="index < 3"></div>
                      </div>
                    </div>
                    <div class="message-text" v-html="resultData.message"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </template>

      <!-- 中间和右侧加载状态 - 仅在非简化版且左侧列表加载中时显示 -->
      <template v-else>
        <el-col :span="20" class="full-height">
          <div class="loading-container">
            <el-skeleton :rows="15" animated />
          </div>
        </el-col>
      </template>
    </el-row>
  </div>
</template>

<script>
import { api } from '@/api/request'; // 导入API
import { formatOutputContent } from '@/utils'; // 导入格式化工具
import * as signalR from "@microsoft/signalr"; // 导入SignalR

export default {
  name: 'ExecuteWorkflowPage',
  data() {
    return {
      workflowId: null,
      workflowDetail: null,
      searchQuery: '',
      currentQuestionIndex: -1,
      currentSessionIndex: -1,
      loading: false,
      isLoading: true,
      hasResult: false,
      activeCollapseNames: ['details'],

      // 问题列表数据
      questions: [],

      // 会话列表数据
      sessionList: [],

      // 表单数据
      formData: {
        question: '',
        fileList: [],
        imageFileList: [],
        customVariables: {}
      },

      // 表单验证规则
      formRules: {
        question: [
          { required: false, message: '请输入问题描述', trigger: 'blur' }
        ],
        imageFile: [
          { required: false, message: '请上传图片', trigger: 'change' }
        ],
        fileList: [
          { required: false, message: '请上传文件', trigger: 'change' }
        ]
      },

      // 结果数据
      resultData: {
        status: '',
        time: '',
        message: '',
        logs: '',
        textInput: '',
        customVariables: {},
        hasImage: false,
        hasFile: false
      },

      // 添加防抖和加载状态变量
      chatListUpdateTimer: null,
      isLoadingChatList: false,
      lastChatListUpdateTime: 0,
      sourceType: 2, // 工作流的sourceType固定为2

      // 会话状态相关
      sessionStatus: {
        isConnected: false,
        sessionId: null,
        userName: "",
      },
      // SignalR连接对象
      hubConnection: null,
    };
  },
  computed: {
    filteredQuestions() {
      if (!this.searchQuery) {
        return this.questions;
      }

      const query = this.searchQuery.toLowerCase();
      return this.questions.filter(item =>
        item.question.toLowerCase().includes(query)
      );
    },

    // 过滤后的会话列表
    filteredSessions() {
      if (!this.searchQuery) {
        return this.sessionList;
      }

      const query = this.searchQuery.toLowerCase();
      return this.sessionList.filter(item =>
        (item.sessionName && item.sessionName.toLowerCase().includes(query)) ||
        (item.description && item.description.toLowerCase().includes(query)) ||
        (item.appName && item.appName.toLowerCase().includes(query))
      );
    },

    // 获取工作流开始节点配置
    startNodeConfig() {
      if (!this.workflowDetail || !this.workflowDetail.flowDetailDto || !this.workflowDetail.flowDetailDto.startNodes || !this.workflowDetail.flowDetailDto.startNodes.length) {
        return null;
      }

      const startNode = this.workflowDetail.flowDetailDto.startNodes[0];
      return startNode.data && startNode.data.nodeTypeConfig ? startNode.data.nodeTypeConfig : null;
    },

    // 获取自定义变量列表
    customVariables() {
      if (!this.startNodeConfig || !this.startNodeConfig.inputSettings || !this.startNodeConfig.inputSettings.customVariables) {
        return [];
      }

      return this.startNodeConfig.inputSettings.customVariables;
    },

    // 是否启用文本输入
    enableTextInput() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings &&
             this.startNodeConfig.inputSettings.enableTextInput;
    },

    // 获取文本输入名称
    textInputName() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings ?
             this.startNodeConfig.inputSettings.textInputName || '问题描述' :
             '问题描述';
    },

    // 是否启用图片上传
    enableImageInput() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings &&
             this.startNodeConfig.inputSettings.enableImageInput;
    },

    // 获取图片上传名称
    imageInputName() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings ?
             this.startNodeConfig.inputSettings.imageInputName || '上传图片' :
             '上传图片';
    },

    // 是否启用文件上传
    enableFileInput() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings &&
             this.startNodeConfig.inputSettings.enableFileInput;
    },

    // 获取文件上传名称
    fileInputName() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings ?
             this.startNodeConfig.inputSettings.fileInputName || '上传文件' :
             '上传文件';
    },

    // 判断是否为简化模式
    isSimpleMode() {
      return !this.workflowDetail?.flowDetailDto ||
        (this.enableFileInput === false &&
        this.enableImageInput === false &&
        this.enableTextInput === false &&
        (!this.customVariables || this.customVariables.length === 0));
    }
  },
  created() {
    // 从路由获取工作流ID
    this.workflowId = this.$route.params.workflowId;

    // 如果没有工作流ID，返回列表页
    if (!this.workflowId) {
      this.$showFriendlyError(null, '工作流ID不存在');
      this.$router.push({ name: 'CreateWorkflow' });
      return;
    }
    // 检查用户登录状态并初始化聊天
    this.checkLoginAndInitSession();
  },
  watch: {
    // 监听工作流详情变化，更新表单验证规则
    workflowDetail: {
      handler(newVal) {
        if (newVal) {
          this.updateFormRules();
        }
      },
      deep: true
    }
  },
  methods: {
    // 更新表单验证规则
    updateFormRules() {
      // 重置表单规则
      this.formRules = {
        question: [
          { required: false, message: '请输入问题描述', trigger: 'blur' }
        ],
        imageFile: [
          { required: false, message: '请上传图片', trigger: 'change' }
        ],
        fileList: [
          { required: false, message: '请上传文件', trigger: 'change' }
        ]
      };

      // 根据工作流配置动态设置必填项规则
      if (this.startNodeConfig && this.startNodeConfig.inputSettings) {
        // 文本输入字段
        if (this.enableTextInput && this.startNodeConfig.inputSettings.textInputRequired) {
          this.formRules.question = [
            { required: true, message: `请输入${this.textInputName}`, trigger: 'blur' }
          ];
        }

        // 图片上传字段
        if (this.enableImageInput && this.startNodeConfig.inputSettings.imageInputRequired) {
          this.formRules.imageFile = [
            { required: true, message: `请上传${this.imageInputName}`, trigger: 'change' }
          ];
        }

        // 文件上传字段
        if (this.enableFileInput && this.startNodeConfig.inputSettings.fileInputRequired) {
          this.formRules.fileList = [
            { required: true, message: `请上传${this.fileInputName}`, trigger: 'change' }
          ];
        }
      }

      // 设置自定义变量的验证规则
      if (this.customVariables && this.customVariables.length > 0) {
        this.formRules.customVariables = {};

        this.customVariables.forEach(variable => {
          if (variable.required) {
            const fieldName = variable.name;
            const displayName = variable.displayName;
            const type = variable.type;

            this.formRules.customVariables[fieldName] = [
              {
                required: true,
                message: `请${type === 1 ? '输入' : '选择'}${displayName}`,
                trigger: type === 1 ? 'blur' : 'change'
              }
            ];
          }
        });
      }
    },

    // 生成GUID方法
    generateGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    // 检查登录并初始化会话
    checkLoginAndInitSession() {
      try {
        // 获取用户信息
        const userStr = localStorage.getItem("user");
        if (!userStr) {
          this.$message.warning("用户未登录，部分功能可能受限");
          return;
        }

        const user = JSON.parse(userStr);

        // 构建当前路由标识符
        const routeKey = `workflow_${this.workflowId}`;

        // 尝试从localStorage获取之前保存的会话ID
        const savedSessionData = localStorage.getItem("workflowSessionData");
        let savedSessionId = null;

        if (savedSessionData) {
          try {
            const sessionData = JSON.parse(savedSessionData);
            // 检查是否存在相同路由的会话ID
            if (sessionData.routeKey === routeKey) {
              savedSessionId = sessionData.sessionId;
              console.log("使用保存的会话ID:", savedSessionId);
            }
          } catch (e) {
            console.error("解析保存的会话数据出错:", e);
          }
        }

        if (savedSessionId) {
          // 如果有保存的会话ID，使用它
          this.sessionStatus.sessionId = savedSessionId;
        } else {
          // 否则生成新的GUID
          this.sessionStatus.sessionId = this.generateGuid();
          console.log("已生成新的会话ID:", this.sessionStatus.sessionId);

          // 保存到localStorage
          const sessionData = {
            routeKey: routeKey,
            sessionId: this.sessionStatus.sessionId,
            timestamp: new Date().getTime(),
          };
          localStorage.setItem(
            "workflowSessionData",
            JSON.stringify(sessionData)
          );
        }

        this.sessionStatus.userName = user.name || "用户";

        // 创建连接
        this.createConnection();
      } catch (error) {
        console.error("检查登录状态失败:", error);
        this.$message.warning("初始化聊天失败，请刷新页面重试");
      }
    },

    // 创建与服务器的SignalR连接
    createConnection() {
      try {
        // 使用SignalR专用的环境变量
        const signalRUrl = process.env.VUE_APP_SIGNALR_URL || 'http://localhost:5280';
        const serverUrl = `${signalRUrl}/message/internal-admin`;

        // 获取认证令牌
        const token = localStorage.getItem("token");

        console.log("开始创建SignalR连接:", serverUrl);
        console.log("认证状态:", token ? "已获取令牌" : "无令牌");

        if (!token) {
          this.$message.warning("用户未登录，无法建立聊天连接");
          return;
        }

        // 先清理旧的连接
        if (this.hubConnection) {
          // 移除所有事件监听器，避免重复处理消息
          console.log("清理旧连接的事件监听器");
          try {
            this.hubConnection.off("ReceiveMessage");
            this.hubConnection.off("UserStatusChanged");
            this.hubConnection.off("SystemNotification");
          } catch (error) {
            console.warn("清理旧连接事件监听器出错:", error);
          }
        }

        // 创建连接配置
        const connectionOptions = {
          // 使用token工厂函数提供认证
          accessTokenFactory: () => token,
        };

        // 创建连接
        this.hubConnection = new signalR.HubConnectionBuilder()
          .withUrl(serverUrl, connectionOptions)
          .withAutomaticReconnect() // 使用默认重连策略
          .configureLogging(signalR.LogLevel.Information) // 使用Information级别日志
          .build();

        // 注册系统通知事件
        this.hubConnection.on("SystemNotification", (message) => {
          console.log("系统通知:", message);
        });

        // 设置连接状态变化处理器
        this.hubConnection.onreconnecting((error) => {
          console.log("正在尝试重新连接:", error);
          this.sessionStatus.isConnected = false;
        });

        this.hubConnection.onreconnected((connectionId) => {
          console.log("已重新连接，ID:", connectionId);
          this.sessionStatus.isConnected = true;
          // 重新加入会话
          this.joinSession();
        });

        this.hubConnection.onclose((error) => {
          console.log("连接已关闭:", error ? error.message : "无错误详情");
          this.sessionStatus.isConnected = false;
        });

        // 启动连接
        this.startConnection();
      } catch (error) {
        console.error("创建连接失败:", error);
        this.$showFriendlyError(null, "连接服务器失败，请稍后重试");
      }
    },

    // 启动连接
    async startConnection() {
      if (
        this.hubConnection &&
        this.hubConnection.state === signalR.HubConnectionState.Connected
      ) {
        this.sessionStatus.isConnected = true;
        return;
      }

      try {
        console.log("开始启动连接...");
        await this.hubConnection.start();
        console.log(
          "已连接到SignalR Hub，连接ID:",
          this.hubConnection.connectionId
        );
        this.sessionStatus.isConnected = true;

        // 连接成功后，如果已有会话ID则加入会话
        if (this.sessionStatus.sessionId && this.workflowId) {
          this.joinSession();
        }
      } catch (error) {
        console.error("连接失败:", error);
        this.sessionStatus.isConnected = false;
      }
    },

    // 加入会话
    async joinSession() {
      if (!this.sessionStatus.sessionId || !this.workflowId) {
        console.error("无法加入会话: 缺少会话ID或工作流ID");
        return;
      }

      try {
        console.log("尝试加入会话:", this.sessionStatus.sessionId);

        // 为参数准备客户端ID和自定义发送者ID
        const clientId = `${this.workflowId}`;
        const customSenderId = this.sessionStatus.sessionId;
        const userName = this.sessionStatus.userName || "用户";
        // 应用来源类型 2=工作流
        const sourceType = this.sourceType;
        const source = "管理后台客户端";

        console.log("会话参数:", {
          sessionId: this.sessionStatus.sessionId,
          clientId,
          sourceType,
          customSenderId,
          source,
          userName,
        });

        // 使用SignalR加入会话
        await this.hubConnection.invoke(
          "JoinSession",
          this.sessionStatus.sessionId,
          clientId,
          sourceType,
          customSenderId
        );
        console.log(
          `已加入会话: ${this.sessionStatus.sessionId}, 客户端ID: ${clientId}, 自定义发送者ID: ${customSenderId}`
        );

        // 设置完会话后立即获取一次会话列表
        this.fetchSessionList();

      } catch (error) {
        console.error("加入会话失败:", error);
        // 加入会话失败时，展示一个界面提示
        this.$message.warning("加入会话失败，可能无法保存对话历史");
        // 仍然尝试初始化表单
        this.updateFormFromWorkflowDetail();
      }
    },

    // 获取会话列表数据
    fetchSessionList() {
      // 如果正在加载中，直接返回
      if (this.isLoadingChatList) {
        console.log("聊天列表正在加载中，跳过本次请求");
        return;
      }

      console.log("开始获取会话列表，来源类型:", this.sourceType);

      // 标记为正在加载
      this.isLoadingChatList = true;

      // 获取认证令牌
      const token = localStorage.getItem("token");
      if (!token) {
        this.$message.warning("用户未登录，无法获取历史记录");
        this.isLoadingChatList = false;
        console.error("获取会话列表失败: 未找到token");
        return;
      }

      // 发起API请求获取聊天会话列表，传入sourceType=2表示工作流
      api.chat.getSessions({
          skipCount: 0,
          maxResultCount: 1000,
          SourceType: this.sourceType,
          IsTestSession: true
        }) // 获取前1000条数据
        .then(result => {
          console.log("获取会话列表响应:", result);

          if (result && result.code === 200 && result.data && result.data.items) {
            // 处理返回的聊天会话数据
            this.sessionList = result.data.items.map(session => {
              // 转换日期格式为显示时间
              const creationTime = new Date(session.creationTime);
              const now = new Date();
              let timeDisplay = "";

              // 格式化显示时间: 今天显示小时:分钟，其他日期显示月-日
              if (creationTime.toDateString() === now.toDateString()) {
                timeDisplay = `${creationTime.getHours().toString().padStart(2, '0')}:${creationTime.getMinutes().toString().padStart(2, '0')}`;
              } else {
                timeDisplay = `${(creationTime.getMonth()+1).toString().padStart(2, '0')}-${creationTime.getDate().toString().padStart(2, '0')}`;
              }

              return {
                id: session.id,
                clientId: session.clientId || session.id,
                sessionId: session.sessionID,
                sessionName: session.sessionName,
                description: session.description,
                timeDisplay: timeDisplay,
                creationTime: session.creationTime,
                appName: session.appName,
                channelName: session.channelName,
                profilePhoto: session.profilePhoto,
                introduce: session.introduce,
                // 保留原始数据以备后用
                originalData: session
              };
            });

            console.log("已处理会话列表数据，长度:", this.sessionList.length);
            if (this.sessionList.length > 0) {
              console.log("会话列表第一条数据示例:", this.sessionList[0]);
            } else {
              console.log("会话列表为空，可能需要创建新会话");
            }
            // 获取工作流详情
            this.fetchWorkflowDetail();
            // 检查是否需要自动选中会话
            this.autoSelectSession();

          } else {
            console.error("获取历史记录返回数据格式错误:", result);
            this.$message.warning("获取历史记录失败，使用默认表单");
            this.updateFormFromWorkflowDetail();
          }
        })
        .catch(error => {
          console.error("获取历史记录出错:", error);
          this.$message.warning("获取聊天历史失败，将使用默认表单");
          // 出错时也更新表单显示
          this.updateFormFromWorkflowDetail();
        })
        .finally(() => {
          this.isLoadingChatList = false;
        });
    },

    // 自动根据当前ID选中会话
    autoSelectSession() {
      console.log("开始自动选择会话，当前工作流ID:", this.workflowId);
      console.log("当前会话ID:", this.sessionStatus.sessionId);
      console.log("会话列表长度:", this.sessionList.length);

      if (this.sessionList.length === 0) {
        console.log("会话列表为空，无法匹配会话");
        // 未找到匹配会话时，刷新工作流详情以显示表单配置
        this.updateFormFromWorkflowDetail();
        return;
      }

      // 输出会话列表中的clientId和sessionId进行调试
      console.log("会话列表中的数据:", this.sessionList.map(s => ({
        clientId: s.clientId,
        sessionId: s.sessionId
      })));

      // 1. 先尝试同时匹配工作流ID和会话ID（精确匹配）
      let sessionIndex = this.sessionList.findIndex(
        session => session.clientId === this.workflowId &&
                  session.sessionId === this.sessionStatus.sessionId
      );

      console.log("同时匹配工作流ID和会话ID结果索引:", sessionIndex);

      // 2. 如果没找到，只匹配工作流ID
      if (sessionIndex < 0) {
        sessionIndex = this.sessionList.findIndex(
          session => session.clientId === this.workflowId
        );
        console.log("仅匹配工作流ID结果索引:", sessionIndex);
      }

      // 3. 如果仍没找到，尝试字符串匹配方式
      if (sessionIndex < 0) {
        sessionIndex = this.sessionList.findIndex(
          session => session.clientId === `${this.workflowId}`
        );
        console.log("字符串转换后匹配结果索引:", sessionIndex);
      }

      // 4. 如果仍未找到，再尝试部分匹配
      if (sessionIndex < 0 && this.workflowId) {
        console.log("尝试进行部分匹配");
        sessionIndex = this.sessionList.findIndex(
          session => session.clientId && session.clientId.includes(this.workflowId)
        );
        console.log("部分匹配结果索引:", sessionIndex);
      }

      // 5. 如果都没找到，尝试只匹配会话ID
      if (sessionIndex < 0 && this.sessionStatus.sessionId) {
        sessionIndex = this.sessionList.findIndex(
          session => session.sessionId === this.sessionStatus.sessionId
        );
        console.log("仅匹配会话ID结果索引:", sessionIndex);
      }

      // 如果找到对应会话，自动选中
      if (sessionIndex >= 0) {
        console.log(`找到匹配的会话，索引: ${sessionIndex}, 工作流ID: ${this.workflowId}, 会话ID: ${this.sessionStatus.sessionId}`);
        console.log("匹配到的会话信息:", {
          clientId: this.sessionList[sessionIndex].clientId,
          sessionId: this.sessionList[sessionIndex].sessionId
        });

        // 设置当前选中的会话索引，使左侧栏显示选中状态
        this.currentSessionIndex = sessionIndex;

        // 仅更新表单数据，不自动显示结果
        this.updateFormFromSession(this.sessionList[sessionIndex]);
      } else {
        console.log(`未找到与工作流ID ${this.workflowId} 或会话ID ${this.sessionStatus.sessionId} 匹配的会话`);
        // 未找到匹配会话时，刷新工作流详情以显示表单配置
        this.updateFormFromWorkflowDetail();
      }
    },

    // 新增方法：仅更新表单数据，不显示结果
    updateFormFromSession(session) {

      // 重置其他表单字段
      this.formData.fileList = [];
      this.formData.imageFileList = [];

      // 如果会话中包含自定义变量数据，则回填
      if (session.originalData && session.originalData.customVariables) {
        try {
          // 尝试解析原始数据中的自定义变量
          const customVars = session.originalData.customVariables;
          // 将这些变量填充到表单中
          Object.keys(customVars).forEach(key => {
            if (Object.prototype.hasOwnProperty.call(this.formData.customVariables, key)) {
              this.formData.customVariables[key] = customVars[key];
            }
          });
        } catch (error) {
          console.error('解析会话自定义变量失败:', error);
        }
      }
    },

    // 根据工作流详情更新表单
    updateFormFromWorkflowDetail() {
      if (!this.workflowDetail) return;

      // 重置表单为初始状态
      this.resetForm();

      // 自定义变量已通过 initCustomVariablesForm 方法初始化
    },

    // 修改selectSession方法
    selectSession(index) {
      // 避免重复选择同一个会话
      if (this.currentSessionIndex === index) return;

      this.currentSessionIndex = index;
      const session = this.sessionList[index];

      console.log(`选中会话: ${session.id}, 描述: ${session.description}`);

      // 清空当前选中的问题
      this.currentQuestionIndex = -1;

      // 更新表单前先显示加载状态
      this.loading = true;

      // 更新表单数据
      this.updateFormFromSession(session);

      // 仅获取会话基本信息，不设置hasResult
      this.loadSessionFormData(session);
    },

    // 新增方法：仅加载会话的表单数据，不显示结果
    async loadSessionFormData(session) {
      this.loading = true;

      try {
        // 更新工作流详情，确保表单配置正确
        if (session.clientId ) {
          // 如果会话的clientId与当前工作流ID不同，需要获取对应的工作流详情
          try {
            const workflowResult = await api.workflow.getDetail(session.clientId);
            if (workflowResult && workflowResult.code === 200 && workflowResult.data) {
              this.workflowDetail = workflowResult.data;
              // 更新自定义变量表单数据
              this.initCustomVariablesForm();
            }
          } catch (workflowError) {
            console.error('获取关联工作流详情失败:', workflowError);
          }
        }

        // 使用会话的id获取详细信息
        const result = await api.chat.getSessionDetail(session.id);

        if (result && result.code === 200 && result.data) {
          // 如果有图片信息，进行回显
          if (result.data.imageInput) {
            this.formData.imageFileList = [{
              name: '上传的图片',
              url: result.data.imageInput,
              status: 'success'
            }];
          }

          // 如果有文件信息，进行回显
          if (result.data.fileInput) {
            this.formData.fileList = [{
              name: '上传的文件',
              url: result.data.fileInput,
              status: 'success'
            }];
          }

          // 更新自定义变量（如果存在）
          if (result.data.customVariables) {
            Object.keys(result.data.customVariables).forEach(key => {
              if (Object.prototype.hasOwnProperty.call(this.formData.customVariables, key)) {
                this.formData.customVariables[key] = result.data.customVariables[key];
              }
            });
          }

          // 准备结果数据，但不设置hasResult为true
          this.resultData = {
            status: 'success',
            time: new Date(session.creationTime).toLocaleString(),
            message: result.data.lastMessage ?
              formatOutputContent(result.data.lastMessage) :
              (session.description || '无详细内容'),
            logs: JSON.stringify(result.data, null, 2) || '',
            textInput: result.data.textInput || session.description,
            customVariables: result.data.customVariables || {},
            hasImage: !!result.data.imageInput,
            hasFile: !!result.data.fileInput
          };

          // 确保不显示结果区域
          this.hasResult = false;
        }
      } catch (error) {
        console.error('获取会话详情失败:', error);
        // 确保不显示结果区域
        this.hasResult = false;
      } finally {
        this.loading = false;
      }
    },

    // 获取会话详情 - 保留原方法用于其他场景
    async fetchSessionDetail(session) {
      this.loading = true;

      try {
        // 更新工作流详情，确保表单配置正确
        if (session.clientId ) {
          // 如果会话的clientId与当前工作流ID不同，需要获取对应的工作流详情
          try {
            const workflowResult = await api.workflow.getDetail(session.clientId);
            if (workflowResult && workflowResult.code === 200 && workflowResult.data) {
              this.workflowDetail = workflowResult.data;
              // 更新自定义变量表单数据
              this.initCustomVariablesForm();
            }
          } catch (workflowError) {
            console.error('获取关联工作流详情失败:', workflowError);
          }
        }

        // 使用会话的id获取详细信息
        const result = await api.chat.getSessionDetail(session.id);

        if (result && result.code === 200 && result.data) {
          // 解析返回结果 - 如果有lastMessage则格式化处理
          const message = result.data.lastMessage ?
            formatOutputContent(result.data.lastMessage) :
            (session.description || '无详细内容');

          // 如果有图片信息，也进行回显
          if (result.data.imageInput) {
            this.formData.imageFileList = [{
              name: '上传的图片',
              url: result.data.imageInput,
              status: 'success'
            }];
          }

          // 如果有文件信息，进行回显
          if (result.data.fileInput) {
            this.formData.fileList = [{
              name: '上传的文件',
              url: result.data.fileInput,
              status: 'success'
            }];
          }

          // 更新自定义变量（如果存在）
          if (result.data.customVariables) {
            Object.keys(result.data.customVariables).forEach(key => {
              if (Object.prototype.hasOwnProperty.call(this.formData.customVariables, key)) {
                this.formData.customVariables[key] = result.data.customVariables[key];
              }
            });
          }

          // 设置结果数据
          this.resultData = {
            status: 'success',
            time: new Date(session.creationTime).toLocaleString(),
            message: message,
            logs: JSON.stringify(result.data, null, 2) || '',
            textInput: result.data.textInput || session.description,
            customVariables: result.data.customVariables || {},
            hasImage: !!result.data.imageInput,
            hasFile: !!result.data.fileInput
          };

          this.hasResult = true;
        } else {
          // 如果获取详情失败，使用基本信息
          this.resultData = {
            status: 'info',
            time: new Date(session.creationTime).toLocaleString(),
            message: session.description || '无详细内容',
            logs: JSON.stringify(session.originalData, null, 2) || '',
            textInput: session.description,
            customVariables: {},
            hasImage: false,
            hasFile: false
          };

          this.hasResult = true;
        }
      } catch (error) {
        console.error('获取会话详情失败:', error);
        // 失败时使用基本信息
        this.resultData = {
          status: 'error',
          time: new Date(session.creationTime).toLocaleString(),
          message: session.description || '无详细内容',
          logs: '',
          textInput: session.description,
          customVariables: {},
          hasImage: false,
          hasFile: false
        };

        this.hasResult = true;
      } finally {
        this.loading = false;
      }
    },

    // 获取工作流详情
    async fetchWorkflowDetail() {
      this.isLoading = true;

      try {
        const result = await api.workflow.getDetail(this.workflowId);

        if (result && result.code === 200 && result.data) {
          this.workflowDetail = result.data;
          // 初始化自定义变量表单数据
          this.initCustomVariablesForm();
          // 更新表单验证规则
          this.updateFormRules();
          console.log('工作流详情:', this.workflowDetail);
        } else {
          this.$showFriendlyError(null, result?.message || '获取工作流详情失败');
          this.$router.push({ name: 'CreateWorkflow' });
        }
      } catch (error) {
        console.error('获取工作流详情失败:', error);
        this.$showFriendlyError(error, '获取工作流详情失败，请重试');
        this.$router.push({ name: 'CreateWorkflow' });
      } finally {
        this.isLoading = false;
      }
    },

    // 初始化自定义变量表单数据
    initCustomVariablesForm() {
      if (!this.customVariables.length) return;

      const customVariables = {};

      this.customVariables.forEach(variable => {
        customVariables[variable.name] = variable.defaultValue || '';
      });

      this.formData.customVariables = customVariables;
    },

    // 选择问题
    selectQuestion(index) {
      this.currentQuestionIndex = index;
      // 清除当前选中的会话
      this.currentSessionIndex = -1;

      const question = this.questions[index];

      // 填充表单
      this.formData.question = question.question;

      // 显示结果
      this.resultData = {
        ...question.result,
        textInput: question.question,
        customVariables: this.formData.customVariables || {},
        hasImage: this.formData.imageFileList && this.formData.imageFileList.length > 0,
        hasFile: this.formData.fileList && this.formData.fileList.length > 0
      };

      this.hasResult = true;
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.formData.fileList = [];
      this.initCustomVariablesForm();
    },

    // 处理图片变化
    handleImageChange(file, fileList) {
      this.formData.imageFileList = fileList;
      // 如果是新上传的文件，自动上传到OSS
      if (file.status === 'ready') {
        this.uploadFileToOss(file.raw, 'image');
      }
    },

    // 处理文件变化
    handleFileChange(file, fileList) {
      this.formData.fileList = fileList;
      // 如果是新上传的文件，自动上传到OSS
      if (file.status === 'ready') {
        this.uploadFileToOss(file.raw, 'file');
      }
    },

    // 上传文件到OSS
    async uploadFileToOss(file, type) {
      if (!file) return;

      try {
        const uploadParams = {
          file: file,
          fileType: type === 'image' ? 'image' : 'file',
          folder: 'workflow_inputs'
        };

        this.$set(file, 'status', 'uploading');
        const uploadResult = await api.oss.upload(uploadParams);

        if (uploadResult && uploadResult.data && uploadResult.data.fileUrl) {
          this.$set(file, 'status', 'success');
          this.$set(file, 'url', uploadResult.data.fileUrl);
          this.$message.success(`${type === 'image' ? '图片' : '文件'}上传成功!`);
          return uploadResult.data.fileUrl;
        } else {
          this.$set(file, 'status', 'error');
          throw new Error('上传成功但未返回有效URL');
        }
      } catch (error) {
        this.$set(file, 'status', 'error');
        console.error(`${type === 'image' ? '图片' : '文件'}上传失败:`, error);
        this.$showFriendlyError(error, `${type === 'image' ? '图片' : '文件'}上传失败，请重试`);
        return null;
      }
    },

    // 根据节点类型获取图标
    getNodeIcon(nodeType) {
      switch(nodeType) {
        case '开始':
          return 'el-icon-s-promotion';
        case '固定节点':
          return 'el-icon-notebook-1';
        case '插件':
          return 'el-icon-s-tools';
        case '结束':
          return 'el-icon-success';
        default:
          return 'el-icon-more';
      }
    },

    // 执行工作流
    executeWorkflow() {
      // 使用Element UI的表单验证
      this.$refs.formRef.validate(valid => {
        if (!valid) {
          return;
        }

        this.loading = true;

        // 使用已有会话ID
        const sessionId = this.sessionStatus.sessionId;

        // 构建请求参数
        const params = {
          sessionId: sessionId,
          flowDetailType: "WorkFlow",
          flowId: this.workflowId,
          textInput: this.formData.question || '',
          imageInput: '',
          fileInput: '',
          customVariables: this.formData.customVariables || {}
        };

        // 如果有图片上传，处理图片URL
        if (this.formData.imageFileList && this.formData.imageFileList.length > 0) {
          const imageFile = this.formData.imageFileList[0];
          if (imageFile.url) {
            params.imageInput = imageFile.url;
          } else if (imageFile.response && imageFile.response.data && imageFile.response.data.fileUrl) {
            params.imageInput = imageFile.response.data.fileUrl;
          }
        }

        // 如果有文件上传，处理文件URL
        if (this.formData.fileList && this.formData.fileList.length > 0) {
          const file = this.formData.fileList[0];
          if (file.url) {
            params.fileInput = file.url;
          } else if (file.response && file.response.data && file.response.data.fileUrl) {
            params.fileInput = file.response.data.fileUrl;
          }
        }

        console.log('执行工作流参数:', params);

        // 实际API调用
        api.workflow.execute(params)
          .then(result => {
            this.loading = false;

            if (result && result.code === 200 && result.data) {
              // 设置hasResult为true，显示结果区域
              this.hasResult = true;

              // 解析返回结果 - 使用格式化函数处理output
              this.resultData = {
                status: 'success',
                time: new Date().toLocaleString(),
                message: formatOutputContent(result.data.output) || '执行成功',
                logs: JSON.stringify(result.data, null, 2) || '',
                textInput: this.formData.question,
                customVariables: {...this.formData.customVariables},
                hasImage: this.formData.imageFileList && this.formData.imageFileList.length > 0,
                hasFile: this.formData.fileList && this.formData.fileList.length > 0
              };

              // 添加到问题列表
              this.questions.unshift({
                question: this.formData.question,
                time: new Date().toLocaleString(),
                result: {...this.resultData}
              });

              this.currentQuestionIndex = 0;
              // 清除会话选择，避免再次触发选择事件
              this.currentSessionIndex = -1;

              // 滚动到结果顶部
              this.$nextTick(() => {
                if (this.$refs.resultContainer) {
                  this.$refs.resultContainer.scrollTop = 0;
                }
              });
            } else {
              this.$showFriendlyError(null, result?.message || '执行工作流失败');
            }
          })
          .catch(error => {
            this.loading = false;
            console.error('执行工作流失败:', error);
            this.$showFriendlyError(error, '执行工作流失败，请重试');
          });
      });
    },

    // 刷新结果
    refreshResult() {
      if (!this.hasResult) {
        this.$message.info('请先执行工作流获取结果');
        return;
      }

      // 如果当前有选中的会话，重新获取该会话的结果
      if (this.currentSessionIndex >= 0 && this.sessionList.length > this.currentSessionIndex) {
        this.loading = true;
        const session = this.sessionList[this.currentSessionIndex];

        // 重新获取详细结果并显示
        this.fetchSessionDetail(session);
      } else {
        // 如果是通过executeWorkflow生成的结果，简单刷新处理
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.$message.success('刷新成功');
        }, 1000);
      }
    },

    // 获取自定义变量的显示名称
    getVariableDisplayName(name) {
      if (name === 'currentTime') return '';
      const variable = this.customVariables.find(v => v.name === name);
      return variable ? variable.displayName : name;
    },

    // 处理用户头像加载错误
    handleUserAvatarError(e) {
      e.target.src = 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png';
    },

    // 处理系统头像加载错误
    handleSystemAvatarError(e) {
      e.target.src = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png';
    }
  }
};
</script>

<style lang="scss" scoped>
.execute-workflow-page {
  height: calc(100vh - 40px);
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.loading-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.full-height {
  height: 100%;
}

.left-panel,
.form-panel,
.result-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    .el-tag {
      margin-left: 8px;
    }
  }

  .header-actions {
    display: flex;
    gap: 5px;
  }
}

.list-container,
.form-container,
.result-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

/* 搜索框样式 */
.search-box {
  padding: 0 0 10px 0;
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
}

/* 左侧问题列表样式 */
.chat-list-item {
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #ecf5ff;
    border-color: #409eff;
  }

  .chat-preview {
    display: flex;
    align-items: center;

    .chat-avatar {
      margin-right: 10px;
    }

    .chat-info {
      flex: 1;
      overflow: hidden;

      .chat-name {
        font-weight: 500;
        margin-bottom: 3px;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-last-message {
        color: #909399;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-app-info {
        margin-top: 5px;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;

        .el-tag {
          font-size: 10px;
          height: 20px;
          line-height: 18px;
          padding: 0 5px;
        }
      }
    }

    .chat-time {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
    }
  }
}

.empty-list,
.empty-result {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;

  i {
    font-size: 32px;
    margin-bottom: 10px;
  }

  p {
    font-size: 14px;
  }
}

/* 表单区域样式 */
.form-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位，作为表单底部按钮的参考 */
}

.form-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  padding-bottom: 70px; /* 为底部按钮腾出空间 */
}

/* 表单底部按钮区域 */
.form-bottom-actions {
  position: absolute; /* 绝对定位使其吸底 */
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 10; /* 确保按钮在内容之上 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-right: auto; /* 将按钮推到左侧 */
}

.execute-btn {
  min-width: 240px;
  height: 40px;
  padding: 0 20px;
  font-size: 16px;
  border-radius: 4px;
}

/* 结果区域样式 */
.result-container {
  display: flex;
  flex-direction: column;
  padding: 0;
}

.result-time {
  text-align: right;
  padding: 8px 15px;
  color: #909399;
  font-size: 12px;
}

.chat-message {
  display: flex;
  margin-bottom: 20px;
  padding: 0 15px;
}

.user-message {
  flex-direction: row-reverse;
}

.system-message {
  flex-direction: row;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  max-width: calc(100% - 60px);
  margin: 0 10px;
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-text {
  padding: 10px 12px;
  border-radius: 4px;
  word-break: break-word;
  line-height: 1.5;
}

.user-message .message-text {
  background-color: #ecf5ff;
  color: #303133;
}

.system-message .message-text {
  background-color: #f5f7fa;
  color: #303133;
}

.message-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.message-flow {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.flow-node {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.node-icon {
  width: 24px;
  height: 24px;
  background-color: #f5f7fa;
  color: #909399;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-active {
  background-color: #409EFF;
  color: white;
}

.node-connector {
  width: 20px;
  height: 2px;
  background-color: #dcdfe6;
  margin: 0 2px;
}

.empty-result {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;

  i {
    font-size: 32px;
    margin-bottom: 10px;
  }

  p {
    font-size: 14px;
  }
}

.user-info-card {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: #eef5ff;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  color: #333;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  max-width: 200px;
  z-index: 5;
}

.custom-variables {
  margin-top: 10px;
}

.variable-item {
  margin-bottom: 5px;
}

.variable-name {
  font-weight: 500;
  margin-right: 5px;
}

.variable-value {
  word-break: break-all;
}

.uploaded-files {
  margin-top: 10px;
  color: #409EFF;
  font-size: 13px;
}

/* 简化模式样式 */
.simple-mode {
  .form-container {
    padding: 20px;
  }

  .el-form-item {
    margin-bottom: 0;
  }

  .form-bottom-actions {
    justify-content: flex-end;
  }
}

/* Markdown 格式化样式 */
:deep(.markdown-content) {
  line-height: 1.6;
  color: #333;

  .md-title {
    position: relative;
    margin-bottom: 12px;
    font-weight: 600;
    color: #2c3e50;
    padding-bottom: 6px;
    border-bottom: 1px solid #eaecef;

    .md-title-icon {
      opacity: 0.5;
      margin-right: 6px;
      font-weight: 400;
    }

    &:hover .md-title-icon {
      opacity: 0.8;
    }
  }

  .md-h1 {
    font-size: 22px;
    margin-top: 20px;
  }

  .md-h2 {
    font-size: 18px;
    margin-top: 16px;
  }

  .md-h3 {
    font-size: 16px;
    margin-top: 14px;
    border-bottom: none;
  }

  .md-list {
    padding-left: 22px;
    margin: 12px 0;

    &.md-list-ordered {
      list-style-type: decimal;
    }
  }

  .md-list-item {
    margin-bottom: 8px;
  }

  .inline-code {
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    padding: 2px 5px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 90%;
    color: #476582;
  }

  .code-container {
    margin: 14px 0;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .code-header {
      background: #f1f1f1;
      padding: 8px 12px;
      font-size: 12px;
      font-weight: 500;
      color: #666;
      border-bottom: 1px solid #ddd;
    }
  }

  pre.code-block {
    background-color: #f6f8fa;
    padding: 16px;
    margin: 0;
    overflow-x: auto;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;

    code {
      background: transparent;
      padding: 0;
      font-size: inherit;
    }
  }

  .md-blockquote {
    border-left: 4px solid #42b983;
    padding: 10px 15px;
    margin: 12px 0;
    background-color: rgba(66, 185, 131, 0.05);
    color: #454d64;
    font-style: italic;
  }

  .md-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
    margin: 20px 0;
  }

  .md-link {
    color: #3eaf7c;
    font-weight: 500;
    text-decoration: none;
    position: relative;

    &:hover {
      text-decoration: underline;
    }

    &:after {
      content: "↗";
      font-size: 80%;
      position: relative;
      top: -3px;
      opacity: 0.5;
    }
  }

  .md-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    overflow-x: auto;
    display: block;

    td {
      border: 1px solid #ddd;
      padding: 8px 12px;
    }

    tr:nth-child(1) td {
      font-weight: 600;
      background-color: #f5f7f9;
    }

    tr:nth-child(even) {
      background-color: #fafafa;
    }
  }

  strong {
    font-weight: 600;
    color: #273849;
  }

  em {
    color: #555;
  }

  strong em, em strong {
    color: #34495e;
  }
}

/* 添加列表加载中样式 */
.loading-list {
  padding: 10px;
}
</style>
