<template>
  <div class="create-workflow-page">
    <router-view v-if="$route.params.workflowId"></router-view>
    <div class="page-content" v-else>
      <div
        v-if="isLoading"
        class="loading-container"
        v-loading="isLoading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="加载工作流中..."
      ></div>
      <div v-else class="workflow-container">
        <!-- 工作流卡片列表 -->
        <div
          v-for="(workflow, index) in workflowList"
          :key="workflow.id"
          class="workflow-card"
          :style="{ '--card-index': index }"
          @click="handleSettingClick(workflow)"
        >
          <div class="workflow-card-header">
            <div class="workflow-info">
              <div class="workflow-avatar">
                <!-- 使用头像，参考应用卡片的实现 -->
                <el-avatar :size="44" :src="workflow.profilePhoto">{{
                  workflow.name.charAt(0)
                }}</el-avatar>
              </div>
              <div class="workflow-title">
                <!-- 确认API返回字段名 -->
                <h3 :title="workflow.name">{{ workflow.name }}</h3>
              </div>
            </div>
            <div class="workflow-actions">
              <div class="action-icon" @click.stop="handleDelete(workflow)">
                <i class="el-icon-delete"></i>
              </div>
              <div class="action-icon" @click.stop="handleCopy(workflow)">
                <i class="el-icon-document-copy"></i>
              </div>
              <div
                class="action-icon"
                @click.stop="handleSettingClick(workflow)"
              >
                <i class="el-icon-setting"></i>
              </div>
            </div>
          </div>
          <!-- 确认API返回字段名 -->
          <p class="workflow-desc">{{ workflow.description }}</p>
          <div class="workflow-footer">
            <div
              class="workflow-chat-btn"
              @click.stop="handleStartChat(workflow)"
            >
              <span>开始对话</span>
            </div>
          </div>
        </div>

        <!-- 创建工作流卡片 - 这个卡片将始终显示 (加载完成后) -->
        <div class="create-card" @click="handleCreateWorkflow">
          <i class="el-icon-plus"></i>
          <span>创建工作流</span>
        </div>
      </div>
    </div>

    <!-- 创建工作流弹窗 -->
    <el-dialog
      title="创建工作流"
      :visible.sync="createDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      center
    >
      <el-form
        :model="createForm"
        :rules="createRules"
        ref="createFormRef"
        label-width="100px"
      >
        <el-form-item label="工作流名称" prop="name">
          <el-input
            v-model="createForm.name"
            placeholder="请输入工作流名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="工作流描述" prop="description">
          <el-input
            type="textarea"
            v-model="createForm.description"
            placeholder="请输入工作流描述"
            rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="工作流头像" prop="avatar">
          <div class="avatar-uploader-container">
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="handleAvatarChange"
              :disabled="isAvatarUploading"
            >
              <div
                v-loading="isAvatarUploading"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(255, 255, 255, 0.7)"
                style="
                  width: 100%;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <img
                  v-if="createForm.avatarUrl && !isAvatarUploading"
                  :src="createForm.avatarUrl"
                  class="avatar"
                />
                <i
                  v-else-if="!isAvatarUploading"
                  class="el-icon-plus avatar-uploader-icon"
                ></i>
              </div>
            </el-upload>
            <el-button
              @click="handleAIGenerateAvatar"
              :disabled="isAvatarUploading"
              style="margin-left: 10px"
              >AI生成</el-button
            >
          </div>
          <div v-if="createForm.profilePhotoUrl" class="upload-success-tip">
            上传成功
          </div>
        </el-form-item>
        <el-form-item label="工作流分类" prop="category">
          <el-select
            v-model="createForm.category"
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option
              v-for="item in applicationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCreateDialog">取消</el-button>
        <el-button
          type="primary"
          @click="submitCreateWorkflow"
          :loading="createLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"; // 导入API
import { EnumApplicationType } from "@/utils/enums"; // 导入应用类型枚举

export default {
  name: "CreateWorkflowPage",
  data() {
    return {
      isLoading: false,
      workflowList: [],
      config: {
        editUrl: "", // 确认此URL
      },
      // 应用类型选项
      applicationTypeOptions: EnumApplicationType,
      // 创建弹窗相关数据
      createDialogVisible: false,
      createLoading: false,
      isAvatarUploading: false,
      createForm: {
        name: "",
        description: "",
        avatar: null, // 存储原始 File 对象
        avatarUrl: "", // 用于本地预览
        profilePhotoUrl: "", // 存储最终的OSS URL
        category: "", // 将存储 "智能客服" 或 "默认分类" 等字符串
      },
      createRules: {
        name: [
          { required: true, message: "请输入工作流名称", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入工作流描述", trigger: "blur" },
        ],
        avatar: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.createForm.profilePhotoUrl) {
                if (this.isAvatarUploading) {
                  callback(new Error("头像上传中..."));
                } else if (!this.createForm.avatar) {
                  callback(new Error("请上传工作流头像")); // No file selected
                } else {
                  callback(new Error("头像上传失败或无效，请重试"));
                }
              } else {
                callback(); // URL exists, validation passes
              }
            },
            trigger: "change", // Validate on change (after upload attempt)
          },
        ],
        category: [
          { required: true, message: "请选择分类", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.config.editUrl = process.env.VUE_APP_BASE_WORKFLOW;
    // 如果不是详情/编辑页，则加载列表
    if (!this.$route.params.id && !this.$route.params.workflowId) {
      this.fetchWorkflowList();
    }
  },
  methods: {
    async fetchWorkflowList() {
      this.isLoading = true;
      try {
        let params = {
          skipCount: 0,
          maxResultCount: 100,
        };
        // 调用API获取列表，假设方法为 getList
        const result = await api.workflow.getList(params);
        if (result && result.code === 200 && result.data) {
          // 使用API返回的items，注意字段名确认
          this.workflowList = result.data.items || [];
        } else {
          this.workflowList = []; // 清空以防显示错误数据
        }
      } catch (error) {
        console.error("获取工作流列表失败:", error);
        this.workflowList = []; // 出错时清空
      } finally {
        this.isLoading = false;
      }
    },

    // --- 创建弹窗相关方法 ---
    openCreateDialog() {
      this.resetCreateForm();
      this.createDialogVisible = true;
    },
    closeCreateDialog() {
      this.createDialogVisible = false;
    },
    resetCreateForm() {
      this.createForm = {
        name: "",
        description: "",
        avatar: null,
        avatarUrl: "",
        profilePhotoUrl: "",
        category: "",
      };
      this.$nextTick(() => {
        if (this.$refs.createFormRef) {
          this.$refs.createFormRef.clearValidate();
        }
      });
    },
    async handleAvatarChange(file) {
      // 1. Basic Validation (size, potentially type)
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$showFriendlyError(null, "上传头像图片大小不能超过 2MB!");
        return false;
      }
      // Add other type checks if necessary

      // 2. Prepare for upload
      this.isAvatarUploading = true;
      this.createForm.avatar = file.raw; // Store the file
      this.createForm.avatarUrl = URL.createObjectURL(file.raw); // Show local preview immediately
      this.createForm.profilePhotoUrl = ""; // Reset previous URL
      this.$refs.createFormRef.validateField("avatar"); // Trigger validation (will likely show 'uploading')

      // 3. Perform Upload
      try {
        const uploadParams = {
          file: file.raw,
          fileType: "image",
          folder: "workflow_avatars",
        };
        console.log("开始上传头像，参数:", uploadParams);
        const uploadResult = await api.oss.upload(uploadParams);

        if (uploadResult && uploadResult.data && uploadResult.data.fileUrl) {
          // 4. Handle Success
          this.createForm.profilePhotoUrl = uploadResult.data.fileUrl; // Store the final URL
          this.createForm.avatarUrl = this.createForm.profilePhotoUrl; // Update preview to final URL
          this.$message.success("头像上传成功!");
          console.log("头像上传成功，URL:", this.createForm.profilePhotoUrl);
        } else {
          // 5. Handle Upload API Success but missing URL
          throw new Error("上传成功但未返回有效URL");
        }
      } catch (uploadError) {
        // 6. Handle Failure
        console.error("头像上传失败:", uploadError);
        this.$showFriendlyError(null, 
          "头像上传失败: " + (uploadError.message || "未知错误")
        );
        this.createForm.profilePhotoUrl = ""; // Clear URL on failure
        // Keep avatarUrl showing the preview of the failed file, or clear it:
        // this.createForm.avatarUrl = "";
        // this.createForm.avatar = null;
      } finally {
        // 7. Finalize upload state
        this.isAvatarUploading = false;
        // Trigger validation again to reflect final state (success/fail)
        this.$refs.createFormRef.validateField("avatar");
      }
    },
    handleAIGenerateAvatar() {
      if (this.isAvatarUploading) return;
      // TODO: Implement AI generation, on success, set:
      // this.createForm.profilePhotoUrl = ai_generated_url;
      // this.createForm.avatarUrl = ai_generated_url;
      // this.createForm.avatar = null; // Clear file object if AI generated
      // this.$refs.createFormRef.validateField('avatar');
      this.$message.info("AI生成功能待实现");
    },
    async submitCreateWorkflow() {
      // Validate the whole form first
      this.$refs.createFormRef.validate(async (valid) => {
        if (valid) {
          // Double-check if avatar upload is somehow still in progress (shouldn't happen if validation passes)
          if (this.isAvatarUploading) {
            this.$message.warning("头像仍在上传中，请稍候...");
            return;
          }
          // The 'avatar' validator should have ensured profilePhotoUrl exists
          if (!this.createForm.profilePhotoUrl) {
            this.$showFriendlyError(null, 
              "获取头像URL失败，无法创建工作流，请重新上传头像。"
            );
            return;
          }

          this.createLoading = true;
          try {
            // Build payload - No upload needed here anymore
            const createPayload = {
              name: this.createForm.name,
              description: this.createForm.description,
              profilePhoto: this.createForm.profilePhotoUrl, // Use the stored URL
              applicationType: this.createForm.category,
            };
            console.log("创建工作流，发送数据:", createPayload);

            // Call create workflow API
            const createResult = await api.workflow.create(createPayload);

            if (createResult && createResult.code === 200) {
              this.$message.success("工作流创建成功!");
              this.closeCreateDialog();
              await this.fetchWorkflowList();
            } else {
              this.$showFriendlyError(null, createResult?.message || "创建工作流失败");
            }
          } catch (error) {
            console.error("创建工作流API调用失败:", error);
          } finally {
            this.createLoading = false;
          }
        } else {
          console.log("表单校验失败");
          // Check if failure is due to avatar upload still pending
          if (
            this.createForm.avatar &&
            !this.createForm.profilePhotoUrl &&
            !this.isAvatarUploading
          ) {
            this.$message.warning("请等待头像上传完成或重新上传头像后再提交。");
          } else if (
            !this.createForm.avatar &&
            !this.createForm.profilePhotoUrl
          ) {
            this.$message.warning("请先上传头像。");
          }
          return false;
        }
      });
    },
    // --- 卡片操作方法 ---
    handleSettingClick(workflow) {
      const url = new URL(this.config.editUrl);
      url.searchParams.append("type", "edit");
      url.searchParams.append("flowId", workflow.id);
      url.searchParams.append("tenantId", localStorage.getItem("tenantId"));

      // 从 localStorage 获取 token
      const token = localStorage.getItem("token");
      if (token) {
        // 如果 token 存在，则添加到 URL 查询参数中
        url.searchParams.append("token", token);
      } else {
        // 可选：如果 token 不存在，可以给出提示或采取其他措施
        console.warn(
          "Token not found in localStorage. Redirecting without token."
        );
        // 例如，可以阻止跳转并提示用户重新登录
        // this.$showFriendlyError(null, '请先登录！');
        // return;
      }

      window.open(url.toString(), "_blank");
    },
    async handleDelete(workflow) {
      // Confirm deletion with the user
      this.$confirm("确认删除该工作流吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          // User confirmed
          try {
            console.log("准备删除工作流 ID:", workflow.id); // Keep for debugging

            // Call the actual delete API
            const deleteResult = await api.workflow.delete(workflow.id);

            // Our request interceptor handles checking isSuccess/code.
            // If the promise resolves here, it means the API call was successful (2xx status)
            // and the interceptor deemed it a success based on the response body (isSuccess/code).
            // The interceptor would throw an error for non-2xx or business errors (isSuccess false / non-200 code).

            this.$message.success(deleteResult?.message || "删除成功!"); // Use message from API if available
            await this.fetchWorkflowList(); // Refresh the list on success
          } catch (error) {
            // Error handling is mostly done by the interceptor (showing message)
            // Log the error here for debugging purposes
            console.error("删除工作流失败 (handleDelete catch block):", error);
            // Optionally, show a generic message if the interceptor didn't
            // if (!error.handledByInterceptor) { // hypothetical flag
            //   this.$showFriendlyError(null, '删除操作失败');
            // }
          }
        })
        .catch(() => {
          // User clicked cancel
          this.$message.info("已取消删除");
        });
    },
    async handleCopy(workflow) {
      console.log("准备复制工作流:", workflow);
      // Show loading state if needed, e.g., on the copied card or globally
      // this.isLoading = true; // Or add a specific loading state for copy

      this.$confirm("确认复制该工作流吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(async () => {
          try {
            console.log("准备复制工作流 ID:", workflow.id);

            // 调用新的复制API
            const copyResult = await api.workflow.copy(workflow.id);

            // 处理响应结果
            this.$message.success(copyResult?.message || "复制成功!");
            await this.fetchWorkflowList(); // 刷新列表
          } catch (error) {
            console.error("复制工作流失败 (handleCopy catch block):", error);
            // 错误信息由拦截器处理
          } finally {
            // Reset loading state if used
            // this.isLoading = false;
          }
        })
        .catch(() => {
          // User clicked cancel
          this.$message.info("已取消复制");
        });
    },
    handleStartChat(workflow) {
      // 创建聊天会话并跳转到临时聊天页面
      this.startChat(workflow);
    },
    handleCreateWorkflow() {
      this.openCreateDialog();
      // const url = new URL(this.config.editUrl);
      // url.searchParams.append('type', 'add');
      // window.open(url.toString(), '_blank');
    },
    // 开始聊天
    async startChat(workflow) {
      try {
        // 设置loading状态（如果需要可以添加loading状态管理）

        // 调用创建SignalR通道的接口
        const result = await this.createSignalRChannel(workflow.id);

        this.$message.success("聊天会话创建成功，正在打开聊天窗口...");

        // 接口成功后进行页面跳转，传递会话ID
        const sessionId = result.sessionData.sessionId;
        // 使用路由跳转到/access/temporary-chat，并在新标签页打开
        const routeUrl = this.$router.resolve({
          path: "/access/temporary-chat",
          query: {
            id: workflow.id,
            sourceType: 2, // 工作流类型使用2
            sessionId: sessionId,
          },
        });
        window.open(routeUrl.href, "_blank");
      } catch (error) {
        console.error("创建聊天通道失败:", error);
        this.$showFriendlyError(error, "创建聊天通道失败，请重试");
      }
    },

    // 创建SignalR通道的接口
    async createSignalRChannel(workflowId) {
      try {
        // 创建聊天会话
        const sessionResponse = await this.createChatSession(workflowId);

        if (!sessionResponse.isSuccess) {
          throw new Error(sessionResponse.message || "创建聊天会话失败");
        }

        const sessionData = sessionResponse.data;

        // 返回会话信息，用于跳转时传递
        return {
          success: true,
          sessionData: sessionData,
          message: "聊天会话创建成功",
        };
      } catch (error) {
        console.error("创建聊天会话失败:", error);
        throw error;
      }
    },

    // 创建聊天会话的接口
    async createChatSession(workflowId) {
      try {
        const response = await api.chat.createSession({
          clientId: workflowId,
          sourceType: 2, // 2: 工作流
          sessionName: `工作流${workflowId}的聊天会话`,
          isTestSession: true,
        });

        return response;
      } catch (error) {
        console.error("创建聊天会话失败:", error);
        return {
          isSuccess: false,
          message:
            error.response?.data?.message ||
            error.message ||
            "创建聊天会话失败",
        };
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.create-workflow-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 20px);
  border-radius: 12px 0 0 12px;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.workflow-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.workflow-card {
  position: relative;
  width: 100%;
  height: 195px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  border: 1px solid #eaeaea;
  box-sizing: border-box;
  padding: 16px 20px;
  margin-bottom: 0;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
  transform-origin: center;

  &:hover {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }

  .workflow-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .workflow-info {
      display: flex;
      align-items: center;

      .workflow-avatar {
        margin-right: 12px;

        .el-avatar {
          width: 44px;
          height: 44px;
          font-size: 16px;
        }
      }

      .workflow-title {
        h3 {
          margin: 0;
          font-size: 16px;
          line-height: 1.5;
          color: #1f2937;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }

        .workflow-code {
          margin: 4px 0 0;
          font-size: 13px;
          color: #6b7280;
        }
      }
    }

    .workflow-actions {
      display: flex;
      gap: 12px;

      .action-icon {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.2s;

        &:hover {
          opacity: 0.8;
        }

        i {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }

  .workflow-desc {
    margin: 0 0 16px;
    font-size: 14px;
    color: #4b5563;
    line-height: 1.5;
    height: 42px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .workflow-footer {
    position: absolute;
    bottom: 16px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: flex-end;

    .workflow-chat-btn {
      flex-shrink: 0;
      border: 1px solid rgba(33, 135, 250, 1);
      border-radius: 4px;
      height: 28px;
      width: 80px;
      position: relative;
      cursor: pointer;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background-color: #ffffff;
      transition: all 0.3s ease;

      span {
        font-size: 12px;
        color: rgba(33, 135, 250, 1);
        font-weight: 500;
        white-space: nowrap;
        line-height: 12px;
        font-family: PingFangSC-Medium, PingFang SC, -apple-system,
          BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
          sans-serif;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: rgba(33, 135, 250, 1);

        span {
          color: #ffffff;
        }
      }
    }
  }
}

.create-card {
  position: relative;
  width: 100%;
  height: 195px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  border: 1px solid #eaeaea;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: 0.3s;
  opacity: 0;

  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    background: var(--el-color-primary-light-9);

    i,
    span {
      color: var(--el-color-primary);
    }

    i {
      transform: rotate(90deg);
    }
  }

  &:active {
    transform: scale(0.98);
  }

  i {
    font-size: 32px;
    margin-bottom: 12px;
    color: #9ca3af;
    transition: all 0.3s ease;
  }

  span {
    font-size: 16px;
    font-weight: 500;
    color: #9ca3af;
    transition: color 0.3s ease;
  }
}

.avatar-uploader-container {
  display: flex;
  align-items: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 80px;
  height: 80px;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}

.avatar {
  width: 80px;
  height: 80px;
  display: block;
  object-fit: cover;
}

.upload-success-tip {
  color: #67c23a;
  margin-top: 5px;
  font-size: 12px;
}

.loading-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 120px;
    font-size: 14px;
  }
}

:deep(.el-avatar > img) {
  width: 100%;
  height: 100%;
}
</style>
