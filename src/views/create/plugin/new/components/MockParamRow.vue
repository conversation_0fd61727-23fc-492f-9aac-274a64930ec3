<!-- 递归组件：MockParamRow.vue -->
<template>
  <div>
    <div class="params-row child-row" :style="{ paddingLeft: `${depth * 20}px` }">
      <div class="col-required" v-if="showRequired">
        <el-checkbox
          :value="param.required"
          @input="updateParam('required', $event)"
        ></el-checkbox>
      </div>
      <div class="col-name">
        <div class="param-name-wrapper">
          <div v-if="depth > 0" class="indent-line"></div>
          <el-button
            v-if="canHaveChildren"
            type="text"
            class="expand-btn"
            @click="toggleExpand"
          >
            <i :class="[
              'el-icon-arrow-right',
              { 'is-expanded': param.isExpanded }
            ]"></i>
          </el-button>
          <el-input
            :value="param.name"
            @input="updateParam('name', $event)"
            placeholder="请输入参数名称"
          />
        </div>
      </div>
      <div class="col-type" v-if="!hideType">
        <el-select
          :value="param.type"
          @change="handleTypeChange"
          placeholder="请选择"
        >
          <el-option label="字符串" :value="1" />
          <el-option label="整数" :value="2" />
          <el-option label="布尔值" :value="3" />
          <el-option label="对象" :value="4" />
          <el-option label="浮点数" :value="5" />
          <el-option
            label="数组"
            :value="6"
            :disabled="isParentArray"
          />
        </el-select>
      </div>
      <div class="col-desc">
        <el-input
          :value="param.description"
          @input="updateParam('description', $event)"
          placeholder="请输入描述"
        />
      </div>
      <div class="col-default" v-if="showDefault && !hasChildren">
        <el-input
          :value="param.defaultValue"
          @input="updateParam('defaultValue', $event)"
          placeholder="请输入默认值"
        />
      </div>
      <div class="col-action">
        <el-button
          v-if="canHaveChildren"
          type="text"
          @click="addChild"
          style="margin-right: 8px;"
        >
          <i class="el-icon-plus"></i>
        </el-button>
        <el-button type="text" @click="function() { $emit('remove') }" class="delete-btn">
          <i class="el-icon-delete"></i>
        </el-button>
      </div>
    </div>

    <!-- 递归渲染子参数 -->
    <template v-if="canHaveChildren && param.children && param.children.length > 0 && param.isExpanded">
      <mock-param-row
        v-for="(child, index) in param.children"
        :key="index"
        :param="child"
        :depth="depth + 1"
        :show-required="showRequired"
        :show-default="showDefault"
        :hide-type="hideType"
        :is-parent-array="param.type === '6' || param.type === 6"
        @remove="removeChild(index)"
        @update="updateChild(index, $event)"
        @add-child="addChildToChild(index)"
        @toggle-expand="toggleChildExpand(index)"
      />
    </template>
  </div>
</template>

<script>
export default {
  name: 'MockParamRow',
  props: {
    param: {
      type: Object,
      required: true
    },
    depth: {
      type: Number,
      default: 0
    },
    showRequired: {
      type: Boolean,
      default: false
    },
    showDefault: {
      type: Boolean,
      default: false
    },
    hideType: {
      type: Boolean,
      default: false
    },
    isParentArray: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    canHaveChildren() {
      // 同时支持字符串类型的'4','6'和数字类型的4,6
      const type = this.param.type
      return ['4', '6', 4, 6].includes(type)
    },
    hasChildren() {
      return this.param.children && this.param.children.length > 0
    }
  },
  methods: {
    updateParam(key, value) {
      if (key === 'type' && ['4', '6', 4, 6].includes(value)) {
        this.$emit('update', {
          key,
          value,
          extra: {
            children: [],
            isExpanded: true,
            defaultValue: ''
          }
        })
      } else {
        this.$emit('update', { key, value })
      }
    },
    toggleExpand() {
      this.$emit('toggle-expand')
    },
    handleTypeChange(type) {
      this.$emit('update', {
        key: 'type',
        value: type,
        extra: ['4', '6', 4, 6].includes(type) ? {
          children: [],
          isExpanded: true,
          defaultValue: ''
        } : null
      })
    },
    addChild() {
      this.$emit('add-child')
    },
    removeChild(index) {
      const newChildren = [...this.param.children]
      newChildren.splice(index, 1)
      this.$emit('update', { key: 'children', value: newChildren })
    },
    updateChild(index, { key, value, extra }) {
      const newChildren = [...this.param.children]
      if (extra) {
        newChildren[index] = { ...newChildren[index], [key]: value, ...extra }
      } else {
        newChildren[index] = { ...newChildren[index], [key]: value }
      }
      this.$emit('update', { key: 'children', value: newChildren })
    },
    addChildToChild(index) {
      const newChildren = [...this.param.children]
      if (!newChildren[index].children) {
        newChildren[index].children = []
      }
      newChildren[index].children.push({
        name: '',
        type: 1, // 1 对应字符串类型
        description: '',
        children: [],
        isExpanded: true
      })
      this.$emit('update', { key: 'children', value: newChildren })
    },
    toggleChildExpand(index) {
      const newChildren = [...this.param.children]
      newChildren[index] = {
        ...newChildren[index],
        isExpanded: !newChildren[index].isExpanded
      }
      this.$emit('update', { key: 'children', value: newChildren })
    }
  }
}
</script>

<style lang="scss" scoped>
.params-row {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  &.child-row {
    background-color: #fafafa;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .col-required {
    width: 60px;
    text-align: center;
  }

  .col-name {
    width: 200px;
    padding: 0 8px;
  }

  .col-type {
    width: 120px;
    padding: 0 8px;
  }

  .col-desc {
    flex: 1;
    padding: 0 8px;
  }

  .col-default {
    width: 160px;
    padding: 0 8px;
  }

  .col-action {
    width: 60px;
    text-align: center;

    .delete-btn {
      color: #f56c6c;
    }
  }

  .param-name-wrapper {
    display: flex;
    align-items: center;
    width: 100%;

    .expand-btn {
      padding: 0;
      width: 20px;
      margin-right: 4px;

      i {
        transition: transform 0.3s;
        font-size: 14px;

        &.is-expanded {
          transform: rotate(90deg);
        }
      }
    }

    .indent-line {
      width: 16px;
      height: 1px;
      background-color: #dcdfe6;
      margin: 0 4px;
    }
  }
}
</style>
