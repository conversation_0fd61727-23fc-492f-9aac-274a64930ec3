<template>
  <div class="create-plugin-page">
    <div class="page-header">
      <h2>{{ pageTitle }}</h2>
      <div class="header-actions">
        <el-button @click="handleCancel">{{ isView ? '返回' : '取消' }}</el-button>
        <el-button v-if="showSaveButton" type="primary" @click="handleSave">保存</el-button>
      </div>
    </div>

    <!-- 插件类型切换 -->
    <div class="plugin-type-tabs">
      <el-radio-group v-model="pluginType" @change="handlePluginTypeChange" size="small">
        <el-radio-button label="HTTP" v-if="!isView">HTTP 插件</el-radio-button>
        <el-radio-button label="MCP">MCP 插件</el-radio-button>
      </el-radio-group>
    </div>

    <el-form
      ref="pluginForm"
      :model="form"
      :rules="dynamicRules"
      label-width="120px"
      class="plugin-form"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-form-item label="插件名称" prop="name" required>
          <el-input v-model="form.name" placeholder="请输入插件名称" maxlength="20" show-word-limit :disabled="isFormDisabled" />
        </el-form-item>

        <el-form-item prop="description" required>
          <template #label>
            <span>插件描述</span>
            <el-tooltip class="item" effect="dark" content="插件描述用于插件的决策，请尽量清楚地描述插件的使用场景，即在什么情况下执行该插件。" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入插件描述"
            :rows="4"
            :disabled="isFormDisabled"
          />
        </el-form-item>

        <!-- 查看模式下显示插件价格 -->
        <el-form-item v-if="isView && form.pluginPoint" label="插件价格">
          {{ form.pluginPoint }}
        </el-form-item>

        <el-form-item label="插件头像" required>
          <el-upload
            v-if="!isFormDisabled"
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="handleAvatarUpload"
          >
            <img v-if="form.avatar" :src="form.avatar" class="avatar-image" />
            <div v-else class="upload-area">
              <i class="el-icon-plus"></i>
            </div>
          </el-upload>

          <!-- 查看模式的头像显示 -->
          <div v-if="isFormDisabled && form.avatar" class="avatar-display">
            <img :src="form.avatar" class="avatar-image" />
          </div>
          <div v-if="isFormDisabled && !form.avatar" class="no-avatar">
            <i class="el-icon-picture-outline"></i>
            <span>暂无头像</span>
          </div>

          <div v-if="!isFormDisabled" class="form-tip">支持 jpg、png 格式，大小不超过 2MB</div>
        </el-form-item>
      </el-card>

      <!-- HTTP 插件配置 -->
      <el-card class="form-card" v-if="pluginType === 'HTTP'">
        <template #header>
          <div class="card-header">
            <span>请求地址</span>
          </div>
        </template>

        <el-form-item label="请求方法" required>
          <el-select v-model="form.method" placeholder="请选择" style="width: 120px" :disabled="isFormDisabled">
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
          </el-select>
        </el-form-item>

        <el-form-item label="请求地址" prop="url" required>
          <el-input v-model="form.url" placeholder="请输入请求地址" @input="handleUrlInput" :disabled="isFormDisabled" />
        </el-form-item>
      </el-card>

      <!-- MCP 插件配置 -->
      <el-card class="form-card mcp-config-card" v-if="pluginType === 'MCP' && !isView">
        <template #header>
          <div class="card-header">
            <span>MCP Server 配置
              <el-tooltip class="item" effect="dark" content="输入MCP Server的JSON配置" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </div>
        </template>

        <div class="mcp-config-content">
          <div class="codemirror-container">
            <div class="editor-toolbar">
              <span class="editor-label">MCP Server JSON 配置</span>
              <div class="editor-actions" v-if="!isFormDisabled">
                <el-button @click="formatJsonCode" size="mini" type="text" icon="el-icon-magic-stick">
                  格式化
                </el-button>
                <el-button @click="validateJsonCode" size="mini" type="text" icon="el-icon-check">
                  验证
                </el-button>

              </div>
            </div>
            <div ref="codeMirrorEditor" class="codemirror-editor"></div>
          </div>
        </div>
      </el-card>

      <!-- 工具列表 - 仅 MCP 插件显示 -->
      <el-card class="form-card" v-if="pluginType === 'MCP'">
        <template #header>
          <div class="card-header">
            <span>工具列表
              <el-tooltip class="item" effect="dark" content="根据MCP Server配置获取的可用工具列表" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </div>
        </template>

        <div class="tools-container" v-loading="toolsLoading">
          <template v-if="mcpTools.length === 0">
            <div class="empty-tools">
              <i class="el-icon-box"></i>
              <p>暂无工具数据</p>
              <span v-if="!isView" class="empty-tip">请先配置MCP Server并保存插件，系统将自动获取工具列表</span>
            </div>
          </template>

          <template v-else>
            <div class="tools-list">
              <div
                v-for="(tool, index) in mcpTools"
                :key="index"
                class="tool-item"
                :class="{ 'is-expanded': tool.isExpanded }"
              >
                <div class="tool-header" @click="toggleToolExpand(index)">
                  <div class="tool-title">
                    <i class="el-icon-caret-right expand-icon" :class="{ 'is-expanded': tool.isExpanded }"></i>
                    <span class="tool-name">{{ tool.name }}</span>
                  </div>
                  <div class="tool-description">
                    {{ tool.description }}
                  </div>
                </div>

                <div class="tool-content" v-show="tool.isExpanded">
                  <div class="tool-details">
                    <!-- 输入参数展示 -->
                    <div class="detail-section" v-if="tool.inputSchema && tool.inputSchema.properties">
                      <div class="param-list">
                        <div
                          v-for="(param, paramName) in tool.inputSchema.properties"
                          :key="paramName"
                          class="param-item"
                        >
                          <div class="param-header">
                            <span class="param-name">{{ paramName }}</span>
                            <span class="param-required" v-if="isRequiredParam(tool.inputSchema, paramName)">*</span>
                          </div>
                          <div class="param-description">{{ param.description }}</div>
                          <div class="param-input">
                            <el-input
                              v-model="tool.paramValues[paramName]"
                              :placeholder="getParamPlaceholder(param)"
                              size="small"
                              style="width: 100%;"
                            />
                          </div>
                        </div>
                      </div>

                      <div class="action-buttons">
                        <el-button size="small" type="primary" @click="runTool(tool, index)">运行</el-button>
                      </div>

                      <!-- 运行结果展示 -->
                      <div class="result-section" v-if="tool.showResult">
                        <div class="result-container">
                          <div class="result-header">
                            <span class="result-label">运行结果：</span>
                            <div class="result-actions">
                              <el-button @click="copyResult(tool.result)" size="mini" type="text" icon="el-icon-copy-document">
                                复制
                              </el-button>
                              <el-button @click="clearResult(index)" size="mini" type="text" icon="el-icon-close">
                                清除
                              </el-button>
                            </div>
                          </div>
                          <div class="result-content">
                            <pre class="result-code">{{ tool.result || '暂无结果' }}</pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </el-card>

      <!-- 请求参数 - 仅 HTTP 插件显示 -->
      <el-card class="form-card" v-if="pluginType === 'HTTP'">
        <template #header>
          <div class="card-header">
            <span>请求参数</span>
          </div>
        </template>

        <div class="params-header">
          <el-radio-group v-model="activeParamsTab">
            <el-radio-button label="params">param</el-radio-button>
            <el-radio-button label="headers">headers</el-radio-button>
          </el-radio-group>
        </div>

        <div class="params-table" v-show="activeParamsTab === 'params'">
          <div class="params-row header">
            <div class="col-required">必填</div>
            <div class="col-name">参数名称</div>
            <div class="col-type">参数类型</div>
            <div class="col-desc">描述</div>
            <div class="col-default">默认值</div>
            <div class="col-action">操作</div>
          </div>

          <template v-for="(param, index) in form.params">
            <mock-param-row
              :key="index"
              :param="param"
              :depth="0"
              :show-required="true"
              :show-default="true"
              @remove="removeParam(index)"
              @update="updateParam(index, $event)"
              @add-child="addChildToParam(index)"
              @toggle-expand="toggleParamExpand(index)"
            />
          </template>

          <div class="add-param">
            <el-button type="primary" plain icon="el-icon-plus" @click="addParam">
              新增参数
            </el-button>
          </div>
        </div>

        <div class="params-table" v-show="activeParamsTab === 'headers'">
          <div class="params-row header">
            <div class="col-required">必填</div>
            <div class="col-name">参数名称</div>
            <div class="col-desc">描述</div>
            <div class="col-default">默认值</div>
            <div class="col-action">操作</div>
          </div>

          <template v-for="(header, index) in form.headers">
            <mock-param-row
              :key="index"
              :param="header"
              :depth="0"
              :show-required="true"
              :show-default="true"
              :hide-type="true"
              @remove="removeHeader(index)"
              @update="updateHeader(index, $event)"
              @add-child="addChildToHeader(index)"
              @toggle-expand="toggleHeaderExpand(index)"
            />
          </template>

          <div class="add-param">
            <el-button type="primary" plain icon="el-icon-plus" @click="addHeader">
              新增参数
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 响应结果 - 仅 HTTP 插件显示 -->
      <el-card class="form-card" v-if="pluginType === 'HTTP'">
        <template #header>
          <div class="card-header">
            <span>响应结果
              <el-tooltip class="item" effect="dark" content="填写接口响应的字段名和描述，帮助模型更好理解响应数据。可根据实际接口返回数据自动填写。" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          </div>
        </template>

        <el-form-item>
          <el-switch v-model="form.mockResponse" class="mock-switch" />
          <span class="mock-label">添加响应结果</span>
          <el-button type="text" class="mock-btn" @click="handlePreviewMock">自动生成</el-button>
        </el-form-item>

        <template v-if="form.mockResponse">
          <div class="params-table">
            <div class="params-row header">
              <div class="col-name">参数名</div>
              <div class="col-type">参数类型</div>
              <div class="col-desc">描述</div>
              <div class="col-action">操作</div>
            </div>

            <template v-for="(param, index) in form.mockParams">
              <mock-param-row
                :key="index"
                :param="param"
                :depth="0"
                @remove="removeMockParam(index)"
                @update="updateMockParam(index, $event)"
                @add-child="addChildToMockParam(index)"
                @toggle-expand="toggleMockParamExpand(index)"
              />
            </template>

            <div class="add-param">
              <el-button type="primary" plain icon="el-icon-plus" @click="addMockParam">
                新增参数
              </el-button>
            </div>
          </div>
        </template>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { api } from '@/api/request'
import { removeSpaces } from '@/utils/index'
import { json } from '@codemirror/lang-json'
import { EditorState } from '@codemirror/state'
import { placeholder } from '@codemirror/view'
import { EditorView, basicSetup } from 'codemirror'
import MockParamRow from './components/MockParamRow.vue'


export default {
  name: 'CreatePlugin',
  components: {
    MockParamRow
  },
  data() {
    return {
      pluginType: 'HTTP', // 插件类型：HTTP 或 MCP
      form: {
        name: '',
        description: '',
        avatar: '',
        method: 'GET',
        url: '',
        params: [],
        headers: [],
        mockResponse: false,
        mockParams: [],
        mcpConfig: '', // MCP Server 配置
        pluginPoint: '' // 插件价格
      },
      activeParamsTab: 'params',
      loading: false,
      toolsLoading: false,
      isEdit: false,
      isView: false, // 是否为查看模式
      codeMirrorView: null, // CodeMirror 编辑器实例
      mcpTools: [] // MCP 工具列表
    }
  },
  computed: {
    // 页面标题
    pageTitle() {
      if (this.isView) {
        return '查看插件'
      } else if (this.isEdit) {
        return '编辑插件'
      } else {
        return '创建插件'
      }
    },
    // 是否禁用表单
    isFormDisabled() {
      return this.isView
    },
    // 是否显示保存按钮
    showSaveButton() {
      return !this.isView
    },
    dynamicRules() {
      // 查看模式下不需要验证
      if (this.isView) {
        return {}
      }

      const baseRules = {
        name: [
          { required: true, message: '请输入插件名称', trigger: 'blur' },
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入插件描述', trigger: 'blur' },
          { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ]
      }

      if (this.pluginType === 'HTTP') {
        baseRules.url = [
          { required: true, message: '请输入请求地址', trigger: 'blur' }
        ]
      } else if (this.pluginType === 'MCP') {
        baseRules.mcpConfig = [
          { required: true, message: '请输入MCP Server配置', trigger: 'blur' }
        ]
      }

      return baseRules
    }
  },
  watch: {
    pluginType(newType) {
      if (newType === 'MCP') {
        this.$nextTick(() => {
          this.initCodeMirror()
        })
      }
    }
  },
  created() {
    // 检查当前路由模式
    const routePath = this.$route.path
    if (routePath.includes('/edit/')) {
      this.isEdit = true
      const pluginId = this.$route.params.id
      if (pluginId) {
        this.loadPluginData(pluginId)
      }
    } else if (routePath.includes('/view/')) {
      this.isView = true
      this.pluginType = 'MCP' // 查看模式固定为MCP插件
      const pluginId = this.$route.params.id
      if (pluginId) {
        this.loadPluginData(pluginId)
        this.loadMcpToolsAfterSave(pluginId)
      }
    }
  },
  mounted() {
    // 使用 nextTick 确保 DOM 已经渲染完成
    this.$nextTick(() => {
      // 仅在 MCP 模式下初始化编辑器
      if (this.pluginType === 'MCP') {
        this.initCodeMirror()
      }
    })
  },
  beforeDestroy() {
    // 清理 CodeMirror 实例
    if (this.codeMirrorView) {
      this.codeMirrorView.destroy()
    }
  },
  methods: {
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$showFriendlyError(null, '上传头像图片只能是图片格式!')
      }
      if (!isLt2M) {
        this.$showFriendlyError(null, '上传头像图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },
    async handleAvatarUpload(options) {
      const file = options.file
      try {
        // 构建上传参数，与工作流头像上传保持一致
        const uploadParams = {
          file: file,
          fileType: 'image',
          folder: 'plugin_avatars'
        };
        // 使用upload方法上传文件
        const uploadRes = await api.oss.upload(uploadParams)

        if (uploadRes.data?.fileUrl) {
          // 设置头像URL
          this.form.avatar = uploadRes.data.fileUrl
          this.$message.success('上传成功')
        } else {
          throw new Error(uploadRes.message || '上传失败')
        }
      } catch (error) {
        console.error('上传头像失败:', error)
        this.$showFriendlyError(null, '上传失败，请稍后重试')
      }
    },
    addParam() {
      this.form.params.push({
        name: '',
        type: 1, // 1 对应字符串类型
        description: '',
        required: false,
        defaultValue: '',
        paramType: 'query',
        children: [],
        isExpanded: true
      })
    },
    removeParam(index) {
      this.form.params.splice(index, 1)
    },
    updateParam(index, { key, value, extra }) {
      const param = { ...this.form.params[index] }
      if (extra) {
        Object.assign(param, { [key]: value }, extra)
      } else {
        param[key] = value
      }
      this.$set(this.form.params, index, param)
    },
    addChildToParam(index) {
      const param = { ...this.form.params[index] }
      if (!param.children) {
        param.children = []
      }
      param.children.push({
        name: '',
        type: 1, // 1 对应字符串类型
        description: '',
        required: false,
        defaultValue: '',
        paramType: 'query',
        children: [],
        isExpanded: true
      })
      this.$set(this.form.params, index, param)
    },
    toggleParamExpand(index) {
      const param = { ...this.form.params[index] }
      param.isExpanded = !param.isExpanded
      this.$set(this.form.params, index, param)
    },
    addHeader() {
      this.form.headers.push({
        name: '',
        description: '',
        required: false,
        defaultValue: '',
        type: 1, // 1 对应字符串类型（隐藏但默认为string）
        children: [],
        isExpanded: true
      })
    },
    removeHeader(index) {
      this.form.headers.splice(index, 1)
    },
    updateHeader(index, { key, value, extra }) {
      const header = { ...this.form.headers[index] }
      if (extra) {
        Object.assign(header, { [key]: value }, extra)
      } else {
        header[key] = value
      }
      this.$set(this.form.headers, index, header)
    },
    addChildToHeader(index) {
      const header = { ...this.form.headers[index] }
      if (!header.children) {
        header.children = []
      }
      header.children.push({
        name: '',
        description: '',
        required: false,
        defaultValue: '',
        type: 1, // 1 对应字符串类型
        children: [],
        isExpanded: true
      })
      this.$set(this.form.headers, index, header)
    },
    toggleHeaderExpand(index) {
      const header = { ...this.form.headers[index] }
      header.isExpanded = !header.isExpanded
      this.$set(this.form.headers, index, header)
    },
    async handlePreviewMock() {
      try {
        // 构造请求参数
        const params = {
          name: this.form.name,
          headSculpture: this.form.avatar,
          httpTypeCode: this.form.method,
          description: this.form.description,
          url: this.form.url,
          headers: this.form.headers.map(header => ({
            name: header.name,
            description: header.description || 'string',
            defaultValue: header.defaultValue || ''
          })),
          parameters: this.transformParams(this.form.params)
        }

        // 调用接口解析响应结构
        const res = await api.apiPlugin.parseResponseSchema(params)

        if (res.code === 200 && res.data) {
          // 将解析结果转换为表单需要的格式
          this.form.mockParams = res.data.map(field => ({
            name: field.name,
            type: parseInt(field.dataType) || 1,
            description: field.description || field.name,
            children: field.children || [],
            isExpanded: true,
            value: field.value || null
          }))

          // 确保自动生成响应结果开关打开
          this.form.mockResponse = true

          this.$message.success('响应结构解析成功')
        } else {
          throw new Error(res.message || '解析响应结构失败')
        }
      } catch (error) {
        console.error('解析响应结构失败:', error)
        this.$showFriendlyError(error, '解析响应结构失败')
      }
    },
    handleCancel() {
      this.$router.push('/create/plugin')
    },
    async handleSave() {
      try {
        // 先进行自定义验证
        if (this.pluginType === 'MCP' && !this.form.mcpConfig.trim()) {
          this.$showFriendlyError(null, '请输入MCP Server配置')
          return
        }

        // 根据插件类型进行验证
        const valid = await this.$refs.pluginForm.validate()
        if (valid) {
          // 构造请求参数
          let params = {
            name: this.form.name,
            headSculpture: this.form.avatar,
            description: this.form.description,
            pluginType: this.pluginType
          }

          if (this.pluginType === 'HTTP') {
            // HTTP 插件参数
            params = {
              ...params,
              httpTypeCode: this.form.method,
              url: this.form.url,
              headers: this.form.headers.map(header => ({
                name: header.name,
                description: header.description,
                defaultValue: header.defaultValue
              })),
              parameters: this.transformParams(this.form.params.map(param => ({
                ...param,
                paramType: this.form.method === 'POST' ? 'body' : 'query'
              }))),
              responseSchema: this.form.mockResponse ? this.transformMockParams(this.form.mockParams) : []
            }
          } else if (this.pluginType === 'MCP') {
            // MCP 插件参数
            params = {
              ...params,
              mcpConfig: this.form.mcpConfig
            }
          }

          let res
          if (this.isEdit) {
            // 编辑模式 - 调用更新接口
            const pluginId = this.$route.params.id
            res = await api.apiPlugin.update(pluginId, params)
          } else {
            // 创建模式 - 调用创建接口
            res = await api.apiPlugin.create(params)
          }

          if (res.code === 200) {
            this.$message.success(this.isEdit ? '插件更新成功' : '插件创建成功')

            // 如果是MCP插件，自动获取工具列表
            if (this.pluginType === 'MCP' && res.data && res.data.id) {
              await this.loadMcpToolsAfterSave(res.data.id)
            }

            this.$router.push('/create/plugin')
          } else {
            throw new Error(res.message || (this.isEdit ? '更新失败' : '创建失败'))
          }
        }
      } catch (error) {
        console.error(this.isEdit ? '更新插件失败:' : '创建插件失败:', error)
        this.$showFriendlyError(error, this.isEdit ? '更新插件失败' : '创建插件失败')
      }
    },
    // 递归转换 params 数据结构
    transformParams(params) {
      if (!params || !params.length) return []

      return params.map(param => {
        const result = {
          name: param.name,
          dataType: param.type,
          description: param.description,
          defaultValue: param.defaultValue || '',
          paramType: param.paramType || 'query',
          children: []
        }

        if (param.children && param.children.length > 0) {
          result.children = this.transformParams(param.children)
        }

        return result
      })
    },
    // 递归转换 mockParams 数据结构
    transformMockParams(params) {
      if (!params || !params.length) return []

      return params.map(param => {
        const result = {
          name: param.name,
          dataType: param.type,
          description: param.description,
          children: []
        }

        if (param.children && param.children.length > 0) {
          result.children = this.transformMockParams(param.children)
        }

        return result
      })
    },
    addMockParam() {
      this.form.mockParams.push({
        name: '',
        type: 1,
        description: '',
        children: [],
        isExpanded: true
      })
    },
    removeMockParam(index) {
      this.form.mockParams.splice(index, 1)
    },
    updateMockParam(index, { key, value, extra }) {
      const param = { ...this.form.mockParams[index] }
      if (extra) {
        Object.assign(param, { [key]: value }, extra)
      } else {
        param[key] = value
      }
      this.$set(this.form.mockParams, index, param)
    },
    addChildToMockParam(index) {
      const param = { ...this.form.mockParams[index] }
      if (!param.children) {
        param.children = []
      }
      param.children.push({
        name: '',
        type: 1,
        description: '',
        children: [],
        isExpanded: true
      })
      this.$set(this.form.mockParams, index, param)
    },
    toggleMockParamExpand(index) {
      const param = { ...this.form.mockParams[index] }
      param.isExpanded = !param.isExpanded
      this.$set(this.form.mockParams, index, param)
    },
    async loadPluginData(pluginId) {
      try {
        this.loading = true

        // 根据插件类型调用不同的API接口
        let res
        if (this.pluginType === 'MCP') {
          // MCP插件调用MCP详情接口
          res = await api.apiPlugin.getMcpDetail(pluginId)
        } else {
          // HTTP插件调用常规详情接口
          res = await api.apiPlugin.getDetail(pluginId)
        }

        if (res.isSuccess) {
          const data = res.data

          if (this.pluginType === 'MCP') {
            // MCP插件的数据结构处理
            this.form = {
              name: data.name || '',
              description: data.description || '',
              avatar: data.iconUrl || '',
              method: 'GET',
              url: '',
              params: [],
              headers: [],
              mockResponse: false,
              mockParams: [],
              mcpConfig: data.configuration || '',
              pluginPoint: data.pluginPoint || ''
            }

            // 更新编辑器内容
            if (data.configuration) {
              this.$nextTick(() => {
                if (this.codeMirrorView) {
                  this.codeMirrorView.dispatch({
                    changes: {
                      from: 0,
                      to: this.codeMirrorView.state.doc.length,
                      insert: data.configuration
                    }
                  })
                }
              })
            }
          } else {
            // HTTP插件的数据结构处理
            // 处理响应字段数据 - 兼容可能的字段名不一致情况
            const responseData = data.responseSchema || data.responseFields || []

            this.form = {
              name: data.name,
              description: data.description,
              avatar: data.headSculpture,
              method: data.httpTypeCode,
              url: data.url,
              params: data.parameters?.map(p => this.mapParameterField(p)) || [],
              headers: data.headers?.map(h => ({
                name: h.name,
                description: h.description,
                required: h.required || false,
                defaultValue: h.defaultValue || '',
                type: 1, // 1 对应字符串类型
                children: [],
                isExpanded: true
              })) || [],
              mockResponse: responseData.length > 0,
              mockParams: responseData.map(r => this.mapResponseField(r)) || [],
              mcpConfig: data.mcpConfig || '',
              pluginPoint: data.pluginPoint || ''
            }
          }
        }
      } catch (error) {
        console.error('获取插件详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 辅助方法：递归映射响应字段
    mapResponseField(field) {
      const mapped = {
        name: field.name,
        type: parseInt(field.dataType) || 1,
        description: field.description,
        isExpanded: true
      }

      if (field.children && field.children.length > 0) {
        mapped.children = field.children.map(child => this.mapResponseField(child))
      } else {
        mapped.children = []
      }

      return mapped
    },
    handleUrlInput() {
      this.form.url = removeSpaces(this.form.url)
    },
    handlePluginTypeChange(type) {
      // 切换插件类型时，清空相关字段
      if (type === 'MCP') {
        // 切换到 MCP 插件时，清空 HTTP 相关配置
        this.form.method = 'GET'
        this.form.url = ''
        this.form.params = []
        this.form.headers = []
        this.form.mockResponse = false
        this.form.mockParams = []

        // 初始化 CodeMirror 编辑器
        this.$nextTick(() => {
          this.initCodeMirror()
        })
      } else if (type === 'HTTP') {
        // 切换到 HTTP 插件时，清空 MCP 配置并销毁编辑器
        this.form.mcpConfig = ''
        this.mcpTools = [] // 清空工具列表
        if (this.codeMirrorView) {
          this.codeMirrorView.destroy()
          this.codeMirrorView = null
        }
      }
    },
    initCodeMirror() {
      // 检查容器是否存在
      if (!this.$refs.codeMirrorEditor) {
        console.warn('CodeMirror 容器不存在')
        return
      }

      // 如果已经有编辑器实例，先销毁
      if (this.codeMirrorView) {
        this.codeMirrorView.destroy()
        this.codeMirrorView = null
      }

      // 创建初始状态
      const extensions = [
        basicSetup,
        json(),
        placeholder(`输入MCP Server的JSON配置，可参考 modelScope 、 mcp.so 等开放MCP市场中获取，格式示例：
            {
              "mcpServers": {
                "server-name": {
                  "command": "npx",
                  "args": [
                    "-y",
                    "@package-name"
                  ],
                  "env": {
                    "xxxxx": "xxxx"
                  }
                }
              }
            }`),
          EditorView.theme({
            '&': {
              fontSize: '14px',
              fontFamily: "'Monaco', 'Menlo', 'Ubuntu Mono', monospace"
            },
            '.cm-content': {
              padding: '12px 16px',
              minHeight: '240px',
              lineHeight: '1.6'
            },
            '.cm-focused': {
              outline: 'none'
            },
            '.cm-gutters': {
              backgroundColor: '#f8f9fa',
              borderRight: '1px solid #e9ecef',
              color: '#6c757d',
              fontSize: '13px'
            },
            '.cm-lineNumbers .cm-gutterElement': {
              padding: '0 12px 0 8px',
              minWidth: '45px',
              textAlign: 'right'
            },
            '.cm-foldGutter .cm-gutterElement': {
              padding: '0 4px',
              cursor: 'pointer'
            },
            '.cm-activeLine': {
              backgroundColor: 'rgba(64, 158, 255, 0.05)'
            },
            '.cm-foldPlaceholder': {
              backgroundColor: '#e9ecef',
              border: '1px solid #dee2e6',
              color: '#6c757d',
              borderRadius: '3px',
              padding: '0 8px',
              margin: '0 2px'
            },
            '.cm-placeholder': {
              color: '#909399',
              fontStyle: 'normal',
              fontSize: '14px',
              fontFamily: "'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
              lineHeight: '1.6',
              whiteSpace: 'pre-wrap',
              opacity: '0.8'
            },
            '.cm-editor .cm-placeholder': {
              padding: '0'
            }
          })
      ]

      // 如果不是查看模式，添加更新监听器
      if (!this.isView) {
        extensions.push(
          EditorView.updateListener.of((update) => {
            if (update.docChanged) {
              this.form.mcpConfig = update.state.doc.toString()
            }
          })
        )
      } else {
        // 查看模式下添加只读状态
        extensions.push(EditorState.readOnly.of(true))
      }

      // 创建初始状态
      const state = EditorState.create({
        doc: this.form.mcpConfig || '',
        extensions
      })

      // 创建编辑器视图
      try {
        this.codeMirrorView = new EditorView({
          state,
          parent: this.$refs.codeMirrorEditor
        })
        console.log('CodeMirror 编辑器初始化成功')
      } catch (error) {
        console.error('CodeMirror 初始化失败:', error)
      }
    },
    formatJsonCode() {
      if (!this.codeMirrorView) return

      try {
        const currentCode = this.codeMirrorView.state.doc.toString()
        if (!currentCode.trim()) return

        const parsed = JSON.parse(currentCode)
        const formatted = JSON.stringify(parsed, null, 2)

        // 更新编辑器内容
        this.codeMirrorView.dispatch({
          changes: {
            from: 0,
            to: this.codeMirrorView.state.doc.length,
            insert: formatted
          }
        })

        this.$message.success('JSON 格式化成功')
      } catch (error) {
        this.$showFriendlyError(null, 'JSON 格式错误，无法格式化')
      }
    },
    validateJsonCode() {
      if (!this.codeMirrorView) return

      try {
        const currentCode = this.codeMirrorView.state.doc.toString()
        if (!currentCode.trim()) {
          this.$message.warning('请输入 JSON 配置')
          return
        }

        JSON.parse(currentCode)
        this.$message.success('JSON 格式正确')
      } catch (error) {
        this.$showFriendlyError(error, 'JSON 格式错误，请检查格式')
      }
    },
    // 辅助方法：递归映射参数字段
    mapParameterField(param) {
      const mapped = {
        name: param.name,
        type: param.dataType,
        description: param.description,
        required: param.required || false,
        defaultValue: param.defaultValue || '',
        paramType: param.paramType || 'query',
        isExpanded: true
      }

      if (param.children && param.children.length > 0) {
        mapped.children = param.children.map(child => this.mapParameterField(child))
      } else {
        mapped.children = []
      }

      return mapped
    },
    // 保存成功后获取MCP工具列表
    async loadMcpToolsAfterSave(pluginId) {
      try {
        this.toolsLoading = true
        // 调用新的API接口获取工具列表
        const res = await api.apiPlugin.getMcpTools(pluginId)
        this.toolsLoading = false

        // 处理返回的数据
        if (res.code === 200 && res.data) {
          // 数据结构已更改：res.data 直接是工具数组，不再包装在tools字段中
          const tools = Array.isArray(res.data) ? res.data : []

          this.mcpTools = tools.map(tool => ({
            name: tool.name,
            description: tool.description,
            inputSchema: tool.inputSchemaJson ? JSON.parse(tool.inputSchemaJson) : null,
            serviceName: tool.serviceName,
            isExpanded: false,
            showResult: false,
            result: '',
            paramValues: this.initParamValues(tool.inputSchemaJson ? JSON.parse(tool.inputSchemaJson) : null)
          }))
        }
      } catch (error) {
        console.error('获取MCP工具列表失败:', error)
      }
    },
    // 切换工具展开状态
    toggleToolExpand(index) {
      if (this.mcpTools[index]) {
        this.$set(this.mcpTools[index], 'isExpanded', !this.mcpTools[index].isExpanded)
      }
    },
    // 格式化工具Schema
    formatToolSchema(schema) {
      if (!schema) return ''

      try {
        if (typeof schema === 'string') {
          return JSON.stringify(JSON.parse(schema), null, 2)
        } else if (typeof schema === 'object') {
          return JSON.stringify(schema, null, 2)
        }
        return String(schema)
      } catch (error) {
        return String(schema)
      }
    },
    // 检查参数是否为必填项
    isRequiredParam(inputSchema, paramName) {
      return inputSchema.required && inputSchema.required.includes(paramName)
    },
    // 获取参数输入提示
    getParamPlaceholder(param) {
      if (param.description) {
        // 从描述中提取格式信息作为占位符
        return param.description
      }
      return `请输入${param.type || 'string'}类型的值`
    },
    // 初始化参数值
    initParamValues(inputSchema) {
      const paramValues = {}
      if (inputSchema && inputSchema.properties) {
        Object.keys(inputSchema.properties).forEach(paramName => {
          paramValues[paramName] = ''
        })
      }
      return paramValues
    },
    // 运行工具
    async runTool(tool, index) {
      try {
        // 验证必填参数
        if (tool.inputSchema && tool.inputSchema.required) {
          for (const requiredParam of tool.inputSchema.required) {
            if (!tool.paramValues[requiredParam] || !tool.paramValues[requiredParam].trim()) {
              this.$showFriendlyError(null, `请填写必填参数：${requiredParam}`)
              return
            }
          }
        }

        this.$message.info('正在执行工具...')

        // 调用真实API接口执行工具
        const res = await api.apiPlugin.executeMcpTool({
          toolName: tool.name,
          serviceName: tool.serviceName,
          argumentsJson: JSON.stringify(tool.paramValues),
          pluginId: this.$route.params.id
        })

        // 处理返回结果
        let formattedResult = ''
        if (res.isSuccess && res.data) {
          formattedResult = JSON.stringify(res.data, null, 2)
        } else {
          formattedResult = res.message || '执行失败'
        }

        // 更新工具结果
        this.$set(this.mcpTools[index], 'result', formattedResult)
        this.$set(this.mcpTools[index], 'showResult', true)

        this.$message.success('工具执行完成')
      } catch (error) {
        console.error('执行工具失败:', error)
        // 显示错误信息
        const errorResult = {
          error: error.message || '执行工具失败',
          timestamp: new Date().toISOString()
        }
        this.$set(this.mcpTools[index], 'result', JSON.stringify(errorResult, null, 2))
        this.$set(this.mcpTools[index], 'showResult', true)
      }
    },
    // 复制结果
    async copyResult(result) {
      try {
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(result)
        } else {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = result
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }
        this.$message.success('复制成功')
      } catch (error) {
        console.error('复制失败:', error)
        this.$showFriendlyError(null, '复制失败')
      }
    },
    // 清除结果
    clearResult(index) {
      this.$set(this.mcpTools[index], 'showResult', false)
      this.$set(this.mcpTools[index], 'result', '')
    }
  }
}
</script>

<style lang="scss" scoped>
.create-plugin-page {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 60px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #1f2937;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .plugin-type-display {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 0;

    .type-label {
      font-size: 14px;
      color: #606266;
      margin-right: 8px;
    }
  }

  .plugin-type-tabs {
    display: flex;
    justify-content: center;
    padding: 12px 0;

    :deep(.el-radio-group) {
      .el-radio-button {
        &:first-child .el-radio-button__inner {
          border-top-left-radius: 6px;
          border-bottom-left-radius: 6px;
        }

        &:last-child .el-radio-button__inner {
          border-top-right-radius: 6px;
          border-bottom-right-radius: 6px;
        }

        .el-radio-button__inner {
          border: 1px solid #dcdfe6;
          border-left: 0;
          background-color: #fff;
          color: #606266;
          font-size: 14px;
          padding: 8px 24px;
          transition: all 0.3s ease;
          min-width: 120px;

          &:hover {
            background-color: #f5f7fa;
            color: #409EFF;
          }
        }

        &:first-child .el-radio-button__inner {
          border-left: 1px solid #dcdfe6;
        }

        &.is-active .el-radio-button__inner {
          background-color: #409EFF;
          border-color: #409EFF;
          color: #fff;
          box-shadow: none;
        }
      }
    }
  }

  .plugin-form {
    margin: 0 auto;
    max-height: calc(100% - 91px); /* 调整高度，为插件类型切换留出空间 */
    overflow-y: auto;
    padding: 8px 8px 0; /* 为左右阴影留出空间 */
    box-sizing: border-box;
  }

  .form-card {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 8px;
    }

    .card-header {
      font-size: 16px;
      font-weight: 500;
      color: #374151;
    }
  }

  .avatar-uploader {
    width: 100px;
    height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--el-color-primary);
    }

    .upload-area {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      i {
        font-size: 28px;
        color: #8c939d;
      }
    }

    .avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .avatar-display {
    width: 100px;
    height: 100px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .no-avatar {
    width: 100px;
    height: 100px;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    color: #909399;

    i {
      font-size: 24px;
      margin-bottom: 4px;
    }

    span {
      font-size: 12px;
    }
  }

  .params-header {
    margin-bottom: 16px;
  }

  .params-table {
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .params-row {
      display: flex;
      align-items: center;
      padding: 8px;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      &.header {
        background-color: #f5f7fa;
        font-weight: 500;
        color: #606266;
      }

      .col-required {
        width: 60px;
        text-align: center;
      }

      .col-name {
        width: 200px;
        padding: 0 8px;
      }

      .col-type {
        width: 120px;
        padding: 0 8px;
      }

      .col-desc {
        flex: 1;
        padding: 0 8px;
      }

      .col-default {
        width: 160px;
        padding: 0 8px;
      }

      .col-action {
        width: 60px;
        text-align: center;

        .delete-btn {
          color: #f56c6c;
        }
      }

      .param-name-wrapper {
        display: flex;
        align-items: center;
        width: 100%;

        .expand-btn {
          padding: 0;
          width: 20px;
          margin-right: 4px;

          i {
            transition: transform 0.3s;
            font-size: 14px;

            &.is-expanded {
              transform: rotate(90deg);
            }
          }
        }

        .indent-line {
          width: 16px;
          height: 1px;
          background-color: #dcdfe6;
          margin: 0 4px;
        }
      }

      &.child-row {
        background-color: #fafafa;

        .param-name-wrapper {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            width: 16px;
            height: 1px;
            background-color: #dcdfe6;
          }

          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: -8px;
            width: 1px;
            height: calc(100% + 16px);
            background-color: #dcdfe6;
          }
        }

        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .add-param {
    margin-top: 16px;
    text-align: center;
  }

  .mock-switch {
    margin-right: 8px;
  }

  .mock-label {
    margin-right: 16px;
    color: #606266;
  }

  .mock-btn {
    color: var(--el-color-primary);
  }

  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }

  .mcp-config-card {
    .mcp-config-content {
      padding: 0; /* 移除默认内边距 */
    }
  }

  // CodeMirror 编辑器容器
  .codemirror-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;

    .editor-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;

      .editor-label {
        font-size: 12px;
        color: #6c757d;
        font-weight: 500;
      }

      .editor-actions {
        display: flex;
        gap: 4px;

        :deep(.el-button--mini) {
          font-size: 12px;
          padding: 4px 8px;

          &:hover {
            color: #409eff;
          }
        }
      }
    }

    .codemirror-editor {
      min-height: 240px;

      // 移除 CodeMirror 自身的边框，因为容器已有边框
      :deep(.cm-editor) {
        border: none;
      }

      // 优化编辑器内部间距
      :deep(.cm-scroller) {
        padding: 0;
      }
    }
  }

  // 工具列表样式
  .tools-container {
    .empty-tools {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #c0c4cc;
      }

      p {
        margin: 0 0 8px 0;
        font-size: 16px;
        color: #606266;
      }

      .empty-tip {
        font-size: 14px;
        color: #909399;
      }
    }

    .tools-list {
      .tool-item {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        margin-bottom: 12px;
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          border-color: #c6e2ff;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
          transform: translateY(-1px);
        }

        .tool-header {
          padding: 16px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: rgba(64, 158, 255, 0.03);
          }

          .tool-title {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .expand-icon {
              font-size: 16px;
              color: #909399;
              margin-right: 8px;
              transition: all 0.3s ease;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 20px;
              height: 20px;
              border-radius: 3px;

              &.is-expanded {
                transform: rotate(90deg);
                color: #409eff;
              }

              &:hover {
                color: #409eff;
                background-color: rgba(64, 158, 255, 0.1);
              }
            }

            .tool-name {
              font-size: 16px;
              font-weight: 500;
              color: #303133;
            }
          }

          .tool-description {
            font-size: 14px;
            color: #606266;
            line-height: 1.5;
            margin-left: 20px;
          }
        }

        .tool-content {
          .tool-details {
            border-top: 1px solid #ebeef5;

            .detail-section {
              padding: 16px;

              .param-list {
                .param-item {
                  margin-bottom: 16px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .param-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;

                    .param-name {
                      font-size: 14px;
                      font-weight: 500;
                      color: #303133;
                    }

                    .param-required {
                      color: #f56c6c;
                      margin-left: 2px;
                      font-weight: bold;
                    }
                  }

                  .param-description {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 8px;
                    line-height: 1.4;
                  }

                  .param-input {
                    margin-bottom: 8px;
                  }
                }
              }

              .action-buttons {
                margin-top: 16px;
                text-align: right;
              }

              .result-section {
                margin-top: 16px;

                .result-container {
                  border: 1px solid #e1e4e8;
                  border-radius: 4px;
                  overflow: hidden;

                  .result-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background: #f6f8fa;
                    border-bottom: 1px solid #e1e4e8;

                    .result-label {
                      font-size: 12px;
                      color: #6c757d;
                      font-weight: 500;
                    }

                    .result-actions {
                      display: flex;
                      gap: 4px;

                      :deep(.el-button--mini) {
                        font-size: 12px;
                        padding: 4px 8px;

                        &:hover {
                          color: #409eff;
                        }
                      }
                    }
                  }

                  .result-content {
                    .result-code {
                      background-color: #fff;
                      padding: 12px;
                      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                      font-size: 13px;
                      line-height: 1.6;
                      color: #24292e;
                      overflow-x: auto;
                      white-space: pre-wrap;
                      word-wrap: break-word;
                      margin: 0;
                      min-height: 60px;
                      max-height: 300px;
                      overflow-y: auto;

                      &::-webkit-scrollbar {
                        width: 6px;
                        height: 6px;
                      }

                      &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                      }

                      &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;

                        &:hover {
                          background: #a8a8a8;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-input.is-disabled .el-input__inner) {
  color: #606266;
  background-color: #f5f7fa;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  color: #606266;
  background-color: #f5f7fa;
}
</style>
