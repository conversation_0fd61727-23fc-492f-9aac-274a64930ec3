<template>
  <div class="database-setting">
    <!-- 左侧菜单 -->
    <div class="setting-menu">
      <el-menu
        :default-active="activeMenu"
        class="setting-menu-list"
        @select="handleMenuSelect"
      >
        <el-menu-item index="preview">
          <i class="el-icon-view"></i>
          <span>数据预览</span>
        </el-menu-item>
        <el-menu-item index="table">
          <i class="el-icon-tickets"></i>
          <span>数据表配置</span>
        </el-menu-item>
        <el-menu-item index="config">
          <i class="el-icon-setting"></i>
          <span>数据库配置</span>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 右侧内容区域 -->
    <div class="setting-content">
      <data-preview v-if="activeMenu === 'preview'" :database-id="databaseId" />
      <table-config v-if="activeMenu === 'table'" :database-id="databaseId" />
      <database-config v-if="activeMenu === 'config'" :database-id="databaseId" />
    </div>
  </div>
</template>

<script>
import DatabaseConfig from './components/DatabaseConfig.vue'
import DataPreview from './components/DataPreview.vue'
import TableConfig from './components/TableConfig.vue'

export default {
  name: 'DatabaseSetting',
  components: {
    DataPreview,
    TableConfig,
    DatabaseConfig
  },
  data() {
    return {
      activeMenu: 'preview',
      databaseId: null
    }
  },
  created() {
    this.initializeData()
  },
  watch: {
    // 监听路由参数变化
    '$route.params.id': {
      handler(newId) {
        if (newId) {
          this.databaseId = newId
        }
      },
      immediate: true
    }
  },
  methods: {
    initializeData() {
      const id = this.$route.params.id
      if (!id) {
        this.$showFriendlyError(null, '数据库ID不能为空')
        this.$router.push('/create/database').catch(err => {
          if (err.name !== 'NavigationDuplicated') {
            throw err
          }
        })
        return
      }
      this.databaseId = id
    },
    handleMenuSelect(index) {
      this.activeMenu = index
    }
  }
}
</script>

<style lang="scss" scoped>
.database-setting {
  display: flex;
  height: calc(100vh - 60px);
  background: #fff;

  .setting-menu {
    width: 200px;
    border-right: 1px solid #f0f0f0;

    .setting-menu-list {
      border-right: none;

      .el-menu-item {
        height: 50px;
        line-height: 50px;
        padding: 0 20px;

        &.is-active {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--el-color-primary);
          }
        }

        i {
          margin-right: 12px;
        }
      }
    }
  }

  .setting-content {
    flex: 1;
    padding: 20px;
    background: #fff;
    overflow-y: auto;
  }
}
</style>
