<template>
  <div class="database-config" v-loading="loading">
    <!-- 基础信息显示 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>基础信息</span>
        </div>
      </template>

      <div class="info-content">
        <div class="info-row">
          <span class="info-label">数据库类型</span>
          <span class="info-value">{{ databaseInfo.type }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">数据库名称</span>
          <span class="info-value">{{ databaseInfo.name }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">数据库描述</span>
          <span class="info-value">{{ databaseInfo.description }}</span>
        </div>
      </div>
    </el-card>

    <!-- 权限管理 -->
    <el-card class="permission-card">
      <template #header>
        <div class="card-header">
          <span>权限管理</span>
        </div>
      </template>

      <div class="permission-content">
        <div class="permission-item">
          <span class="permission-label">
            <span class="required-star">*</span>
            权限设置
          </span>
          <div class="permission-checkboxes">
            <el-checkbox-group v-model="permissionsList" disabled>
              <el-checkbox :label="1">查询</el-checkbox>
              <el-checkbox :label="2">新增</el-checkbox>
              <el-checkbox :label="4">编辑</el-checkbox>
              <el-checkbox :label="8">删除</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="handleEdit">编辑</el-button>
      <el-button type="danger" @click="handleDelete">删除</el-button>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog
      title="修改数据库信息"
      :visible.sync="editDialogVisible"
      :close-on-click-modal="false"
      width="50%"
      @close="handleEditDialogClose"
    >
      <el-form :model="editForm" ref="editForm" :rules="editRules" label-width="120px">
        <el-form-item label="数据库名称" prop="name">
          <el-input 
            v-model="editForm.name" 
            placeholder="请填写数据库名称"
            maxlength="100"
            show-word-limit />
        </el-form-item>

        <el-form-item label="数据库描述" prop="description">
          <el-input
            type="textarea"
            v-model="editForm.description"
            placeholder="请详细描述您的数据库使用场景"
            :rows="4"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="权限设置" prop="permissions">
          <div class="permission-checkboxes">
            <el-checkbox-group v-model="editForm.permissions">
              <el-checkbox :label="1">查询</el-checkbox>
              <el-checkbox :label="2">新增</el-checkbox>
              <el-checkbox :label="4">编辑</el-checkbox>
              <el-checkbox :label="8">删除</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleEditDialogClose">取消</el-button>
        <el-button type="primary" @click="handleEditConfirm" :loading="editLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";

export default {
  name: 'DatabaseConfig',
  props: {
    databaseId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 数据库信息显示
      databaseInfo: {
        type: '',
        name: '',
        description: ''
      },
      // 权限信息显示 (数字数组格式)
      permissionsList: [],
      loading: false,
      // 编辑弹窗相关
      editDialogVisible: false,
      editLoading: false,
      editForm: {
        name: '',
        description: '',
        permissions: []  // 直接使用数字数组
      },
      editRules: {
        name: [
          { required: true, message: '请输入数据库名称', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        description: [
          { max: 1000, message: '长度不能超过1000个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadDatabaseInfo();
  },
  methods: {
    // 加载数据库信息
    async loadDatabaseInfo() {
      this.loading = true;
      try {
        const response = await api.schemaStore.getDatabaseDetail(this.databaseId);
        console.log('数据库配置详情响应:', response);
        
        if (response && response.data) {
          const data = response.data;
          
          // 设置基础信息
          this.databaseInfo = {
            type: data.databaseType?.value === 1 ? '内置数据库' : '远程数据库',
            name: data.databaseName || '',
            description: data.description || ''
          };
          
          // 处理权限数组 - 从对象数组中提取 value 值
          this.permissionsList = (data.permissions || []).map(permission => permission.value);
        }
      } catch (error) {
        console.error('获取数据库信息失败:', error);
      } finally {
        this.loading = false;
      }
    },


    // 编辑按钮点击
    handleEdit() {
      this.editForm = {
        name: this.databaseInfo.name,
        description: this.databaseInfo.description,
        permissions: [...this.permissionsList]  // 直接复制权限数组
      }
      this.editDialogVisible = true
    },
    // 删除按钮点击
    async handleDelete() {
      this.$confirm(`确定要删除数据库"${this.databaseInfo.name}"吗？删除后将无法恢复，相关的数据表也将一并删除。`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(async () => {
        try {
          this.loading = true;
          const response = await api.schemaStore.deleteDatabase(this.databaseId);
          
          if (response && response.isSuccess) {
            this.$message.success('数据库删除成功');
            // 返回上一页或数据库列表页
            this.$router.push('/create/database');
          }
        } catch (error) {
          console.error('删除数据库失败:', error);
        } finally {
          this.loading = false;
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 编辑弹窗关闭
    handleEditDialogClose() {
      this.editDialogVisible = false
      this.resetEditForm()
    },
    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        name: '',
        description: '',
        permissions: []  // 重置为空数组
      }
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields()
      }
    },
    // 编辑确认
    async handleEditConfirm() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          this.editLoading = true;

          try {
            // 构建请求参数
            const requestData = {
              databaseName: this.editForm.name,
              description: this.editForm.description,
              permissions: this.editForm.permissions
            };

            console.log('更新数据库请求参数:', requestData);

            const response = await api.schemaStore.updateDatabase(this.databaseId, requestData);

            if (response && response.isSuccess) {
              // 更新显示的数据
              this.databaseInfo.name = this.editForm.name;
              this.databaseInfo.description = this.editForm.description;
              this.permissionsList = [...this.editForm.permissions];  // 更新权限数组

              this.$message.success('数据库信息更新成功');
              this.editDialogVisible = false;
              this.resetEditForm();
              
              // 重新加载数据库信息
              await this.loadDatabaseInfo();
            }
          } catch (error) {
            console.error('更新数据库信息失败:', error);
          } finally {
            this.editLoading = false;
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.database-config {
  .info-card,
  .permission-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      align-items: center;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .info-content {
    .info-row {
      display: flex;
      margin-bottom: 16px;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        width: 120px;
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }

      .info-value {
        color: #303133;
        font-size: 14px;
        flex: 1;
      }
    }
  }

  .permission-content {
    .permission-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .permission-label {
        width: 120px;
        color: #606266;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 4px;

        .required-star {
          color: #f56c6c;
          font-weight: bold;
        }
      }

      .permission-checkboxes {
        flex: 1;
        display: flex;
        gap: 24px;
        flex-wrap: wrap;

        :deep(.el-checkbox) {

          .el-checkbox__label {
            font-size: 14px;
            color: #606266;
          }

          &.is-checked .el-checkbox__label {
            color: #409eff;
          }
        }
      }
    }
  }

  .action-buttons {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 16px;

    .el-button {
      padding: 10px 24px;
      font-weight: 500;
      border-radius: 6px;

      &.el-button--primary {
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
      }

      &.el-button--danger {
        box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
      }
    }
  }
}

// 编辑弹窗样式
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 20px;
    text-align: center;

    .el-button {
      padding: 10px 24px;
      font-weight: 500;
      border-radius: 6px;
    }
  }
}

// 卡片样式
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;

  .el-card__header {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fc 0%, #f5f7fa 100%);
    border-bottom: 1px solid #e4e7ed;
  }

  .el-card__body {
    padding: 24px 20px;
  }
}

// 表单样式
.permission-checkboxes {
  :deep(.el-checkbox) {
    margin-right: 20px;
    margin-bottom: 8px;

    .el-checkbox__label {
      font-size: 14px;
      color: #606266;
      padding-left: 8px;
    }

    &.is-checked .el-checkbox__label {
      color: #409eff;
    }
  }
}
</style>
