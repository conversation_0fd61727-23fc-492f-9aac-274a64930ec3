<template>
  <div class="table-config">
    <div class="config-header">
      <h2>数据表配置</h2>
    </div>

    <div class="config-body">
      <!-- 左侧数据表列表 -->
      <div class="table-list-sidebar">
        <div class="sidebar-header">
          <h3>数据表</h3>
          <el-button type="text" icon="el-icon-plus" @click="showCreateTableDialog" class="create-table-btn">
            新增
          </el-button>
        </div>
        <div class="table-list" v-loading="tableListLoading">
          <div
            v-for="table in tableList"
            :key="table.id"
            :class="['table-item', { active: selectedTable?.id === table.id }]"
            @click="selectTable(table)"
          >
            <div class="table-info">
              <el-tooltip :content="table.name" placement="top" :disabled="!isNameOverflow(table.name)">
                <span class="table-name">{{ table.name }}</span>
              </el-tooltip>
              <el-tooltip :content="table.description" placement="top">
                <i class="el-icon-info table-info-icon"></i>
              </el-tooltip>
            </div>
            <el-button
              type="text"
              icon="el-icon-edit"
              size="mini"
              @click.stop="handleEditTable(table)"
              class="edit-btn"
            />
          </div>

          <!-- 空状态 -->
          <div v-if="!tableListLoading && tableList.length === 0" class="empty-table-list">
            <i class="el-icon-document"></i>
            <p>暂无数据表</p>
          </div>
        </div>
      </div>

      <!-- 右侧配置内容 -->
      <div class="config-content">
        <div v-if="selectedTable" class="config-wrapper">
          <div class="config-header">
            <h3>字段配置</h3>
            <div class="action-buttons">
              <el-button type="primary" plain icon="el-icon-plus" @click="handleAddField">
                添加字段
              </el-button>
              <el-button type="danger" plain icon="el-icon-delete" @click="handleDeleteTable">
                删除数据表
              </el-button>
            </div>
          </div>

          <div class="table-structure" v-loading="structureLoading">
            <el-table :data="structureData" border>
              <el-table-column label="字段名" prop="name">
                <template #default="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="数据类型" prop="type">
                <template #default="{ row }">
                  <span>{{ row.type }}</span>
                </template>
              </el-table-column>
              <el-table-column label="字段描述" prop="comment">
                <template #default="{ row }">
                  <el-input v-if="row.isEditing" v-model="row.comment" placeholder="请输入字段描述" />
                  <span v-else>{{ row.comment || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row, $index }">
                  <template v-if="row.isEditing">
                    <el-button type="text" @click="handleSaveField($index)">保存</el-button>
                    <el-button type="text" @click="handleCancelEditField($index)">取消</el-button>
                  </template>
                  <template v-else>
                    <el-button type="text" @click="handleEditField($index)">编辑</el-button>
                    <el-button
                      v-if="!row.isPrimaryKey"
                      type="text"
                      class="delete-btn"
                      @click="handleDeleteField($index)"
                    >
                      删除
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div v-else class="empty-state">
          <i class="el-icon-table"></i>
          <p>请选择一个数据表进行配置</p>
        </div>
      </div>
    </div>

    <!-- 编辑数据表信息弹窗 -->
    <el-dialog
      title="修改数据表信息"
      :visible.sync="editTableDialogVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="handleEditTableDialogClose"
    >
      <el-form :model="editTableForm" :rules="editTableRules" ref="editTableForm" label-width="100px">
        <el-form-item label="数据表名称">
          <el-input v-model="editTableForm.name" disabled />
        </el-form-item>
        <el-form-item label="数据表描述" prop="description">
          <el-input
            type="textarea"
            v-model="editTableForm.description"
            placeholder="记录每张咨询单的完整数据: 姓名、性别、年龄、主诉、建议方案。"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleEditTableDialogClose">取消</el-button>
        <el-button type="primary" @click="handleEditTableConfirm" :loading="editTableLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 新建数据表弹窗 -->
    <el-dialog
      title="新建数据表"
      :visible.sync="createTableDialogVisible"
      :close-on-click-modal="false"
      width="65%"
      @close="handleCreateTableDialogClose"
    >
      <div class="create-table-content">
        <!-- 表格导入按钮 -->
        <div class="dialog-header">
          <div></div>
          <el-button type="primary" plain @click="showImportDialog">表格导入</el-button>
        </div>

        <el-form :model="createTableForm" :rules="createTableRules" ref="createTableForm" label-width="120px" label-position="right">
          <!-- 数据表名称 -->
          <el-form-item prop="name" class="custom-form-item">
            <template slot="label">
              <span>数据表名称</span>
              <el-tooltip content="建议使用英文单词作为表名" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </template>
            <el-input
              v-model="createTableForm.name"
              placeholder="请输入数据表名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <!-- 数据表描述 -->
          <el-form-item class="custom-form-item">
            <template slot="label">
              <span>数据表描述</span>
              <el-tooltip content="数据表描述将用于SQL语句的生成，请尽量清楚地描述该数据表中的数据内容和用途" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </template>
            <el-input
              type="textarea"
              v-model="createTableForm.description"
              placeholder="请输入数据表描述"
              :rows="3"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <!-- 字段配置 -->
          <el-form-item class="custom-form-item">
            <template slot="label">
              <span class="required-star">*</span>
              <span style="margin-right: 14px;">字段配置</span>
            </template>
            <div class="field-config-table">
              <div class="table-header">
                <div class="header-item" style="width: 200px;">字段名</div>
                <div class="header-item" style="width: 140px;">字段类型</div>
                <div class="header-item" style="width: 480px;">
                  <span>字段描述</span>
                  <el-tooltip content="字段描述将用于SQL语句的生成，请尽量清楚地描述该字段的含义" placement="top">
                    <i class="el-icon-question help-icon"></i>
                  </el-tooltip>
                </div>
                <div class="header-item" style="width: 100px;">操作</div>
              </div>
              <div class="table-body">
                <div v-for="(field, index) in createTableForm.fields" :key="index" class="table-row">
                  <div class="row-item" style="width: 200px;">
                    <el-input
                      v-model="field.name"
                      placeholder="请输入字段名"
                      :class="{ 'is-error': !field.name && showFieldValidation }"
                      :disabled="index === 0"
                    />
                  </div>
                  <div class="row-item" style="width: 140px;">
                    <el-select v-model="field.type" placeholder="请选择" style="width: 100%;" :disabled="index === 0">
                      <el-option label="整数" value="1" />
                      <el-option label="小数" value="2" />
                      <el-option label="文本" value="3" />
                      <el-option label="日期时间" value="4" />
                      <el-option label="长文本" value="5">
                        <span>长文本</span>
                        <el-tooltip content="长度超过200字符的文本" placement="right" class="option-tooltip">
                          <i class="el-icon-question option-help-icon"></i>
                        </el-tooltip>
                      </el-option>
                    </el-select>
                  </div>
                  <div class="row-item" style="width: 480px;">
                    <el-input
                      v-model="field.description"
                      :class="{ 'is-error': !field.description && showFieldValidation }"
                      placeholder="请输入字段描述"
                      :disabled="index === 0"
                    />
                  </div>
                  <div class="row-item" style="width: 100px;">
                    <el-button
                      v-if="index !== 0"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDeleteCreateField(index)"
                      class="delete-btn"
                    />
                  </div>
                </div>
              </div>
              <div class="table-footer">
                <el-button type="primary" plain icon="el-icon-plus" @click="handleAddCreateField" class="add-field-btn">
                  新增字段
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCreateTableDialogClose">取消</el-button>
        <el-button type="primary" @click="handleCreateTableConfirm" :loading="createTableLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 新增表字段弹窗 -->
    <el-dialog
      title="新增表字段"
      :visible.sync="addFieldDialogVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="handleAddFieldDialogClose"
    >
      <el-form :model="addFieldForm" :rules="addFieldRules" ref="addFieldForm" label-width="100px">
        <el-form-item label="字段名:" prop="name">
          <el-input v-model="addFieldForm.name" placeholder="输入字段名" />
        </el-form-item>
        <el-form-item label="字段类型:" prop="type">
          <el-select v-model="addFieldForm.type" placeholder="选择字段类型" style="width: 100%;">
            <el-option label="整数" value="1" />
            <el-option label="小数" value="2" />
            <el-option label="文本" value="3" />
            <el-option label="日期时间" value="4" />
            <el-option label="长文本" value="5">
              <span>长文本</span>
              <el-tooltip content="长度超过200字符的文本" placement="right" class="option-tooltip">
                <i class="el-icon-question option-help-icon"></i>
              </el-tooltip>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="字段描述:" prop="description">
          <el-input
            type="textarea"
            v-model="addFieldForm.description"
            placeholder="输入字段描述"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAddFieldDialogClose">取消</el-button>
        <el-button type="primary" @click="handleAddFieldConfirm" :loading="addFieldLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 表格导入弹窗 -->
    <el-dialog
      title="表格导入"
      :visible.sync="importDialogVisible"
      :close-on-click-modal="false"
      width="600px"
      @close="handleImportDialogClose"
    >
      <div class="import-content">
        <p class="import-tip">
          上传excel 或 csv 表格，自动创建表结构并导入数据
        </p>

        <el-upload
          ref="uploadRef"
          class="upload-area"
          drag
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-change="handleFileChange"
          :show-file-list="false"
          :auto-upload="false"
          accept=".xlsx,.xls,.csv"
        >
          <i class="el-icon-upload2 upload-icon"></i>
          <div class="upload-text">
            <span class="upload-main-text">拖入文件或</span>
            <el-button type="text" class="upload-link">点击上传</el-button>
          </div>
        </el-upload>

        <div v-if="uploadFile" class="file-info">
          <i class="el-icon-document"></i>
          <span class="file-name">{{ uploadFile.name }}</span>
          <el-button type="text" @click="removeFile" class="remove-btn">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleImportDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleImportConfirm"
          :loading="importSubmitting"
          :disabled="!uploadFile"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api/request'

export default {
  name: 'TableConfig',
  props: {
    databaseId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      structureData: [
        {
          id: 1,
          name: 'id',
          type: 'integer',
          comment: '主键ID',
          isPrimaryKey: true,
          isEditing: false
        },
        {
          id: 2,
          name: 'name',
          type: 'varchar',
          comment: '名称',
          isPrimaryKey: false,
          isEditing: false
        }
      ],
      // 新建数据表弹窗相关
      createTableDialogVisible: false,
      createTableLoading: false,
      showFieldValidation: false,
      createTableForm: {
        name: '',
        description: '',
        fields: [
          {
            name: 'id',
            type: '1',
            description: '主键ID'
          },
          {
            name: '',
            type: '3',
            description: ''
          }
        ]
      },
      createTableRules: {
        name: [
          { required: true, message: '请输入数据表名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '表名必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ]
      },
      // 新增字段相关
      addFieldDialogVisible: false,
      addFieldLoading: false,
      addFieldForm: {
        name: '',
        type: '3',
        description: ''
      },
      addFieldRules: {
        name: [
          { required: true, message: '请输入字段名', trigger: 'blur' },
          { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段名必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择字段类型', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入字段描述', trigger: 'blur' }
        ]
      },
      // 表格导入相关
      importDialogVisible: false,
      uploadFile: null,
      uploadAction: 'https://api.temp.com/upload', // 临时占位，等待后端提供真实接口
      importSubmitting: false, // 导入提交状态
      // 编辑数据表相关
      editTableDialogVisible: false,
      editTableLoading: false,
      editTableForm: {
        name: '',
        description: ''
      },
      editTableRules: {
        description: [
          { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
        ]
      },
      // 数据表列表相关
      tableList: [],
      selectedTable: null,
      tableListLoading: false,
      structureLoading: false
    }
  },
  mounted() {
    this.fetchTableList()
  },
  methods: {
    handleAddField() {
      this.addFieldDialogVisible = true
      this.resetAddFieldForm()
    },
    handleDeleteField(index) {
      const field = this.structureData[index]
      const fieldName = field.name

      // 检查是否是主键字段
      if (field.isPrimaryKey) {
        this.$message.warning('主键字段不能删除')
        return
      }

      this.$confirm(`确定要删除字段"${fieldName}"吗？`, '删除字段', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (!field.id) {
          this.$showFriendlyError(null, '字段ID不存在，无法删除')
          return
        }

        try {
          const response = await api.schemaStore.deleteColumn(field.id);

          if (response && response.isSuccess) {
            this.structureData.splice(index, 1)
            this.$message.success(`字段"${fieldName}"删除成功`)
          }
        } catch (error) {
          console.error('删除字段失败:', error)
        }
      }).catch(() => {
        // 用户取消删除
      })
    },
    handleEditField(index) {
      // 保存原始数据用于取消编辑时恢复
      const row = this.structureData[index]
      row.originalData = {
        name: row.name,
        type: row.type,
        comment: row.comment
      }
      this.$set(this.structureData[index], 'isEditing', true)
    },
    async handleSaveField(index) {
      const row = this.structureData[index]

      if (!row.id) {
        this.$showFriendlyError(null, '字段ID不存在，无法保存')
        return
      }

      try {
        // 构建请求参数
        const requestData = {
          columnId: row.id,
          description: row.comment
        };

        console.log('修改字段请求参数:', requestData);

        const response = await api.schemaStore.updateColumn(row.id, requestData);

        if (response && response.isSuccess) {
          this.$message.success('字段保存成功')
          // 清除原始数据
          delete row.originalData
          this.$set(this.structureData[index], 'isEditing', false)
        }
      } catch (error) {
        console.error('保存字段失败:', error)
      }
    },
    handleCancelEditField(index) {
      const row = this.structureData[index]
      if (row.originalData) {
        // 恢复原始数据
        Object.assign(row, row.originalData)
        delete row.originalData
      }
      this.$set(this.structureData[index], 'isEditing', false)
    },
    getTypeLabel(type) {
      const typeMap = {
        varchar: 'VARCHAR',
        integer: 'INTEGER',
        datetime: 'DATETIME',
        text: 'TEXT',
        '1': '整数类型',
        '2': '小数类型',
        '3': '文本类型',
        '4': '日期时间类型',
        '5': '长文本类型'
      }
      return typeMap[type] || type.toUpperCase()
    },
    // 将接口返回的数据类型映射为字段类型选项值
    mapDataTypeToFieldType(dataType) {
      if (!dataType) return '3' // 默认文本类型

      // 如果是对象，取其name属性
      const typeName = typeof dataType === 'object' && dataType.name ? dataType.name : dataType

      // 转换为小写进行匹配
      const typeStr = String(typeName).toLowerCase()

      // 根据数据类型名称映射到字段类型
      if (typeStr.includes('int') || typeStr.includes('number') || typeStr === '1') {
        return '1' // 整数
      } else if (typeStr.includes('decimal') || typeStr.includes('float') || typeStr.includes('double') || typeStr === '2') {
        return '2' // 小数
      } else if (typeStr.includes('datetime') || typeStr.includes('timestamp') || typeStr.includes('date') || typeStr === '4') {
        return '4' // 日期时间
      } else if (typeStr.includes('text') || typeStr.includes('longtext') || typeStr === '5') {
        return '5' // 长文本
      } else {
        return '3' // 默认文本类型
      }
    },
    async handleDeleteTable() {
      if (!this.selectedTable) {
        this.$message.warning('请先选择要删除的数据表')
        return
      }

      this.$confirm(`确定要删除数据表"${this.selectedTable.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await api.schemaStore.deleteTable(this.selectedTable.id);

          if (response && response.isSuccess) {
            this.$message.success('数据表删除成功')
            // 清空当前选中的表
            this.selectedTable = null
            this.structureData = []
            // 刷新表列表
            await this.fetchTableList()
          }
        } catch (error) {
          console.error('删除数据表失败:', error)
        }
      }).catch(() => {
        // 用户取消删除
      })
    },
    // 新建数据表相关方法
    showCreateTableDialog() {
      this.createTableDialogVisible = true
      this.resetCreateTableForm()
    },
    handleCreateTableDialogClose() {
      this.createTableDialogVisible = false
      this.resetCreateTableForm()
      this.showFieldValidation = false
    },
    resetCreateTableForm() {
      this.createTableForm = {
        name: '',
        description: '',
        fields: [
          {
            name: 'id',
            type: '1',
            description: '主键ID'
          },
          {
            name: '',
            type: '3',
            description: ''
          }
        ]
      }
      if (this.$refs.createTableForm) {
        this.$refs.createTableForm.resetFields()
      }
    },
    handleAddCreateField() {
      this.createTableForm.fields.push({
        name: '',
        type: '3',
        description: ''
      })
    },
    handleDeleteCreateField(index) {
      if (index !== 0) {
        this.createTableForm.fields.splice(index, 1)
      }
    },
    validateFields() {
      // 验证字段配置
      for (let field of this.createTableForm.fields) {
        if (!field.name || !field.type || !field.description) {
          return false
        }
      }
      return true
    },
    async handleCreateTableConfirm() {
      this.$refs.createTableForm.validate(async (valid) => {
        if (valid) {
          // 验证字段配置
          this.showFieldValidation = true
          if (!this.validateFields()) {
            this.$showFriendlyError(null, '请完善字段配置信息')
            return
          }

          this.createTableLoading = true

          try {
            // 构建请求参数
            const requestData = {
              virtualDatabaseId: this.databaseId,
              tableName: this.createTableForm.name,
              description: this.createTableForm.description,
              initialFields: this.createTableForm.fields.map(field => ({
                fieldName: field.name,
                fieldType: parseInt(field.type), // 确保传入整数类型
                description: field.description,
                isPrimaryKey: field.name === 'id', // id字段设为主键
                isRequired: field.name === 'id' // id字段设为必填
              }))
            };

            console.log('创建数据表请求参数:', requestData);

            const response = await api.schemaStore.createTable(requestData);

            if (response && response.isSuccess) {
              this.$message.success('数据表创建成功')
              this.handleCreateTableDialogClose()
              // 刷新表列表
              await this.fetchTableList()
            }
          } catch (error) {
            this.$message.error(error.message || '创建数据表失败')
            console.error('创建数据表失败:', error)
          } finally {
            this.createTableLoading = false
          }
        }
      })
    },
    // 表格导入相关方法
    showImportDialog() {
      this.importDialogVisible = true
    },
    handleImportDialogClose() {
      this.importDialogVisible = false
      this.uploadFile = null
      this.$refs.uploadRef?.clearFiles()
    },

    // 文件状态改变时的回调（备用方法）
    handleFileChange(file) {
      console.log('handleFileChange被调用:', file);
      if (file.raw) {
        this.beforeUpload(file.raw);
      }
    },

    // 文件上传前检查
    beforeUpload(file) {
      console.log('文件上传前检查 - 文件信息:', {
        name: file.name,
        type: file.type,
        size: file.size
      });

      // 检查文件扩展名
      const fileName = file.name.toLowerCase()
      const validExtensions = ['.xlsx', '.xls', '.csv']
      const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      // 检查MIME类型
      const validMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv', // .csv
        'application/csv' // 另一种CSV MIME类型
      ]
      const hasValidMimeType = validMimeTypes.includes(file.type)

      console.log('文件验证结果:', {
        hasValidExtension,
        hasValidMimeType,
        fileName,
        fileType: file.type
      });

      // 如果扩展名和MIME类型都不匹配，则拒绝
      if (!hasValidExtension && !hasValidMimeType) {
        this.$showFriendlyError(null, `只能上传 Excel (.xlsx, .xls) 或 CSV (.csv) 文件！当前文件类型：${file.type || '未知'}`)
        return false
      }

      // 检查文件大小（10MB限制）
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$showFriendlyError(null, '上传文件大小不能超过 10MB!')
        return false
      }

      console.log('文件验证通过，设置uploadFile:', file.name);
      this.uploadFile = file
      return false // 阻止自动上传
    },

    // 上传成功
    handleUploadSuccess(response) {
      this.$message.success('文件上传成功')
      console.log('上传成功:', response)
    },

    // 上传失败
    handleUploadError(error) {
      this.$showFriendlyError(error, '文件上传失败')
      console.error('上传失败:', error)
    },

    // 移除文件
    removeFile() {
      this.uploadFile = null
      this.$refs.uploadRef?.clearFiles()
    },

    // 导入确认
    async handleImportConfirm() {
      if (!this.uploadFile) {
        this.$message.warning('请先选择文件')
        return
      }

      this.importSubmitting = true
      try {
        // 调用真实的导入接口
        const importData = {
          excelFile: this.uploadFile,
          virtualDatabaseId: this.databaseId
        }

        console.log('导入表结构接口调用参数:', {
          fileName: this.uploadFile.name,
          virtualDatabaseId: this.databaseId
        })

        const response = await api.schemaStore.importExcelColumns(importData)

        if (response && response.isSuccess) {
          const columnsData = response.data?.columns || []

          // 将返回的字段列表添加到新建数据表弹框的字段配置中
          if (columnsData && Array.isArray(columnsData) && columnsData.length > 0) {
            // 清空除第一个id字段外的其他字段
            this.createTableForm.fields = [this.createTableForm.fields[0]]

            // 将导入的字段添加到字段配置中
            columnsData.forEach(column => {
              const field = {
                name: column.columnName || column.name || '',
                type: this.mapDataTypeToFieldType(column.dataType || column.dataTypeName),
                description: column.description || column.comment || ''
              }
              this.createTableForm.fields.push(field)
            })

            this.$message.success(`已将 ${columnsData.length} 个字段添加到字段配置中，请检查并完善信息`)

            // 显示新建数据表弹框（如果未显示）
            if (!this.createTableDialogVisible) {
              this.createTableDialogVisible = true
            }
          }
        }

        this.importDialogVisible = false
        this.uploadFile = null
      } catch (error) {
        this.$message.error(error.message)
        console.error('导入表结构失败:', error)
      } finally {
        this.importSubmitting = false
      }
    },

    // 模拟文件下载
    simulateDownload(filename) {
      // 创建模拟下载
      const link = document.createElement('a')
      link.href = 'data:application/octet-stream;base64,UEsDBBQAAAAI' // 模拟文件内容
      link.download = filename
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    // 真实文件下载（当有真实接口时使用）
    downloadFile(data, filename) {
      const blob = new Blob([data])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(link.href)
    },
    // 数据表列表相关方法
    selectTable(table) {
      if (this.selectedTable?.id !== table.id) {
        this.selectedTable = table
        this.fetchTableStructure(table.id)
      }
    },
    handleEditTable(table) {
      this.selectedTable = table
      this.editTableForm = {
        name: table.name,
        description: table.description
      }
      this.editTableDialogVisible = true
    },
    handleEditTableDialogClose() {
      this.editTableDialogVisible = false
      this.resetEditTableForm()
    },
    resetEditTableForm() {
      this.editTableForm = {
        name: '',
        description: ''
      }
      if (this.$refs.editTableForm) {
        this.$refs.editTableForm.resetFields()
      }
    },
    handleEditTableConfirm() {
      this.$refs.editTableForm.validate(async (valid) => {
        if (valid) {
          this.editTableLoading = true

          try {
            // 构建请求参数
            const requestData = {
              tableId: this.selectedTable.id,
              description: this.editTableForm.description
            };

            console.log('更新数据表请求参数:', requestData);

            const response = await api.schemaStore.updateTable(requestData);

            if (response && response.isSuccess) {
              this.$message.success('数据表信息更新成功')
              this.handleEditTableDialogClose()
              // 刷新表列表
              await this.fetchTableList()
            }
          } catch (error) {
            console.error('更新数据表失败:', error)
          } finally {
            this.editTableLoading = false
          }
        }
      })
    },
    // 判断表名是否溢出
    isNameOverflow(name) {
      // 简单判断：如果名称长度超过15个字符，认为会溢出
      return name && name.length > 15
    },
    // 获取数据表列表
    async fetchTableList() {
      this.tableListLoading = true
      try {
        // 调用新的查询表数据接口，去掉includeColumns参数
        const response = await api.schemaStore.getTables({
          skipCount: 0,
          maxResultCount: 100,
          virtualDatabaseId: this.databaseId
        });
        console.log('表数据响应:', response);

        if (response && response.data && response.data.items) {
          // 将items转换为表列表格式
          this.tableList = response.data.items.map(table => ({
            id: table.id,
            name: table.tableName,
            description: table.description
          }));
        } else {
          this.tableList = [];
        }

        // 默认选中第一张表
        if (this.tableList.length > 0) {
          this.selectTable(this.tableList[0])
        }

        console.log('获取表列表接口调用', this.databaseId, '表格数量:', this.tableList.length)
      } catch (error) {
        console.error('获取数据表列表失败:', error)
      } finally {
        this.tableListLoading = false
      }
    },
    // 获取表结构
    async fetchTableStructure(tableId) {
      this.structureLoading = true
      try {
        // 调用新的字段配置接口获取列信息
        const response = await api.schemaStore.getTableColumns(tableId);
        console.log('字段配置响应:', response);

        if (response && response.data) {
          // 将字段数据转换为表结构格式
          this.structureData = response.data.map(column => ({
            id: column.id, // 保存字段ID用于后续编辑和删除
            name: column.name,
            type: column.dataTypeStr,
            comment: column.description || '',
            isPrimaryKey: column.isPrimaryKey || false, // 添加主键标识
            isEditing: false
          }));
        } else {
          this.structureData = [];
        }

        console.log('获取表结构信息:', tableId, '字段数量:', this.structureData.length)
      } catch (error) {
        console.error('获取表结构失败:', error)
        this.structureData = []
      } finally {
        this.structureLoading = false
      }
    },
    // 新增字段相关方法
    resetAddFieldForm() {
      this.addFieldForm = {
        name: '',
        type: '3',
        description: ''
      }
      if (this.$refs.addFieldForm) {
        this.$refs.addFieldForm.resetFields()
      }
    },
    handleAddFieldDialogClose() {
      this.addFieldDialogVisible = false
      this.resetAddFieldForm()
    },
    async handleAddFieldConfirm() {
      this.$refs.addFieldForm.validate(async (valid) => {
        if (valid) {
          if (!this.selectedTable) {
            this.$showFriendlyError(null, '请先选择一个数据表')
            return
          }

          this.addFieldLoading = true

          try {
            // 构建请求参数
            const requestData = {
              virtualTableId: this.selectedTable.id,
              fieldName: this.addFieldForm.name,
              dataType: parseInt(this.addFieldForm.type), // 确保传入整数类型
              description: this.addFieldForm.description,
              isPrimaryKey: false, // 新增字段默认不是主键
              isRequired: false // 新增字段默认非必填
            };

            console.log('新增字段请求参数:', requestData);

            const response = await api.schemaStore.createColumn(requestData);

            if (response && response.isSuccess) {
              this.$message.success('字段添加成功')
              this.handleAddFieldDialogClose()
              // 刷新表结构
              await this.fetchTableStructure(this.selectedTable.id)
            }
          } catch (error) {
            this.$message.error(error.message || '添加字段失败')
            console.error('添加字段失败:', error)
          } finally {
            this.addFieldLoading = false
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-config {
  height: 100%;
  display: flex;
  flex-direction: column;

  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .config-body {
    flex: 1;
    display: flex;

    .table-list-sidebar {
      width: 240px;
      border-right: 1px solid #ebeef5;
      display: flex;
      flex-direction: column;
      margin-left: -20px;

      .sidebar-header {
        padding: 20px 16px 16px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .create-table-btn {
          color: #409eff;
          font-size: 14px;
          padding: 0;

          &:hover {
            color: #66b1ff;
          }
        }
      }

      .table-list {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;

        .table-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 16px;
          margin: 0 8px 4px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          border: 1px solid transparent;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #e6f7ff;
            border-color: #b3d8ff;
          }

          .table-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 0; // 允许缩小

            .table-name {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              min-width: 0; // 允许缩小
            }

            .table-info-icon {
              color: #909399;
              font-size: 14px;
              cursor: pointer;
              transition: color 0.3s ease;
              flex-shrink: 0; // 不缩小

              &:hover {
                color: #409eff;
              }
            }
          }

          .edit-btn {
            color: #909399;
            font-size: 14px;
            transition: color 0.3s ease;

            &:hover {
              color: #409eff;
            }
          }
        }

        .empty-table-list {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 20px;
          color: #999;

          i {
            font-size: 32px;
            margin-bottom: 12px;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }

    .config-content {
      flex: 1;
      display: flex;
      flex-direction: column;

      .config-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 20px;

        .config-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .action-buttons {
            display: flex;
            gap: 12px;
          }
        }

        .table-structure {
          flex: 1;
          overflow: auto;

          .delete-btn {
            color: #f56c6c;
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;

        i {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 16px;
        }

        p {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }
}

// 新建数据表弹窗样式
.create-table-content {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .custom-form-item {
    :deep(.el-form-item__label) {
      // display: flex;
      // align-items: center;
      // gap: 4px;

      .required-star {
        color: #f56c6c;
        font-weight: bold;
        margin-right: 4px;
      }

      .help-icon {
        color: #909399;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .field-config-table {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

    .table-header {
      display: flex;
      background: linear-gradient(135deg, #f8f9fc 0%, #f5f7fa 100%);
      border-bottom: 1px solid #dcdfe6;

      .header-item {
        padding: 14px 12px;
        font-weight: 600;
        color: #303133;
        border-right: 1px solid #dcdfe6;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;

        &:last-child {
          border-right: none;
          justify-content: center;
        }

        .help-icon {
          color: #909399;
          cursor: pointer;
          font-size: 14px;
          transition: color 0.3s ease;

          &:hover {
            color: #409eff;
          }
        }
      }
    }

    .table-body {
      max-height: 320px;
      overflow-y: auto;

      .table-row {
        display: flex;
        border-bottom: 1px solid #ebeef5;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .row-item {
          padding: 12px;
          border-right: 1px solid #ebeef5;
          display: flex;
          align-items: center;

          &:last-child {
            border-right: none;
            justify-content: center;
          }

          .is-error {
            :deep(.el-input__inner) {
              border-color: #f56c6c;
              box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
            }
          }

          .delete-btn {
            color: #f56c6c;
            font-size: 16px;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.3s ease;

            &:hover {
              background-color: #fef0f0;
              color: #f56c6c;
            }

            &:disabled {
              color: #c0c4cc;
              cursor: not-allowed;

              &:hover {
                background-color: transparent;
              }
            }
          }
        }
      }
    }

    .table-footer {
      padding: 16px;
      background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
      border-top: 1px solid #ebeef5;
      display: flex;
      justify-content: center;

      .add-field-btn {
        padding: 10px 24px;
        font-weight: 500;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }

  // 选项中的tooltip样式
  :deep(.el-select-dropdown .el-option) {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .option-tooltip {
      margin-left: 8px;

      .option-help-icon {
        color: #909399;
        cursor: pointer;
        font-size: 12px;
        transition: color 0.3s ease;

        &:hover {
          color: #409eff;
        }
      }
    }
  }
}

// 表格导入弹窗样式
.import-content {
  .import-tip {
    margin: 0 0 20px 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }

  .upload-area {
    margin-bottom: 20px;

    :deep(.el-upload) {
      width: 100%;
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }

    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 8px;
    }

    .upload-text {
      color: #606266;
      font-size: 14px;

      .upload-main-text {
        margin-right: 4px;
      }

      .upload-link {
        color: #409eff;
        padding: 0;
        font-size: 14px;
      }
    }
  }

  .file-info {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .el-icon-document {
      font-size: 20px;
      color: #409eff;
      margin-right: 8px;
    }

    .file-name {
      flex: 1;
      color: #303133;
      font-size: 14px;
    }

    .remove-btn {
      color: #c0c4cc;
      padding: 4px;

      &:hover {
        color: #f56c6c;
      }

      .el-icon-close {
        font-size: 14px;
      }
    }
  }
}
</style>
