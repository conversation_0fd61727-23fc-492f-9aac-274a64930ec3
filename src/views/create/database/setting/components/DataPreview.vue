<template>
  <div class="data-preview">
    <div class="preview-header">
      <el-select
        v-model="selectedTable"
        placeholder="请选择表"
        class="table-select"
        @change="handleTableChange"
      >
        <el-option
          v-for="table in tableList"
          :key="table.value"
          :label="table.label"
          :value="table.value"
        />
      </el-select>

      <div class="header-actions">
        <el-button type="primary" size="small" @click="handleAdd">新增</el-button>
        <el-button type="default" size="small" @click="handleImport">导入</el-button>
        <el-button type="default" size="small" @click="handleExport">导出</el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%"
      height="calc(100vh - 180px)"
      v-loading="loading"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        show-overflow-tooltip
      >
        <template slot="header">
          <el-tooltip :content="column.label" placement="top" :disabled="!isTextOverflow(column.label)">
            <span class="table-header-text">{{ column.label }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns.length > 0"
        label="操作"
        width="160"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)" class="delete-btn">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="preview-footer">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑/查看数据弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="addDialogVisible"
      :close-on-click-modal="false"
      width="500px"
      @close="handleAddDialogClose"
    >
      <el-form
        v-if="formColumns.length > 0"
        :model="addForm"
        ref="addForm"
        label-position="top"
        class="add-form"
      >
        <el-form-item
          v-for="column in formColumns"
          :key="column.prop"
          :label="column.label"
          class="form-item"
        >
          <el-input
            v-if="getInputType(column) === 'text'"
            v-model="addForm[column.prop]"
            :placeholder="getPlaceholder(column)"
            :disabled="dialogMode === 'view'"
          />
          <el-input
            v-else-if="getInputType(column) === 'number'"
            v-model="addForm[column.prop]"
            type="number"
            :placeholder="getPlaceholder(column)"
            :disabled="dialogMode === 'view'"
          />
          <el-input
            v-else-if="getInputType(column) === 'textarea'"
            v-model="addForm[column.prop]"
            type="textarea"
            :rows="3"
            :placeholder="getPlaceholder(column)"
            :disabled="dialogMode === 'view'"
          />
          <el-date-picker
            v-else-if="getInputType(column) === 'datetime'"
            v-model="addForm[column.prop]"
            type="datetime"
            :placeholder="getPlaceholder(column)"
            :disabled="dialogMode === 'view'"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
          <el-input
            v-else
            v-model="addForm[column.prop]"
            :placeholder="getPlaceholder(column)"
            :disabled="dialogMode === 'view'"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAddDialogClose">{{ dialogMode === 'view' ? '关 闭' : '取 消' }}</el-button>
        <el-button
          v-if="dialogMode !== 'view'"
          type="primary"
          @click="handleAddConfirm"
          :loading="addSubmitting"
        >
          {{ dialogMode === 'edit' ? '保 存' : '确 定' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 导入数据弹窗 -->
    <el-dialog
      title="导入表格"
      :visible.sync="importDialogVisible"
      :close-on-click-modal="false"
      width="600px"
      @close="handleImportDialogClose"
    >
      <div class="import-content">
        <p class="import-tip">
          上传excel 或 csv 表格，表格中列名需要和数据库字段一致，
          <el-button type="text" @click="downloadTemplate" class="download-link">点击下载模板</el-button>
        </p>

        <el-upload
          ref="uploadRef"
          class="upload-area"
          drag
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-change="handleFileChange"
          :show-file-list="false"
          :auto-upload="false"
          accept=".xlsx,.xls,.csv"
        >
          <i class="el-icon-upload2 upload-icon"></i>
          <div class="upload-text">
            <span class="upload-main-text">拖入文件或</span>
            <el-button type="text" class="upload-link">点击上传</el-button>
          </div>
        </el-upload>

        <div v-if="uploadFile" class="file-info">
          <i class="el-icon-document"></i>
          <span class="file-name">{{ uploadFile.name }}</span>
          <el-button type="text" @click="removeFile" class="remove-btn">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleImportDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleImportConfirm"
          :loading="importSubmitting"
          :disabled="!uploadFile"
        >
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="delete-content">
        <i class="el-icon-warning" style="color: #E6A23C; font-size: 24px; margin-right: 10px;"></i>
        <span>确定要删除这条数据吗？此操作不可恢复。</span>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleteSubmitting">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api/request'

export default {
  name: 'DataPreview',
  props: {
    databaseId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      selectedTable: '',
      tableList: [], // 表列表
      currentPage: 1,
      pageSize: 20,
      total: 0,
      columns: [], // 动态列头
      tableData: [], // 表数据
      loading: false, // 加载状态
      addDialogVisible: false, // 新增弹窗显示状态
      addForm: {}, // 新增表单数据
      addSubmitting: false, // 新增提交状态
      formColumns: [], // 表单字段列表
      dialogMode: 'add', // 弹窗模式：add-新增、edit-编辑、view-查看
      currentRow: null, // 当前操作的行数据
      importDialogVisible: false, // 导入弹窗显示状态
      importSubmitting: false, // 导入提交状态
      uploadFile: null, // 上传的文件
      uploadAction: 'https://api.temp.com/upload', // 临时占位，等待后端提供真实接口
      deleteDialogVisible: false, // 删除确认弹窗显示状态
      deleteSubmitting: false // 删除提交状态
    }
  },
  computed: {
    // 弹窗标题
    dialogTitle() {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return titleMap[this.dialogMode] || '新增'
    }
  },
  created() {
    this.loadTableList()
  },
  methods: {
    // 获取表列表
    async loadTableList() {
      try {
        // 调用新的查询表数据接口，去掉includeColumns参数
        const response = await api.schemaStore.getTables({
          skipCount: 0,
          maxResultCount: 100,
          virtualDatabaseId: this.databaseId
        });
        console.log('表数据响应:', response);

        if (response && response.data && response.data.items) {
          // 将items转换为下拉选项格式
          this.tableList = response.data.items.map(table => ({
            label: table.tableName,
            value: table.id
          }));
        } else {
          this.tableList = [];
        }

        // 默认选中第一条
        if (this.tableList.length > 0) {
          this.selectedTable = this.tableList[0].value
          this.handleTableChange(this.selectedTable)
        }

        console.log('获取表列表接口调用', this.databaseId, '表格数量:', this.tableList.length)
      } catch (error) {
        console.error('获取表列表失败:', error)
      }
    },

    // 表切换
    async handleTableChange(tableId) {
      if (!tableId) return

      this.loading = true
      try {
        // 并行请求表头和数据
        await Promise.all([
          this.loadTableColumns(tableId),
          this.loadTableData(tableId)
        ])
      } catch (error) {
        this.$showFriendlyError(error, '加载表数据失败')
        console.error('加载表数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取表头结构
    async loadTableColumns(tableId) {
      try {
        // 调用新的字段配置接口获取列信息
        const response = await api.schemaStore.getTableColumns(tableId);
        console.log('字段配置响应:', response);

        if (response && response.data) {
          // 将字段数据转换为表格列格式
          this.columns = response.data.map(column => ({
            prop: column.name,
            label: column.name,
            dataType: column.dataType, // 保持完整的dataType对象
            isNullable: column.isNullable,
            isPrimaryKey: column.isPrimaryKey,
            maxLength: column.maxLength,
            description: column.description
          }));
        } else {
          this.columns = [];
        }

        console.log('获取表头信息:', tableId, '列数量:', this.columns.length)
      } catch (error) {
        console.error('获取表头失败:', error)
        this.columns = []
      }
    },

    // 获取表数据
    async loadTableData(tableId, page = 1) {
      try {
        // 调用新的数据记录接口
        const response = await api.schemaStore.getDataRecords({
          virtualTableId: tableId,
          skipCount: (page - 1) * this.pageSize, // 从0开始，需要减1
          maxResultCount: this.pageSize
        });
        // 处理返回的数据结构
        if (response && response.data) {
          // 处理数据，将fieldValues中的字段值展开到记录根级别
          const items = response.data.items || response.data || [];
          this.tableData = items.map(item => {
            // 将fieldValues中的字段值展开到根级别
            const processedItem = {
              id: item.id,
              createTime: item.createTime,
              updateTime: item.updateTime,
              ...item.fieldValues // 展开fieldValues中的字段值
            };

            // 格式化 time 字段：从 MM/dd/yyyy HH:mm:ss 转换为 yyyy/MM/dd HH:mm:ss
            if (processedItem.time && processedItem.time.trim()) {
              try {
                const timeStr = processedItem.time.trim();
                // 匹配 MM/dd/yyyy HH:mm:ss 格式
                const timeMatch = timeStr.match(/^(\d{2})\/(\d{2})\/(\d{4})\s+(.+)$/);
                if (timeMatch) {
                  const [, month, day, year, timePart] = timeMatch;
                  processedItem.time = `${year}/${month}/${day} ${timePart}`;
                }
              } catch (error) {
                console.warn('时间格式转换失败:', processedItem.time, error);
              }
            }

            return processedItem;
          });

          this.total = response.data.totalCount || response.data.total || 0;
        } else {
          this.tableData = [];
          this.total = 0;
        }

        this.currentPage = page;
      } catch (error) {
        this.tableData = [];
        this.total = 0;
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      if (this.selectedTable) {
        this.loadTableData(this.selectedTable, 1)
      }
    },

    handleCurrentChange(val) {
      this.currentPage = val
      if (this.selectedTable) {
        this.loadTableData(this.selectedTable, val)
      }
    },

    // 新增按钮点击
    handleAdd() {
      if (!this.selectedTable) {
        this.$message.warning('请先选择表')
        return
      }

      // 设置表单字段（排除主键字段和系统字段）
      this.formColumns = this.columns.filter(col =>
        !col.isPrimaryKey && !['createTime', 'updateTime'].includes(col.prop)
      )

      // 初始化表单数据
      this.addForm = {}
      this.formColumns.forEach(col => {
        this.$set(this.addForm, col.prop, '')
      })

      console.log('表单字段:', this.formColumns);
      console.log('表单数据:', this.addForm);

      this.addDialogVisible = true
    },

    // 导入按钮点击
    handleImport() {
      if (!this.selectedTable) {
        this.$message.warning('请先选择表')
        return
      }
      this.importDialogVisible = true
    },

    // 导出按钮点击
    async handleExport() {
      if (!this.selectedTable) {
        this.$message.warning('请先选择表')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '正在导出数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 获取当前选中表的名称
        const selectedTableInfo = this.tableList.find(table => table.value === this.selectedTable)
        const tableName = selectedTableInfo ? selectedTableInfo.label : `table_${this.selectedTable}`

        console.log('导出数据接口调用参数:', {
          virtualTableId: this.selectedTable,
          tableName: tableName
        })

        // 调用导出接口获取Blob数据
        const response = await api.schemaStore.exportDataRecordsToExcel(this.selectedTable, tableName)

        // 关闭加载提示
        loading.close()

        // 检查响应是否是blob类型
        if (response instanceof Blob) {
          // 创建下载链接
          const url = window.URL.createObjectURL(response)
          const link = document.createElement('a')
          link.href = url
          link.download = `${tableName}_数据导出.xlsx`
          link.style.display = 'none'

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$message.success('数据导出成功')
        } else {
          this.$showFriendlyError(null, '导出失败，响应格式异常')
        }
      } catch (error) {
        loading.close()
        console.error('导出数据失败:', error)
        let errorMessage = '导出失败，请稍后重试'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        this.$showFriendlyError(null, errorMessage)
      }
    },

    // 新增/编辑/查看弹窗关闭
    handleAddDialogClose() {
      this.addDialogVisible = false
      // 重置状态
      this.dialogMode = 'add'
      this.currentRow = null
      // 清空表单数据
      this.addForm = {}
      this.$nextTick(() => {
        this.$refs.addForm?.resetFields()
      })
    },

    // 新增/编辑确认
    async handleAddConfirm() {
      // 直接提交，不需要表单验证
      this.addSubmitting = true
      try {
        if (this.dialogMode === 'edit') {
          // 调用编辑数据记录接口
          const requestData = {
            virtualTableId: this.selectedTable,
            recordId: this.currentRow.id,
            fieldValues: {
              ...this.addForm
            }
          };

          console.log('编辑数据记录请求参数:', requestData);

          const response = await api.schemaStore.updateDataRecord(this.currentRow.id, requestData);

          if (response && response.isSuccess) {
            this.$message.success('编辑成功')
          }
        } else {
          // 调用新增数据记录接口
          const requestData = {
            virtualTableId: this.selectedTable,
            fieldValues: {
              ...this.addForm
            }
          };

          console.log('新增数据记录请求参数:', requestData);

          const response = await api.schemaStore.createDataRecord(requestData);

          if (response && response.isSuccess) {
            this.$message.success('新增成功')
          }
        }

        this.addDialogVisible = false
        this.$nextTick(() => {
          this.$refs.addForm?.resetFields()
        })

        // 刷新数据
        this.loadTableData(this.selectedTable, this.currentPage)
      } catch (error) {
        this.$message.error(error.message)
        const action = this.dialogMode === 'edit' ? '编辑' : '新增'
        console.error(`${action}数据失败:`, error)
      } finally {
        this.addSubmitting = false
      }
    },

    // 获取输入框类型
    getInputType(column) {
      // 根据column中dataType.value进行判断
      // 1整数，2小数，3文本，4日期时间，5长文本
      if (column.dataType && column.dataType.value) {
        const dataTypeValue = column.dataType.value;
        switch (dataTypeValue) {
          case 1: // 整数
          case 2: // 小数
            return 'number';
          case 3: // 文本
            return 'text';
          case 4: // 日期时间
            return 'datetime';
          case 5: // 长文本
            return 'textarea';
          default:
            return 'text';
        }
      }

      // 如果没有dataType信息，默认返回text
      return 'text';
    },

    // 获取占位符文本
    getPlaceholder(column) {
      const inputType = this.getInputType(column)
      const typeMap = {
        'text': '请输入',
        'number': '请输入数字',
        'textarea': '请详细描述',
        'datetime': '请选择日期时间'
      }
      return `${typeMap[inputType] || '请输入'}${column.label}`
    },

    // 导入弹窗关闭
    handleImportDialogClose() {
      this.importDialogVisible = false
      this.uploadFile = null
      this.$refs.uploadRef?.clearFiles()
    },

    // 下载模板
    async downloadTemplate() {
      if (!this.selectedTable) {
        this.$message.warning('请先选择表')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '正在生成模板...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 调用导出接口获取Blob数据
        const response = await api.schemaStore.exportTableColumnsTemplate(this.selectedTable)

        // 关闭加载提示
        loading.close()

        // 检查响应是否是blob类型
        if (response instanceof Blob) {
          // 创建下载链接
          const url = window.URL.createObjectURL(response)
          const link = document.createElement('a')
          link.href = url
          let _name = this.tableList.filter(it => it.value === this.selectedTable)[0].label
          link.download = `${_name}_template.xlsx`
          link.style.display = 'none'

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$message.success('模板下载成功')
        } else {
          this.$showFriendlyError(null, '下载失败，响应格式异常')
        }
      } catch (error) {
        loading.close()
        console.error('下载模板失败:', error)
        this.$showFriendlyError(error, '模板下载失败，请稍后重试')
      }
    },

    // 文件状态改变时的回调（备用方法）
    handleFileChange(file) {
      console.log('DataPreview handleFileChange被调用:', file);
      if (file.raw) {
        this.beforeUpload(file.raw);
      }
    },

    // 文件上传前检查
    beforeUpload(file) {
      console.log("DataPreview beforeUpload - file:",file)
      const isExcelOrCsv = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                          file.type === 'application/vnd.ms-excel' ||
                          file.type === 'text/csv'

      if (!isExcelOrCsv) {
        this.$showFriendlyError(null, '只能上传 Excel 或 CSV 文件!')
        return false
      }

      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$showFriendlyError(null, '上传文件大小不能超过 10MB!')
        return false
      }

      this.uploadFile = file
      return false // 阻止自动上传
    },

    // 上传成功
    handleUploadSuccess(response) {
      this.$message.success('文件上传成功')
      console.log('上传成功:', response)
    },

    // 上传失败
    handleUploadError(error) {
      this.$showFriendlyError(error, '文件上传失败')
      console.error('上传失败:', error)
    },

    // 移除文件
    removeFile() {
      this.uploadFile = null
      this.$refs.uploadRef?.clearFiles()
    },

    // 导入确认
    async handleImportConfirm() {
      if (!this.uploadFile) {
        this.$message.warning('请先选择文件')
        return
      }

      if (!this.selectedTable) {
        this.$message.warning('请先选择表')
        return
      }

      this.importSubmitting = true
      try {
        // 调用真实的导入接口
        const importData = {
          excelFile: this.uploadFile,
          virtualTableId: this.selectedTable,
          hasHeader: true // 假设文件有表头，可以根据需要调整
        }

        console.log('导入数据接口调用参数:', {
          fileName: this.uploadFile.name,
          virtualTableId: this.selectedTable,
          hasHeader: true
        })

        const response = await api.schemaStore.importDataRecordsFromExcel(importData)

        if (response && response.isSuccess) {
          this.$message.success(`导入成功！共处理 ${response.data?.totalRows || 0} 行数据`)

          // 如果有导入结果详情，可以显示更多信息
          if (response.data) {
            const { successRows, failedRows } = response.data
            if (failedRows > 0) {
              this.$message.warning(`导入完成：成功 ${successRows} 行，失败 ${failedRows} 行`)
            }
          }
        } else {
          this.$showFriendlyError(null, response.message || '导入失败')
        }

        this.importDialogVisible = false
        this.uploadFile = null

        // 刷新数据
        this.loadTableData(this.selectedTable, this.currentPage)
      } catch (error) {
        console.error('导入数据失败:', error)
        let errorMessage = '导入失败，请检查文件格式'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        this.$showFriendlyError(null, errorMessage)
      } finally {
        this.importSubmitting = false
      }
    },

    // 模拟文件下载
    simulateDownload(filename) {
      // 创建模拟下载
      const link = document.createElement('a')
      link.href = 'data:application/octet-stream;base64,UEsDBBQAAAAI' // 模拟文件内容
      link.download = filename
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    // 真实文件下载（当有真实接口时使用）
    downloadFile(data, filename) {
      const blob = new Blob([data])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(link.href)
    },

    // 查看数据
    handleView(row) {
      this.dialogMode = 'view'
      this.currentRow = row

      // 设置表单字段（显示所有字段）
      this.formColumns = [...this.columns]

      // 填充表单数据
      this.addForm = { ...row }

      this.addDialogVisible = true
    },

    // 编辑数据
    handleEdit(row) {
      this.dialogMode = 'edit'
      this.currentRow = row

      // 设置表单字段（排除主键字段和系统字段）
      this.formColumns = this.columns.filter(col =>
        !col.isPrimaryKey && !['createTime', 'updateTime'].includes(col.prop)
      )

      // 填充表单数据（只填充可编辑字段）
      this.addForm = {}
      this.formColumns.forEach(col => {
        this.$set(this.addForm, col.prop, row[col.prop] || '')
      })

      this.addDialogVisible = true
    },

    // 删除数据
    handleDelete(row) {
      this.currentRow = row
      this.deleteDialogVisible = true
    },

    // 确认删除
    async confirmDelete() {
      this.deleteSubmitting = true
      try {
        // 调用删除数据记录接口
        const params = {
          virtualTableId: this.selectedTable,
          recordId: this.currentRow.id
        };

        console.log('删除数据记录请求参数:', params);

        const response = await api.schemaStore.deleteDataRecord(this.currentRow.id, params);

        if (response && response.isSuccess) {
          this.$message.success('删除成功')
        }

        this.deleteDialogVisible = false
        this.currentRow = null

        // 刷新数据
        this.loadTableData(this.selectedTable, this.currentPage)
      } catch (error) {
        console.error('删除数据失败:', error)
      } finally {
        this.deleteSubmitting = false
      }
    },

    // 检测文本是否溢出（用于表头tooltip）
    isTextOverflow(text) {
      if (!text) return false
      // 简单的长度检测，可以根据实际需要调整
      return text.length > 10
    }
  }
}
</script>

<style lang="scss" scoped>
.data-preview {
  height: 100%;
  display: flex;
  flex-direction: column;

    .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .table-select {
      width: 240px;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .preview-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.add-form {
  .form-item {
    margin-bottom: 20px;

    :deep(.el-form-item__label) {
      padding-bottom: 8px;
      line-height: 1;
      color: #606266;
      font-weight: 500;
    }
  }
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

.import-content {
  .import-tip {
    margin: 0 0 20px 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;

    .download-link {
      color: #409eff;
      padding: 0;
      font-size: 14px;
    }
  }

  .upload-area {
    margin-bottom: 20px;

    :deep(.el-upload) {
      width: 100%;
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }

    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 8px;
    }

    .upload-text {
      color: #606266;
      font-size: 14px;

      .upload-main-text {
        margin-right: 4px;
      }

      .upload-link {
        color: #409eff;
        padding: 0;
        font-size: 14px;
      }
    }
  }

  .file-info {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    .el-icon-document {
      font-size: 20px;
      color: #409eff;
      margin-right: 8px;
    }

    .file-name {
      flex: 1;
      color: #303133;
      font-size: 14px;
    }

    .remove-btn {
      color: #c0c4cc;
      padding: 4px;

      &:hover {
        color: #f56c6c;
      }

      .el-icon-close {
        font-size: 14px;
      }
    }
  }
}

// 操作列样式
:deep(.el-table) {
  .delete-btn {
    color: #f56c6c;

    &:hover {
      color: #f56c6c;
    }
  }

  // 表头单元格省略号效果
  .el-table__header-wrapper {
    .el-table__header {
      th {
        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          .table-header-text {
            display: inline-block;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

// 删除弹窗样式
.delete-content {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}
</style>
