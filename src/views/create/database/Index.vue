<template>
  <div class="create-database-page">
    <router-view v-if="$route.params.id"></router-view>
    <div class="page-content" v-else>
      <div
        v-loading="loading"
        class="loading-container"
        element-loading-text="加载数据库中..."
      >
        <div class="database-container">
          <!-- 数据库卡片列表 -->
          <div
            v-for="(database, index) in databaseList"
            :key="database.id"
            class="database-card"
            :style="{ '--card-index': index }"
            @click="handleSettingClick(database)"
          >
            <div class="database-actions">
              <div
                class="action-icon"
                @click.stop="handleSettingClick(database)"
              >
                <i class="el-icon-setting"></i>
              </div>
            </div>

            <div class="database-content">
              <div class="database-header">
                <i
                  :class="[
                    'database-icon',
                    database.dbType === 'internal'
                      ? 'el-icon-coin'
                      : 'el-icon-connection',
                  ]"
                ></i>
                <h3 class="database-name">{{ database.name }}</h3>
              </div>

              <el-tooltip
                :content="database.description"
                placement="top"
                :disabled="database.description.length <= 60"
              >
                <p class="database-desc">{{ database.description }}</p>
              </el-tooltip>

              <div class="database-footer">
                <div class="database-info">
                  <el-tag
                    :class="[
                      'db-type-tag',
                      database.dbType === 'internal'
                        ? 'internal-tag'
                        : 'remote-tag',
                    ]"
                  >
                    {{
                      database.dbType === "internal"
                        ? "内置数据库"
                        : "远程数据库"
                    }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 创建数据库卡片 -->
          <div class="create-card" @click="handleCreateDatabase">
            <i class="el-icon-plus"></i>
            <span>创建数据库</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建数据库弹窗 -->
    <el-dialog
      title="新建数据库"
      :visible.sync="createDialogVisible"
      :close-on-click-modal="false"
      width="600px"
      @close="handleDialogClose"
    >
      <!-- 弹框右上角的查看文档按钮 -->
      <div class="dialog-header-actions">
        <el-button type="text" @click="handleViewDocs" class="view-docs-btn">
          <i class="el-icon-document"></i>
          查看文档
        </el-button>
      </div>

      <!-- 数据库类型选择 -->
      <div class="database-type-select">
        <div
          :class="['type-card', { active: form.type === 'internal' }]"
          @click="form.type = 'internal'"
        >
          <el-tooltip
            content="使用系统内置的数据库，无需额外配置，支持增删改查操作"
            placement="top"
            class="tooltip-wrapper"
            @click.native.stop
          >
            <i class="el-icon-question tooltip-icon"></i>
          </el-tooltip>
          <i class="el-icon-coin"></i>
          <span>内置数据库</span>
        </div>
        <div
          :class="['type-card', { active: form.type === 'remote', disabled: true }]"
          @click="handleRemoteClick"
        >
          <el-tooltip
            content="连接外部数据库，需要配置连接信息，仅支持查询操作（暂未开放）"
            placement="top"
            class="tooltip-wrapper"
            @click.native.stop
          >
            <i class="el-icon-question tooltip-icon"></i>
          </el-tooltip>
          <i class="el-icon-connection"></i>
          <span>远程数据库</span>
          <div class="disabled-overlay">敬请期待</div>
        </div>
      </div>

      <!-- 基础信息表单 -->
      <div class="form-section">
        <div class="section-title">基础信息</div>
        <el-form
          :model="form"
          ref="createForm"
          :rules="rules"
          label-position="top"
        >
          <el-form-item label="数据库名称" prop="name" class="form-item">
            <el-input
              v-model="form.name"
              placeholder="请填写数据库名称"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item>

          <el-form-item label="数据库描述" prop="description" class="form-item">
            <el-input
              type="textarea"
              v-model="form.description"
              placeholder="请详细描述您的数据库使用场景"
              :rows="4"
              maxlength="1000"
              show-word-limit
            ></el-input>
          </el-form-item>

          <el-form-item label="权限设置" prop="permissions" class="form-item">
            <el-checkbox-group v-model="form.permissions">
              <el-checkbox :label="1" :disabled="form.type === 'remote'"
                >查询</el-checkbox
              >
              <el-checkbox v-if="form.type === 'internal'" :label="2"
                >新增</el-checkbox
              >
              <el-checkbox v-if="form.type === 'internal'" :label="4"
                >编辑</el-checkbox
              >
              <el-checkbox v-if="form.type === 'internal'" :label="8"
                >删除</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 连接信息表单，仅在选择远程数据库时显示 -->
      <div v-if="form.type === 'remote'" class="form-section">
        <div class="section-title">连接信息</div>
        <el-form
          :model="form"
          ref="connectionForm"
          :rules="connectionRules"
          label-position="top"
        >
          <el-form-item label="数据库类型" prop="dbType" class="form-item">
            <el-select
              v-model="form.dbType"
              placeholder="请选择数据库类型"
              class="full-width"
            >
              <el-option label="MySQL" value="mysql">
                <img src="mysql-logo.png" class="db-type-icon" alt="MySQL" />
                <span>MySQL</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="主机" prop="host" class="form-item">
            <el-input v-model="form.host" placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item label="端口" prop="port" class="form-item">
            <el-input v-model="form.port" placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item label="用户名" prop="username" class="form-item">
            <el-input v-model="form.username" placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item label="密码" prop="password" class="form-item">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入"
              show-password
            ></el-input>
          </el-form-item>

          <el-form-item label="数据库名" prop="database" class="form-item">
            <el-input v-model="form.database" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleCreateConfirm"
          :loading="submitting"
          >{{ form.type === "remote" ? "下一步" : "确定" }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";

export default {
  name: "CreateDatabasePage",
  data() {
    return {
      loading: false, // 页面加载状态
      createDialogVisible: false,
      submitting: false, // 提交状态
      form: {
        type: "internal",
        name: "",
        description: "",
        permissions: [1, 2],
        dbType: "mysql",
        host: "",
        port: "3306",
        username: "",
        password: "",
        database: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入数据库名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入数据库描述", trigger: "blur" },
          { min: 1, max: 1000, message: "长度在 1 到 1000 个字符", trigger: "blur" },
        ],
      },
      connectionRules: {
        dbType: [
          { required: true, message: "请选择数据库类型", trigger: "change" },
        ],
        host: [{ required: true, message: "请输入主机地址", trigger: "blur" }],
        port: [
          { required: true, message: "请输入端口号", trigger: "blur" },
          { pattern: /^\d+$/, message: "端口号必须为数字", trigger: "blur" },
        ],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        database: [
          { required: true, message: "请输入数据库名", trigger: "blur" },
        ],
      },
      databaseList: [],
    };
  },
  watch: {
    "form.type": {
      handler(newType) {
        if (newType === "remote") {
          this.form.permissions = [1];
                  } else {
            this.form.permissions = [1, 2];
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.fetchDatabaseList();
  },
  methods: {
    // 获取数据库列表
    async fetchDatabaseList() {
      this.loading = true;
      try {
        const response = await api.schemaStore.getDatabases({
          skipCount: 0,
          maxResultCount: 100,
        });
        console.log(response);

        if (response && response.data && response.data.items) {
          // 映射接口返回的数据到组件需要的格式
          this.databaseList = response.data.items.map((item) => ({
            id: item.id,
            name: item.databaseName,
            description: item.description,
            dbType: item.databaseType?.value === 1 ? "internal" : "remote",
          }));
        } else {
          this.databaseList = [];
        }
      } catch (error) {
        console.error("获取数据库列表失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 创建数据库
    async createDatabase() {
      if(this.form.permissions.length < 1){
        this.$showFriendlyError(null, "请至少选择一个权限！");
        return;
      }
      this.submitting = true;
      try {
        // 构建请求参数
        const requestData = {
          databaseName: this.form.name,
          description: this.form.description,
          databaseType: this.form.type === "internal" ? 1 : 2, // 1=内置，2=远程
          permissions: this.form.permissions, // 直接使用数字数组
        };

        // 如果是远程数据库，添加连接信息
        if (this.form.type === "remote") {
          requestData.connectionString = this.buildConnectionString();
        }

        const response = await api.schemaStore.createDatabase(requestData);

        if (response && response.isSuccess) {
          this.$message.success("创建成功");
          this.createDialogVisible = false;
          this.resetForm();
          // 刷新数据库列表
          this.fetchDatabaseList();
        }
      } catch (error) {
        console.error("创建数据库失败:", error);
      } finally {
        this.submitting = false;
      }
    },



    // 构建连接字符串（远程数据库）
    buildConnectionString() {
      const { dbType, host, port, username, password, database } = this.form;
      if (dbType === "mysql") {
        return `Server=${host};Port=${port};Database=${database};Uid=${username};Pwd=${password};`;
      }
      // 可以根据需要支持其他数据库类型
      return "";
    },

    // 重置表单
    resetForm() {
      this.$refs.createForm?.resetFields();
      this.$refs.connectionForm?.resetFields();
      // 手动重置一些字段
      this.form.type = "internal";
      this.form.permissions = [1, 2];
      this.form.port = "3306";
    },

    handleCreateDatabase() {
      this.createDialogVisible = true;
    },
    handleSettingClick(database) {
      this.$router
        .push({
          name: "DatabaseSetting",
          params: { id: database.id },
        })
        .catch((err) => {
          if (err.name !== "NavigationDuplicated") {
            throw err;
          }
        });
    },
    handleDialogClose() {
      this.createDialogVisible = false;
      this.resetForm();
    },
    handleCreateConfirm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 如果是远程数据库，还需要验证连接信息
          if (this.form.type === "remote") {
            this.$refs.connectionForm.validate((connectionValid) => {
              if (connectionValid) {
                this.createDatabase();
              }
            });
          } else {
            this.createDatabase();
          }
        }
      });
    },
    handleViewDocs() {
      this.$message.info("为了给您更完美的体验，我们正在做最后的冲刺，很快解锁");
    },
    handleRemoteClick() {
      this.$message.warning("远程数据库功能暂未开放，敬请期待！");
    },
  },
};
</script>

<style lang="scss" scoped>
.create-database-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 20px);
  border-radius: 12px 0 0 12px;

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 10px;
      font-weight: 500;
    }

    .page-desc {
      color: #606266;
      font-size: 14px;
      margin: 0;
    }
  }

  .page-content {
    .el-card + .el-card {
      margin-top: 20px;
    }
  }

  .database-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }

  .database-card {
    position: relative;
    width: 100%;
    height: 195px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    overflow: hidden;
    border: 1px solid #eaeaea;
    box-sizing: border-box;
    padding: 16px 20px;
    margin-bottom: 0;
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: calc(var(--card-index, 0) * 0.1s);
    opacity: 0;
    transform-origin: center;

    &:hover {
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.98);
    }

    .database-actions {
      position: absolute;
      top: 16px;
      right: 20px;
      display: flex;
      gap: 12px;

      .action-icon {
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.2s;

        &:hover {
          opacity: 0.8;
        }

        i {
          font-size: 16px;
          color: #666;
        }
      }
    }

    .database-content {
      .database-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;

        .database-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          flex-shrink: 0;
          font-size: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
        }

        .database-name {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          line-height: 1.4;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .database-desc {
        margin: 0 0 20px 0;
        color: #666666;
        font-size: 14px;
        line-height: 1.5;
        height: 42px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .database-footer {
        position: absolute;
        bottom: 16px;
        right: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .database-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .db-type-tag {
            border: none;
            font-size: 12px;
            height: 22px;
            line-height: 20px;
            border-radius: 4px;
            padding: 0 8px;

            &.internal-tag {
              background: #e7f2ff;
              color: #2187fa;
            }

            &.remote-tag {
              background: #ddfbfe;
              color: #00adbe;
            }
          }

          .database-details {
            color: #666666;
            font-size: 12px;
            line-height: 1.4;
          }
        }

        .status-tag {
          border-radius: 4px;
          font-size: 12px;
          height: 24px;
          line-height: 22px;
          padding: 0 8px;

          &.status-running {
            background: #ebfdec;
            color: #54c86a;
            border: 1px solid #54c86a;
          }

          &.status-maintenance {
            background: #f6f6f6;
            color: #c2c2c2;
            border: 1px solid #c2c2c2;
          }
        }
      }
    }
  }

  .create-card {
    position: relative;
    width: 100%;
    height: 195px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    border: 1px solid #eaeaea;
    box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: 0.3s;
    opacity: 0;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
      background: var(--el-color-primary-light-9);

      i,
      span {
        color: var(--el-color-primary);
      }

      i {
        transform: rotate(90deg);
      }
    }

    &:active {
      transform: scale(0.98);
    }

    i {
      font-size: 32px;
      margin-bottom: 12px;
      color: #9ca3af;
      transition: all 0.3s ease;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #9ca3af;
      transition: color 0.3s ease;
    }
  }
}

.loading-container {
  min-height: 300px;
}

.form-item {
  margin-bottom: 24px;

  :deep(.el-form-item__label) {
    padding-bottom: 8px;
    line-height: 1;
    color: #606266;
  }
}

.dialog-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 14px;

  i {
    color: var(--el-color-primary);
  }
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
  }

  .el-dialog__body {
    padding: 24px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

.dialog-header-actions {
  position: absolute;
  top: 20px;
  right: 60px;
  z-index: 1;

  .view-docs-btn {
    color: #409eff;
    padding: 8px 12px;
    font-size: 14px;

    &:hover {
      background-color: #ecf5ff;
      border-radius: 4px;
    }

    i {
      margin-right: 4px;
    }
  }
}

.database-type-select {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;

  .type-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }

    &.active {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);

      i,
      span {
        color: var(--el-color-primary);
      }
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
      position: relative;

      &:hover {
        border-color: #e4e7ed;
        background: #fff;
      }
    }

    .disabled-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #909399;
      z-index: 1;
    }

    .tooltip-wrapper {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 18px;
    }

    i {
      font-size: 32px;
      margin-bottom: 12px;
      color: #909399;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.form-section {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 20px;
  }
}

.full-width {
  width: 100%;
}

.db-type-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  align-items: center;
}
</style>
