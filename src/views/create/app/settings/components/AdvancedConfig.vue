<template>
  <div class="settings-content" :class="{ 'debug-layout': isDebugging }">
    <!-- 调试模式下的左右布局 -->
    <div v-if="isDebugging" class="debug-container">
      <!-- 左侧配置区域 -->
      <div class="config-area">
        <div class="config-sections">
          <!-- 转人工设置 -->
          <div class="config-card human-transfer-config">
            <div class="card-header">
              <h3>转人工设置</h3>
            </div>
            <div class="card-content">
              <div class="config-row">
                <div class="config-item">
                  <span class="config-label">开启转人工</span>
                  <div class="config-control">
                    <el-switch
                      v-model="humanTransferConfig.nodeTypeConfig.isEnable"
                      @change="updateHumanTransferConfig"
                    />
                  </div>
                </div>
              </div>

              <!-- 转人工详细配置 - 当开启时显示 -->
              <div v-if="humanTransferConfig.nodeTypeConfig.isEnable" class="human-transfer-details">
                <!-- 分割线 -->
                <div class="config-divider"></div>

                <!-- 按照demo样式的左右分栏布局 -->
                <div class="demo-transfer-layout">
                  <!-- 左栏：触发规则、默认回复、回复模式 -->
                  <div class="demo-left-column">
                    <!-- 触发规则 -->
                    <div class="demo-section">
                      <div class="demo-section-header">
                        <span class="demo-section-title">触发规则</span>
                        <span class="demo-section-desc">当用户提问满足下列设置的任意条件时，触发转人工</span>
                      </div>

                      <!-- 触发规则表格 -->
                      <div class="demo-trigger-table">
                        <!-- 表头 -->
                        <div class="demo-table-header">
                          <div class="demo-table-col">触发方式</div>
                          <div class="demo-table-col">触发内容</div>
                          <div class="demo-table-col">
                            <div class="demo-add-btn" @click="handleAddTrigger">
                              <i class="el-icon-plus"></i>
                              <span>新增</span>
                            </div>
                          </div>
                        </div>

                        <!-- 表格内容 -->
                        <div class="demo-table-body">
                          <div
                            v-for="(trigger, index) in humanTransferConfig.nodeTypeConfig.triggerConditions"
                            :key="index"
                            class="demo-table-row"
                          >
                            <div class="demo-table-col">
                              <el-select v-model="trigger.matchType" placeholder="选择触发方式" size="small">
                                <el-option label="关键词匹配" :value="1" />
                                <el-option label="意图识别" :value="2" />
                              </el-select>
                            </div>
                            <div class="demo-table-col">
                              <el-input
                                v-model="trigger.keyWord"
                                placeholder="请输入触发内容"
                                size="small"
                                maxlength="200"
                                show-word-limit
                              />
                            </div>
                            <div class="demo-table-col">
                              <el-button
                                type="text"
                                icon="el-icon-delete"
                                @click="handleDeleteTrigger(index)"
                                size="small"
                                class="demo-delete-btn"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 默认回复 -->
                    <div class="demo-section">
                      <div class="demo-section-header">
                        <span class="demo-section-title">默认回复</span>
                        <span class="demo-section-desc">触发转人工后，智能体向用户发送的默认回复（仅回复一次）</span>
                      </div>
                      <div class="demo-textarea-wrapper">
                        <el-input
                          type="textarea"
                          v-model="humanTransferConfig.nodeTypeConfig.defaultReply"
                          placeholder="请输入转人工时的默认回复内容"
                          :rows="3"
                          maxlength="200"
                          show-word-limit
                        />
                      </div>
                    </div>

                    <!-- 回复模式 -->
                    <div class="demo-section">
                      <div class="demo-section-header">
                        <span class="demo-section-title">回复模式</span>
                        <i class="el-icon-question demo-help-icon"></i>
                      </div>
                      <div class="demo-reply-modes">
                        <div class="demo-mode-row">
                          <el-radio
                            v-model="humanTransferConfig.nodeTypeConfig.replyMode"
                            :label="1"
                            class="demo-radio"
                          >
                            转人工不回复
                          </el-radio>
                          <el-radio
                            v-model="humanTransferConfig.nodeTypeConfig.replyMode"
                            :label="3"
                            class="demo-radio"
                          >
                            转人工后延迟回复
                          </el-radio>
                          <div v-if="humanTransferConfig.nodeTypeConfig.replyMode === 3" class="demo-delay-setting">
                            <el-input-number
                              v-model="humanTransferConfig.nodeTypeConfig.delayMinutes"
                              :min="0"
                              :max="60"
                              size="small"
                              class="demo-delay-input"
                            />
                            <span class="demo-delay-text">秒后自动ai回复</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 右栏：通知配置 -->
                  <div class="demo-right-column">
                    <!-- 通知配置 -->
                    <div class="demo-section demo-notification-section">
                      <div class="demo-section-header">
                        <span class="demo-section-title">通知配置</span>
                        <i class="el-icon-question demo-help-icon"></i>
                      </div>

                      <!-- 通知方式选择 -->
                      <div class="demo-notification-types">
                        <div class="demo-notification-item">
                          <el-radio v-model="selectedNotificationType" label="wechat">
                            <span class="demo-notification-label">公众号</span>
                          </el-radio>
                        </div>
                        <div class="demo-notification-item">
                          <el-radio v-model="selectedNotificationType" label="sms">
                            <span class="demo-notification-label">短信</span>
                          </el-radio>
                        </div>
                        <div class="demo-notification-item">
                          <el-radio v-model="selectedNotificationType" label="webhook">
                            <span class="demo-notification-label">Webhook</span>
                          </el-radio>
                        </div>
                      </div>

                      <!-- 选择成员 -->
                      <div class="demo-notification-target">
                        <div class="demo-target-label">选择成员</div>
                        <div class="demo-target-select">
                          <el-select
                            v-model="notificationTarget"
                            placeholder="请选择通知团队对象"
                            size="small"
                          >
                            <el-option label="全部成员" value="all" />
                          </el-select>
                        </div>
                      </div>

                      <!-- Webhook配置 -->
                      <div v-if="selectedNotificationType === 'webhook'" class="demo-webhook-config">
                        <div class="demo-webhook-title">智能总结模版</div>

                        <div class="demo-webhook-table">
                          <div class="demo-webhook-header">
                            <div class="demo-webhook-col">模版摘要</div>
                            <div class="demo-webhook-col">摘要具体含义</div>
                            <div class="demo-webhook-col">
                              <div class="demo-add-btn" @click="addWebhookTemplate">
                                <i class="el-icon-plus"></i>
                                <span>新增</span>
                              </div>
                            </div>
                          </div>

                          <div class="demo-webhook-body">
                            <div
                              v-for="(template, index) in webhookTemplates"
                              :key="index"
                              class="demo-webhook-row"
                            >
                              <div class="demo-webhook-col">
                                <el-input
                                  v-model="template.summary"
                                  placeholder="请输入模版摘要内容"
                                  size="small"
                                  maxlength="200"
                                  show-word-limit
                                />
                              </div>
                              <div class="demo-webhook-col">
                                <el-input
                                  v-model="template.description"
                                  placeholder="请描述此摘要的具体含义"
                                  size="small"
                                  maxlength="200"
                                  show-word-limit
                                />
                              </div>
                              <div class="demo-webhook-col">
                                <el-button
                                  type="text"
                                  icon="el-icon-delete"
                                  @click="deleteWebhookTemplate(index)"
                                  size="small"
                                  class="demo-delete-btn"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 拟人化设置 -->
          <div class="config-card humanization-config">
            <div class="card-header">
              <h3>拟人化设置</h3>
            </div>
            <div class="card-content">
              <!-- 第一行：分段回复 + 合并回复 -->
              <div class="config-row two-columns">
                <div class="config-item">
                  <div class="config-info">
                    <span class="config-label">分段回复</span>
                    <span class="config-desc">开启后ai模拟人工，将较长回复分多端发送</span>
                  </div>
                  <div class="config-control">
                    <el-switch
                      v-model="responseConfig.enableSegmentedResponse"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>

                <div class="config-item">
                  <div class="config-info">
                    <span class="config-label">合并回复</span>
                    <span class="config-desc">开启后ai将合并段时间内的多个回复</span>
                  </div>
                  <div class="config-control">
                    <el-switch
                      v-model="responseConfig.enableMergeResponse"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>

              <!-- 第二行：延迟回复 + 空位 -->
              <div class="config-row two-columns">
                <div class="config-item">
                  <div class="config-info">
                    <span class="config-label">延迟回复</span>
                    <span class="config-desc">模拟人工打字速度，建议设置5-15秒</span>
                  </div>
                  <div class="config-control">
                    <el-switch
                      v-model="responseConfig.tipOptimization"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>

                <!-- 空的配置项占位 -->
                <div class="config-item placeholder">
                  <div class="config-info">
                    <span class="config-label"></span>
                    <span class="config-desc"></span>
                  </div>
                  <div class="config-control">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="settings-card">
            <div class="card-header">
              <h2 class="main-title">拟人化</h2>
            </div>

            <div class="settings-section">
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">分段回复</div>
                  <div class="item-action">
                    <el-switch
                      v-model="responseConfig.enableSegmentedResponse"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>

              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">合并回复</div>
                  <div class="item-action">
                    <el-switch
                      v-model="responseConfig.enableMergeResponse"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">提示词优化</div>
                  <div class="item-action">
                    <el-switch
                      v-model="responseConfig.tipOptimization"
                      @change="updateResponseConfig"
                    />
                  </div>
                </div>
              </div>
              <div class="config-row">
                <div class="config-item">
                  <div class="item-label">延迟回复</div>
                  <div
                    class="item-action"
                    style="
                      display: flex;
                      align-items: center;
                      gap: 10px;
                      width: 200px;
                    "
                  >
                    <el-input-number
                      v-model="responseConfig.responseDelay"
                      :min="0"
                      :max="60"
                      controls-position="right"
                      @change="updateResponseConfig"
                    ></el-input-number>
                    <span style="margin-left: 5px">秒</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

            <!-- 右侧调试区域 -->
      <div class="debug-area">
        <div class="debug-panel">
          <div class="debug-header">
            <h3>调试面板</h3>
            <el-button
              type="text"
              size="mini"
              @click="$emit('toggle-debug')"
              class="close-debug-btn"
            >
              <i class="el-icon-close"></i>
            </el-button>
          </div>
          <div class="debug-content">
            <div class="debug-chat-container">
              <DebugChatPanel
                :app-info="getDebugAppInfo()"
                :app-id="$route.params.id"
                :source-type="1"
                debug-mode="advanced"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 非调试模式下的布局 -->
    <div v-else class="main-config-container">
      <!-- 转人工设置 -->
      <div class="config-card human-transfer-config">
        <div class="card-header">
          <h3>转人工设置</h3>
        </div>
        <div class="card-content">
          <div class="config-row">
            <div class="config-item">
              <span class="config-label">开启转人工</span>
              <div class="config-control">
                <el-switch
                  v-model="humanTransferConfig.nodeTypeConfig.isEnable"
                  @change="updateHumanTransferConfig"
                />
              </div>
            </div>
          </div>

          <!-- 转人工详细配置 - 当开启时显示 -->
          <div v-if="humanTransferConfig.nodeTypeConfig.isEnable" class="human-transfer-details">
            <!-- 分割线 -->
            <div class="config-divider"></div>

            <!-- 按照demo样式的左右分栏布局 -->
            <div class="demo-transfer-layout">
              <!-- 左栏：触发规则、默认回复、回复模式 -->
              <div class="demo-left-column">
                <!-- 触发规则 -->
                <div class="demo-section">
                  <div class="demo-section-header">
                    <span class="demo-section-title">触发规则</span>
                    <span class="demo-section-desc">当用户提问满足下列设置的任意条件时，触发转人工</span>
                  </div>

                  <!-- 触发规则表格 -->
                  <div class="demo-trigger-table">
                    <!-- 表头 -->
                    <div class="demo-table-header">
                      <div class="demo-table-col">触发方式</div>
                      <div class="demo-table-col">触发内容</div>
                      <div class="demo-table-col">
                        <div class="demo-add-btn" @click="handleAddTrigger">
                          <i class="el-icon-plus"></i>
                          <span>新增</span>
                        </div>
                      </div>
                    </div>

                    <!-- 表格内容 -->
                    <div class="demo-table-body">
                      <div
                        v-for="(trigger, index) in humanTransferConfig.nodeTypeConfig.triggerConditions"
                        :key="index"
                        class="demo-table-row"
                      >
                        <div class="demo-table-col">
                          <el-select v-model="trigger.matchType" placeholder="选择触发方式" size="small">
                            <el-option label="关键词匹配" :value="1" />
                            <el-option label="意图识别" :value="2" />
                          </el-select>
                        </div>
                        <div class="demo-table-col">
                          <el-input
                            v-model="trigger.keyWord"
                            placeholder="请输入触发内容"
                            size="small"
                            maxlength="200"
                            show-word-limit
                          />
                        </div>
                        <div class="demo-table-col">
                          <el-button
                            type="text"
                            icon="el-icon-delete"
                            @click="handleDeleteTrigger(index)"
                            size="small"
                            class="demo-delete-btn"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 默认回复 -->
                <div class="demo-section">
                  <div class="demo-section-header">
                    <span class="demo-section-title">默认回复</span>
                    <span class="demo-section-desc">触发转人工后，智能体向用户发送的默认回复（仅回复一次）</span>
                  </div>
                  <div class="demo-textarea-wrapper">
                    <el-input
                      type="textarea"
                      v-model="humanTransferConfig.nodeTypeConfig.defaultReply"
                      placeholder="请输入转人工时的默认回复内容"
                      :rows="3"
                      maxlength="200"
                      show-word-limit
                    />
                  </div>
                </div>

                <!-- 回复模式 -->
                <div class="demo-section">
                  <div class="demo-section-header">
                    <span class="demo-section-title">回复模式</span>
                    <i class="el-icon-question demo-help-icon"></i>
                  </div>
                  <div class="demo-reply-modes">
                    <div class="demo-mode-row">
                      <el-radio
                        v-model="humanTransferConfig.nodeTypeConfig.replyMode"
                        :label="1"
                        class="demo-radio"
                      >
                        转人工不回复
                      </el-radio>
                      <el-radio
                        v-model="humanTransferConfig.nodeTypeConfig.replyMode"
                        :label="3"
                        class="demo-radio"
                      >
                        转人工后延迟回复
                      </el-radio>
                      <div v-if="humanTransferConfig.nodeTypeConfig.replyMode === 3" class="demo-delay-setting">
                        <el-input-number
                          v-model="humanTransferConfig.nodeTypeConfig.delayMinutes"
                          :min="0"
                          :max="60"
                          size="small"
                          class="demo-delay-input"
                        />
                        <span class="demo-delay-text">秒后自动ai回复</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右栏：通知配置 -->
              <div class="demo-right-column">
                <!-- 通知配置 -->
                <div class="demo-section demo-notification-section">
                  <div class="demo-section-header">
                    <span class="demo-section-title">通知配置</span>
                    <i class="el-icon-question demo-help-icon"></i>
                  </div>

                  <!-- 通知方式选择 -->
                  <div class="demo-notification-types">
                    <div class="demo-notification-item">
                      <el-radio v-model="selectedNotificationType" label="wechat">
                        <span class="demo-notification-label">公众号</span>
                      </el-radio>
                    </div>
                    <div class="demo-notification-item">
                      <el-radio v-model="selectedNotificationType" label="sms">
                        <span class="demo-notification-label">短信</span>
                      </el-radio>
                    </div>
                    <div class="demo-notification-item">
                      <el-radio v-model="selectedNotificationType" label="webhook">
                        <span class="demo-notification-label">Webhook</span>
                      </el-radio>
                    </div>
                  </div>

                  <!-- 选择成员 -->
                  <div class="demo-notification-target">
                    <div class="demo-target-label">选择成员</div>
                    <div class="demo-target-select">
                      <el-select
                        v-model="notificationTarget"
                        placeholder="请选择通知团队对象"
                        size="small"
                      >
                        <el-option label="全部成员" value="all" />
                      </el-select>
                    </div>
                  </div>

                  <!-- Webhook配置 -->
                  <div v-if="selectedNotificationType === 'webhook'" class="demo-webhook-config">
                    <div class="demo-webhook-title">智能总结模版</div>

                    <div class="demo-webhook-table">
                      <div class="demo-webhook-header">
                        <div class="demo-webhook-col">模版摘要</div>
                        <div class="demo-webhook-col">摘要具体含义</div>
                        <div class="demo-webhook-col">
                          <div class="demo-add-btn" @click="addWebhookTemplate">
                            <i class="el-icon-plus"></i>
                            <span>新增</span>
                          </div>
                        </div>
                      </div>

                      <div class="demo-webhook-body">
                        <div
                          v-for="(template, index) in webhookTemplates"
                          :key="index"
                          class="demo-webhook-row"
                        >
                          <div class="demo-webhook-col">
                            <el-input
                              v-model="template.summary"
                              placeholder="请输入模版摘要内容"
                              size="small"
                              maxlength="200"
                              show-word-limit
                            />
                          </div>
                          <div class="demo-webhook-col">
                            <el-input
                              v-model="template.description"
                              placeholder="请描述此摘要的具体含义"
                              size="small"
                              maxlength="200"
                              show-word-limit
                            />
                          </div>
                          <div class="demo-webhook-col">
                            <el-button
                              type="text"
                              icon="el-icon-delete"
                              @click="deleteWebhookTemplate(index)"
                              size="small"
                              class="demo-delete-btn"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 拟人化设置 -->
      <div class="config-card humanization-config">
        <div class="card-header">
          <h3>拟人化设置</h3>
        </div>
        <div class="card-content">
          <!-- 第一行：分段回复 + 合并回复 -->
          <div class="config-row two-columns">
            <div class="config-item">
              <div class="config-info">
                <span class="config-label">分段回复</span>
                <span class="config-desc">开启后ai模拟人工，将较长回复分多端发送</span>
              </div>
              <div class="config-control">
                <el-switch
                  v-model="responseConfig.enableSegmentedResponse"
                  @change="updateResponseConfig"
                />
              </div>
            </div>

            <div class="config-item">
              <div class="config-info">
                <span class="config-label">合并回复</span>
                <span class="config-desc">开启后ai将合并段时间内的多个回复</span>
              </div>
              <div class="config-control">
                <el-switch
                  v-model="responseConfig.enableMergeResponse"
                  @change="updateResponseConfig"
                />
              </div>
            </div>
          </div>

          <!-- 第二行：延迟回复 + 空位 -->
          <div class="config-row two-columns">
            <div class="config-item">
              <div class="config-info">
                <span class="config-label">延迟回复</span>
                <span class="config-desc">模拟人工打字速度，建议设置5-15秒</span>
              </div>
              <div class="config-control">
                <el-switch
                  v-model="responseConfig.tipOptimization"
                  @change="updateResponseConfig"
                />
              </div>
            </div>

            <!-- 空的配置项占位 -->
            <div class="config-item placeholder">
              <div class="config-info">
                <span class="config-label"></span>
                <span class="config-desc"></span>
              </div>
              <div class="config-control">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>


<script>
import DebugChatPanel from "@/components/chat/DebugChatPanel.vue"

export default {
  name: "AdvancedConfig",
  components: {
    DebugChatPanel
  },
  props: {
    flowDetail: {
      type: Object,
      default: () => ({})
    },
    isDebugging: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 拟人化响应配置，对应结束节点数据
      responseConfig: {
        enableSegmentedResponse: false,
        enableMergeResponse: true,
        responseDelay: 0,
        tipOptimization: true,
      },

      // 通知配置
      notificationConfig: {
        wechat: false,
        sms: false,
        webhook: false
      },

      // Webhook配置
      webhookConfig: {
        type: 'dingtalk',
        url: '',
        secret: '',
        members: []
      },

      // 新增的demo样式相关数据
      selectedNotificationType: 'wechat', // 选中的通知类型
      notificationTarget: '', // 通知对象
      webhookTemplates: [ // webhook模版列表
        {
          summary: '',
          description: ''
        }
      ],

      // 转人工配置，与接口数据对应
      humanTransferConfig: {
        activeTab: "config",
        label: "转人工",
        name: "转人工",
        description: "转人工节点描述",
        nodeTypeConfig: {
          memory: false,
          memoryTurns: 1,
          defaultReply: "",
          notifyPerson: "staff1",
          delayMinutes: 5,
          replyMode: 3,
          inputs: [],
          outputs: [],
          content: "",
          isEnable: true,
          name: null,
          notifyMethods: null,
          structuredOutput: false,
          triggerConditions: [
            {
              matchType: 1,
              operatorType: 0,
              keyWord: "",
            },
          ],
        },
        type: "humanTransfer",
        id: "",
      },
    };
  },
  created() {
    // 初始化配置
    this.initHumanTransferConfig();
    this.initResponseConfig();

    // 初始化后立即向父组件传递配置数据
    this.$nextTick(() => {
      this.updateHumanTransferConfig();
      this.updateResponseConfig();
    });
  },
  watch: {
    flowDetail: {
      handler(newVal) {
        if (newVal && newVal.flowDetailDto) {
          this.initHumanTransferConfig(newVal.flowDetailDto);
          this.initResponseConfig(newVal.flowDetailDto);
        }
      },
      immediate: true,
      deep: true
    },
    humanTransferConfig: {
      handler: "updateHumanTransferConfig",
      deep: true
    }
  },
  methods: {
    // 获取调试应用信息
    getDebugAppInfo() {
      const flowDetail = this.flowDetail || {};
      return {
        id: this.$route.params.id,
        name: flowDetail.name || "高级调试应用",
        description: flowDetail.description || "正在调试应用的高级配置",
        profilePhoto: flowDetail.profilePhoto || "",
        introduce: flowDetail.introduce || "这是高级配置调试环境，您可以测试应用的高级功能如转人工、拟人化响应等。"
      };
    },
    // 初始化拟人化响应配置
    initResponseConfig(flowData) {
      if (
        flowData &&
        flowData.endNodes &&
        flowData.endNodes.length > 0
      ) {
        let endNode= flowData.endNodes[0]
          // 从结束节点获取responseConfig数据
          if (endNode.data.nodeTypeConfig.responseConfig) {
              const rc = endNode.data.nodeTypeConfig.responseConfig;

              // 更新响应配置
              this.responseConfig = {
                enableSegmentedResponse:
                  rc.enableSegmentedResponse !== undefined
                    ? rc.enableSegmentedResponse
                    : false,
                enableMergeResponse:
                  rc.enableMergeResponse !== undefined
                    ? rc.enableMergeResponse
                    : true,
                responseDelay:
                  rc.responseDelay !== undefined ? rc.responseDelay : 0,
                tipOptimization:
                  rc.tipOptimization !== undefined ? rc.tipOptimization : true,
              }
            }
      }
    },
    // 初始化转人工配置
    initHumanTransferConfig(flowData) {
      if (
        flowData &&
        flowData.humanTransferNodes &&
        flowData.humanTransferNodes.length > 0
      ) {
        const humanTransferNode = flowData.humanTransferNodes[0];
        if (humanTransferNode && humanTransferNode.data) {
          // 创建新的配置对象
          const newConfig = {
            activeTab: humanTransferNode.data.activeTab || "config",
            label: humanTransferNode.data.label || "转人工",
            name: humanTransferNode.data.name || "转人工",
            description: humanTransferNode.data.description || "",
            nodeTypeConfig: {
              memory: false,
              memoryTurns: 1,
              defaultReply: "",
              notifyPerson: "staff1",
              delayMinutes: 5,
              replyMode: 3,
              inputs: [],
              outputs: [],
              content: "",
              isEnable: true,
              name: null,
              notifyMethods: null,
              structuredOutput: false,
              triggerConditions: [
                {
                  matchType: 1,
                  operatorType: 0,
                  keyWord: "",
                },
              ],
            },
            type: "humanTransfer",
            id: humanTransferNode.data.id || "",
          };

          // 如果存在nodeTypeConfig，则更新相关属性
          if (humanTransferNode.data.nodeTypeConfig) {
            const ntc = humanTransferNode.data.nodeTypeConfig;

            // 更新基本属性
            if (ntc.memory !== undefined)
              newConfig.nodeTypeConfig.memory = ntc.memory;
            if (ntc.memoryTurns !== undefined)
              newConfig.nodeTypeConfig.memoryTurns = ntc.memoryTurns;
            if (ntc.defaultReply !== undefined)
              newConfig.nodeTypeConfig.defaultReply = ntc.defaultReply;
            if (ntc.notifyPerson !== undefined)
              newConfig.nodeTypeConfig.notifyPerson = ntc.notifyPerson;
            if (ntc.delayMinutes !== undefined)
              newConfig.nodeTypeConfig.delayMinutes = ntc.delayMinutes;
            if (ntc.replyMode !== undefined)
              newConfig.nodeTypeConfig.replyMode = ntc.replyMode;
            if (ntc.content !== undefined)
              newConfig.nodeTypeConfig.content = ntc.content;
            if (ntc.isEnable !== undefined)
              newConfig.nodeTypeConfig.isEnable = ntc.isEnable;
            if (ntc.name !== undefined)
              newConfig.nodeTypeConfig.name = ntc.name;
            if (ntc.structuredOutput !== undefined)
              newConfig.nodeTypeConfig.structuredOutput = ntc.structuredOutput;

            // 更新数组
            if (ntc.inputs) newConfig.nodeTypeConfig.inputs = ntc.inputs;
            if (ntc.outputs) newConfig.nodeTypeConfig.outputs = ntc.outputs;

            // 更新通知方法
            if (ntc.notifyMethods) {
              newConfig.nodeTypeConfig.notifyMethods = ntc.notifyMethods;
            }

            // 更新触发条件，包含operatorType字段
            if (ntc.triggerConditions) {
              newConfig.nodeTypeConfig.triggerConditions = ntc.triggerConditions.map(condition => ({
                matchType: condition.matchType || 1,
                operatorType: condition.operatorType || 0,
                keyWord: condition.keyWord || "",
              }));
            }
          }

          // 更新配置
          this.humanTransferConfig = newConfig;
        }
      }
    },

    handleAddTrigger() {
      this.humanTransferConfig.nodeTypeConfig.triggerConditions.push({
        matchType: 1,
        operatorType: 0,
        keyWord: "",
      });
    },
    handleDeleteTrigger(index) {
      this.humanTransferConfig.nodeTypeConfig.triggerConditions.splice(
        index,
        1
      );
    },
    // 更新转人工配置，并通知父组件
    updateHumanTransferConfig() {
      this.$emit("update-human-transfer", this.humanTransferConfig);
    },

    // 更新响应配置，并通知父组件
    updateResponseConfig() {
      this.$emit("update-response-config", this.responseConfig);
    },

    // 添加webhook模版
    addWebhookTemplate() {
      this.webhookTemplates.push({
        summary: '',
        description: ''
      });
    },

    // 删除webhook模版
    deleteWebhookTemplate(index) {
      this.webhookTemplates.splice(index, 1);
    },


  },
};
</script>

<style lang="scss" scoped>
.settings-content {
  padding: 16px 24px;
  background: #f2f6fc;
  min-height: calc(100vh - 60px);
  overflow-y: auto;
  overflow-x: hidden;

  .main-config-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

// 配置卡片样式 - 按照demo布局
.config-card {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .card-header {
    padding: 24px 32px 0;

    h3 {
      font-size: 16px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: rgba(0, 0, 0, 1);
      line-height: 22px;
      margin: 0;
    }
  }

  .card-content {
    padding: 24px 32px 32px;

    .config-row {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      // 两列布局
      &.two-columns {
        display: flex;
        gap: 40px;

        .config-item {
          flex: 1;
        }
      }

      .config-item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .config-label {
          font-size: 14px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: rgba(0, 0, 0, 1);
          line-height: 20px;
        }

        .config-info {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .config-label {
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: rgba(0, 0, 0, 1);
            line-height: 20px;
          }

          .config-desc {
            font-size: 12px;
            color: rgba(153, 153, 153, 1);
            line-height: 17px;
          }
        }

        .config-control {
          display: flex;
          align-items: center;
        }

        // 占位符配置项样式
        &.placeholder {
          visibility: hidden;
        }
      }
    }
  }
}

// 转人工设置特殊样式
.human-transfer-config {
  min-height: 130px;
  background: rgba(255, 255, 255, 1);

  // 转人工详细配置样式 - 按照demo设计
  .human-transfer-details {
    margin-top: 24px;

    .config-divider {
      height: 1px;
      background: #f0f0f0;
      margin: 24px 0;
    }

    // Demo样式布局 - 左右分栏
    .demo-transfer-layout {
      display: flex;
      gap: 40px;
      align-items: flex-start;
    }

    .demo-left-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 32px;
    }

    .demo-right-column {
      flex: 1;
    }

    .demo-section {
      &:last-child {
        margin-bottom: 0;
      }

      .demo-section-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .demo-section-title {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 1);
          margin-right: 16px;
        }

        .demo-section-desc {
          font-size: 14px;
          color: rgba(153, 153, 153, 1);
          flex: 1;
        }

        .demo-help-icon {
          color: #909399;
          cursor: pointer;
          margin-left: 8px;
        }
      }
    }

    // 触发规则表格样式
    .demo-trigger-table {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;

      .demo-table-header {
        display: flex;
        background: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;

        .demo-table-col {
          flex: 1;
          padding: 12px 16px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(0, 0, 0, 1);
          border-right: 1px solid #e4e7ed;

          &:last-child {
            border-right: none;
            display: flex;
            justify-content: center;
          }

          .demo-add-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #409eff;
            cursor: pointer;
            font-size: 14px;
            font-weight: normal;

            &:hover {
              color: #66b1ff;
            }
          }
        }
      }

      .demo-table-body {
        .demo-table-row {
          display: flex;
          border-bottom: 1px solid #e4e7ed;

          &:last-child {
            border-bottom: none;
          }

          .demo-table-col {
            flex: 1;
            padding: 12px 16px;
            border-right: 1px solid #e4e7ed;

            &:last-child {
              border-right: none;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .demo-delete-btn {
              color: #f56c6c;

              &:hover {
                color: #f78989;
              }
            }
          }
        }
      }
    }

    // 文本域样式
    .demo-textarea-wrapper {
      :deep(.el-textarea) {
        .el-textarea__inner {
          border-radius: 4px;
          border: 1px solid #dcdfe6;
        }
      }
    }

    // 回复模式样式
    .demo-reply-modes {
      .demo-mode-row {
        display: flex;
        align-items: center;
        gap: 24px;

        .demo-radio {
          margin-right: 0;
        }

        .demo-delay-setting {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-left: 16px;

          .demo-delay-input {
            width: 180px;
          }

          .demo-delay-text {
            font-size: 14px;
            color: rgba(0, 0, 0, 1);
          }
        }
      }
    }

    // 通知配置样式
    .demo-notification-section {
      .demo-notification-types {
        display: flex;
        gap: 32px;
        margin-bottom: 24px;

        .demo-notification-item {
          .demo-notification-label {
            font-size: 14px;
            color: rgba(0, 0, 0, 1);
          }
        }
      }

      .demo-notification-target {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        .demo-target-label {
          font-size: 14px;
          color: rgba(0, 0, 0, 1);
          margin-right: 16px;
          min-width: 80px;
        }

        .demo-target-select {
          flex: 1;
          max-width: 300px;
        }
      }

      // Webhook配置样式
      .demo-webhook-config {
        .demo-webhook-title {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 1);
          margin-bottom: 16px;
        }

        .demo-webhook-table {
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          overflow: hidden;

          .demo-webhook-header {
            display: flex;
            background: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;

            .demo-webhook-col {
              flex: 1;
              padding: 12px 16px;
              font-size: 14px;
              font-weight: 500;
              color: rgba(0, 0, 0, 1);
              border-right: 1px solid #e4e7ed;

              &:last-child {
                border-right: none;
                display: flex;
                justify-content: center;
              }

              .demo-add-btn {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #409eff;
                cursor: pointer;
                font-size: 14px;
                font-weight: normal;

                &:hover {
                  color: #66b1ff;
                }
              }
            }
          }

          .demo-webhook-body {
            .demo-webhook-row {
              display: flex;
              border-bottom: 1px solid #e4e7ed;

              &:last-child {
                border-bottom: none;
              }

              .demo-webhook-col {
                flex: 1;
                padding: 12px 16px;
                border-right: 1px solid #e4e7ed;

                &:last-child {
                  border-right: none;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                }

                .demo-delete-btn {
                  color: #f56c6c;

                  &:hover {
                    color: #f78989;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 拟人化设置特殊样式
.humanization-config {
  min-height: 270px;
  background: rgba(255, 255, 255, 1);
}



.config-section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #1f2937;
    }
  }

  .section-content {
    padding: 20px;
    background: #f9fafb;

    .label {
      color: #606266;
      margin-right: 8px;
    }

    .el-icon-question {
      color: #909399;
      cursor: pointer;
      font-size: 14px;
    }
  }
}

.trigger-item {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .trigger-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 14px;
      color: #1f2937;
      font-weight: 500;
    }
  }

  .trigger-content {
    padding: 16px;
  }

  .trigger-row {
    display: flex;
    gap: 16px;
    align-items: flex-start;
  }

  .trigger-col {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;

    &:last-child {
      flex: 0 0 auto;
      align-self: flex-end;
    }

    .label {
      font-size: 14px;
      color: #606266;
    }

    :deep(.el-select) {
      width: 100%;
    }

    :deep(.el-input) {
      width: 100%;
    }

    .delete-btn {
      margin-top: 24px;
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

.add-trigger {
  text-align: center;
  margin-top: 16px;

  :deep(.el-button--dashed) {
    width: 100%;
    border-style: dashed;
    border-color: #d9d9d9;

    &:hover {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
}

.reply-row,
.notify-row,
.summary-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  &.mt-16 {
    margin-top: 16px;
  }
}

.notify-options {
  display: flex;
  gap: 24px;
}

.summary-item {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  .summary-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 14px;
      color: #1f2937;
      font-weight: 500;
    }
  }

  .summary-content {
    padding: 16px;
  }

  .summary-row {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
      text-align: right;
    }
  }

  .summary-col {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .label {
      font-size: 14px;
      color: #606266;
    }

    :deep(.el-input) {
      width: 100%;

      .el-input__append {
        cursor: pointer;
      }
    }

    .delete-btn {
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

.add-summary {
  text-align: center;
  margin-top: 16px;

  :deep(.el-button--dashed) {
    width: 100%;
    border-style: dashed;
    border-color: #d9d9d9;

    &:hover {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
}



/* 调试模式样式 */
.settings-content {
  &.debug-layout {
    .debug-container {
      display: flex;
      height: calc(100vh - 120px); // 减去顶部菜单和标题的高度
      gap: 10px; // 减少间隙，与模块间距保持一致
      width: 100%;
      overflow: hidden; // 防止横向滚动

      .config-area {
        width: calc(50% - 5px); // 减去一半gap的宽度
        flex-shrink: 0;
        overflow-y: auto;
        padding-right: 0; // 移除padding避免挤压

        .config-sections {
          display: flex;
          flex-direction: column;
          gap: 16px; // 保持与原始布局一致的间距

          .config-card {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            height: auto;
            min-height: 200px;

            &:last-child {
              margin-bottom: 0;
            }

            // 调试模式下的转人工设置样式
            &.human-transfer-config {
              height: 130px;
              background: rgba(255, 255, 255, 1);
            }

            // 调试模式下的拟人化设置样式
            &.humanization-config {
              height: 270px;
              background: rgba(255, 255, 255, 1);
            }
          }
        }
      }

      .debug-area {
        width: calc(50% - 5px); // 减去一半gap的宽度
        background: #fff;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;

        .debug-panel {
          height: 100%;
          display: flex;
          flex-direction: column;

          .debug-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 20px 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            flex-shrink: 0;

            h3 {
              margin: 0;
              font-size: 16px;
              font-weight: 500;
              color: #1f2937;
            }
          }

          .debug-content {
            flex: 1;
            overflow: hidden;
            padding: 20px;

            .debug-chat-container {
              height: 100%;
              overflow: hidden;

              :deep(.debug-chat-panel) {
                height: 100%;
                border: none;
                box-shadow: none;
              }
            }
          }
        }
      }
    }
  }
}

// 关闭调试按钮样式
.close-debug-btn {
  color: #909399;
  padding: 0;

  &:hover {
    color: #f56c6c;
  }

  i {
    font-size: 16px;
  }
}
</style>

