# 基础配置组件
<template>
    <div class="settings-content" :class="{ 'debug-mode': isDebugging }">
      <div class="settings-scroll-container">
    <!-- 主配置区域容器 -->
    <div class="main-config-container">
      <!-- 第一行：基础配置 + 智能体分享 -->
      <div class="config-row">
        <div class="config-card basic-config">
          <div class="card-header">
            <h3>基础配置</h3>
          </div>
          <div class="card-content">
            <div class="avatar-section">
              <div class="avatar-label">智能体头像</div>
              <div class="avatar-wrapper">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :http-request="handleAvatarUpload"
                >
                  <el-image
                    v-if="basicForm.profilePhoto"
                    :src="basicForm.profilePhoto"
                    fit="cover"
                  >
                    <template #error>
                      <div class="avatar-placeholder">
                        <i class="el-icon-picture-outline"></i>
                        点击上传
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="avatar-placeholder">
                    <i class="el-icon-plus"></i>
                    <div>点击上传头像</div>
                  </div>
                </el-upload>
              </div>
              <div class="avatar-actions">
                <i class="el-icon-magic-stick"></i>
                <span>AI生成头像</span>
              </div>
            </div>

            <div class="form-section">
              <el-form
                ref="basicForm"
                :model="basicForm"
                label-width="100px"
                :rules="rules"
              >
                <el-form-item label="智能体名称" prop="name">
                  <el-input
                    v-model="basicForm.name"
                    placeholder="请输入智能体名称"
                    maxlength="50"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item label="智能体分类" prop="applicationType">
                  <el-select
                    v-model="basicForm.applicationType"
                    placeholder="请选择智能体分类"
                    style="width: 100%"
                    @change="handleTypeChange"
                  >
                    <el-option
                      v-for="item in applicationTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="智能体类型" prop="sessionFlowCode">
                  <el-select
                    v-model="basicForm.sessionFlowCode"
                    placeholder="请选择智能体类型"
                    style="width: 100%"
                    @change="handleTypeChange"
                  >
                    <el-option
                      v-for="item in agentTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value.toString()"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="智能体code" prop="sessionFlowCode">
                  <el-input
                    v-model="basicForm.id"
                    placeholder="智能体code"
                    readonly
                  >
                    <template #append>
                      <el-button
                        type="primary"
                        plain
                        @click="handleCopyAppCode"
                        >复制</el-button
                      >
                    </template>
                  </el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <div class="config-card share-config">
          <div class="card-header">
            <h3>智能体分享</h3>
          </div>
          <div class="card-content">
            <el-form ref="shareForm" :model="basicForm" label-width="100px">
              <el-form-item label="专属链接">
                <el-input
                  v-model="basicForm.shareLink"
                  placeholder="https://link-ai.tech/app/Hre0ppGC"
                  readonly
                >
                  <template #append>
                    <el-button type="primary" plain @click="copyShareLink"
                      >复制</el-button
                    >
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="是否公开">
                <div class="switch-group">
                  <el-switch v-model="basicForm.isPublic" />
                  <span
                    class="switch-label switch-on"
                    :class="{ active: basicForm.isPublic }"
                    >是</span
                  >
                  <span class="switch-tip">(如选择公开则可在应用广场被搜索和使用)</span>
                </div>

              </el-form-item>
              <el-form-item label="智能体描述" style="width: 90%;">
                <div class="textarea-with-external-btn">
                  <el-input
                    type="textarea"
                    v-model="basicForm.description"
                    rows="2"
                    maxlength="500"
                    show-word-limit
                    placeholder="根据患者信息辨证并给出京鸿阁用药方案回答中医相关的问题"
                  />
                  <el-button
                    type="text"
                    size="mini"
                    class="ai-optimize-external-btn"
                    @click="handleAIOptimizeDescription">
                    <span style="display: flex;align-items: center;">
                      <img class="ai-optimize-icon" src="@/assets/app/<EMAIL>" alt="AI优化" />
                      <span>AI优化</span>
                    </span>

                  </el-button>
                </div>
              </el-form-item>
              <el-form-item label="开场白" style="width: 90%;">
                <div class="textarea-with-external-btn">
                  <el-input
                    @focus="showIntroductionDialog"
                    type="textarea"
                    v-model="basicForm.introduce"
                    rows="4"
                    maxlength="2000"
                    show-word-limit
                    placeholder="将在用户开启对话时展示，引导用户快速了解功能并开启对话。例如：需要什么帮助？"
                  />
                  <el-button
                    type="text"
                    size="mini"
                    class="ai-optimize-external-btn"
                    @click="handleAIOptimizeIntroduction">
                    <span style="display: flex;align-items: center;">
                      <img class="ai-optimize-icon" src="@/assets/app/<EMAIL>" alt="AI优化" />
                      <span>AI优化</span>
                    </span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 第二行：模型设置 + 知识库配置 -->
      <div class="config-row">
        <div class="config-card model-config">
          <div class="card-header">
            <h3>模型设置</h3>
            <div class="header-subtitle">应用接入渠道提供服务时使用的默认模型</div>
          </div>
          <div class="card-content">
            <el-form ref="modelForm" :model="modelForm" label-width="100px">
              <div class="model-row">
                <div class="model-container">
                  <el-form-item label="默认模型">
                    <div class="model-select-box" @click="showDefaultModelDialog">
                      <div class="selected-value">
                        <template v-if="getSelectedModelName()">
                          <div class="model-option-item">
                            <span>{{ getSelectedModelName() }}</span>
                          </div>
                        </template>
                        <template v-else>
                          <span class="placeholder">请选择默认模型</span>
                        </template>
                      </div>
                      <i class="el-icon-arrow-down"></i>
                    </div>
                    <div
                      class="selected-model-display"
                      v-if="getSelectedModelName()"
                    >
                      <img
                        :src="getSelectedModelImg()"
                        alt="模型图标"
                        class="selected-model-icon"
                      />
                    </div>
                  </el-form-item>
                </div>
              </div>
              <el-form-item label="记忆对话">
                <el-input-number
                  v-model="modelForm.data.nodeTypeConfig.memoryTurns"
                  :min="1"
                  :max="10"
                />
                <span class="form-tip">轮次，保留</span>
                <el-input-number
                  v-model="modelForm.data.nodeTypeConfig.memoryTime"
                  :min="0"
                  :max="60"
                />
                <span class="form-tip">分钟</span>
              </el-form-item>
              <el-form-item label="声音">
                <div class="voice-select-container">
                  <el-select
                    v-model="modelForm.data.nodeTypeConfig.voiceId"
                    placeholder="请选择声音"
                    style="width: 200px"
                    @change="handleVoiceChange"
                  >
                    <el-option
                      v-for="voice in voiceOptions"
                      :key="voice.id"
                      :label="voice.name"
                      :value="voice.id"
                    />
                  </el-select>
                  <el-button
                    type="text"
                    size="large"
                    icon="el-icon-video-play"
                    style="margin-left: 10px"
                    :loading="voicePlayLoading"
                    :disabled="!modelForm.data.nodeTypeConfig.voiceId"
                    @click="handlePlayVoice"
                  />
                </div>
              </el-form-item>

              <el-form-item label="温度">
                <el-slider
                  v-model="modelForm.data.nodeTypeConfig.temperature"
                  :min="0"
                  :max="1"
                  :step="0.1"
                />
              </el-form-item>
              <el-form-item label="智能体设定" style="width: 90%;">
                <div class="textarea-with-external-btn">
                  <el-input
                    @focus="showApplicationSettingDialog"
                    type="textarea"
                    v-model="basicForm.sessionFlowSetting"
                    :rows="4"
                    show-word-limit
                    maxlength="10000"
                    placeholder="## 角色&#10;你是一位资深的中医师，拥有丰富的临床经验。&#10;&#10;## 目标&#10;根据患者提供的症状，给出用药建议。&#10;&#10;## 技能"
                  />
                  <el-button
                    type="text"
                    size="mini"
                    class="ai-optimize-external-btn"
                    @click="handleAIOptimizeSettings">
                    <span style="display: flex;align-items: center;">
                      <img class="ai-optimize-icon" src="@/assets/app/<EMAIL>" alt="AI优化" />
                      <span>AI优化</span>
                    </span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div v-if="+basicForm.sessionFlowCode === 2" class="config-card knowledge-config">
          <div class="card-header">
            <h3>知识库配置</h3>
            <div class="header-actions">
              <div class="demo-button config-btn" @click="showKnowledgeConfigDialog">
                <i class="demo-icon el-icon-setting"></i>
                <span class="demo-text">配置</span>
              </div>
              <div class="demo-button bind-btn" @click="showBindKnowledgeDialog">
                <i class="demo-icon el-icon-plus"></i>
                <span class="demo-text">绑定知识库</span>
              </div>
            </div>
          </div>
          <div class="card-content">
            <!-- 配置信息网格 -->
            <div class="knowledge-config-grid">
              <div class="config-row">
                <div class="config-item">
                  <span class="config-label">检索方式：</span>
                  <span class="config-value">
                    {{
                      knowledgeNodeData.data &&
                      knowledgeNodeData.data.nodeTypeConfig &&
                      knowledgeNodeData.data.nodeTypeConfig.searchType == "1"
                        ? "语义检索"
                        : knowledgeNodeData.data &&
                          knowledgeNodeData.data.nodeTypeConfig &&
                          knowledgeNodeData.data.nodeTypeConfig.searchType == "2"
                        ? "增强检索"
                        : "关键词检索"
                    }}
                  </span>
                </div>
                <div class="config-item">
                  <span class="config-label">语义相似度阈值：</span>
                  <span class="config-value">
                    {{
                      knowledgeNodeData.data &&
                      knowledgeNodeData.data.nodeTypeConfig
                        ? knowledgeNodeData.data.nodeTypeConfig.similarity
                        : 16
                    }}
                  </span>
                </div>
                <div class="config-item">
                  <span class="config-label">单次检索条数：</span>
                  <span class="config-value">
                    {{
                      knowledgeNodeData.data &&
                      knowledgeNodeData.data.nodeTypeConfig
                        ? knowledgeNodeData.data.nodeTypeConfig.semanticCount
                        : 20
                    }}
                  </span>
                </div>
                <div class="config-item">
                  <span class="config-label">全文检索条数：</span>
                  <span class="config-value">
                    {{
                      knowledgeNodeData.data &&
                      knowledgeNodeData.data.nodeTypeConfig
                        ? knowledgeNodeData.data.nodeTypeConfig.fullTextCount
                        : 20
                    }}
                  </span>
                </div>
              </div>
              <div class="config-row">
                <div class="config-item">
                  <span class="config-label">未命中策略：</span>
                  <span class="config-value">
                    {{
                      knowledgeNodeData.data &&
                      knowledgeNodeData.data.nodeTypeConfig &&
                      knowledgeNodeData.data.nodeTypeConfig.missStrategy === "manual"
                        ? "转人工"
                        : "自由发挥"
                    }}
                  </span>
                </div>
                <div class="config-item">
                  <span class="config-label">知识库引用来源：</span>
                  <span class="config-value">
                    {{
                      knowledgeNodeData.data &&
                      knowledgeNodeData.data.nodeTypeConfig &&
                      knowledgeNodeData.data.nodeTypeConfig.showReference
                        ? "开启"
                        : "关闭"
                    }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 分割线 -->
            <div class="knowledge-divider"></div>

            <!-- 绑定的知识库列表 -->
            <div class="knowledge-bound-list">
              <div
                v-for="(knowledge, index) in selectedKnowledges"
                :key="index"
                class="knowledge-item"
              >
                <div class="knowledge-icon">
                  <i class="el-icon-document"></i>
                </div>
                <div class="knowledge-info">
                  <div class="knowledge-name">{{ knowledge.name }}</div>
                  <div class="knowledge-desc">{{ knowledge.description || '复制系统模版时生成复制系统模版时生成复制' }}</div>
                </div>
                <div class="knowledge-actions">
                  <span class="action-link" @click="handleViewKnowledge()">查看</span>
                  <span class="action-separator">｜</span>
                  <span class="action-link" @click="handleUnbindKnowledge(knowledge)">解绑</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第三行：插件配置 + 数据库配置 -->
      <div class="config-row">
        <div class="config-card plugin-config">
          <div class="card-header">
            <h3>插件配置</h3>
            <div class="header-actions">
              <div class="demo-button bind-btn" @click="showAddPluginDialog">
                <i class="demo-icon el-icon-plus"></i>
                <span class="demo-text">添加插件</span>
              </div>
            </div>
          </div>
          <div class="card-content">
            <!-- 插件执行策略 -->
            <div v-if="+basicForm.sessionFlowCode === 2" class="plugin-strategy-section">
              <div class="strategy-row">
                <div class="strategy-label">
                  <span class="label-text">插件执行策略</span>
                  <i class="el-icon-question"></i>
                </div>
                <div class="strategy-select">
                  <el-select
                    v-model="basicForm.pluginOrKnowledge"
                    placeholder="请选择插件执行策略"
                    size="small"
                  >
                    <el-option label="插件优先" value="1"></el-option>
                    <el-option label="知识库优先" value="2"></el-option>
                    <el-option label="知识库与插件同时执行" value="3"></el-option>
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 分割线 -->
            <div class="plugin-divider"></div>

            <!-- 插件列表 -->
            <div class="plugin-list" v-if="allActivePlugins.length > 0">
              <div
                class="plugin-item"
                v-for="(plugin, index) in allActivePlugins"
                :key="index"
              >
                <div class="plugin-icon">
                  <img
                    v-if="plugin.headSculpture"
                    :src="plugin.headSculpture"
                    alt="插件图标"
                  />
                  <i
                    v-else
                    :class="plugin.iconClass || 'el-icon-connection'"
                  ></i>
                </div>
                <div class="plugin-info">
                  <div class="plugin-header">
                    <div class="plugin-name">{{ plugin.data.name }}</div>
                    <div class="plugin-type-tag" v-if="getPluginTypeLabel(plugin)">
                      {{ getPluginTypeLabel(plugin) }}
                    </div>
                  </div>
                  <div class="plugin-desc">{{ plugin.data.description }}</div>
                </div>
                <div class="plugin-actions">
                  <span class="action-link" @click="showAddPluginDialog">查看</span>
                  <span class="action-separator">｜</span>
                  <span class="action-link" @click="removePlugin(plugin)">解绑</span>
                  <el-switch
                    v-model="plugin.enabled"
                    @change="togglePlugin(plugin)"
                    size="mini"
                    class="plugin-switch"
                  ></el-switch>
                </div>
              </div>
            </div>

            <div class="empty-plugins" v-if="allActivePlugins.length === 0">
              <p>还没有添加插件</p>
            </div>
          </div>
        </div>

        <div v-if="+basicForm.sessionFlowCode === 2" class="config-card database-config">
          <div class="card-header">
            <h3>数据库配置</h3>
            <div class="header-actions">
              <div class="demo-button bind-btn" @click="showBindDatabaseDialog">
                <i class="demo-icon el-icon-plus"></i>
                <span class="demo-text">绑定数据库</span>
              </div>
            </div>
          </div>
          <div class="card-content">
            <!-- 数据库模型配置 -->
            <div class="database-model-section">
              <div class="model-row">
                <div class="model-label">
                  <span class="label-text">数据库模型</span>
                  <i class="el-icon-question"></i>
                </div>
                <div class="model-select">
                  <el-select
                    v-model="selectedDatabaseModel"
                    placeholder="请选择数据库模型"
                    size="small"
                    :loading="loadingStates.database"
                  >
                    <el-option-group
                      v-for="(models, provider) in groupedDatabaseModels"
                      :key="provider"
                      :label="provider"
                    >
                      <el-option
                        v-for="model in models"
                        :key="model.code"
                        :label="model.name"
                        :value="model.code"
                      ></el-option>
                    </el-option-group>
                  </el-select>
                  <i class="el-icon-arrow-down model-arrow"></i>
                </div>
              </div>
            </div>

            <!-- 分割线 -->
            <div class="database-divider"></div>

            <!-- 数据库列表 -->
            <div class="database-list" v-if="selectedDatabases.length > 0">
              <div
                class="database-item"
                v-for="database in selectedDatabases"
                :key="database.id"
              >
                <div class="database-icon">
                  <i class="el-icon-coin"></i>
                </div>
                <div class="database-info">
                  <div class="database-name">{{ database.name }}</div>
                  <div class="database-desc">{{ database.description || '复制系统模版时生成复制系统模版时生成复制' }}</div>
                </div>
                <div class="database-actions">
                  <span class="action-link" @click="showBindDatabaseDialog">查看</span>
                  <span class="action-separator">｜</span>
                  <span class="action-link" @click="handleUnbindDatabase(database)">解绑</span>
                </div>
              </div>
            </div>

            <div class="empty-database" v-if="selectedDatabases.length === 0">
              <p>暂未绑定数据库</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" v-if="isDebugging">
      <div class="debug-header">
        <h3>调试面板</h3>
        <el-button
          type="text"
          size="mini"
          @click="$emit('toggle-debug')"
          class="close-debug-btn"
        >
          <i class="el-icon-close"></i>
        </el-button>
      </div>
      <div class="debug-content">
        <div class="debug-chat-container">
          <DebugChatPanel
            :app-info="getDebugAppInfo()"
            :app-id="$route.params.id"
            :source-type="1"
            debug-mode="basic"
            :plugin-config="getDebugPluginConfig()"
          />
        </div>
      </div>
    </div>

    <!-- 智能体设定弹窗 -->
    <el-dialog
                title="智能体设定"
      :close-on-click-modal="false"
      :visible.sync="applicationSettingDialogVisible"
      width="70%"
    >
      <div class="application-setting-content">
        <el-form ref="applicationSettingForm" >
          <el-form-item>
            <el-input
              @focus="showApplicationSettingDialog"
              type="textarea"
              v-model="basicForm.sessionFlowSetting"
              :rows="24"
              show-word-limit
              maxlength="10000"
              placeholder="## 角色&#10;你是一位资深的中医师，拥有丰富的临床经验。&#10;&#10;## 目标&#10;根据患者提供的症状，给出用药建议。&#10;&#10;## 技能"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <!-- 开场介绍弹窗 -->
    <el-dialog
      title="开场介绍"
      :visible.sync="introductionDialogVisible"
      :close-on-click-modal="false"
      width="70%"
    >
      <div class="application-setting-content">
        <el-form ref="applicationSettingForm" >
          <el-form-item>
            <el-input
              type="textarea"
              v-model="basicForm.introduce"
              rows="24"
              maxlength="2000"
              show-word-limit
              placeholder="欢迎来到京鸿阁智能咨询，在这里你可以咨询用药建议方案、中医方面的问题。"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <!-- 添加对话框组件 -->
    <el-dialog
      title="选择默认模型"
      :visible.sync="defaultModelDialogVisible"
      :close-on-click-modal="false"
      width="40%"
      :before-close="handleCloseDefaultModelDialog"
    >
      <div class="model-dialog-content">
        <!-- 模型类型标签页 -->
        <el-tabs v-model="activeModelTab" class="model-tabs">
          <el-tab-pane label="内置模型" name="builtin">
            <div
              class="model-context-container"
              v-for="item in builtinModelOptions"
              :key="item.Code"
            >
              <div class="model-select-row">
                <el-radio
                  :label="item.Code"
                  v-model="tempDefaultModel"
                  class="model-radio"
                >
                  <div class="model-option-item">
                    <img :src="item.Img" alt="模型图标" class="option-icon" />
                    <span>{{ item.Name }}</span>
                  </div>
                </el-radio>
              </div>
              <div class="context-row">
                <div class="context-label">最大上下文</div>
                <div class="context-slider-container">
                  <el-slider
                    v-model="contextSizes[item.Code]"
                    :min="1000"
                    :max="16000"
                    :step="1000"
                    show-stops
                    class="context-slider"
                  ></el-slider>
                </div>
                <div class="context-input-container">
                  <el-input-number
                    v-model="contextSizes[item.Code]"
                    :min="1000"
                    :max="16000"
                    :step="1000"
                    class="context-input"
                    controls-position="right"
                  ></el-input-number>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="自定义模型" name="custom">
            <div class="empty-state">
              <i class="el-icon-setting"></i>
              <p>暂无自定义模型</p>
              <p class="tip">您可以在模型管理中添加自定义模型</p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDefaultModelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDefaultModel">确认</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="选择对话模型"
      :visible.sync="dialogModelDialogVisible"
      :close-on-click-modal="false"
      width="40%"
      :before-close="handleCloseDialogModelDialog"
    >
      <div class="model-dialog-content">
        <div
          class="model-context-container"
          v-for="item in modelOptions"
          :key="item.Code"
        >
          <div class="model-select-row">
            <el-checkbox
              :label="item.Code"
              v-model="tempDialogModelsMap[item.Code]"
              class="model-checkbox"
            >
              <div class="model-option-item">
                <img :src="item.Img" alt="模型图标" class="option-icon" />
                <span>{{ item.Name }}</span>
              </div>
            </el-checkbox>
          </div>
          <div class="context-row">
            <div class="context-label">最大上下文</div>
            <div class="context-slider-container">
              <el-slider
                v-model="dialogContextSizes[item.Code]"
                :min="1000"
                :max="16000"
                :step="1000"
                show-stops
                class="context-slider"
              ></el-slider>
            </div>
            <div class="context-input-container">
              <el-input-number
                v-model="dialogContextSizes[item.Code]"
                :min="1000"
                :max="16000"
                :step="1000"
                class="context-input"
                controls-position="right"
              ></el-input-number>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialogModelDialog">取消</el-button>
        <el-button type="primary" @click="confirmDialogModels">确认</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="绑定知识库"
      :visible.sync="knowledgeDialogVisible"
      :close-on-click-modal="false"
      width="60%"
      :before-close="handleCloseKnowledgeDialog"
    >
      <div class="knowledge-dialog-content">
        <div class="knowledge-search-box">
          <el-input
            v-model="knowledgeSearchKeyword"
            prefix-icon="el-icon-search"
            placeholder="搜索知识库"
            clearable
          ></el-input>
        </div>
        <div class="knowledge-list">
          <div
            v-for="item in filteredKnowledgeList()"
            :key="item.id"
            class="knowledge-card"
            :class="{
              'knowledge-card-selected': selectedKnowledgeIds.includes(item.id),
            }"
            @click="toggleKnowledgeSelection(item.id)"
          >
            <div class="knowledge-card-header">
              <div class="knowledge-card-checkbox">
                <el-checkbox
                  :value="selectedKnowledgeIds.includes(item.id)"
                  @click.native.stop
                  @change="
                    (val) =>
                      val
                        ? selectedKnowledgeIds.push(item.id)
                        : toggleKnowledgeSelection(item.id)
                  "
                ></el-checkbox>
              </div>
              <img
                src="@/assets/knowledge/knowledge.png"
                class="knowledge-icon"
                alt="知识库"
              />
              <div class="knowledge-card-name">{{ item.name }}</div>
            </div>
            <div class="knowledge-card-content">
              <div class="knowledge-card-desc">{{ item.description }}</div>
              <div class="knowledge-card-info">
                <div class="knowledge-card-code">{{ item.code }}</div>
                <div class="knowledge-card-time">
                  创建时间：{{ item.create_time }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="empty-state" v-if="knowledgeList.length === 0">
          <i class="el-icon-document"></i>
          <p>暂无知识库</p>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseKnowledgeDialog">取消</el-button>
        <el-button type="primary" @click="confirmBindKnowledge">确认</el-button>
      </span>
    </el-dialog>

    <!-- 知识库配置弹窗 -->
    <el-dialog
      title="知识库配置"
      :visible.sync="knowledgeConfigDialogVisible"
      width="50%"
      :close-on-click-modal="false"
      :before-close="handleCloseKnowledgeConfigDialog"
    >
      <div class="knowledge-config-dialog-content">
        <el-form ref="knowledgeConfigForm" :model="tempKnowledgeForm" :rules="knowledgeConfigRules">
        <h4 class="config-section-title">检索配置</h4>

        <div class="config-item">
          <div class="config-label">检索方式</div>
          <div class="config-value">
            <el-select
              v-model="tempKnowledgeForm.searchMethod"
              style="width: 100%"
            >
              <el-option label="语义检索" value="1">
                <div class="search-option">
                  <i class="search-icon el-icon-connection"></i>
                  <span>语义检索</span>
                </div>
              </el-option>
              <el-option label="增强检索" value="2">
                <div class="search-option">
                  <i class="search-icon el-icon-data-analysis"></i>
                  <span>增强检索</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">
            <div class="label-text">语义相似度阈值</div>
            <el-tooltip
              content="相似度阈值越高，召回的内容越精准，但可能漏掉相关内容"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="config-value slider-container">
            <div class="slider-wrapper">
              <el-slider
                v-model="tempKnowledgeForm.threshold"
                :min="0"
                :max="1"
                :step="0.01"
              ></el-slider>
            </div>
            <div class="slider-range">
              <span>0</span>
              <span>1</span>
            </div>
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">
            <div class="label-text">语义检索条数</div>
            <el-tooltip content="语义检索返回的知识片段数量" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="config-value slider-container">
            <div class="slider-wrapper">
              <el-slider
                v-model="tempKnowledgeForm.searchLimit"
                :min="0"
                :max="10"
                :step="1"
              ></el-slider>
            </div>
            <div class="slider-range">
              <span>0</span>
              <span>10</span>
            </div>
          </div>
        </div>

        <div class="config-item" v-if="tempKnowledgeForm.searchMethod === '2'">
          <div class="config-label">
            <div class="label-text">全文检索条数</div>
            <el-tooltip content="全文检索返回的知识片段数量" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="config-value slider-container">
            <div class="slider-wrapper">
              <el-slider
                v-model="tempKnowledgeForm.fullTextLimit"
                :min="0"
                :max="10"
                :step="1"
              ></el-slider>
            </div>
            <div class="slider-range">
              <span>0</span>
              <span>10</span>
            </div>
          </div>
        </div>

        <div class="config-tip" v-if="tempKnowledgeForm.searchMethod === '2'">
          <i class="el-icon-info"></i>
          <span
            >提示：全文检索为语义检索的补充，两者条数之和最大为10，优先满足语义检索条数设置</span
          >
        </div>

        <h4 class="config-section-title">回复配置</h4>

        <div class="config-item">
          <div class="config-label">
            <div class="label-text">未命中策略</div>
            <el-tooltip
              content="未找到知识库中相似内容时的策略：1. 自由发挥: 由AI根据智能体设定自行推理并回复；2. 固定文案: 根据指定文本进行回复；3. 转人工：依据应用高级配置-转人工配置中的规则提醒人工处理"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="config-value">
            <el-radio-group v-model="tempKnowledgeForm.missStrategy" class="radio-group" @change="handleMissStrategyChange">
              <el-radio label="fixedText" class="radio-label">固定文案</el-radio>
              <el-radio label="manual" class="radio-label">转人工</el-radio>
            </el-radio-group>
          </div>
        </div>

        <div class="config-item" v-if="tempKnowledgeForm.missStrategy === 'fixedText'">
          <div class="config-label">
            <div class="label-text">固定文案</div>
          </div>
          <div class="config-value">
            <el-form-item prop="fixedText">
              <el-input
                type="textarea"
                v-model="tempKnowledgeForm.fixedText"
                rows="3"
                maxlength="500"
                show-word-limit
                placeholder="请输入文案"
              />
            </el-form-item>
          </div>
        </div>

        <div class="config-item">
          <div class="config-label">
            <div class="label-text">展示知识库引用来源</div>
            <el-tooltip
              content="开启后，如提问命中知识库内容，将在回复中展示知识库内容出处的文件名、网站名和网址"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="config-value">
            <el-switch
              v-model="tempKnowledgeForm.showReference"
              @change="handleReferenceChange"
            />
          </div>
        </div>

        <div class="config-item" v-if="tempKnowledgeForm.showReference">
          <div class="config-label">
            <div class="label-text">知识库引用展示阈值</div>
                          <el-tooltip
                content="知识库检索结果中一般包含多条内容，模型在生成回复时一般会参考与问题相关度更高的知识库内容而并非所有的检索结果。设置该阈值后，在展示知识库引用来源时只展示相似度满足该阈值的检索结果。需设置大于等于「检索配置」中的语义相似度阈值。"
                placement="top"
              >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="config-value slider-container">
            <div class="slider-wrapper">
              <el-slider
                v-model="tempKnowledgeForm.referenceThreshold"
                :min="0"
                :max="1"
                :step="0.01"
              ></el-slider>
            </div>
            <div class="slider-range">
              <span>0</span>
              <span>1</span>
            </div>
          </div>
        </div>
        </el-form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseKnowledgeConfigDialog">取消</el-button>
        <el-button type="primary" @click="confirmKnowledgeConfig"
          >确认配置</el-button
        >
      </span>
    </el-dialog>

    <!-- 添加插件弹窗 -->
    <el-dialog
      title="添加插件"
      :visible.sync="addPluginDialogVisible"
      width="70%"
      :close-on-click-modal="false"
      :before-close="handleCloseAddPluginDialog"
    >
      <div class="add-plugin-content">
        <!-- 插件分类标签 -->
        <el-tabs v-model="activePluginTab" @tab-click="handlePluginTabChange">
          <el-tab-pane label="自定义插件" name="custom">
            <div class="plugin-search-box">
              <el-input
                v-model="customPluginSearchKeyword"
                prefix-icon="el-icon-search"
                placeholder="搜索自定义插件"
                clearable
                @input="handleCustomPluginSearch"
                @clear="handleCustomPluginSearch"
              ></el-input>
            </div>

            <div class="plugin-grid" v-loading="customPluginLoading" element-loading-text="加载自定义插件中...">
              <div
                v-for="plugin in filteredCustomPluginList"
                :key="`custom-${plugin.id}`"
                class="plugin-card"
                :class="{
                  'plugin-card-selected': tempSelectedPluginIds.includes(plugin.id)
                }"
                @click="selectPlugin(plugin)"
              >
                <div class="plugin-card-header">
                  <div class="plugin-card-checkbox">
                    <el-checkbox
                      :value="tempSelectedPluginIds.includes(plugin.id)"
                      @click.native.stop
                      @change="(val) => val ? tempSelectedPluginIds.push(plugin.id) : selectPlugin(plugin)"
                    ></el-checkbox>
                  </div>
                  <div class="plugin-card-icon">
                    <img
                      v-if="plugin.headSculpture || plugin.iconUrl"
                      :src="plugin.headSculpture || plugin.iconUrl"
                      alt="插件图标"
                    />
                    <i v-else class="el-icon-connection"></i>
                  </div>
                  <div class="plugin-card-name">{{ plugin.name }}</div>
                </div>
                <div class="plugin-card-content">
                  <div class="plugin-card-desc">{{ plugin.description || plugin.shortDesc }}</div>
                  <div class="plugin-card-info">
                    <div class="plugin-card-type">{{ plugin.type || 'HTTP' }}</div>
                  </div>
                </div>
              </div>

              <!-- 自定义插件空状态 -->
              <div v-if="!customPluginLoading && filteredCustomPluginList.length === 0" class="empty-state">
                <i class="el-icon-connection"></i>
                <p>{{ customPluginSearchKeyword ? '没有找到匹配的自定义插件' : '暂无自定义插件' }}</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="内置插件" name="builtin">
            <!-- 内置插件分类选择 -->
            <div class="builtin-categories">
              <el-radio-group
                v-model="activeBuiltinCategory"
                size="small"
                @change="handleBuiltinCategoryChange"
              >
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="basic">基础工具</el-radio-button>
                <el-radio-button label="search">搜索工具</el-radio-button>
                <el-radio-button label="content">内容工具</el-radio-button>
                <el-radio-button label="image">图像工具</el-radio-button>
                <el-radio-button label="media">音视频工具</el-radio-button>
                <el-radio-button label="other">其他</el-radio-button>
              </el-radio-group>
            </div>

            <div class="plugin-search-box">
              <el-input
                v-model="builtinPluginSearchKeyword"
                prefix-icon="el-icon-search"
                placeholder="搜索内置插件"
                clearable
                @input="handleBuiltinPluginSearch"
                @clear="handleBuiltinPluginSearch"
              ></el-input>
            </div>

            <div class="plugin-grid" v-loading="builtinPluginLoading" element-loading-text="加载内置插件中...">
              <div
                v-for="plugin in filteredBuiltinPluginList"
                :key="`builtin-${plugin.id}`"
                class="plugin-card"
                :class="{
                  'plugin-card-selected': tempSelectedPluginIds.includes(plugin.id)
                }"
                @click="selectPlugin(plugin)"
              >
                <div class="plugin-card-header">
                  <div class="plugin-card-checkbox">
                    <el-checkbox
                      :value="tempSelectedPluginIds.includes(plugin.id)"
                      @click.native.stop
                      @change="(val) => val ? tempSelectedPluginIds.push(plugin.id) : selectPlugin(plugin)"
                    ></el-checkbox>
                  </div>
                  <div class="plugin-card-icon">
                    <img
                      v-if="plugin.headSculpture || plugin.iconUrl"
                      :src="plugin.headSculpture || plugin.iconUrl"
                      alt="插件图标"
                    />
                    <i v-else class="el-icon-connection"></i>
                  </div>
                  <div class="plugin-card-name">{{ plugin.name }}</div>
                </div>
                <div class="plugin-card-content">
                  <div class="plugin-card-desc">{{ plugin.description || plugin.shortDesc }}</div>
                  <div class="plugin-card-footer">
                    <el-tag
                      v-for="tag in (plugin.tags || [])"
                      :key="tag"
                      type="primary"
                      size="mini"
                      class="plugin-tag"
                    >
                      {{ getPluginCategoryLabel(tag) }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 内置插件空状态 -->
              <div v-if="!builtinPluginLoading && filteredBuiltinPluginList.length === 0" class="empty-state">
                <i class="el-icon-box"></i>
                <p>{{ builtinPluginSearchKeyword ? '没有找到匹配的内置插件' : `暂无${getBuiltinCategoryLabel()}插件` }}</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseAddPluginDialog">取消</el-button>
        <el-button type="primary" @click="confirmAddPlugins">确认</el-button>
      </span>
    </el-dialog>

    <!-- 绑定数据库弹窗 -->
    <el-dialog
      title="绑定数据库"
      :visible.sync="bindDatabaseDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      :before-close="handleCloseBindDatabaseDialog"
    >
      <div class="bind-database-content" v-loading="loadingStates.database" element-loading-text="加载数据库列表中...">
        <div class="database-search-box">
          <el-input
            v-model="databaseSearchKeyword"
            prefix-icon="el-icon-search"
            placeholder="搜索数据库"
            clearable
            @input="handleDatabaseSearch"
            @clear="handleDatabaseSearch"
          ></el-input>
        </div>
        <div class="database-grid">
          <div
            v-for="database in filteredDatabaseList"
            :key="database.id"
            class="database-card"
            :class="{
              'database-card-selected': tempSelectedDatabaseIds.includes(database.id),
            }"
            @click="toggleDatabaseSelection(database.id)"
          >
            <div class="database-card-header">
              <div class="database-card-checkbox">
                <el-checkbox
                  :value="tempSelectedDatabaseIds.includes(database.id)"
                  @click.native.stop
                  @change="
                    (val) =>
                      val
                        ? tempSelectedDatabaseIds.push(database.id)
                        : toggleDatabaseSelection(database.id)
                  "
                ></el-checkbox>
              </div>
              <div class="database-card-icon">
                <i :class="database.dbType === 'internal' ? 'el-icon-coin' : 'el-icon-connection'"></i>
              </div>
              <div class="database-card-title">{{ database.name }}</div>
            </div>
            <div class="database-card-content">
              <div class="database-card-desc">{{ database.description }}</div>
              <div class="database-card-footer">
                <el-tag
                  :type="database.dbType === 'internal' ? 'success' : 'info'"
                  size="mini"
                >
                  {{ database.dbType === 'internal' ? '内置数据库' : '远程数据库' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <div class="empty-state" v-if="filteredDatabaseList.length === 0">
          <i class="el-icon-coin"></i>
          <p>{{ databaseSearchKeyword ? '没有找到匹配的数据库' : '暂无可用数据库' }}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="success" @click="createNewDatabase">新建数据库</el-button>
        <el-button @click="handleCloseBindDatabaseDialog">取消</el-button>
        <el-button type="primary" @click="confirmBindDatabase">确定</el-button>
      </span>
    </el-dialog>

    <!-- AI优化弹窗组件 -->
    <AIOptimizeDialog
      :visible.sync="aiOptimizeDialogVisible"
      :title="aiOptimizeConfig.title"
      :content-label="aiOptimizeConfig.contentLabel"
      :current-content="aiOptimizeConfig.currentContent"
      :optimize-type="aiOptimizeConfig.optimizeType"
      :additional-params="aiOptimizeConfig.additionalParams"
      @confirm="handleAIOptimizeConfirm"
      @close="handleAIOptimizeClose"
    />

    <!-- 插件配置弹窗 -->
    <el-dialog
      title="插件配置"
      :visible.sync="pluginConfigDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      :before-close="handleClosePluginConfigDialog"
    >
      <div class="plugin-config-content">
        <!-- 插件配置内容将在这里添加 -->
        <div class="config-placeholder">
          <p>插件配置内容待补充</p>
          <p v-if="currentConfigPlugin">当前配置插件：{{ currentConfigPlugin.data.name }}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmPluginConfig">确定</el-button>
      </span>
    </el-dialog>
      </div>
  </div>
</template>

<script>
import { api } from "@/api/request"
import AIOptimizeDialog from "@/components/AIOptimizeDialog.vue"
import DebugChatPanel from "@/components/chat/DebugChatPanel.vue"
import { copyToClipboard, generateShareLink } from "@/utils"
import { EnumAgentType, EnumApplicationType } from "@/utils/enums"

export default {
  name: "BasicConfig",
  components: {
    DebugChatPanel,
    AIOptimizeDialog
  },
  props: {
    isDebugging: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      basicForm: {
        id: "",
        name: "",
        description: "",
        introduce: "",
        profilePhoto: "",
        isPublic: false,
        shareLink: "",
        isTrueCustomerServer: false,
        applicationType: "",

        sessionFlowCode: "",
        sessionFlowSetting: "",
        pluginOrKnowledge: "", // 将根据智能体类型自动设置
      },
      applicationSettingDialogVisible: false,
      introductionDialogVisible: false,
      rules: {
        name: [
          { required: true, message: "请输入智能体名称", trigger: "blur" },
          { min: 0, max: 50, message: "长度不能超过50个字符", trigger: "blur" },
        ],
        sessionFlowCode: [
          { required: true, message: "请选择智能体类型", trigger: "change" },
        ],
        applicationType: [
          { required: true, message: "请选择智能体分类", trigger: "change" },
        ],

      },
      // 知识库配置校验规则
      knowledgeConfigRules: {
        fixedText: [
          {
            validator: (rule, value, callback) => {
              // 只有在选择了固定文案策略时才需要校验
              if (this.tempKnowledgeForm.missStrategy === 'fixedText') {
                if (!value || value.trim() === '') {
                  callback(new Error('请输入固定文案内容'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      },
      modelForm: {
        model: "", // 默认模型code（仅用于UI显示）
        selectedModels: [], // 选中的模型codes（仅用于UI显示）
        id: "node-1745544192305",
        type: "largeModel",
        position: {
          x: 0,
          y: 0,
        },
        data: {
          label: "大模型",
          type: "largeModel",
          name: "",
          description: "大模型节点描述",
          activeTab: "config",
          executionStatus: {},
          nodeTypeConfig: {
            name: "大模型配置",
            memory: true,
            memoryTurns: 2,
            memoryTime: 0,
            contextSizes: 8000, // 最大上下文设置
            inputs: [],
            systemPrompt: "知识智能体",
            temperature: 0.4,
            model: [
              {
                code: "qwen-turbo",
                name: "通义千问-Turbo",
                type: "",
              },
            ],
            structuredOutput: true,
            outputs: [],
            retentions: 1,
            voiceId: "", // 音色ID
          },
        },
      },
      contextSize: 7000, // 默认上下文大小
      contextSizes: {
        "qwen-turbo": 1000,
        "doubao-pro-32k": 1000,
        wenxin: 1000,
        "deepseek-chat": 1000,
        "deepseek-reasoner": 1000,
      },
      dialogContextSizes: {
        "qwen-turbo": 1000,
        "doubao-pro-32k": 1000,
        wenxin: 1000,
        "deepseek-chat": 1000,
        "deepseek-reasoner": 1000,
      },
      tempDefaultModel: "", // 临时存储选择的默认模型
      tempDialogModelsMap: {}, // 临时储存选中状态的映射
      defaultModelDialogVisible: false, // 默认模型弹窗可见性
      dialogModelDialogVisible: false, // 对话模型弹窗可见性
      activeModelTab: "builtin", // 当前选中的模型标签页
      modelOptions: [
        {
          Code: "customer",
          Name: "自定义",
          Type: "",
          Img: "/images/largemodel/customer.png",
        },
        {
          Code: "qwen-turbo",
          Name: "通义千问-Turbo",
          Type: "",
          Img: "/images/largemodel/qianwen.png",
        },
        {
          Code: "doubao-pro-32k",
          Name: "豆包-pro-32k",
          Type: "",
          Img: "/images/largemodel/doubao.png",
        },
        {
          Code: "wenxin",
          Name: "文心一言",
          Type: "",
          Img: "/images/largemodel/wenxin.png",
        },
        {
          Code: "deepseek-chat",
          Name: "Deepseek-v3",
          Type: "",
          Img: "/images/largemodel/deepseek.png",
        },
        {
          Code: "deepseek-reasoner",
          Name: "Deepseek-R1",
          Type: "",
          Img: "/images/largemodel/deepseek.png",
        },
      ],
      memoryForm: {
        minutes: 10,
      },
      knowledgeForm: {
        searchMethod: "2", // 默认增强检索
        threshold: 0.3, // 默认相似度
        searchLimit: 7, // 默认语义检索条数
        fullTextLimit: 3, // 默认全文检索条数
        missStrategy: "fixedText", // 未命中策略：fixedText-固定文案，manual-转人工
        fixedText: "", // 固定文案内容
        showReference: false, // 是否展示知识库引用来源
        referenceThreshold: 0.3, // 知识库引用展示阈值
      },
      tempKnowledgeForm: {
        searchMethod: "2", // 默认增强检索
        threshold: 0.3, // 默认相似度
        searchLimit: 7, // 默认语义检索条数
        fullTextLimit: 3, // 默认全文检索条数
        missStrategy: "fixedText", // 未命中策略：fixedText-固定文案，manual-转人工
        fixedText: "", // 固定文案内容
        showReference: false, // 是否展示知识库引用来源
        referenceThreshold: 0.3, // 知识库引用展示阈值
      },
      // 移除插件相关数据属性
      knowledgeDialogVisible: false,
      knowledgeConfigDialogVisible: false,
      knowledgeSearchKeyword: "",
      knowledgeList: [
        // 添加示例知识库数据用于演示
        {
          id: 'demo-1',
          name: '物流知识问答',
          description: '复制系统模版时生成复制系统模版时生成复制',
          code: 'logistics_qa',
          create_time: '2024-01-01 10:00:00'
        },
        {
          id: 'demo-2',
          name: '医药电商客服百事通知识问答',
          description: '电商产品介绍，包含各类产品的经典处方',
          code: 'medical_ecommerce_qa',
          create_time: '2024-01-02 10:00:00'
        }
      ],
      knowledgeLoading: false,
      selectedKnowledgeIds: ['demo-1', 'demo-2'], // 已选中的知识库ID列表
      addPluginDialogVisible: false,
      activePluginTab: "custom", // 当前选中的插件标签页
      selectedCategory: "all",
      selectedPluginIds: [], // 临时存储选中的插件ID
      addedPluginIds: [], // 默认已添加的插件
      tempSelectedPluginIds: [], // 弹窗临时选中的插件ID列表
      customPlugins: [
        // 添加示例自定义插件数据
        {
          id: 'demo-plugin-1',
          name: 'ai识图',
          description: '高性能图像处理MCP Server，支持生成等操作，可以进行大规模、高并发处理。',
          type: 'HTTP',
          tags: ['http']
        }
      ], // 自定义插件列表
      builtinPlugins: [], // 内置插件列表
      allBuiltinPlugins: [
        // 添加示例内置插件数据
        {
          id: 'demo-builtin-1',
          name: '百度地图',
          description: '百度地图客户端MCP Server，包含地理编码、导航等功能，支持多种地图操作。',
          type: 'MCP',
          tags: ['mcp']
        }
      ], // 所有内置插件数据
      allPluginsData: [], // 存储所有从/api/v1/Plugin/list获取的数据
      filteredCustomPluginList: [], // 过滤后的自定义插件列表
      filteredBuiltinPluginList: [], // 过滤后的内置插件列表
      customPluginLoading: false, // 自定义插件加载状态
      builtinPluginLoading: false, // 内置插件加载状态
      activePlugins: [
        // 添加示例插件数据用于演示
        {
          id: 'demo-plugin-1',
          data: {
            name: 'ai识图',
            description: '高性能图像处理MCP Server，支持生成等操作，可以进行大规模、高并发处理。',
            nodeTypeConfig: {
              pluginId: 'demo-plugin-1'
            }
          },
          headSculpture: null,
          enabled: true,
          pluginType: 'custom'
        }
      ], // 已激活的自定义插件列表
      activeNewPlugins: [
        // 添加示例内置插件数据用于演示
        {
          id: 'demo-builtin-1',
          data: {
            name: '百度地图',
            description: '百度地图客户端MCP Server，包含地理编码、导航等功能，支持多种地图操作。',
            nodeTypeConfig: {
              pluginId: 'demo-builtin-1'
            }
          },
          headSculpture: null,
          enabled: true,
          pluginType: 'builtin'
        }
      ], // 已激活的内置插件列表
      activeBuiltinCategory: "all", // 内置插件分类
      customPluginSearchKeyword: "", // 自定义插件搜索关键词
      builtinPluginSearchKeyword: "", // 内置插件搜索关键词
      // 在modelForm初始化中添加知识库节点的数据结构
      knowledgeNodeData: {
        id: "knowledge",
        type: "knowledge",
        position: {
          x: 63,
          y: 203,
        },
        data: {
          label: "知识库",
          type: "knowledge",
          name: "",
          description: "节点描述",
          activeTab: "config",
          executionStatus: {},
          nodeTypeConfig: {
            name: "知识库配置",
            memory: false,
            memoryTurns: 1,
            knowledgeBase: "",
            searchType: "2", // 使用数字类型表示检索方式
            similarity: 0.3,
            semanticCount: 7,
            fullTextCount: 3,
            missStrategy: "fixedText", // 未命中策略
            fixedText: "", // 固定文案内容
            showReference: false, // 是否展示知识库引用来源
            referenceThreshold: 0.3, // 知识库引用展示阈值
          },
        },
      },
      voiceOptions: [], // 音色选项列表
      voicePlayLoading: false, // 播放语音加载状态
      // loading状态管理
      loadingStates: {
        voice: false,        // 获取声音列表loading
        detail: false,       // 获取详情loading
        plugin: false,       // 获取插件列表loading
        knowledge: false,    // 获取知识库列表loading
        upload: false,       // 上传头像loading
        database: false      // 获取数据库列表loading
      },
      // 数据库相关
      bindDatabaseDialogVisible: false, // 绑定数据库弹窗可见性
      databaseList: [], // 可选择的数据库列表
      selectedDatabases: [
        // 添加示例数据库数据用于演示
        {
          id: 'demo-db-1',
          name: '知识图谱库',
          description: '复制系统模版时生成复制系统模版时生成复制'
        }
      ], // 已选中的数据库列表
      tempSelectedDatabaseIds: [], // 弹窗中临时选中的数据库ID列表
      databaseModels: [], // 数据库模型列表
      selectedDatabaseModel: '', // 选中的数据库模型
      activeDatabaseNodes: [], // 已激活的数据库配置节点列表
      databaseSearchKeyword: "", // 数据库搜索关键词

      // 枚举选项
      applicationTypeOptions: EnumApplicationType,
      agentTypeOptions: EnumAgentType,

      // AI优化弹窗相关
      aiOptimizeDialogVisible: false,
      aiOptimizeConfig: {
        title: "",
        contentLabel: "",
        currentContent: "",
        optimizeType: "",
        additionalParams: {}
      },

      // 插件配置弹框相关
      pluginConfigDialogVisible: false,
      currentConfigPlugin: null
    };
  },
  created() {
    this.id = this.$route.params.id;
    // 先获取知识库列表、插件列表、音色列表和数据库列表
    Promise.all([this.getKnowledgeList(), this.fetchPluginList(), this.getVoiceList(), this.initDatabaseList()])
      .then(() => {
        // 然后获取应用详情
        this.fetchAppDetail();
      })
      .catch((error) => {
        console.error("加载列表失败:", error);
        // 即使列表加载失败，仍然继续加载应用详情
        this.fetchAppDetail();
      });
    this.initTempDialogModelsMap(); // 初始化临时映射

    // 初始化时，从表单同步到知识库节点数据结构
    this.syncKnowledgeFormToNodeData();
  },
  methods: {
    // 获取调试应用信息
    getDebugAppInfo() {
      return {
        id: this.$route.params.id,
        name: this.basicForm.name || "调试应用",
        description: this.basicForm.description || "正在调试应用配置",
        profilePhoto: this.basicForm.profilePhoto || "",
        introduce: this.basicForm.introduce || "这是一个调试环境，您可以测试应用的基本功能。"
      };
    },

        // 获取调试插件配置信息
    getDebugPluginConfig() {
      // 合并自定义插件和内置插件
      const allPlugins = [...this.activePlugins, ...this.activeNewPlugins];

      // 检查是否包含图像相关插件
      const hasImageVision = allPlugins.some(plugin => {
        // 首先检查插件是否被启用
        if (plugin.enabled === false) {
          return false;
        }

        const pluginId = plugin.data?.nodeTypeConfig?.pluginId;
        if (!pluginId) {
          return false;
        }

        // 检查自定义插件
        const customPlugin = this.customPlugins.find(cp => cp.id === pluginId);
        if (customPlugin) {
          return this.isImageRelatedPlugin(customPlugin);
        }

        // 检查内置插件
        const builtinPlugin = this.allBuiltinPlugins.find(bp => bp.id === pluginId);
        if (builtinPlugin) {
          return this.isImageRelatedPlugin(builtinPlugin);
        }

        return false;
      });

      console.log('调试插件配置 - 图像功能状态:', hasImageVision);
      console.log('调试插件配置 - 活跃插件:', allPlugins.map(p => ({
        id: p.data?.nodeTypeConfig?.pluginId,
        name: p.data?.name,
        enabled: p.enabled
      })));

      return {
        hasImageVision: hasImageVision,
        enabledPlugins: allPlugins.filter(plugin => plugin.enabled !== false)
      };
    },

    // 检查插件是否为图像相关插件
    isImageRelatedPlugin(plugin) {
      if (!plugin) return false;

      // 检查插件名称中是否包含图像相关关键词
      const imageKeywords = [
        'image', 'vision', 'picture', 'photo', 'img', 'visual',
        '图像', '图片', '照片', '视觉', '识别', '图象', '影像'
      ];

      const name = (plugin.name || '').toLowerCase();
      const description = (plugin.description || '').toLowerCase();
      const shortDesc = (plugin.shortDesc || '').toLowerCase();
      const nickName = (plugin.nickName || '').toLowerCase();

      // 检查名称、描述、简短描述、昵称中是否包含图像相关关键词
      const hasImageKeyword = imageKeywords.some(keyword =>
        name.includes(keyword.toLowerCase()) ||
        description.includes(keyword.toLowerCase()) ||
        shortDesc.includes(keyword.toLowerCase()) ||
        nickName.includes(keyword.toLowerCase())
      );

      // 检查插件类型或标签
      const tags = plugin.tags || [];
      const hasImageTag = tags.some(tag =>
        typeof tag === 'string' && imageKeywords.some(keyword =>
          tag.toLowerCase().includes(keyword.toLowerCase())
        )
      );

      console.log('检查插件图像功能:', {
        pluginId: plugin.id,
        name: plugin.name,
        hasImageKeyword,
        hasImageTag,
        isImagePlugin: hasImageKeyword || hasImageTag
      });

      return hasImageKeyword || hasImageTag;
    },
    showIntroductionDialog() {
      this.introductionDialogVisible = true;
    },
    showApplicationSettingDialog() {
      this.applicationSettingDialogVisible = true;
    },
    async fetchAppDetail() {
      try {
        this.loadingStates.detail = true;
        const res = await api.sessionFlow.getDetail(this.id);
        if (res.code === 200 && res.data) {
          // 有选择性地合并数据，保持默认值不被空值覆盖
          const responseData = { ...res.data };

          // 如果服务器没有返回pluginOrKnowledge或为空值，删除该字段以便后续设置默认值
          if (responseData.pluginOrKnowledge === undefined ||
              responseData.pluginOrKnowledge === null ||
              responseData.pluginOrKnowledge === '') {
            delete responseData.pluginOrKnowledge;
            console.log('fetchAppDetail - 删除空的pluginOrKnowledge字段，后续将设置默认值');
          }

          this.basicForm = {
            ...this.basicForm,
            ...responseData,
          };

          // 调试信息：打印插件执行策略的值
          console.log('fetchAppDetail - 设置后的插件执行策略:', this.basicForm.pluginOrKnowledge, typeof this.basicForm.pluginOrKnowledge);

          // 生成分享链接
          this.basicForm.shareLink = generateShareLink(this.id);

          // 解析会话流配置
          if (res.data.flowDetailDto) {
            try {
              const flowSettings = res.data.flowDetailDto;

              // 初始化大模型配置
              this.initializeModelSettings(flowSettings);

              // 初始化知识库配置
              this.initializeKnowledgeSettings(flowSettings);

              // 初始化插件配置
              this.initializePluginSettings(flowSettings);
            } catch (parseError) {
              console.error("解析会话流配置失败:", parseError);
            }
          }
        } else {
          throw new Error(res.message || "获取应用详情失败");
        }
      } catch (error) {
        console.error("获取应用详情失败:", error);
        this.$showFriendlyError(null, "获取应用详情失败");
      } finally {
        this.loadingStates.detail = false;

        // 最终检查：确保 pluginOrKnowledge 总是有值，根据应用类型设置默认值
        console.log('最终检查 - 当前插件执行策略:', this.basicForm.pluginOrKnowledge, typeof this.basicForm.pluginOrKnowledge);
        console.log('最终检查 - 当前应用类型(sessionFlowCode):', this.basicForm.sessionFlowCode, typeof this.basicForm.sessionFlowCode);

        // 检查pluginOrKnowledge是否需要设置默认值
        const needsDefault = this.basicForm.pluginOrKnowledge === undefined ||
                             this.basicForm.pluginOrKnowledge === null ||
                             this.basicForm.pluginOrKnowledge === '' ||
                             !this.basicForm.pluginOrKnowledge;

        if (needsDefault) {
          // 根据应用类型设置默认值
          // sessionFlowCode = "1" 轻量智能体 -> 插件优先 (pluginOrKnowledge = "1")
          // sessionFlowCode = "2" 知识智能体 -> 知识库优先 (pluginOrKnowledge = "2")
          if (+this.basicForm.sessionFlowCode === 2) {
            this.basicForm.pluginOrKnowledge = "2"; // 知识智能体默认知识库优先
            console.log('最终检查：知识智能体 - 设置插件执行策略默认值为知识库优先');
          } else {
            this.basicForm.pluginOrKnowledge = "1"; // 轻量智能体默认插件优先
            console.log('最终检查：轻量智能体 - 设置插件执行策略默认值为插件优先');
          }
        } else {
          console.log('最终检查：插件执行策略值有效，保持不变');
        }
      }
    },

    // 初始化大模型配置
    initializeModelSettings(flowSettings) {
      if (
        !flowSettings ||
        !flowSettings.largeModelNodes ||
        !flowSettings.largeModelNodes.length
      )
        return;

      const modelConfig = flowSettings.largeModelNodes[0];
      modelConfig.data.nodeTypeConfig.outputs=[]
      modelConfig.data.nodeTypeConfig.structuredOutput=false
      // 更新大模型节点数据
      if (modelConfig && modelConfig.data && modelConfig.data.nodeTypeConfig) {
        this.modelForm = modelConfig;

        // 如果有选择模型，获取模型信息
        if (
          modelConfig.data.nodeTypeConfig.model &&
          modelConfig.data.nodeTypeConfig.model.code
        ) {
          const modelCode = modelConfig.data.nodeTypeConfig.model.code;
          this.modelForm.model = modelCode;
          this.tempDefaultModel = modelCode; // 设置临时默认模型

          // 如果有context size设置，同步它
          if (modelConfig.data.nodeTypeConfig.contextSizes) {
            // 确保contextSizes对象已经初始化
            if (!this.contextSizes[modelCode]) {
              this.contextSizes[modelCode] = 4000; // 默认值
            }

            // 设置上下文大小
            this.contextSizes[modelCode] = parseInt(
              modelConfig.data.nodeTypeConfig.contextSizes
            );
          }
        }

        // 初始化声音配置 - 从endNodes中读取已保存的声音配置
        let savedVoiceId = "";
        if (flowSettings.endNodes && flowSettings.endNodes.length > 0) {
          const endNode = flowSettings.endNodes[0];
          if (endNode.data &&
              endNode.data.nodeTypeConfig &&
              endNode.data.nodeTypeConfig.responseConfig &&
              endNode.data.nodeTypeConfig.responseConfig.voice) {
            savedVoiceId = endNode.data.nodeTypeConfig.responseConfig.voice;
            console.log('从endNodes中读取到的声音配置:', savedVoiceId);
          }
        }

        // 确保音色ID字段存在，并设置已保存的值
        if (!modelConfig.data.nodeTypeConfig.voiceId) {
          this.$set(this.modelForm.data.nodeTypeConfig, 'voiceId', savedVoiceId);
        } else if (savedVoiceId) {
          // 如果已经存在voiceId字段但值为空，则使用已保存的值
          this.modelForm.data.nodeTypeConfig.voiceId = savedVoiceId;
        }

        // 同时设置voice字段，保持数据一致性
        this.$set(this.modelForm.data.nodeTypeConfig, 'voice', savedVoiceId);

        // 如果没有保存的声音配置且声音列表已加载，默认选中第一个声音
        if (!savedVoiceId && this.voiceOptions.length > 0) {
          const firstVoice = this.voiceOptions[0];
          this.modelForm.data.nodeTypeConfig.voiceId = firstVoice.id;
          this.modelForm.data.nodeTypeConfig.voice = firstVoice.id;
          console.log('在初始化大模型配置时默认选中第一个声音:', firstVoice.name, firstVoice.id);
        }

        console.log('初始化后的声音配置 - voiceId:', this.modelForm.data.nodeTypeConfig.voiceId);
        console.log('初始化后的声音配置 - voice:', this.modelForm.data.nodeTypeConfig.voice);
      }
    },

    // 初始化知识库配置
    initializeKnowledgeSettings(flowSettings) {
      const knowledgeConfig = flowSettings.knowledgeNodes[0];

      // 更新知识库节点数据
      if (
        knowledgeConfig &&
        knowledgeConfig.data &&
        knowledgeConfig.data.nodeTypeConfig
      ) {
        this.knowledgeNodeData = knowledgeConfig;

        // 初始化知识库表单，同步节点配置到表单
        this.knowledgeForm = {
          searchMethod:
            this.knowledgeNodeData.data.nodeTypeConfig.searchType || "2", // 默认增强检索
          threshold:
            this.knowledgeNodeData.data.nodeTypeConfig.similarity || 0.3,
          searchLimit:
            this.knowledgeNodeData.data.nodeTypeConfig.semanticCount || 7,
          fullTextLimit:
            this.knowledgeNodeData.data.nodeTypeConfig.fullTextCount || 3,
          missStrategy:
            this.knowledgeNodeData.data.nodeTypeConfig.missStrategy || "fixedText",
          fixedText:
            this.knowledgeNodeData.data.nodeTypeConfig.fixedText || "",
          showReference:
            this.knowledgeNodeData.data.nodeTypeConfig.showReference || false,
          referenceThreshold:
            this.knowledgeNodeData.data.nodeTypeConfig.referenceThreshold || 0.3,
        };

        // 获取已绑定的知识库code
        const knowledgeBase =
          knowledgeConfig.data.nodeTypeConfig.knowledgeBase || "";

        if (knowledgeBase) {
          // 保存知识库code列表
          const knowledgeBaseCodes = knowledgeBase.split(",");
          console.log("已绑定的知识库codes:", knowledgeBaseCodes);

          // 根据code找到对应的知识库，并添加到选中ID列表
          this.selectedKnowledgeIds = this.knowledgeList
            .filter((item) => knowledgeBaseCodes.includes(item.code))
            .map((item) => item.id);

          console.log("根据code匹配到的知识库ID:", this.selectedKnowledgeIds);
        }
      }
    },

    // 初始化插件配置
    initializePluginSettings(flowSettings) {
      console.log(
        "插件配置数据结构(flowSettings.pluginNodes):",
        flowSettings.pluginNodes
      );
      console.log(
        "新插件配置数据结构(flowSettings.newPluginNodes):",
        flowSettings.newPluginNodes
      );

      // 处理自定义插件 (pluginNodes)
      if (
        flowSettings &&
        flowSettings.pluginNodes &&
        flowSettings.pluginNodes.length > 0
      ) {
        // 获取已激活的自定义插件列表
        this.activePlugins = flowSettings.pluginNodes
          .filter((plugin) => {
            // 检查plugin.data下的name、description、label是否不全为空
            const data = plugin.data || {};
            const name = data.name || '';
            const description = data.description || '';
            const label = data.label || '';

            // 至少有一个字段不为空才添加
            return name.trim() !== '' || description.trim() !== '' || label.trim() !== '';
          })
          .map((plugin) => {
            return {
              ...plugin,
              enabled: plugin.enabled !== undefined ? plugin.enabled : true, // 使用接口返回的enabled值，默认为true
              pluginType: 'custom' // 标记为自定义插件
            };
          });

        // 设置已添加的自定义插件ID列表
        this.addedPluginIds = this.activePlugins.map(
          (plugin) => plugin.data.nodeTypeConfig.pluginId
        );
        this.activePlugins.forEach((plugin) => {
          const customPlugin = this.customPlugins.find(
            (item) => item.id === plugin.data.nodeTypeConfig.pluginId
          );
          if (customPlugin) {
            plugin.headSculpture = customPlugin.headSculpture;
            plugin.description = customPlugin.description;
            plugin.name = customPlugin.name;
          }
        });
      }

      // 处理内置插件 (newPluginNodes)
      if (
        flowSettings &&
        flowSettings.newPluginNodes &&
        flowSettings.newPluginNodes.length > 0
      ) {
        // 获取已激活的内置插件列表
        this.activeNewPlugins = flowSettings.newPluginNodes
          .filter((plugin) => {
            // 检查plugin.data下的name、description、label是否不全为空
            const data = plugin.data || {};
            const name = data.name || '';
            const description = data.description || '';
            const label = data.label || '';

            // 至少有一个字段不为空才添加
            return name.trim() !== '' || description.trim() !== '' || label.trim() !== '';
          })
          .map((plugin) => {
            return {
              ...plugin,
              enabled: plugin.enabled !== undefined ? plugin.enabled : true, // 使用接口返回的enabled值，默认为true
              pluginType: 'builtin' // 标记为内置插件
            };
          });

        // 将内置插件ID添加到已添加列表中
        const newPluginIds = this.activeNewPlugins.map(
          (plugin) => plugin.data.nodeTypeConfig.pluginId
        );
        this.addedPluginIds = [...this.addedPluginIds, ...newPluginIds];

        // 补充内置插件的信息
        this.activeNewPlugins.forEach((plugin) => {
          const builtinPlugin = this.allBuiltinPlugins.find(
            (item) => item.id === plugin.data.nodeTypeConfig.pluginId
          );
          if (builtinPlugin) {
            plugin.headSculpture = builtinPlugin.headSculpture || builtinPlugin.iconUrl;
            plugin.description = builtinPlugin.description || builtinPlugin.shortDesc;
            plugin.name = builtinPlugin.name;
          }
        });
      }

      // 处理数据库节点 (dbNodes)
      if (
        flowSettings &&
        flowSettings.dbNodes &&
        flowSettings.dbNodes.length > 0
      ) {
        // 获取已激活的数据库节点列表
        this.activeDatabaseNodes = flowSettings.dbNodes
          .filter((dbNode) => {
            // 检查dbNode.data下的配置是否有效
            const data = dbNode.data || {};
            const dbConfigs = data.nodeTypeConfig?.dbConfigs || [];

            // 至少有一个数据库配置才算有效
            return dbConfigs.length > 0 && dbConfigs.some(config => config.dbId && config.dbName);
          })
          .map((dbNode) => {
            return {
              ...dbNode,
              enabled: dbNode.enabled !== undefined ? dbNode.enabled : true, // 使用接口返回的enabled值，默认为true
            };
          });

        // 从数据库节点中获取已选择的数据库
        if (this.activeDatabaseNodes.length > 0) {
          const firstDbNode = this.activeDatabaseNodes[0];
          const dbConfigs = firstDbNode.data?.nodeTypeConfig?.dbConfigs || [];

          // 将dbConfigs转换为selectedDatabases格式
          this.selectedDatabases = dbConfigs.map(config => ({
            id: config.dbId,
            name: config.dbName
          }));

          // 获取已选择的数据库模型
          if (firstDbNode.data?.nodeTypeConfig?.modelCode) {
            this.selectedDatabaseModel = firstDbNode.data.nodeTypeConfig.modelCode;
          }
        }

        console.log("已激活的数据库节点:", this.activeDatabaseNodes);
        console.log("已选择的数据库:", this.selectedDatabases);
        console.log("已选择的数据库模型:", this.selectedDatabaseModel);
      }

      // 插件执行策略已经在fetchAppDetail中通过基本信息设置，这里不需要重复处理
      console.log('当前插件执行策略:', this.basicForm.pluginOrKnowledge);
    },

    handleTypeChange() {
      // 当智能体类型改变时，确保sessionFlowCode的值是字符串格式
      if (this.basicForm.sessionFlowCode) {
        // 确保sessionFlowCode是字符串格式
        this.basicForm.sessionFlowCode = this.basicForm.sessionFlowCode.toString();

        // 根据新的智能体类型自动调整插件执行策略为对应的默认值
        // sessionFlowCode = "1" 轻量智能体 -> 插件优先 (pluginOrKnowledge = "1")
        // sessionFlowCode = "2" 知识智能体 -> 知识库优先 (pluginOrKnowledge = "2")
        if (+this.basicForm.sessionFlowCode === 2) {
          this.basicForm.pluginOrKnowledge = "2"; // 知识智能体默认知识库优先
          console.log('智能体类型切换为知识智能体 - 自动设置插件执行策略为知识库优先');
        } else if (+this.basicForm.sessionFlowCode === 1) {
          this.basicForm.pluginOrKnowledge = "1"; // 轻量智能体默认插件优先
          console.log('智能体类型切换为轻量智能体 - 自动设置插件执行策略为插件优先');

          // 如果从知识智能体切换为轻量智能体，需要清空知识库和数据库配置
          this.resetKnowledgeConfig();
          this.resetDatabaseConfig();
        }
      }
    },

    // 重置知识库配置
    resetKnowledgeConfig() {
      // 重置知识库节点数据为初始状态
      this.knowledgeNodeData = {
        id: "knowledge",
        type: "knowledge",
        position: {
          x: 63,
          y: 203,
        },
        data: {
          label: "知识库",
          type: "knowledge",
          name: "",
          description: "节点描述",
          activeTab: "config",
          executionStatus: {},
          nodeTypeConfig: {
            name: "知识库配置",
            memory: false,
            memoryTurns: 1,
            knowledgeBase: "",
            searchType: "2", // 使用数字类型表示检索方式
            similarity: 0.3,
            semanticCount: 7,
            fullTextCount: 3,
            missStrategy: "fixedText", // 未命中策略
            fixedText: "", // 固定文案内容
            showReference: false, // 是否展示知识库引用来源
            referenceThreshold: 0.3, // 知识库引用展示阈值
          },
        },
      };

      // 重置知识库表单配置
      this.knowledgeForm = {
        searchMethod: "2", // 默认增强检索
        threshold: 0.3, // 默认相似度
        searchLimit: 7, // 默认语义检索条数
        fullTextLimit: 3, // 默认全文检索条数
        missStrategy: "fixedText", // 未命中策略：fixedText-固定文案，manual-转人工
        fixedText: "", // 固定文案内容
        showReference: false, // 是否展示知识库引用来源
        referenceThreshold: 0.3, // 知识库引用展示阈值
      };

      // 重置临时知识库表单配置
      this.tempKnowledgeForm = {
        searchMethod: "2", // 默认增强检索
        threshold: 0.3, // 默认相似度
        searchLimit: 7, // 默认语义检索条数
        fullTextLimit: 3, // 默认全文检索条数
        missStrategy: "fixedText", // 未命中策略：fixedText-固定文案，manual-转人工
        fixedText: "", // 固定文案内容
        showReference: false, // 是否展示知识库引用来源
        referenceThreshold: 0.3, // 知识库引用展示阈值
      };

      // 清空已选择的知识库
      this.selectedKnowledgeIds = [];

      console.log('已重置知识库配置为初始状态');
    },

    // 重置数据库配置
    resetDatabaseConfig() {
      // 清空已选择的数据库
      this.selectedDatabases = [];

      // 清空已激活的数据库节点
      this.activeDatabaseNodes = [];

      // 重置数据库模型选择
      this.selectedDatabaseModel = "";

      // 重置临时选中的数据库ID
      this.tempSelectedDatabaseIds = [];

      console.log('已重置数据库配置为初始状态');
    },
    async copyShareLink() {
      try {
        const success = await copyToClipboard(this.basicForm.shareLink);
        if (success) {
          this.$message.success('链接已复制到剪贴板');
        } else {
          this.$showFriendlyError(null, '复制失败，请手动复制');
        }
      } catch (err) {
        console.error('复制失败:', err);
        this.$showFriendlyError(null, '复制失败，请手动复制');
      }
    },
    getSelectedModelImg() {
      // 先查看model中是否有选中的模型
      if (
        this.modelForm.data?.nodeTypeConfig?.model &&
        this.modelForm.data.nodeTypeConfig.model.length > 0
      ) {
        const selectedModel = this.modelOptions.find(
          (item) =>
            item.Code === this.modelForm.data.nodeTypeConfig.model[0].code
        );
        if (selectedModel) {
          return selectedModel.Img;
        }
      }

      // 兼容旧逻辑
      const selected = this.modelOptions.find(
        (item) => item.Code === this.modelForm.model
      );

      // 返回默认图片或模型图片
      return selected?.Img || "path/to/default-model-icon.png";
    },

    getSelectedModelName() {
      // 先查看model中是否有选中的模型
      if (
        this.modelForm.data?.nodeTypeConfig?.model &&
        this.modelForm.data.nodeTypeConfig.model.length > 0
      ) {
        return this.modelForm.data.nodeTypeConfig.model[0].name || "";
      }

      // 兼容旧逻辑
      const selected = this.modelOptions.find(
        (item) => item.Code === this.modelForm.model
      );
      return selected?.Name || "";
    },
    getModelImgByCode(code) {
      const selected = this.modelOptions.find((item) => item.Code === code);
      return selected?.Img || "path/to/default-model-icon.png";
    },
    showDefaultModelDialog() {
      // 检查当前选中的模型
      if (
        this.modelForm.data &&
        this.modelForm.data.nodeTypeConfig &&
        this.modelForm.data.nodeTypeConfig.model &&
        this.modelForm.data.nodeTypeConfig.model.length > 0
      ) {
        this.tempDefaultModel =
          this.modelForm.data.nodeTypeConfig.model[0].code;
      } else if (this.modelForm.model) {
        this.tempDefaultModel = this.modelForm.model;
      } else {
        // 默认选择第一个模型选项
        this.tempDefaultModel =
          this.modelOptions.length > 0 ? this.modelOptions[0].Code : "";
      }

      this.defaultModelDialogVisible = true;
    },
    handleCloseDefaultModelDialog() {
      this.tempDefaultModel = this.modelForm.model;
      this.defaultModelDialogVisible = false;
    },
    confirmDefaultModel() {
      this.modelForm.model = this.tempDefaultModel;

      // 更新nodeTypeConfig中的模型
      const selectedModel = this.modelOptions.find(
        (item) => item.Code === this.tempDefaultModel
      );

      if (selectedModel) {
        // 使用model数组
        if (this.modelForm.data && this.modelForm.data.nodeTypeConfig) {
          this.modelForm.data.nodeTypeConfig.model =
            {
              code: selectedModel.Code,
              name: selectedModel.Name,
              type: selectedModel.Type || "",
            }


          // 确保有上下文大小
          if (!this.contextSizes[selectedModel.Code]) {
            this.contextSizes[selectedModel.Code] = 4000; // 默认值
          }

          // 更新上下文大小
          this.modelForm.data.nodeTypeConfig.contextSizes =
            this.contextSizes[selectedModel.Code];
        }
      }

      this.defaultModelDialogVisible = false;
    },
    initTempDialogModelsMap() {
      this.modelOptions.forEach((item) => {
        this.$set(this.tempDialogModelsMap, item.Code, false);
      });
    },
    showDialogModelDialog() {
      // 重置临时映射
      this.modelOptions.forEach((item) => {
        this.$set(
          this.tempDialogModelsMap,
          item.Code,
          this.modelForm.selectedModels.includes(item.Code)
        );
      });
      this.dialogModelDialogVisible = true;
    },
    handleCloseDialogModelDialog() {
      // 重置临时映射
      this.modelOptions.forEach((item) => {
        this.$set(
          this.tempDialogModelsMap,
          item.Code,
          this.modelForm.selectedModels.includes(item.Code)
        );
      });
      this.dialogModelDialogVisible = false;
    },
    confirmDialogModels() {
      // 根据映射更新selectedModels
      const selectedModels = [];
      Object.keys(this.tempDialogModelsMap).forEach((code) => {
        if (this.tempDialogModelsMap[code]) {
          selectedModels.push(code);
        }
      });
      this.modelForm.selectedModels = selectedModels;

      this.dialogModelDialogVisible = false;
    },
    getModelNameByCode(code) {
      const selected = this.modelOptions.find((item) => item.Code === code);
      return selected?.Name || code;
    },
    showBindKnowledgeDialog() {
      this.knowledgeDialogVisible = true;
      this.knowledgeSearchKeyword = "";

      // 只在知识库列表为空时才重新获取
      if (this.knowledgeList.length === 0) {
        this.getKnowledgeList();
      }
    },
    handleCloseKnowledgeDialog() {
      this.knowledgeDialogVisible = false;
    },
    toggleKnowledgeSelection(id) {
      const index = this.selectedKnowledgeIds.indexOf(id);
      if (index > -1) {
        this.selectedKnowledgeIds.splice(index, 1);
      } else {
        this.selectedKnowledgeIds.push(id);
      }
    },
    confirmBindKnowledge() {
      // 将选中的知识库转换为以逗号分隔的code字符串格式
      if (this.selectedKnowledgeIds.length > 0) {
        // 获取选中知识库的code
        const selectedKnowledgeCodes = this.selectedKnowledges.map(
          (item) => item.code
        );
        if (
          this.knowledgeNodeData.data &&
          this.knowledgeNodeData.data.nodeTypeConfig
        ) {
          this.knowledgeNodeData.data.nodeTypeConfig.knowledgeBase =
            selectedKnowledgeCodes.join(",");
        }

        // 更新显示的知识库列表
        this.$message.success(
          `已成功绑定${this.selectedKnowledgeIds.length}个知识库`
        );
      } else {
        if (
          this.knowledgeNodeData.data &&
          this.knowledgeNodeData.data.nodeTypeConfig
        ) {
          this.knowledgeNodeData.data.nodeTypeConfig.knowledgeBase = "";
        }
        this.$message.warning("未选择任何知识库");
      }

      this.knowledgeDialogVisible = false;
    },
    // 查看知识库 - 打开知识库选择弹框
    handleViewKnowledge() {
      this.showBindKnowledgeDialog();
    },
    // 解绑知识库 - 从选中列表中移除
    handleUnbindKnowledge(knowledge) {
      this.$confirm(`确定要解绑知识库"${knowledge.name}"吗？`, '确认解绑', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从selectedKnowledgeIds中移除该知识库
        const index = this.selectedKnowledgeIds.indexOf(knowledge.id);
        if (index > -1) {
          this.selectedKnowledgeIds.splice(index, 1);

          // 更新knowledgeNodeData中的配置
          if (
            this.knowledgeNodeData.data &&
            this.knowledgeNodeData.data.nodeTypeConfig
          ) {
            const selectedKnowledgeCodes = this.selectedKnowledges.map(
              (item) => item.code
            );
            this.knowledgeNodeData.data.nodeTypeConfig.knowledgeBase =
              selectedKnowledgeCodes.join(",");
          }

          this.$message.success(`已成功解绑知识库"${knowledge.name}"`);
        }
      }).catch(() => {
        // 用户取消操作
      });
    },
    // 解绑数据库 - 从选中列表中移除
    handleUnbindDatabase(database) {
      this.$confirm(`确定要解绑数据库"${database.name}"吗？`, '确认解绑', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从selectedDatabases中移除该数据库
        const index = this.selectedDatabases.findIndex(db => db.id === database.id);
        if (index > -1) {
          this.selectedDatabases.splice(index, 1);

          // 更新activeDatabaseNodes中的配置
          if (this.activeDatabaseNodes.length > 0) {
            const dbNode = this.activeDatabaseNodes[0];
            if (dbNode.data && dbNode.data.nodeTypeConfig) {
              dbNode.data.nodeTypeConfig.dbConfigs = this.selectedDatabases.map(db => ({
                dbId: db.id,
                dbName: db.name
              }));
            }
          }

          this.$message.success(`已成功解绑数据库"${database.name}"`);
        }
      }).catch(() => {
        // 用户取消操作
      });
    },
    filteredKnowledgeList() {
      if (!this.knowledgeSearchKeyword) {
        return this.knowledgeList;
      }
      const keyword = this.knowledgeSearchKeyword.toLowerCase();
      return this.knowledgeList.filter(
        (item) =>
          item.name.toLowerCase().includes(keyword) ||
          (item.description && item.description.toLowerCase().includes(keyword))
      );
    },
    showKnowledgeConfigDialog() {
      // 从knowledgeNodeData同步到临时表单
      if (
        this.knowledgeNodeData.data &&
        this.knowledgeNodeData.data.nodeTypeConfig
      ) {
        this.tempKnowledgeForm = {
          searchMethod:
            this.knowledgeNodeData.data.nodeTypeConfig.searchType == "1"
              ? "1"
              : this.knowledgeNodeData.data.nodeTypeConfig.searchType == "2"
              ? "2"
              : "0",
          threshold:
            this.knowledgeNodeData.data.nodeTypeConfig.similarity || 0.3,
          searchLimit:
            this.knowledgeNodeData.data.nodeTypeConfig.semanticCount || 7,
          fullTextLimit:
            this.knowledgeNodeData.data.nodeTypeConfig.fullTextCount || 3,
          missStrategy:
            this.knowledgeNodeData.data.nodeTypeConfig.missStrategy || "fixedText",
          fixedText:
            this.knowledgeNodeData.data.nodeTypeConfig.fixedText || "",
          showReference:
            this.knowledgeNodeData.data.nodeTypeConfig.showReference || false,
          referenceThreshold:
            this.knowledgeNodeData.data.nodeTypeConfig.referenceThreshold || 0.3,
        };
      }
      this.knowledgeConfigDialogVisible = true;
    },
    handleCloseKnowledgeConfigDialog() {
      // 清除表单校验状态
      if (this.$refs.knowledgeConfigForm) {
        this.$refs.knowledgeConfigForm.clearValidate();
      }
      this.knowledgeConfigDialogVisible = false;
    },
    // 处理未命中策略变化
    handleMissStrategyChange() {
      // 当策略改变时，清除固定文案字段的校验状态
      if (this.$refs.knowledgeConfigForm) {
        this.$refs.knowledgeConfigForm.clearValidate(['fixedText']);
      }
    },
    confirmKnowledgeConfig() {
      // 表单校验
      this.$refs.knowledgeConfigForm.validate((valid) => {
        if (valid) {
          // 更新表单数据
          this.knowledgeForm = { ...this.tempKnowledgeForm };

          // 将表单数据同步到知识库节点数据
          this.syncKnowledgeFormToNodeData();

          this.$message.success("知识库配置已更新");
          this.knowledgeConfigDialogVisible = false;
        } else {
          // 校验失败，提示用户
          this.$message.error("请完善配置信息");
          return false;
        }
      });
    },
    viewKnowledge(knowledge) {
      // 跳转到知识库菜单
      this.$router.push(`/create/knowledge/${knowledge.id}/setting`);
    },
    // 添加插件相关的空方法，用于防止DOM渲染错误
    showAddPluginDialog() {
      // 重置临时选中的插件ID，只包含当前已添加的插件
      this.tempSelectedPluginIds = [...this.addedPluginIds];
      this.addPluginDialogVisible = true;
    },
    handleCloseAddPluginDialog() {
      this.addPluginDialogVisible = false;
    },
    // 空方法，仅供UI显示使用
    getPluginTypeName(type) {
      const typeMap = {
        http: "HTTP插件",
        rpa: "RPA插件",
        function: "函数插件",
        openapi: "OpenAPI",
      };
      return typeMap[type] || "未知类型";
    },
    // 检查上传的文件是否符合要求
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$showFriendlyError(null, "上传头像图片只能是图片格式!");
      }
      if (!isLt2M) {
        this.$showFriendlyError(null, "上传头像图片大小不能超过 2MB!");
      }
      return isImage && isLt2M;
    },

    // 处理头像上传
    async handleAvatarUpload(options) {
      const file = options.file;
      try {
        // 构建上传参数
        const uploadParams = {
          file: file,
          fileType: "image",
          folder: "app_avatars",
        };

        // 调用上传API
        const uploadRes = await api.oss.upload(uploadParams);

        if (uploadRes.data?.fileUrl) {
          // 设置头像URL
          this.basicForm.profilePhoto = uploadRes.data.fileUrl;
          this.$message.success("头像上传成功");

          // 更新应用信息
          // this.saveAppInfo();
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        console.error("上传头像失败:", error);
        this.$showFriendlyError(null, "头像上传失败，请稍后重试");
      }
    },

    // 保存应用设置
    async saveAppInfo() {
      try {
        // 仅更新基本信息
        const updateData = {
          id: this.basicForm.id,
          name: this.basicForm.name,
          description: this.basicForm.description,
          introduce: this.basicForm.introduce,
          profilePhoto: this.basicForm.profilePhoto,
          isPublic: this.basicForm.isPublic,
          isTrueCustomerServer: this.basicForm.isTrueCustomerServer,
        };

        const res = await api.sessionFlow.update(updateData);

        if (res.code === 200) {
          console.log("保存成功");
        }
      } catch (error) {
        console.error("保存失败:", error);
      }
    },
    // 同步知识库表单数据到节点数据结构
    syncKnowledgeFormToNodeData() {
      // 将knowledgeForm中的属性映射到knowledgeNodeData
      if (
        this.knowledgeNodeData.data &&
        this.knowledgeNodeData.data.nodeTypeConfig
      ) {
        this.knowledgeNodeData.data.nodeTypeConfig.searchType =
          this.knowledgeForm.searchMethod;
        this.knowledgeNodeData.data.nodeTypeConfig.similarity =
          this.knowledgeForm.threshold;
        this.knowledgeNodeData.data.nodeTypeConfig.semanticCount =
          this.knowledgeForm.searchLimit;
        this.knowledgeNodeData.data.nodeTypeConfig.fullTextCount =
          this.knowledgeForm.fullTextLimit;
        this.knowledgeNodeData.data.nodeTypeConfig.missStrategy =
          this.knowledgeForm.missStrategy;
        this.knowledgeNodeData.data.nodeTypeConfig.fixedText =
          this.knowledgeForm.fixedText;
        this.knowledgeNodeData.data.nodeTypeConfig.showReference =
          this.knowledgeForm.showReference;
        this.knowledgeNodeData.data.nodeTypeConfig.referenceThreshold =
          this.knowledgeForm.referenceThreshold;
      }
    },
    // 获取知识库列表
    getKnowledgeList() {
      this.knowledgeLoading = true;
      this.loadingStates.knowledge = true;
      // 查询参数
      const params = {
        keyword: "",
        pageIndex: 0,
        pageSize: 20,
      };

      return new Promise((resolve, reject) => {
        api.rag
          .getKnowledgeList(params)
          .then((res) => {
            if (res.code === 200) {
              // 处理返回数据
              if (res.data && res.data.items && res.data.items.length > 0) {
                // 将API返回的数据映射为组件需要的结构
                this.knowledgeList = res.data.items.map((item) => ({
                  id: item.id,
                  name: item.name,
                  description: item.description || "暂无描述",
                  code: item.code,
                  create_time: item.create_time,
                  update_time: item.update_time,
                }));
              } else {
                this.knowledgeList = []; // 没有数据时设为空数组
              }
              resolve(this.knowledgeList); // 成功时解析Promise
            } else {
              this.$showFriendlyError(null, res.message || "获取知识库列表失败");
              reject(new Error(res.message || "获取知识库列表失败")); // 失败时拒绝Promise
            }
          })
          .catch((err) => {
            this.$showFriendlyError(null,
              "获取知识库列表失败：" + (err.message || "未知错误")
            );
            reject(err); // 异常时拒绝Promise
          })
          .finally(() => {
            this.knowledgeLoading = false;
            this.loadingStates.knowledge = false;
          });
      });
    },
    // 搜索知识库
    searchKnowledge() {
      this.getKnowledgeList();
    },
    // 将配置数据传递给父组件
    updateParentConfig() {
      // 深拷贝 modelForm 以避免修改原始数据
      const modelFormCopy = JSON.parse(JSON.stringify(this.modelForm));

      // 如果选择了声音，将 voiceId 对应的 code 设置到 voice 字段
      if (this.modelForm.data.nodeTypeConfig.voiceId) {
        console.log('updateParentConfig - 当前选中的 voiceId:', this.modelForm.data.nodeTypeConfig.voiceId);
        console.log('updateParentConfig - voiceOptions 列表:', this.voiceOptions);
        console.log('updateParentConfig - voiceOptions 数量:', this.voiceOptions.length);

        const selectedVoice = this.voiceOptions.find(voice =>
          voice.id === this.modelForm.data.nodeTypeConfig.voiceId
        );
        console.log('updateParentConfig - 在 voiceOptions 中找到的声音:', selectedVoice);

        if (selectedVoice) {
          modelFormCopy.data.nodeTypeConfig.voice = selectedVoice.id; // 使用 voice.id (即 voice.code)
          console.log('updateParentConfig - 设置的 voice 值:', selectedVoice.id);
        } else {
          console.warn('updateParentConfig - 未找到对应的声音选项');
          console.warn('updateParentConfig - 查找的 voiceId:', this.modelForm.data.nodeTypeConfig.voiceId);
          console.warn('updateParentConfig - 可用的声音 ID 列表:', this.voiceOptions.map(v => v.id));
        }
      } else {
        console.log('updateParentConfig - 未选择声音，voiceId 为空');
      }

      // 构建基础配置数据对象，区分自定义插件和内置插件
      const configData = {
        basicForm: this.basicForm,
        modelForm: modelFormCopy, // 使用修改后的副本
        knowledgeNodeData: this.knowledgeNodeData,
        pluginNodes: this.activePlugins, // 自定义插件
        newPluginNodes: this.activeNewPlugins, // 内置插件
        dbNodes: this.activeDatabaseNodes, // 数据库节点
        selectedDatabaseModel: this.selectedDatabaseModel,
      };

      // 触发事件，将数据传递给父组件
      this.$emit("update-basic-config", configData);
    },
    // 获取所有插件数据
    async fetchPluginList() {
      // 并行加载两个接口的数据
      await Promise.all([this.fetchCustomPluginList(), this.fetchAllPluginList()]);
    },

    // 获取自定义插件列表
    async fetchCustomPluginList() {
      try {
        this.customPluginLoading = true;
        this.loadingStates.plugin = true;
        const params = {
          skipCount: 0,
          maxResultCount: 100,
          category: 2, // 自定义插件
        };
        const res = await api.apiPlugin.getList(params);
        if (res.isSuccess) {
          this.customPlugins = res.data.items.map((item) => ({
            id: item.id,
            name: item.name,
            type: item.httpTypeCode,
            description: item.description,
            headSculpture: item.headSculpture,
            url: item.url,
            headers: item.headers,
            parameters: item.parameters,
            responseFields: item.responseFields,
            isDeleted: item.isDeleted,
          }));

          // 使用API获取的插件信息更新已添加的插件，补充headSculpture和description
          if (this.activePlugins.length > 0) {
            this.activePlugins.forEach((plugin) => {
              const pluginId = plugin.data?.nodeTypeConfig?.pluginId;
              if (pluginId) {
                const matchingPlugin = this.customPlugins.find(
                  (item) => item.id === pluginId
                );
                if (matchingPlugin) {
                  plugin.headSculpture = matchingPlugin.headSculpture;
                  plugin.description = matchingPlugin.description;
                }
              }
            });
          }

          // 初始化过滤后的自定义插件列表
          this.filterCustomPlugins();
        }
      } catch (error) {
        console.error("获取自定义插件列表失败:", error);
        this.$showFriendlyError(error, "获取自定义插件列表失败，请重试");
      } finally {
        this.customPluginLoading = false;
        this.loadingStates.plugin = false;
      }
    },

    // 获取插件列表（新接口）
    async fetchAllPluginList() {
      try {
        this.builtinPluginLoading = true;
        const params = {
          scope: 'app',
          skipCount: 0,
          maxResultCount: 1000, // 获取更多数据用于前端过滤
        };
        const res = await api.plugin.getList(params);
        if (res.isSuccess) {
          this.allPluginsData = res.data.items || [];

          // 处理数据分类
          this.processPluginData();
        }
      } catch (error) {
        console.error("获取插件列表失败:", error);
        this.$showFriendlyError(error, "获取插件列表失败，请重试");
      } finally {
        this.builtinPluginLoading = false;
      }
    },

    // 处理插件数据分类
    processPluginData() {
      const customPlugins = [];
      const builtinPlugins = [];

      this.allPluginsData.forEach((item) => {
        const plugin = {
          id: item.id,
          name: item.name,
          type: item.httpTypeCode || item.type || "HTTP",
          description: item.description,
          shortDesc: item.shortDesc, // 保留短描述
          headSculpture: item.headSculpture,
          iconUrl: item.iconUrl, // 保留图标URL
          nickName: item.nickName, // 保留昵称
          author: item.author, // 保留作者
          price: item.price, // 保留价格
          url: item.url,
          headers: item.headers,
          parameters: item.parameters,
          responseFields: item.responseFields,
          isDeleted: item.isDeleted,
          tags: item.tags || [], // 改为tags数组
          category: item.category,
        };

        // tags包含1且category为2的数据放到自定义插件数组中
        if ((item.tags || []).includes(1) && item.category === 2) {
          customPlugins.push(plugin);
        } else {
          // 其他的放在内置插件数组中
          builtinPlugins.push(plugin);
        }
      });

      // 将新接口的自定义插件合并到现有的自定义插件列表中
      this.customPlugins = [...this.customPlugins, ...customPlugins];

      // 设置内置插件列表，并应用当前分类过滤
      this.allBuiltinPlugins = builtinPlugins;
      this.filterBuiltinPlugins();

      // 重新初始化过滤后的自定义插件列表
      this.filterCustomPlugins();
    },

    // 选择插件
    selectPlugin(plugin) {
      // 切换选中状态
      const index = this.tempSelectedPluginIds.indexOf(plugin.id);
      if (index > -1) {
        this.tempSelectedPluginIds.splice(index, 1);
      } else {
        this.tempSelectedPluginIds.push(plugin.id);
      }
    },

    // 确认添加插件
    confirmAddPlugins() {
      // 合并自定义插件和内置插件列表
      const allPlugins = [...this.customPlugins, ...this.allBuiltinPlugins];

      // 获取当前所有已激活插件的ID（包括自定义和内置）
      const currentActivePluginIds = [
        ...this.activePlugins.map(plugin => plugin.data.nodeTypeConfig.pluginId),
        ...this.activeNewPlugins.map(plugin => plugin.data.nodeTypeConfig.pluginId)
      ];

      // 找出需要添加的插件ID (在tempSelectedPluginIds中但不在当前激活列表中)
      const toAddIds = this.tempSelectedPluginIds.filter(id => !currentActivePluginIds.includes(id));

      // 找出需要移除的插件ID (在当前激活列表中但不在tempSelectedPluginIds中)
      const toRemoveIds = currentActivePluginIds.filter(id => !this.tempSelectedPluginIds.includes(id));

      // 移除不再选中的插件（从两个列表中移除）
      if (toRemoveIds.length > 0) {
        this.activePlugins = this.activePlugins.filter(plugin =>
          !toRemoveIds.includes(plugin.data.nodeTypeConfig.pluginId)
        );
        this.activeNewPlugins = this.activeNewPlugins.filter(plugin =>
          !toRemoveIds.includes(plugin.data.nodeTypeConfig.pluginId)
        );
      }

      // 添加新选中的插件
      if (toAddIds.length > 0) {
        const newCustomPlugins = [];
        const newBuiltinPlugins = [];

        allPlugins
          .filter((plugin) => toAddIds.includes(plugin.id))
          .forEach((plugin) => {
            // 判断插件类型：如果在customPlugins中找到，则为自定义插件；否则为内置插件
            const isCustomPlugin = this.customPlugins.some(cp => cp.id === plugin.id);

            const pluginNode = {
              id: `node-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
              type: isCustomPlugin ? "plugin" : "newPlugin",
              position: { x: 174, y: 310 },
              data: {
                label: plugin.name,
                type: isCustomPlugin ? "plugin" : "newPlugin",
                name: plugin.name,
                description: plugin.description || plugin.shortDesc || "插件的节点描述",
                activeTab: "config",
                executionStatus: {},
                nodeTypeConfig: isCustomPlugin ? {
                  name: "插件配置",
                  memory: false,
                  memoryTurns: 1,
                  inputs: [],
                  content: "",
                  structuredOutput: true,
                  outputs: [],
                  pluginId: plugin.id,
                  pluginName: plugin.name,
                  structuredParams: false,
                  pluginInputs: []
                } : {
                  name: plugin.name,
                  memory: false,
                  memoryTurns: 1,
                  inputConfig: {},
                  inputConfigJson: {},
                  defaultInput: "",
                  defaultInputJson: [],
                  structuredOutput: true,
                  outputConfig: {},
                  pluginConfig: {},
                  pluginId: plugin.id,
                  pluginName: plugin.name,
                  structuredParams: false
                }
              },
              headSculpture: plugin.headSculpture || plugin.iconUrl,
              enabled: true,
              pluginType: isCustomPlugin ? 'custom' : 'builtin'
            };

            if (isCustomPlugin) {
              newCustomPlugins.push(pluginNode);
            } else {
              newBuiltinPlugins.push(pluginNode);
            }
          });

        // 分别添加到对应的插件列表
        this.activePlugins = [...this.activePlugins, ...newCustomPlugins];
        this.activeNewPlugins = [...this.activeNewPlugins, ...newBuiltinPlugins];
      }

      // 更新已添加的插件ID列表
      this.addedPluginIds = [...this.tempSelectedPluginIds];

      // 提示消息
      const addCount = toAddIds.length;
      const removeCount = toRemoveIds.length;

      if (addCount > 0 && removeCount > 0) {
        this.$message.success(`已添加${addCount}个插件，移除${removeCount}个插件`);
      } else if (addCount > 0) {
        this.$message.success(`已添加${addCount}个插件`);
      } else if (removeCount > 0) {
        this.$message.success(`已移除${removeCount}个插件`);
      } else {
        this.$message.info('插件配置无变化');
      }

      console.log("更新后的自定义插件列表:", this.activePlugins);
      console.log("更新后的内置插件列表:", this.activeNewPlugins);

      this.addPluginDialogVisible = false;
    },

    // 移除插件
    removePlugin(plugin) {
      this.$confirm("确定要移除该插件吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 判断插件类型并从对应列表移除
          if (plugin.pluginType === 'custom' || plugin.type === 'plugin') {
            this.activePlugins = this.activePlugins.filter(
              (item) => item.id !== plugin.id
            );
          } else {
            this.activeNewPlugins = this.activeNewPlugins.filter(
              (item) => item.id !== plugin.id
            );
          }

          // 从addedPluginIds移除
          const index = this.addedPluginIds.indexOf(plugin.data.nodeTypeConfig.pluginId);
          if (index > -1) {
            this.addedPluginIds.splice(index, 1);
          }

          // 从tempSelectedPluginIds移除
          const tempIndex = this.tempSelectedPluginIds.indexOf(plugin.data.nodeTypeConfig.pluginId);
          if (tempIndex > -1) {
            this.tempSelectedPluginIds.splice(tempIndex, 1);
          }

          this.$message.success("插件已移除");
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 切换插件启用状态
    togglePlugin(plugin) {
      // 在实际应用中，这里可以添加对插件启用状态的保存逻辑
      console.log("切换插件状态:", plugin.name, plugin.enabled);

      // 触发父组件更新，确保数据同步
      this.updateParentConfig();
    },

    // 显示插件配置弹框
    showPluginConfigDialog(plugin) {
      this.currentConfigPlugin = plugin;
      this.pluginConfigDialogVisible = true;
    },

    // 关闭插件配置弹框
    handleClosePluginConfigDialog() {
      this.pluginConfigDialogVisible = false;
      this.currentConfigPlugin = null;
    },

    // 确认插件配置
    confirmPluginConfig() {
      // 这里后续会添加具体的配置逻辑
      this.pluginConfigDialogVisible = false;
      this.currentConfigPlugin = null;
      this.$message.success("插件配置已保存");
    },


    // 根据分类获取对应的tag值
    getTagByCategory(category) {
      const tagMap = {
        basic: 2, // 基础工具
        search: 3, // 搜索工具
        content: 4, // 内容工具
        image: 5, // 图像工具
        media: 6, // 音视频工具
        other: 99, // 其他
      };
      return tagMap[category] || null;
    },

    // 处理自定义插件搜索
    handleCustomPluginSearch() {
      this.filterCustomPlugins();
    },

    // 过滤自定义插件
    filterCustomPlugins() {
      if (!this.customPluginSearchKeyword.trim()) {
        this.filteredCustomPluginList = [...this.customPlugins];
      } else {
        const keyword = this.customPluginSearchKeyword.toLowerCase().trim();
        this.filteredCustomPluginList = this.customPlugins.filter((plugin) =>
          plugin.name.toLowerCase().includes(keyword) ||
          (plugin.description && plugin.description.toLowerCase().includes(keyword))
        );
      }
    },

    // 处理内置插件搜索
    handleBuiltinPluginSearch() {
      this.filterBuiltinPlugins();
    },

    // 过滤内置插件
    filterBuiltinPlugins() {
      // 首先根据分类过滤
      let plugins = this.allBuiltinPlugins || [];

      if (this.activeBuiltinCategory !== "all") {
        const targetTag = this.getTagByCategory(this.activeBuiltinCategory);
        plugins = plugins.filter((plugin) =>
          plugin.tags && plugin.tags.includes(targetTag)
        );
      }

      // 然后根据搜索关键词过滤
      if (this.builtinPluginSearchKeyword.trim()) {
        const keyword = this.builtinPluginSearchKeyword.toLowerCase().trim();
        plugins = plugins.filter((plugin) =>
          plugin.name.toLowerCase().includes(keyword) ||
          (plugin.description && plugin.description.toLowerCase().includes(keyword)) ||
          (plugin.shortDesc && plugin.shortDesc.toLowerCase().includes(keyword))
        );
      }

      this.filteredBuiltinPluginList = plugins;
    },

    // 处理插件分类切换
    handlePluginTabChange() {
      // 切换分类时可以做一些初始化操作
      if (this.activePluginTab === "custom") {
        this.filterCustomPlugins();
      } else {
        this.filterBuiltinPlugins();
      }
    },

    // 处理内置插件分类变化
    handleBuiltinCategoryChange() {
      this.filterBuiltinPlugins();
    },

    // 获取插件分类对应的中文标签
    getPluginCategoryLabel(tag) {
      const categoryLabels = {
        1: "MCP插件",
        2: "基础工具",
        3: "搜索工具",
        4: "内容工具",
        5: "图像工具",
        6: "音视频工具",
        99: "其他",
      };
      return categoryLabels[tag] || categoryLabels[parseInt(tag)] || "其他插件";
    },

    // 获取分类名称
    getBuiltinCategoryLabel() {
      const categoryLabels = {
        all: "全部",
        basic: "基础工具",
        search: "搜索工具",
        content: "内容工具",
        image: "图像工具",
        media: "音视频工具",
        other: "其他",
      };
      return categoryLabels[this.activeBuiltinCategory] || "全部";
    },
    handleCopyAppCode() {
              // 复制智能体code到剪贴板
      const appCode = this.basicForm.id;
      if (!appCode) {
                  this.$showFriendlyError(null, '智能体code为空，无法复制');
        return;
      }

      copyToClipboard(appCode).then((success) => {
        if (success) {
          this.$message.success('智能体code已复制到剪贴板');
        } else {
          this.$showFriendlyError(null, '复制失败，请稍后重试');
        }
      }).catch(() => {
        this.$showFriendlyError(null, '复制失败，请稍后重试');
      });
    },
        handleAIOptimizeDescription() {
      // 配置AI优化弹窗
      this.aiOptimizeConfig = {
        title: "智能体描述",
        contentLabel: "智能体描述",
        currentContent: this.basicForm.description,
        optimizeType: "description",
        additionalParams: {
          name: this.basicForm.name,
          description: this.basicForm.description,
          applicationType: this.getApplicationTypeName(this.basicForm.applicationType)
        }
      };

      // 显示AI优化弹窗
      this.aiOptimizeDialogVisible = true;
    },
        handleAIOptimizeIntroduction() {
      // 配置AI优化弹窗
      this.aiOptimizeConfig = {
        title: "开场介绍",
        contentLabel: "开场介绍",
        currentContent: this.basicForm.introduce,
        optimizeType: "introduction",
        additionalParams: {
          name: this.basicForm.name,
          description: this.basicForm.description,
          applicationType: this.getApplicationTypeName(this.basicForm.applicationType)
        }
      };

      // 显示AI优化弹窗
      this.aiOptimizeDialogVisible = true;
    },
    handleVoiceChange() {
      // 声音变化时的处理逻辑
      console.log('选中的声音ID:', this.modelForm.data.nodeTypeConfig.voiceId);

      // 立即同步更新 voice 字段
      if (this.modelForm.data.nodeTypeConfig.voiceId) {
        const selectedVoice = this.voiceOptions.find(voice =>
          voice.id === this.modelForm.data.nodeTypeConfig.voiceId
        );
        if (selectedVoice) {
          this.modelForm.data.nodeTypeConfig.voice = selectedVoice.id;
          console.log('handleVoiceChange - 同步更新 voice 字段为:', selectedVoice.id);
        } else {
          console.warn('handleVoiceChange - 未找到匹配的声音选项');
          console.warn('handleVoiceChange - 查找的 voiceId:', this.modelForm.data.nodeTypeConfig.voiceId);
          console.warn('handleVoiceChange - 可用的声音选项:', this.voiceOptions);
        }
      } else {
        this.modelForm.data.nodeTypeConfig.voice = "";
        console.log('handleVoiceChange - 清空 voice 字段');
      }
    },

    async handlePlayVoice() {
      if (!this.modelForm.data.nodeTypeConfig.voiceId) {
        this.$message.warning('请先选择声音');
        return;
      }

      try {
        this.voicePlayLoading = true;

        // 从声音选项列表中找到选中的声音
        const selectedVoice = this.voiceOptions.find(voice =>
          voice.id === this.modelForm.data.nodeTypeConfig.voiceId
        );

        if (!selectedVoice || !selectedVoice.url) {
          this.$showFriendlyError(null, '未找到选中声音的播放地址');
          return;
        }

        // 使用选中声音的真实URL播放音频
        this.playAudio(selectedVoice.url);

      } catch (error) {
        console.error('播放声音失败:', error);
        this.$showFriendlyError(null, '播放失败，请稍后重试');
      } finally {
        this.voicePlayLoading = false;
      }
    },

    // 播放音频文件
    playAudio(audioUrl) {
      try {
        const audio = new Audio(audioUrl);
        audio.play().then(() => {
          this.$message.success('正在播放声音试听');
        }).catch((error) => {
          console.error('播放音频失败:', error);
          this.$showFriendlyError(null, '播放失败，请检查音频文件');
        });
      } catch (error) {
        console.error('创建音频对象失败:', error);
        this.$showFriendlyError(null, '播放失败，请稍后重试');
      }
    },

    // 获取声音列表
    async getVoiceList() {
      try {
        this.loadingStates.voice = true;
        const res = await api.voice.getVoices();

        if (res.isSuccess && res.data && res.data.voices) {
          // 将接口返回的数据格式转换为组件需要的格式，保存完整信息
          this.voiceOptions = res.data.voices.map(voice => ({
            id: voice.code,
            name: voice.name,
            url: voice.url
          }));

          // 如果当前没有选择声音且声音列表不为空，默认选中第一个声音
          if (this.voiceOptions.length > 0 && !this.modelForm.data.nodeTypeConfig.voiceId) {
            const firstVoice = this.voiceOptions[0];
            this.modelForm.data.nodeTypeConfig.voiceId = firstVoice.id;
            this.modelForm.data.nodeTypeConfig.voice = firstVoice.id;
            console.log('默认选中第一个声音:', firstVoice.name, firstVoice.id);
          }
        } else {
          console.warn('获取声音列表返回数据格式异常:', res);
          this.$message.warning('获取声音列表失败：' + (res.message || '数据格式异常'));
        }

      } catch (error) {
        console.error('获取声音列表失败:', error);
        this.$showFriendlyError(null, '获取声音列表失败');
      } finally {
        this.loadingStates.voice = false;
      }
    },
    // 处理引用来源开关变化
    handleReferenceChange(value) {
      console.log('引用来源开关状态:', value);
      // 如果关闭引用来源，可以在这里添加额外的处理逻辑
    },
        handleAIOptimizeSettings() {
      // 配置AI优化弹窗
      this.aiOptimizeConfig = {
        title: "智能体设定",
        contentLabel: "智能体设定",
        currentContent: this.basicForm.sessionFlowSetting,
        optimizeType: "settings",
        additionalParams: {
          name: this.basicForm.name,
          description: this.basicForm.description,
          applicationType: this.getApplicationTypeName(this.basicForm.applicationType)
        }
      };

      // 显示AI优化弹窗
      this.aiOptimizeDialogVisible = true;
    },
    // 获取数据库列表
    async initDatabaseList() {
      try {
        this.loadingStates.database = true;
        const response = await api.schemaStore.getDatabases({
          skipCount: 0,
          maxResultCount: 100,
        });

        if (response && response.data && response.data.items) {
          // 映射接口返回的数据到组件需要的格式
          this.databaseList = response.data.items.map((item) => ({
            id: item.id,
            name: item.databaseName,
            description: item.description || "暂无描述",
            dbType: item.databaseType?.value === 1 ? "internal" : "remote",
          }));
        } else {
          this.databaseList = [];
        }
      } catch (error) {
        console.error("获取数据库列表失败:", error);
        this.$showFriendlyError(null, "获取数据库列表失败");
        this.databaseList = [];
      } finally {
        this.loadingStates.database = false;
      }
    },

    // 获取数据库模型列表
    async fetchDatabaseModels() {
      try {
        this.loadingStates.database = true;
        const response = await api.llmService.searchModels({
          capabilityCodes: ["chat"],
          page: 0,
          pageSize: 100
        });

        if (response && response.data && response.data.items) {
          this.databaseModels = response.data.items;

          // 如果当前没有选中的数据库模型，自动选择第一个作为默认值
          if (!this.selectedDatabaseModel && this.databaseModels.length > 0) {
            this.selectedDatabaseModel = this.databaseModels[0].code;
          }
        } else {
          this.databaseModels = [];
        }
      } catch (error) {
        console.error("获取数据库模型列表失败:", error);
        this.$showFriendlyError(null, "获取数据库模型列表失败");
        this.databaseModels = [];
      } finally {
        this.loadingStates.database = false;
      }
    },



    // 显示绑定数据库弹窗
    showBindDatabaseDialog() {
      // 重置临时选中的数据库ID，包含当前已选中的数据库
      this.tempSelectedDatabaseIds = this.selectedDatabases.map(db => db.id);
      // 重置搜索关键词
      this.databaseSearchKeyword = "";
      this.bindDatabaseDialogVisible = true;

      // 如果数据库列表为空，则重新加载
      if (this.databaseList.length === 0) {
        this.initDatabaseList();
      }
    },
    // 关闭绑定数据库弹窗
    handleCloseBindDatabaseDialog() {
      this.bindDatabaseDialogVisible = false;
    },
    // 切换数据库选择状态
    toggleDatabaseSelection(id) {
      const index = this.tempSelectedDatabaseIds.indexOf(id);
      if (index > -1) {
        this.tempSelectedDatabaseIds.splice(index, 1);
      } else {
        this.tempSelectedDatabaseIds.push(id);
      }
    },
    // 确认绑定数据库
    confirmBindDatabase() {
      // 根据选中的ID获取对应的数据库对象（从原始列表中获取，不是过滤后的列表）
      this.selectedDatabases = this.databaseList.filter(db =>
        this.tempSelectedDatabaseIds.includes(db.id)
      );

      if (this.selectedDatabases.length > 0) {
        // 更新数据库节点配置
        this.updateDatabaseNodesConfig();
        this.$message.success(`已成功绑定${this.selectedDatabases.length}个数据库`);
      } else {
        this.$message.warning("未选择任何数据库");
      }

      this.bindDatabaseDialogVisible = false;
    },

    // 查看数据库（在新标签页打开）
    viewDatabase(database) {
      // 构建数据库设置页面的路由
      const routeUrl = this.$router.resolve({
        name: 'DatabaseSetting',
        params: { id: database.id }
      });

      // 在新标签页打开
      window.open(routeUrl.href, '_blank');
    },

    // AI优化弹窗回调方法
    handleAIOptimizeConfirm(optimizedContent) {
      // 根据优化类型更新对应的表单字段
      switch (this.aiOptimizeConfig.optimizeType) {
        case "description":
          this.basicForm.description = optimizedContent;
          this.$message.success('智能体描述优化成功');
          break;
        case "introduction":
          this.basicForm.introduce = optimizedContent;
          this.$message.success('开场介绍优化成功');
          break;
        case "settings":
          this.basicForm.sessionFlowSetting = optimizedContent;
          this.$message.success('智能体设定优化成功');
          break;
      }
    },

    handleAIOptimizeClose() {
      // 弹窗关闭时的处理，可以在这里添加额外逻辑
      console.log('AI优化弹窗已关闭');
    },

    // 根据智能体分类值获取对应的名称
    getApplicationTypeName(value) {
      const option = this.applicationTypeOptions.find(item => item.value === value);
      return option ? option.label : '';
    },

    // 更新数据库节点中的模型配置
    updateDatabaseModelInNodes(modelCode) {
      if (this.activeDatabaseNodes.length > 0) {
        // 更新第一个数据库节点的modelCode
        this.activeDatabaseNodes.forEach(dbNode => {
          if (dbNode.data && dbNode.data.nodeTypeConfig) {
            dbNode.data.nodeTypeConfig.modelCode = modelCode;
          }
        });
        console.log('数据库节点模型已更新:', modelCode);
      }
    },

    // 更新数据库节点配置
    updateDatabaseNodesConfig() {
      // 如果没有数据库节点，创建一个
      if (this.activeDatabaseNodes.length === 0) {
        const dbNode = {
          id: `node-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
          type: "database",
          position: { x: 219, y: 264 },
          data: {
            label: "",
            type: "database",
            name: "",
            description: "",
            activeTab: "config",
            executionStatus: {},
            nodeTypeConfig: {
              name: "",
              inputs: [],
              outputs: [],
              modelCode: this.selectedDatabaseModel || "",
              structuredOutput: false,
              dbConfigs: []
            }
          },
          enabled: true
        };
        this.activeDatabaseNodes.push(dbNode);
      }

      // 更新数据库配置
      if (this.activeDatabaseNodes.length > 0) {
        const dbNode = this.activeDatabaseNodes[0];

        // 确保nodeTypeConfig结构完整
        if (!dbNode.data.nodeTypeConfig) {
          dbNode.data.nodeTypeConfig = {
            name: "",
            inputs: [],
            outputs: [],
            modelCode: this.selectedDatabaseModel || "",
            structuredOutput: false,
            dbConfigs: []
          };
        }

        // 更新dbConfigs
        dbNode.data.nodeTypeConfig.dbConfigs = this.selectedDatabases.map(db => ({
          dbId: db.id,
          dbName: db.name
        }));

        // 更新modelCode
        if (this.selectedDatabaseModel) {
          dbNode.data.nodeTypeConfig.modelCode = this.selectedDatabaseModel;
        }

        console.log('数据库节点配置已更新:', dbNode.data.nodeTypeConfig);
      }
    },

    // 新建数据库
    createNewDatabase() {
      // 跳转到创建数据库页面
      this.$router.push('/create/database');
    },

    // 处理数据库搜索
    handleDatabaseSearch() {
      // 搜索是通过computed属性filteredDatabaseList实现的，这里可以添加额外的逻辑
      console.log('数据库搜索关键词:', this.databaseSearchKeyword);
    },

    // 获取插件类型标签
    getPluginTypeLabel(plugin) {
      try {
        const typeInfo = this.getPluginTypeInfo(plugin);
        return typeInfo ? typeInfo.label : null;
      } catch (error) {
        console.warn('获取插件类型标签时出错:', error, plugin);
        return null;
      }
    },

    // 获取插件类型信息
    getPluginTypeInfo(plugin) {
      try {
        // 查找对应的插件数据获取详细信息
        const pluginId = plugin?.data?.nodeTypeConfig?.pluginId;
        if (!pluginId) return null;

        // 先在自定义插件中查找
        const customPlugin = this.customPlugins.find(item => item.id === pluginId);
        if (customPlugin) {
          return this.getTypeInfoFromPlugin(customPlugin);
        }

        // 再在内置插件中查找
        const builtinPlugin = this.allBuiltinPlugins.find(item => item.id === pluginId);
        if (builtinPlugin) {
          return this.getTypeInfoFromPlugin(builtinPlugin);
        }

        return null;
      } catch (error) {
        console.warn('获取插件类型信息时出错:', error, plugin);
        return null;
      }
    },

            // 根据插件数据获取类型信息
    getTypeInfoFromPlugin(pluginData) {
      // 判断是否是MCP插件
      if (this.isMCPPlugin(pluginData)) {
        return { label: 'MCP', type: 'primary' };
      }

      // 判断是否是HTTP类型插件
      if (this.isHTTPPlugin(pluginData)) {
        return { label: 'HTTP', type: 'info' };
      }

      // 其他类型不显示标签
      return null;
    },

        // 判断是否是MCP插件
    isMCPPlugin(pluginData) {
      if (!pluginData) return false;

      try {
        // 首先检查插件的tags数组，如果包含1则表示MCP插件
        if (pluginData.tags && Array.isArray(pluginData.tags) && pluginData.tags.includes(1)) {
          return true;
        }

        // 根据插件名称、描述或其他字段判断是否是MCP插件
        const name = String(pluginData.name || '').toLowerCase();
        const description = String(pluginData.description || pluginData.shortDesc || '').toLowerCase();

        // 检查名称或描述中是否包含MCP关键词
        const mcpKeywords = ['mcp', 'model context protocol'];

        return mcpKeywords.some(keyword =>
          name.includes(keyword) || description.includes(keyword)
        ) ||
        // 也可以根据特定的插件ID或其他标识来判断
        this.isMCPPluginById(pluginData.id);
      } catch (error) {
        console.warn('判断MCP插件时出错:', error, pluginData);
        return false;
      }
    },

    // 根据插件ID判断是否是MCP插件（可以配置特定的MCP插件ID列表）
    isMCPPluginById(pluginId) {
      // 这里可以配置已知的MCP插件ID列表
      const mcpPluginIds = [
        // 可以在这里添加已知的MCP插件ID
      ];

      return mcpPluginIds.includes(pluginId);
    },

    // 判断是否是HTTP插件
    isHTTPPlugin(pluginData) {
      if (!pluginData) return false;

      try {
        // 获取插件类型，确保是字符串
        const type = String(pluginData.type || pluginData.httpTypeCode || '').toUpperCase();

        // HTTP相关的类型和方法
        const httpTypes = [
          'HTTP', 'API', 'WEBHOOK', 'REST', 'RESTFUL',
          'GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'
        ];

        // 检查类型是否匹配HTTP相关
        if (httpTypes.includes(type)) {
          return true;
        }

        // 检查URL字段是否存在（通常HTTP插件会有URL配置）
        if (pluginData.url) {
          return true;
        }

        // 检查是否有HTTP相关的配置字段
        if (pluginData.headers || pluginData.parameters) {
          return true;
        }

        return false;
      } catch (error) {
        console.warn('判断HTTP插件时出错:', error, pluginData);
        return false;
      }
    },
  },
  computed: {
    selectedKnowledges() {
      return this.knowledgeList.filter((item) =>
        this.selectedKnowledgeIds.includes(item.id)
      );
    },
    // 内置模型选项（排除自定义模型）
    builtinModelOptions() {
      return this.modelOptions.filter(item => item.Code !== "customer");
    },
    // 合并所有激活的插件（自定义 + 内置）
    allActivePlugins() {
      return [...this.activePlugins, ...this.activeNewPlugins];
    },
    // 根据providerName分组模型
    groupedDatabaseModels() {
      const grouped = {};
      this.databaseModels.forEach(model => {
        const provider = model.providerName || '未知提供商';
        if (!grouped[provider]) {
          grouped[provider] = [];
        }
        grouped[provider].push(model);
      });
      return grouped;
    },
    // 过滤后的数据库列表
    filteredDatabaseList() {
      if (!this.databaseSearchKeyword) {
        return this.databaseList;
      }
      const keyword = this.databaseSearchKeyword.toLowerCase();
      return this.databaseList.filter(database =>
        database.name.toLowerCase().includes(keyword) ||
        (database.description && database.description.toLowerCase().includes(keyword))
      );
    },
  },
  // 当组件中的数据变化时，通知父组件
  watch: {
    basicForm: {
      handler: "updateParentConfig",
      deep: true,
    },
    modelForm: {
      handler: "updateParentConfig",
      deep: true,
    },
    knowledgeNodeData: {
      handler: "updateParentConfig",
      deep: true,
    },
    activePlugins: {
      handler: "updateParentConfig",
      deep: true,
    },
    activeNewPlugins: {
      handler: "updateParentConfig",
      deep: true,
    },
    // 监听插件变化，确保调试面板的插件配置实时更新
    allActivePlugins: {
      handler() {
        // 强制更新插件配置
        this.$forceUpdate();
      },
      deep: true
    },
    selectedDatabaseModel: {
      handler: function(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.updateDatabaseModelInNodes(newValue);
          this.updateParentConfig();
        }
      },
      deep: true,
    },
    // 监听sessionFlowCode变化，当显示数据库配置时获取模型列表
    'basicForm.sessionFlowCode': {
      handler: function(newValue, oldValue) {
        // 当sessionFlowCode为2时（显示数据库配置），获取数据库模型
        if (+newValue === 2) {
          // 如果是初始加载（oldValue为undefined）或者从其他值变为2，都需要获取模型
          if (oldValue === undefined || +oldValue !== 2) {
            this.fetchDatabaseModels();
          }
        }
      },
      immediate: true, // 立即执行一次，处理初始值为2的情况
    },
  },
};
</script>

<style lang="scss" scoped>
.settings-content {
  padding: 16px 24px;
  background: #f2f6fc;
  min-height: calc(100vh - 60px);
  overflow-y: auto;
  overflow-x: hidden;

  .main-config-container {
    .config-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .config-card {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        flex: 1;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

        .card-header {
          padding: 20px 24px 0;
          display: flex;
          justify-content: space-between;
          align-items: center;

          h3 {
            color: rgba(0, 0, 0, 1);
            font-size: 16px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            margin: 0;
          }

          .header-subtitle {
            background-color: rgba(0, 0, 0, 0.5);
            height: 23px;
            padding: 4px 8px;
            border-radius: 4px;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-weight: normal;
          }

          .header-actions {
            display: flex;
            gap: 8px;
          }
        }

        .card-content {
          padding: 24px;

          .avatar-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            .avatar-label {
              width: 70px;
              height: 20px;
              color: rgba(136, 136, 136, 1);
              font-size: 14px;
              font-weight: normal;
              margin-right: 28px;
            }

            .avatar-wrapper {
              width: 80px;
              height: 80px;
              margin-right: 13px;

              .avatar-uploader {
                width: 100%;
                height: 100%;
                border: 1px dashed #d9d9d9;
                border-radius: 4px;
                cursor: pointer;
                position: relative;
                overflow: hidden;

                &:hover {
                  border-color: #409eff;
                }

                .el-image {
                  width: 100%;
                  height: 100%;
                  border-radius: 4px;
                }

                .avatar-placeholder {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  height: 100%;
                  color: #8c939d;
                  font-size: 12px;

                  i {
                    font-size: 20px;
                    margin-bottom: 4px;
                  }
                }
              }
            }

            .avatar-actions {
              display: flex;
              align-items: center;
              color: rgba(37, 109, 255, 1);
              font-size: 12px;
              cursor: pointer;

              i {
                margin-right: 8px;
                font-size: 14px;
              }
            }
          }

          .form-section {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                color: rgba(136, 136, 136, 1);
                font-size: 14px;
                font-weight: normal;
              }
            }
          }

          .switch-group {
            display: flex;
            align-items: center;
            gap: 12px;

            .switch-label {
              font-size: 14px;
              color: rgba(0, 0, 0, 1);

              &.active {
                color: rgba(37, 109, 255, 1);
              }
            }

            .switch-tip {
              color: rgba(186, 186, 186, 1);
              font-size: 12px;
            }
          }

          .textarea-with-external-btn {
            position: relative;

            .ai-optimize-external-btn {
              position: absolute;
              right: -12%;
              top: 45%;
              color: rgba(37, 109, 255, 1);
              font-size: 12px;
              border-radius: 4px;
              padding: 4px 8px;
              border: none;

              i {
                margin-right: 4px;
              }
            }
          }

          .model-row {
            .model-container {
              .model-select-box {
                background-color: rgba(244, 246, 248, 1);
                border-radius: 8px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .selected-value {
                  .model-option-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  }

                  .placeholder {
                    color: rgba(186, 186, 186, 1);
                  }
                }
              }

              .selected-model-display {
                margin-top: 8px;

                .selected-model-icon {
                  width: 18px;
                  height: 20px;
                }
              }
            }
          }

          .voice-select-container {
            display: flex;
            align-items: center;
            gap: 10px;
          }

          .form-tip {
            color: rgba(0, 0, 0, 1);
            font-size: 14px;
            margin: 0 12px;
          }

          .knowledge-config-info {
            .info-grid {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 16px;
              margin-bottom: 24px;

              .info-item {
                display: flex;
                justify-content: space-between;

                .info-label {
                  color: rgba(136, 136, 136, 1);
                  font-size: 14px;
                }

                .info-value {
                  color: rgba(0, 0, 0, 1);
                  font-size: 14px;
                }
              }
            }
          }

          .knowledge-bound-list {
            .bound-knowledge-list {
              .bound-knowledge-item {
                background: rgba(244, 246, 248, 1);
                border-radius: 8px;
                padding: 12px 16px;
                margin-bottom: 16px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .knowledge-name {
                  color: rgba(17, 26, 52, 1);
                  font-size: 12px;
                  font-weight: 500;
                }

                .knowledge-actions {
                  display: flex;
                  gap: 16px;
                  color: rgba(37, 109, 255, 1);
                  font-size: 12px;

                  span {
                    cursor: pointer;
                  }
                }
              }
            }
          }

          .plugin-content {
            .plugin-card-compact {
              background: rgba(244, 246, 248, 1);
              border-radius: 8px;
              padding: 12px 16px;
              margin-bottom: 16px;

              .plugin-card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .plugin-card-left {
                  display: flex;
                  align-items: center;
                  gap: 12px;

                  .plugin-icon {
                    width: 36px;
                    height: 36px;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #fff;

                    img {
                      width: 20px;
                      height: 20px;
                    }

                    i {
                      font-size: 16px;
                      color: #909399;
                    }
                  }

                  .plugin-name-container {
                    .plugin-name {
                      color: rgba(17, 26, 52, 1);
                      font-size: 12px;
                      font-weight: 500;
                      margin-bottom: 4px;
                    }

                    .plugin-type-tag {
                      font-size: 10px;
                    }
                  }
                }

                .plugin-card-right {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                }
              }

              .plugin-card-desc {
                color: rgba(186, 186, 186, 1);
                font-size: 12px;
                line-height: 1.4;
              }
            }
          }

          .empty-plugins {
            text-align: center;
            padding: 40px 0;
            color: #909399;

            p {
              font-size: 14px;
            }
          }

          .database-content {
            .empty-state {
              text-align: center;
              padding: 40px 0;
              color: #909399;

              i {
                font-size: 24px;
                margin-bottom: 8px;
              }

              p {
                font-size: 14px;
              }
            }

            .database-settings {
              .database-list {
                .database-item {
                  background: rgba(244, 246, 248, 1);
                  border-radius: 8px;
                  padding: 12px 16px;
                  margin-bottom: 16px;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .database-name {
                    color: rgba(17, 26, 52, 1);
                    font-size: 12px;
                    font-weight: 500;
                  }

                  .database-actions {
                    display: flex;
                    gap: 16px;
                    color: rgba(37, 109, 255, 1);
                    font-size: 12px;

                    span {
                      cursor: pointer;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 调试面板样式保持不变
  .debug-panel {
    position: fixed;
    top: 60px;
    right: 0;
    width: 400px;
    height: calc(100vh - 60px);
    background: #fff;
    border-left: 1px solid #e0e0e0;
    z-index: 1000;
    display: flex;
    flex-direction: column;

    .debug-header {
      padding: 16px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }

      .close-debug-btn {
        padding: 4px;
        border: none;
        background: none;
        cursor: pointer;
        color: #909399;

        &:hover {
          color: #606266;
        }
      }
    }

    .debug-content {
      flex: 1;
      overflow: hidden;

      .debug-chat-container {
        height: 100%;
      }
    }
  }

  // 弹窗样式保持不变
  .application-setting-content {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .model-dialog-content {
    .model-tabs {
      .model-context-container {
        margin-bottom: 20px;
        padding: 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;

        .model-select-row {
          margin-bottom: 16px;

          .model-radio {
            width: 100%;

            .model-option-item {
              display: flex;
              align-items: center;
              gap: 8px;

              .option-icon {
                width: 18px;
                height: 20px;
              }
            }
          }
        }

        .context-row {
          display: flex;
          align-items: center;
          gap: 16px;

          .context-label {
            width: 80px;
            font-size: 14px;
            color: #606266;
          }

          .context-slider-container {
            flex: 1;

            .context-slider {
              margin: 0;
            }
          }

          .context-input-container {
            width: 120px;

            .context-input {
              width: 100%;
            }
          }
        }
      }
    }
  }

  .knowledge-dialog-content {
    .knowledge-search-box {
      margin-bottom: 16px;
    }

    .knowledge-list {
      max-height: 400px;
      overflow-y: auto;

      .knowledge-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        }

        &.knowledge-card-selected {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        .knowledge-card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .knowledge-card-checkbox {
            flex-shrink: 0;
          }

          .knowledge-icon {
            width: 24px;
            height: 24px;
          }

          .knowledge-card-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }
        }

        .knowledge-card-content {
          .knowledge-card-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .knowledge-card-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;

            .knowledge-card-code {
              font-family: monospace;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #909399;

      i {
        font-size: 24px;
        margin-bottom: 8px;
      }

      p {
        font-size: 14px;
        margin-bottom: 4px;

        &.tip {
          font-size: 12px;
          color: #c0c4cc;
        }
      }
    }
  }

  .knowledge-config-dialog-content {
    .config-section-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0 0 16px 0;
      color: #333;
    }

    .config-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .config-label {
        width: 120px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: #606266;

        .label-text {
          flex: 1;
        }

        i {
          color: #909399;
          cursor: help;
        }
      }

      .config-value {
        flex: 1;

        .slider-container {
          .slider-wrapper {
            margin-bottom: 8px;
          }

          .slider-range {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #909399;
          }
        }

        .radio-group {
          .radio-label {
            margin-right: 16px;
          }
        }
      }
    }

    .config-tip {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 8px 12px;
      background-color: #f0f9ff;
      border-radius: 4px;
      margin-bottom: 16px;
      font-size: 12px;
      color: #409eff;

      i {
        font-size: 14px;
      }
    }
  }

  .add-plugin-content {
    .builtin-categories {
      margin-bottom: 16px;
    }

    .plugin-search-box {
      margin-bottom: 16px;
    }

    .plugin-grid {
      max-height: 400px;
      overflow-y: auto;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 12px;

      .plugin-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        }

        &.plugin-card-selected {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        .plugin-card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .plugin-card-checkbox {
            flex-shrink: 0;
          }

          .plugin-card-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;

            img {
              width: 20px;
              height: 20px;
              border-radius: 2px;
            }

            i {
              font-size: 16px;
              color: #909399;
            }
          }

          .plugin-card-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }
        }

        .plugin-card-content {
          .plugin-card-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .plugin-card-footer {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;

            .plugin-tag {
              font-size: 10px;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #909399;

      i {
        font-size: 24px;
        margin-bottom: 8px;
      }

      p {
        font-size: 14px;
      }
    }
  }

  .bind-database-content {
    .database-search-box {
      margin-bottom: 16px;
    }

    .database-grid {
      max-height: 400px;
      overflow-y: auto;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 12px;

      .database-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        }

        &.database-card-selected {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        .database-card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .database-card-checkbox {
            flex-shrink: 0;
          }

          .database-card-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;

            i {
              font-size: 16px;
              color: #909399;
            }
          }

          .database-card-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }
        }

        .database-card-content {
          .database-card-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .database-card-footer {
            .el-tag {
              font-size: 10px;
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #909399;

      i {
        font-size: 24px;
        margin-bottom: 8px;
      }

      p {
        font-size: 14px;
      }
    }
  }

  // 插件配置弹框样式
  .plugin-config-content {
    .config-placeholder {
      text-align: center;
      padding: 40px 0;
      color: #909399;

      p {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
  }
}

.debug-panel {
  width: calc(50% - 5px); // 减去一半gap的宽度
  flex-shrink: 0;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 0;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .debug-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 16px 20px;
    flex-shrink: 0;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #1f2937;
    }
  }

  .debug-content {
    flex: 1;
    overflow: hidden;
    padding: 20px;

    p {
      font-size: 14px;
      color: #606266;
      margin: 0;
      text-align: center;
      padding: 40px 0;
    }

    .debug-chat-container {
      height: 100%;
      overflow: hidden;

      :deep(.debug-chat-panel) {
        height: 100%;
        border: none;
        box-shadow: none;
      }
    }
  }
}

.settings-section {
  padding: 24px 20px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 100%;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    flex-shrink: 0; // 防止标题被压缩

    h3 {
      margin: 0;
      font-size: 16px;
      line-height: 16px;
      font-weight: 500;
      color: #000000;
    }
  }
}

.basic-info-container {
  display: flex;
  flex: 1;

  .basic-info-left {
    flex: 1;
    padding-right: 20px;
  }

  .basic-info-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;

    .avatar-container {
      display: flex;
      align-items: flex-start;

      .avatar-label {
        font-size: 14px;
        color: #606266;
        line-height: 32px;
        margin-right: 32px;
      }
    }
  }
}

.avatar-wrapper {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px dashed #d9d9d9;
  margin-right: 16px;

  .el-image {
    width: 100%;
    height: 100%;
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 12px;
    cursor: pointer;
    background-color: #fafafa;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      color: #409eff;
      background-color: #f5f7fa;
    }

    i {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }

  .avatar-uploader {
    width: 100%;
    height: 100%;

    ::v-deep .el-upload {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }
}

.avatar-actions {
  display: flex;
  height: 100px;
  align-items: center;
}

.form-tip {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.model-selection {
  display: flex;
  gap: 16px;

  .model-item {
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;

    &.active {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }

    img {
      width: 24px;
      height: 24px;
    }
  }
}



.plugin-content {
  margin-bottom: 16px;
  display: flex;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
}

.plugin-name-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0; // 防止内容溢出
}

.plugin-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.plugin-type-tag {
  align-self: flex-start;
  font-size: 10px !important;
  padding: 2px 6px !important;
  height: auto !important;
  line-height: 1.2 !important;
}

.plugin-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  height: 100px;
  margin-bottom: 0;
  width: 46%;
  box-sizing: border-box;

  .plugin-card-item {
    display: flex;
    align-items: flex-start;
    flex: 1;
    width: calc(100% - 70px); /* 减去操作按钮区域的宽度 */
    overflow: hidden;
  }

  .plugin-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    flex: 1;
    overflow: hidden;
    width: 100%;
  }

  .plugin-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background: #e6f1fc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    overflow: hidden;
    flex-shrink: 0;

    i,
    img {
      font-size: 16px;
      color: #409eff;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .plugin-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .plugin-desc {
    font-size: 13px;
    color: #606266;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-bottom: 8px;
  }

  .plugin-type {
    font-size: 12px;
    color: #909399;
    background-color: #f0f2f5;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    white-space: nowrap;
  }

  .plugin-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 60px;
    max-width: 60px;
    align-self: center;
    justify-content: flex-end;

    .el-button {
      padding: 0;

      &:hover {
        color: #409eff;
      }
    }
  }
}

.empty-plugins {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  grid-column: span 2;

  p {
    margin: 0 0 8px;

    &.tip {
      font-size: 12px;
    }
  }
}

.database-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .empty-state {
    text-align: center;
    color: #909399;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    i {
      font-size: 36px;
      margin-bottom: 8px;
    }

    p {
      margin: 0;

      &.tip {
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }
}

.link-public-row {
  display: flex;

  .link-container {
    flex: 2;
    padding-right: 20px;
  }

  .public-container {
    flex: 1;
  }
}

.model-row {
  display: flex;

  .model-container {
    flex: 1;
    padding-right: 20px;
  }

  .dialog-model-container {
    flex: 1;
  }
}

.selected-model-icons {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .model-icon {
    width: 16px;
    height: 16px;
    border-radius: 2px;
  }
}

.selected-model {
  display: none; /* 隐藏旧的展示区域 */
}

.model-option-item {
  display: flex;
  align-items: center;

  .option-icon {
    width: 30px;
    height: 30px;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.model-select-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  padding: 0 15px;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  width: 100%;
  min-height: 40px;
  background-color: #fff;

  &:hover {
    border-color: #c0c4cc;
  }

  .selected-value {
    flex: 1;
    overflow: hidden;

    .placeholder {
      color: #c0c4cc;
    }
  }

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .model-tag {
      margin-right: 4px;
      max-width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }

    .more-tag {
      font-size: 12px;
      color: #909399;
      line-height: 24px;
    }
  }
}

.model-dialog-content {
  max-height: 400px;
  overflow-y: auto;

  .model-tabs {
    ::v-deep .el-tab-pane {
      // 移除嵌套的滚动设置，避免双滚动条
      max-height: none;
      overflow-y: visible;
    }
  }

  .model-dialog-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-radio,
    .el-checkbox {
      width: 100%;
      display: flex;
      align-items: center;

      .model-option-item {
        margin-left: 8px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    i {
      font-size: 36px;
      margin-bottom: 8px;
    }

    p {
      margin: 0;

      &.tip {
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }
}

.selected-model-display {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .selected-model-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    padding: 2px;
    background-color: #f9f9f9;
  }
}

.selected-models-display {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .selected-model-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    padding: 2px;
    background-color: #f9f9f9;
  }
}

.model-context-container {
  border-bottom: 1px solid #ebeef5;
  padding: 16px 0;

  &:last-child {
    border-bottom: none;
  }
}

.model-select-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.model-radio,
.model-checkbox {
  width: 100%;

  :deep(.el-radio__input),
  :deep(.el-checkbox__input) {
    float: left;
    margin-top: 10px;
  }

  :deep(.el-radio__label),
  :deep(.el-checkbox__label) {
    padding-left: 8px;
  }
}

.model-option-item {
  display: flex;
  align-items: center;
}

.context-row {
  display: flex;
  align-items: center;
  padding-left: 30px;

  .context-label {
    width: 80px;
    font-size: 14px;
    color: #606266;
  }

  .context-slider-container {
    flex: 1;
    margin: 0 15px;
  }

  .context-input-container {
    width: 120px;

    .context-input {
      width: 100%;
    }
  }
}

.context-setting {
  display: none;
}

.knowledge-dialog-content {
  max-height: 500px;
  overflow-y: auto;

  .knowledge-search-box {
    margin-bottom: 16px;
    padding: 0 4px;

    .el-input {
      width: 100%;
    }
  }

  .knowledge-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;

    .knowledge-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      height: 160px;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      &-selected {
        background-color: #ecf5ff;
        border-color: #409eff;
      }

      &-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      &-checkbox {
        margin-right: 8px;
      }

      &-name {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      &-desc {
        font-size: 12px;
        color: #606266;
        margin-bottom: 8px;
        line-height: 1.4;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      &-info {
        display: flex;
        flex-direction: column;
        font-size: 11px;
        color: #909399;
      }

      &-code {
        color: #909399;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-time {
        color: #909399;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      font-size: 14px;
    }
  }
}

.switch-group {
  display: flex;
  align-items: center;
}

.switch-label {
  margin: 0 10px;
  font-size: 14px;
  color: #909399;
  transition: color 0.3s;

  &.active {
    color: #409eff;
    font-weight: bold;
  }

  &.switch-off.active {
    color: #409eff;
  }

  &.switch-on.active {
    color: #409eff;
  }
}

.switch-row {
  display: flex;
  align-items: center;
  gap: 40px;
  padding-left: 24px;

  .switch-item {
    display: flex;
    align-items: center;
    gap: 10px;

    .switch-item-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
    }
  }
}

.knowledge-config-info {
  margin-bottom: 20px;

  .info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .info-item {
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    // background-color: #f8f9fa;

    .info-label {
      color: #606266;
      font-size: 13px;
      margin-bottom: 5px;
    }

    .info-value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.knowledge-bound-list {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .bound-knowledge-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 5px;
  }

  .bound-knowledge-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .knowledge-name {
      font-size: 14px;
      color: #303133;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.knowledge-config-dialog-content {
  .config-section-title {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    color: #303133;
    font-size: 16px;
  }

  .config-item {
    display: flex;
    margin-bottom: 24px;

    .config-label {
      width: 150px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #606266;

      .label-text {
        margin-right: 5px;
      }

      i {
        color: #909399;
        cursor: pointer;
        font-size: 16px;
      }
    }

    .config-value {
      flex: 1;
    }
  }

  .slider-container {
    .slider-wrapper {
      margin-bottom: 5px;
    }

    .slider-range {
      display: flex;
      justify-content: space-between;
      color: #909399;
      font-size: 12px;
    }
  }

  .radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .radio-label {
      color: #606266;
    }
  }

  .config-tip {
    background: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    color: #909399;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    i {
      margin-right: 5px;
      font-size: 14px;
    }
  }

  .search-option {
    display: flex;
    align-items: center;

    .search-icon {
      color: #409eff;
      margin-right: 8px;
    }
  }
}

.add-plugin-content {
  .plugin-search-box {
    margin-bottom: 16px;
    padding: 0 4px;

    .el-input {
      width: 100%;
    }
  }

  // 内置插件分类样式
  .builtin-categories {
    margin-bottom: 20px;

    :deep(.el-radio-group) {
      .el-radio-button {
        &:first-child .el-radio-button__inner {
          border-top-left-radius: 6px;
          border-bottom-left-radius: 6px;
        }

        &:last-child .el-radio-button__inner {
          border-top-right-radius: 6px;
          border-bottom-right-radius: 6px;
        }

        .el-radio-button__inner {
          border: 1px solid #dcdfe6;
          border-left: 0;
          background-color: #fff;
          color: #606266;
          font-size: 14px;
          padding: 8px 16px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f5f7fa;
            color: #409eff;
          }
        }

        &:first-child .el-radio-button__inner {
          border-left: 1px solid #dcdfe6;
        }

        &.is-active .el-radio-button__inner {
          background-color: #409eff;
          border-color: #409eff;
          color: #fff;
          box-shadow: none;
        }
      }
    }
  }

  .plugin-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 5px;
    max-height: 500px;
    overflow-y: auto;

    .plugin-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      height: 160px;
      position: relative;
      width: 100%;
      box-sizing: border-box;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      &-selected {
        background-color: #ecf5ff;
        border-color: #409eff;
      }

      &-added {
        opacity: 0.8;
        cursor: not-allowed;
      }

      &-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        width: 100%;
        overflow: hidden;
      }

      &-checkbox {
        margin-right: 8px;
        flex-shrink: 0;
      }

      &-icon {
        width: 30px;
        height: 30px;
        border-radius: 6px;
        background: #f0f2f5;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        i {
          font-size: 16px;
          color: #409eff;
        }
      }

      &-name {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        width: 100%;
      }

      &-desc {
        font-size: 12px;
        color: #606266;
        margin-bottom: 8px;
        line-height: 1.4;
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      &-info {
        display: flex;
        flex-direction: column;
        font-size: 11px;
        color: #909399;
        width: 100%;
      }

      &-type {
        color: #909399;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: #f5f7fa;
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
        width: fit-content;
      }

      &-time {
        color: #909399;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-type {
        color: #909399;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: #f5f7fa;
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
        width: fit-content;
        font-size: 11px;
      }

      &-footer {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: 8px;

        .plugin-tag {
          font-size: 10px;
          height: 16px;
          line-height: 14px;
          padding: 0 4px;
        }
      }

      &-status {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #409eff;
        color: #fff;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      font-size: 14px;
    }
  }
}

.button-container {
  margin-top: 20px;
  text-align: center;
}
.knowledge-icon {
  width: 30px;
  height: 30px;
  margin-right: 15px;
}

// 数据库相关样式
.database-settings {
  .database-list {
    margin-bottom: 16px;
    display: flex;
    gap: 16px;
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;

    .database-item {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f8f9fa;
      height: 60px;
      margin-bottom: 0;
      width: 46%;
      box-sizing: border-box;

      .database-name {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 8px;
      }

      .database-actions {
        display: flex;
        align-items: center;
        min-width: 40px;
        justify-content: flex-end;

        .el-button {
          padding: 0;

          &:hover {
            color: #409eff;
          }
        }
      }
    }
  }
}

.bind-database-content {
  .database-search-box {
    margin-bottom: 20px;
  }

  .database-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;

    .database-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s;
      background-color: #fff;

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      &-selected {
        background-color: #ecf5ff;
        border-color: #409eff;
      }

      &-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .database-card-checkbox {
          margin-right: 8px;
          margin-top: 2px;
        }

        .database-card-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          background: #e6f1fc;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          i {
            font-size: 16px;
            color: #409eff;
          }
        }

        .database-card-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          line-height: 1.4;
          flex: 1;
        }
      }

      &-content {
        .database-card-desc {
          font-size: 12px;
          color: #606266;
          line-height: 1.4;
          margin-bottom: 12px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .database-card-footer {
          .el-button--text {
            padding: 0;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      font-size: 14px;
    }
  }
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.introduce-example {
  font-size: 12px;
  color: #606266;
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f9f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.input-with-button {
  position: relative;
}

.ai-optimize-btn {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: 0;
  margin-right: 8px;
  color: #409EFF;
  font-size: 13px;
  z-index: 2;
}

.voice-select-container {
  display: flex;
  align-items: center;
}

.ai-optimize-btn-dialog {
  top: 10px !important;
  right: 10px !important;
}

.ai-optimize-external {
  margin-top: 8px;
  text-align: right;
}

.ai-optimize-external-btn {
  color: #409EFF;
  font-size: 13px;

  &:hover {
    color: #66b1ff;
  }
}

.form-label-with-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.ai-optimize-label-btn {
  color: #409EFF;
  font-size: 12px;
  padding: 0 4px;
  margin-left: 8px;

  &:hover {
    color: #66b1ff;
  }

  .el-icon-magic-stick {
    margin-right: 2px;
  }
}

.textarea-with-external-btn {
  position: relative;
}

.ai-optimize-external-btn {
  position: absolute;
  top: -18px;
  right: 0;
  color: #409EFF;
  font-size: 12px;
  z-index: 2;
  padding: 4px 8px;

  &:hover {
    color: #66b1ff;
    background-color: #ecf5ff;
  }

  .el-icon-magic-stick {
    margin-right: 2px;
  }
}

// 关闭调试按钮样式
.close-debug-btn {
  color: #909399;
  padding: 0;

  &:hover {
    color: #f56c6c;
  }

  i {
    font-size: 16px;
  }
}

// 插件卡片紧凑样式
.plugin-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .plugin-card-compact {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 0;
    transition: all 0.3s ease;
    width: calc(50% - 4px);
    box-sizing: border-box;

    &:hover {
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
    }

    .plugin-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;

      .plugin-card-left {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        flex: 1;
        min-width: 0;

        .plugin-icon {
          width: 28px;
          height: 28px;
          border-radius: 4px;
          background-color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #e9ecef;
          flex-shrink: 0;
          margin-top: 2px;

          img {
            width: 20px;
            height: 20px;
            border-radius: 2px;
          }

          i {
            font-size: 16px;
            color: #909399;
          }
        }

        .plugin-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }

      .plugin-card-right {
        display: flex;
        align-items: center;
        gap: 4px;

        .el-button {
          padding: 4px;
          min-width: auto;

          &:hover {
            background-color: #f0f0f0;
          }
        }

        :deep(.el-switch) {
          transform: scale(0.8);
        }
      }
    }

    .plugin-card-desc {
      font-size: 12px;
      color: #666;
      line-height: 1.3;
      margin-left: 36px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 插件配置弹框样式
.plugin-config-content {
  .config-placeholder {
    text-align: center;
    padding: 40px 0;
    color: #909399;

    p {
      font-size: 14px;
      margin-bottom: 8px;
    }
  }
}

// AI优化图标样式
.ai-optimize-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  vertical-align: middle;
}

// Demo按钮样式 - 完全按照demo设计
.demo-button {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;

  .demo-icon {
    width: 12px;
    height: 12px;
    font-size: 12px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    vertical-align: middle;
  }

  .demo-text {
    color: rgba(37, 109, 255, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
  }

  // 配置按钮样式（无边框）
  &.config-btn {
    background: transparent;
    border: none;

    .demo-icon {
      color: rgba(0, 0, 0, 1);
    }

    .demo-text {
      color: rgba(0, 0, 0, 1);
    }

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  // 绑定按钮样式（蓝色边框）
  &.bind-btn {
    background: transparent;
    border: 1px solid rgba(37, 109, 255, 1);

    .demo-icon {
      color: rgba(37, 109, 255, 1);
    }

    .demo-text {
      color: rgba(37, 109, 255, 1);
    }

    &:hover {
      background: rgba(37, 109, 255, 0.05);
    }
  }
}

// Demo按钮样式 - 完全按照demo设计
.demo-button {
  display: flex;
  align-items: center;
  height: 20px;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.8;
  }

  .demo-icon {
    width: 12px;
    height: 12px;
    font-size: 12px;
    color: rgba(37, 109, 255, 1);
    margin-right: 4px;
    margin-top: 4px;
  }

  .demo-text {
    height: 20px;
    color: rgba(37, 109, 255, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
  }
}

// 插件配置样式 - 按照demo布局
.plugin-config {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  .card-content {
    .plugin-strategy-section {
      margin-bottom: 16px;

      .strategy-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .strategy-label {
          display: flex;
          align-items: center;
          gap: 4px;

          .label-text {
            font-size: 14px;
            color: #000;
            font-weight: 500;
          }

          i {
            font-size: 14px;
            color: #999;
            cursor: help;
          }
        }

        .strategy-select {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-select {
            width: 200px;
          }
        }
      }
    }

    .plugin-divider {
      width: 100%;
      height: 1px;
      background-color: #e9ecef;
      margin: 24px 0;
    }

    .plugin-list {
      .plugin-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        }

        .plugin-icon {
          width: 36px;
          height: 36px;
          background-color: #fff;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          img {
            width: 24px;
            height: 24px;
            border-radius: 2px;
          }

          i {
            font-size: 20px;
            color: #409eff;
          }
        }

        .plugin-info {
          flex: 1;
          min-width: 0;

          .plugin-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 3px;

            .plugin-name {
              font-size: 12px;
              font-weight: 500;
              color: #111a34;
              line-height: 18px;
              text-transform: uppercase;
            }

            .plugin-type-tag {
              background-color: #e8f4fd;
              color: #256dff;
              font-size: 10px;
              padding: 2px 6px;
              border-radius: 2px;
              line-height: 14px;
            }
          }

          .plugin-desc {
            font-size: 12px;
            color: #bbb;
            line-height: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .plugin-actions {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-left: 16px;

          .action-link {
            font-size: 12px;
            color: #256dff;
            cursor: pointer;
            line-height: 17px;

            &:hover {
              text-decoration: underline;
            }
          }

          .action-separator {
            font-size: 12px;
            color: #256dff;
            margin: 0 4px;
          }

          .plugin-switch {
            margin-left: 8px;
          }
        }
      }
    }

    .empty-plugins {
      text-align: center;
      padding: 40px 0;
      color: #999;

      p {
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

// 数据库配置样式 - 按照demo布局
.database-config {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  .card-content {
    .database-model-section {
      margin-bottom: 16px;

      .model-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .model-label {
          display: flex;
          align-items: center;
          gap: 4px;

          .label-text {
            font-size: 14px;
            color: #000;
            font-weight: 500;
          }

          i {
            font-size: 14px;
            color: #999;
            cursor: help;
          }
        }

        .model-select {
          display: flex;
          align-items: center;
          gap: 8px;
          position: relative;

          .el-select {
            width: 200px;
          }

          .model-arrow {
            position: absolute;
            right: 8px;
            font-size: 12px;
            color: #999;
            pointer-events: none;
          }
        }
      }
    }

    .database-divider {
      width: 100%;
      height: 1px;
      background-color: #e9ecef;
      margin: 24px 0;
    }

    .database-list {
      .database-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        }

        .database-icon {
          width: 36px;
          height: 36px;
          background-color: #fff;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          i {
            font-size: 20px;
            color: #409eff;
          }
        }

        .database-info {
          flex: 1;
          min-width: 0;

          .database-name {
            font-size: 12px;
            font-weight: 500;
            color: #111a34;
            line-height: 18px;
            margin-bottom: 3px;
            text-transform: uppercase;
          }

          .database-desc {
            font-size: 12px;
            color: #bbb;
            line-height: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .database-actions {
          display: flex;
          align-items: center;
          margin-left: 16px;

          .action-link {
            font-size: 12px;
            color: #256dff;
            cursor: pointer;
            line-height: 17px;

            &:hover {
              text-decoration: underline;
            }
          }

          .action-separator {
            font-size: 12px;
            color: #256dff;
            margin: 0 4px;
          }
        }
      }
    }

    .empty-database {
      text-align: center;
      padding: 40px 0;
      color: #999;

      p {
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

// 知识库配置样式 - 按照demo布局
.knowledge-config {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #000;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  .card-content {
    .knowledge-config-grid {
      .config-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        gap: 20px;

        &:last-child {
          margin-bottom: 0;
          gap: 20px;
          justify-content: flex-start;

          .config-item:first-child {
            margin-right: 60px;
          }
        }

        .config-item {
          display: flex;
          align-items: center;
          min-width: 0;
          flex: 1;
          white-space: nowrap;

          .config-label {
            color: #888;
            font-size: 14px;
            margin-right: 4px;
            white-space: nowrap;
            flex-shrink: 0;
          }

          .config-value {
            color: #000;
            font-size: 14px;
            font-weight: normal;
            white-space: nowrap;
          }
        }
      }
    }

    .knowledge-divider {
      width: 100%;
      height: 1px;
      background-color: #e9ecef;
      margin: 24px 0;
    }

    .knowledge-bound-list {
      .knowledge-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
        }

        .knowledge-icon {
          width: 36px;
          height: 36px;
          background-color: #fff;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          i {
            font-size: 20px;
            color: #409eff;
          }
        }

        .knowledge-info {
          flex: 1;
          min-width: 0;

          .knowledge-name {
            font-size: 12px;
            font-weight: 500;
            color: #111a34;
            line-height: 18px;
            margin-bottom: 3px;
            text-transform: uppercase;
          }

          .knowledge-desc {
            font-size: 12px;
            color: #bbb;
            line-height: 17px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .knowledge-actions {
          display: flex;
          align-items: center;
          margin-left: 16px;

          .action-link {
            font-size: 12px;
            color: #256dff;
            cursor: pointer;
            line-height: 17px;

            &:hover {
              text-decoration: underline;
            }
          }

          .action-separator {
            font-size: 12px;
            color: #256dff;
            margin: 0 4px;
          }
        }
      }
    }
  }
}
</style>
