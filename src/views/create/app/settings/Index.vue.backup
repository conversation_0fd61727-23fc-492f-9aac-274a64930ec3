# 创建智能体设置页面的主组件
<template>
  <div class="app-settings">
    <!-- 顶部操作栏 -->
    <div class="settings-header">
      <div class="header-content">
        <div class="header-left">
          <img class="header-icon" src="@/assets/layouts/icon-agent.png" alt="智能体" />
          <span class="header-title">智能体配置</span>
          <div class="tab-group">
            <div class="tab-item active">
              <span>基础配置</span>
            </div>
            <div class="tab-item">
              <span>限流配置</span>
            </div>
            <div class="tab-item">
              <span>高级配置</span>
            </div>
          </div>
        </div>
        <div class="header-right">
          <div class="action-btn">
            <span>分享</span>
          </div>
          <div class="action-btn">
            <span>调试</span>
          </div>
          <div class="action-btn primary">
            <span>保存</span>
          </div>
          <img class="header-avatar" src="@/assets/layouts/top-user-avatar.png" alt="用户头像" />
        </div>
      </div>
      <div class="header-divider"></div>
    </div>

    <!-- 配置内容 -->
    <div v-loading="loadingStates.detail" element-loading-text="正在加载应用配置..." element-loading-background="rgba(255, 255, 255, 0.8)">
      <BasicConfig
        v-show="activeTab === 'basic'"
        ref="basicConfig"
        :is-debugging="debugStates.basic"
        @update-basic-config="updateBasicConfig"
        @toggle-debug="handleCloseDebug"
      />
      <!-- 暂时屏蔽限流配置组件 -->
      <!-- <LimitConfig
        v-show="activeTab === 'limit'"
        ref="limitConfig"
      /> -->
      <AdvancedConfig
        v-show="activeTab === 'advanced'"
        ref="advancedConfig"
        :flow-detail="flowDetail"
        :is-debugging="debugStates.advanced"
        @update-human-transfer="updateHumanTransfer"
        @update-response-config="updateResponseConfig"
        @toggle-debug="handleCloseDebug"
      />
    </div>
  </div>
</template>

<script>
import BasicConfig from "./components/BasicConfig.vue"
// 暂时屏蔽限流配置组件导入
// import LimitConfig from "./components/LimitConfig.vue";
import { api } from "@/api/request"
import AdvancedConfig from "./components/AdvancedConfig.vue"

export default {
  name: "AppSettings",
  components: {
    BasicConfig,
    // 暂时屏蔽限流配置组件注册
    // LimitConfig,
    AdvancedConfig,
  },
  data() {
    return {
      activeTab: "basic",
      // 为每个配置组件维护独立的调试状态
      debugStates: {
        basic: false,    // 基础配置调试状态
        advanced: false  // 高级配置调试状态
      },
      // 存储各配置组件的数据
      allConfigs: {
        basicConfig: null,
        humanTransferConfig: null,
        responseConfig: null,
      },
      flowDetail: null,
      // loading状态管理
      loadingStates: {
        detail: false,    // 获取详情loading
        save: false,      // 保存loading
        debug: false,     // 调试loading
        delete: false     // 删除loading
      },
    };
  },
  created() {
    this.getFlowDetail();
  },
  methods: {
    // 获取流程详情
    async getFlowDetail() {
      try {
        this.loadingStates.detail = true;
        const id = this.$route.params.id;
        const res = await api.sessionFlow.getDetail(id);
        if (res.code === 200) {
          this.flowDetail = res.data;
        } else {
          this.$showFriendlyError(null, res.message || "获取详情失败");
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$showFriendlyError(null, "获取详情失败");
      } finally {
        this.loadingStates.detail = false;
      }
    },
    // 更新基础配置数据
    updateBasicConfig(config) {
      this.allConfigs.basicConfig = config;
    },

    // 更新转人工配置数据
    updateHumanTransfer(config) {
      this.allConfigs.humanTransferConfig = config;
    },

    // 更新响应配置数据
    updateResponseConfig(config) {
      this.allConfigs.responseConfig = config;
    },

    // 通用保存方法，可以被handleSave和handleDebug调用
    async performSave() {
      // 确保获取所有组件最新数据
      const basicConfig = this.$refs.basicConfig;
      const advancedConfig = this.$refs.advancedConfig;

      // 强制更新基础配置数据（确保最新状态）
      if (basicConfig) {
        basicConfig.updateParentConfig();
      }

              // 如果基础配置组件存在且数据未通过事件更新，则直接获取
        if (basicConfig && !this.allConfigs.basicConfig) {
          this.allConfigs.basicConfig = {
            basicForm: basicConfig.basicForm,
            modelForm: basicConfig.modelForm,
            knowledgeNodeData: basicConfig.knowledgeNodeData,
            pluginNodes: basicConfig.activePlugins,
            newPluginNodes: basicConfig.activeNewPlugins,
            dbNodes: basicConfig.activeDatabaseNodes,
          };
        }

      // 如果高级配置组件存在且数据未通过事件更新，则直接获取
      if (advancedConfig) {
        if (!this.allConfigs.humanTransferConfig) {
          this.allConfigs.humanTransferConfig =
            advancedConfig.humanTransferConfig;
        }
        if (!this.allConfigs.responseConfig) {
          this.allConfigs.responseConfig = advancedConfig.responseConfig;
        }
      }

      // 确保基础配置数据存在
      if (this.allConfigs.basicConfig) {
        const basicData = this.allConfigs.basicConfig;

        // 调试日志：检查模型配置中的声音数据
        console.log('保存前的模型配置:', basicData.modelForm.data.nodeTypeConfig);
        console.log('voiceId:', basicData.modelForm.data.nodeTypeConfig.voiceId);
        console.log('voice:', basicData.modelForm.data.nodeTypeConfig.voice);

        // 封装param
        let param = {
          name: basicData.basicForm.name,
          description: basicData.basicForm.description,
          introduce: basicData.basicForm.introduce,
          profilePhoto: basicData.basicForm.profilePhoto,
          isPublic: basicData.basicForm.isPublic,
          isTrueCustomerServer: basicData.basicForm.isTrueCustomerServer,
          applicationType: basicData.basicForm.applicationType,
          sessionFlowCode: basicData.basicForm.sessionFlowCode,
          sessionFlowSetting: basicData.basicForm.sessionFlowSetting,
          pluginOrKnowledge: basicData.basicForm.pluginOrKnowledge,
          createFlowDetailInput: {
            name: basicData.basicForm.name,
            largeModelNodes: [basicData.modelForm],
            knowledgeNodes: [
              {
                ...basicData.knowledgeNodeData,
                data: {
                  ...basicData.knowledgeNodeData.data,
                  name: "知识库"
                }
              }
            ],
            pluginNodes: basicData.pluginNodes || [],
            newPluginNodes: basicData.newPluginNodes || [],
            dbNodes: basicData.dbNodes || [],
            endNodes: [
              {
                id: "endNodes",
                type: "end",
                position: {
                  x: 0,
                  y: 0,
                },
                data: {
                  label: "string",
                  type: "string",
                  name: "结束节点",
                  description: "string",
                  activeTab: "string",
                  nodeTypeConfig: {
                    name: "string",
                    inputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: true,
                        inputOutputType: "Input",
                      },
                    ],
                    structuredOutput: true,
                    outputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: true,
                        inputOutputType: "Input",
                      },
                    ],
                    responseConfig: {
                      enableSegmentedResponse: this.allConfigs.responseConfig?.enableSegmentedResponse ?? true,
                      enableMergeResponse: this.allConfigs.responseConfig?.enableMergeResponse ?? true,
                      responseDelay: this.allConfigs.responseConfig?.responseDelay ?? 0,
                      voice: basicData.modelForm.data.nodeTypeConfig.voice || "",
                    },
                  },
                },
              },
            ],
            startNodes: [
              {
                id: "startNodes",
                type: "start",
                position: {
                  x: 0,
                  y: 0,
                },
                data: {
                  label: "string",
                  type: "string",
                  name: "开始节点",
                  description: "string",
                  activeTab: "string",
                  nodeTypeConfig: {
                    name: "string",
                    inputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: false,
                        inputOutputType: "Input",
                      },
                    ],
                    structuredOutput: false,
                    outputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: false,
                        inputOutputType: "Input",
                      },
                    ],
                    inputSettings: {
                      enableTextInput: false,
                      textInputName: "string",
                      textInputRequired: false,
                      textInputPlaceholder: "string",
                      enableImageInput: false,
                      imageInputName: "string",
                      imageInputRequired: false,
                      imageInputPlaceholder: "string",
                      enableFileInput: false,
                      fileInputName: "string",
                      fileInputRequired: false,
                      fileInputPlaceholder: "string",
                      customVariables: [
                        {
                          displayName: "string",
                          name: "string",
                          type: "text",
                          required: false,
                          placeholder: "string",
                          options: [
                            {
                              label: "string",
                              value: "string",
                            },
                          ],
                          defaultValue: "string",
                        },
                      ],
                    },
                  },
                },
              },
            ],
            // 如果存在转人工配置，则添加到数据中
            humanTransferNodes: this.allConfigs.humanTransferConfig
              ? [
                  {
                    id: "humanTransfer",
                    type: "humanTransfer",
                    position: { x: 0, y: 0 },
                    data: this.allConfigs.humanTransferConfig,
                  },
                ]
              : [],
            edges: [],
            Version: new Date().getTime().toString(),
          },
        };

        // 调试日志：检查endNodes中的voice配置
        console.log('endNodes.responseConfig:', param.createFlowDetailInput.endNodes[0].data.nodeTypeConfig.responseConfig);
        console.log('endNodes.responseConfig.voice:', param.createFlowDetailInput.endNodes[0].data.nodeTypeConfig.responseConfig.voice);
        console.log('完整的保存参数:', param);

        // 获取应用ID，通常从路由参数中获取
        const id = this.$route.params.id;
        // 将id添加到param对象中
        param.id = id;

        // 调用接口更新应用
        const res = await api.sessionFlow.update(param);
        if (res.code === 200) {
          return true; // 保存成功
        } else {
          throw new Error(res.message || "保存失败");
        }
      }
      return false;
    },

    async handleSave() {
      try {
        this.loadingStates.save = true;
        const success = await this.performSave();
        if (success) {
          this.$message.success("保存成功");
          this.$router.push({
            path: "/create/app",
          });
        }
      } catch (error) {
        console.error("保存失败:", error);
      } finally {
        this.loadingStates.save = false;
      }
    },
    async handleDebug() {
      // 判断当前活跃的Tab
      if (this.activeTab === 'basic' || this.activeTab === 'advanced') {
        // 如果调试面板已经打开，直接关闭
        if (this.debugStates[this.activeTab]) {
          this.debugStates[this.activeTab] = false;
          return;
        }

        // 如果调试面板未打开，先保存配置再打开调试面板
        try {
          this.loadingStates.debug = true;
          const success = await this.performSave();
          if (success) {
            this.$message.success("配置已保存，正在启动调试模式");
            // 保存成功后开启调试模式
            this.debugStates[this.activeTab] = true;
          }
        } catch (error) {
          console.error("保存失败，无法启动调试模式:", error);
          this.$showFriendlyError(null, "保存失败，无法启动调试模式");
        } finally {
          this.loadingStates.debug = false;
        }
      }
    },
    handleCloseDebug() {
      // 关闭当前Tab的调试模式
      if (this.activeTab === 'basic' || this.activeTab === 'advanced') {
        this.debugStates[this.activeTab] = false;
      }
    },
    async handleDeleteApp() {
      try {
        const id = this.$route.params.id;

        // 首先校验应用是否被引用
        const checkRes = await this.checkAppReferences(id);
        if (!checkRes.canDelete) {
          this.$showFriendlyError(null, '该应用被其他渠道或工作流引用，无法删除');
          return;
        }

        // 弹窗确认删除操作
        await this.$confirm('确定要删除该应用吗？删除后将无法恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 执行删除操作
        this.loadingStates.delete = true;
        const res = await api.sessionFlow.deleteApp(id);
        if (res.code === 200) {
          this.$message.success('删除成功');
          this.$router.push('/create/app');
        } else {
          this.$showFriendlyError(null, res.message || '删除失败');
        }
      } catch (error) {
        // 用户取消删除操作
        if (error === 'cancel') {
          return;
        }
        console.error('删除应用失败:', error);
        this.$showFriendlyError(null, '删除应用失败，请稍后重试');
      } finally {
        this.loadingStates.delete = false;
      }
    },

    // 校验应用是否被引用（临时方法，等后端接口提供后替换）
    async checkAppReferences(appId) {
      // TODO: 替换为真实的校验接口
      // const res = await api.app.checkReferences(appId);

      // 模拟校验接口调用
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log('校验应用引用情况:', appId);
          // 模拟校验结果，这里可以修改 canDelete 为 false 来测试被引用的情况
          resolve({
            canDelete: true, // 改为 false 可以测试被引用的情况
            references: [] // 引用信息
          });
        }, 500);
              });
      },
  },
};
</script>

<style lang="scss" scoped>
.app-settings {
  background: #f5f7fa;
  height: 100%;

  .settings-header {
    position: relative;
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    // margin-bottom: 20px;
    // padding: 0 20px;
    // height: 60px;
    // background: #fff;
    // border-radius: 8px;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .action-group {
      // display: flex;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  // 页面加载状态样式
  .page-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    background: #fff;
    border-radius: 8px;
    margin: 20px;
  }
}
</style>
