# 创建智能体设置页面的主组件
<template>
  <div class="page flex-col">
    <div class="group_1 flex-col justify-end">
      <div class="box_1 flex-row">
        <!-- 返回按钮 -->
        <div class="back-button" @click="handleBack()">
          <span class="el-icon-back" style="font-size: 18px; font-weight: 800; width: 16px; height: 12px; "></span>
        </div>

        <span class="text_1">智能体配置</span>
        <div class="group_2 flex-row">
          <div class="text-wrapper_1 flex-col" :class="{ active: activeTab === 'basic' }" @click="switchTab('basic')">
            <span class="text_2">基础配置</span>
          </div>
          <div class="text-wrapper_2 flex-col" :class="{ active: activeTab === 'limit' }" @click="switchTab('limit')">
            <span class="text_3">限流配置</span>
          </div>
          <div class="text-wrapper_3 flex-col" :class="{ active: activeTab === 'advanced' }" @click="switchTab('advanced')">
            <span class="text_4">高级配置</span>
          </div>
        </div>
        <div class="header-right">
          <div class="text-wrapper_4 flex-col" @click="handleShare">
            <span class="text_5">分享</span>
          </div>
          <div class="text-wrapper_5 flex-col" @click="handleDebug">
            <span class="text_6">调试</span>
          </div>
          <div class="text-wrapper_6 flex-col" @click="handleSave">
            <span class="text_7">保存</span>
          </div>
          <div class="back-button" @click="handleBack()">
          <span class="el-icon-more" style="font-size: 18px; font-weight: 800; width: 16px; height: 12px; "></span>
        </div>
        </div>
      </div>
      <img
        class="image_1"
        referrerpolicy="no-referrer"
        src="@/assets/layouts/divider-horizontal.png"
      />
    </div>

    <!-- 配置内容 -->
    <div class="group_3 flex-col" v-loading="loadingStates.detail" element-loading-text="正在加载应用配置..." element-loading-background="rgba(255, 255, 255, 0.8)">
      <BasicConfig
        v-show="activeTab === 'basic'"
        ref="basicConfig"
        :is-debugging="debugStates.basic"
        @update-basic-config="updateBasicConfig"
        @toggle-debug="handleCloseDebug"
      />
      <!-- 暂时屏蔽限流配置组件 -->
      <LimitConfig
        v-show="activeTab === 'limit'"
        ref="limitConfig"
      />
      <AdvancedConfig
        v-show="activeTab === 'advanced'"
        ref="advancedConfig"
        :flow-detail="flowDetail"
        :is-debugging="debugStates.advanced"
        @update-human-transfer="updateHumanTransfer"
        @update-response-config="updateResponseConfig"
        @toggle-debug="handleCloseDebug"
      />
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request"
import AdvancedConfig from "./components/AdvancedConfig.vue"
import BasicConfig from "./components/BasicConfig.vue"
import LimitConfig from "./components/LimitConfig.vue"

export default {
  name: "AppSettings",
  components: {
    BasicConfig,
    LimitConfig,
    AdvancedConfig,
  },
  data() {
    return {
      activeTab: "basic",
      // 为每个配置组件维护独立的调试状态
      debugStates: {
        basic: false,    // 基础配置调试状态
        advanced: false  // 高级配置调试状态
      },
      // 存储各配置组件的数据
      allConfigs: {
        basicConfig: null,
        humanTransferConfig: null,
        responseConfig: null,
      },
      flowDetail: null,
      // loading状态管理
      loadingStates: {
        detail: false,    // 获取详情loading
        save: false,      // 保存loading
        debug: false,     // 调试loading
        delete: false     // 删除loading
      },
    };
  },
  created() {
    this.getFlowDetail();
  },
  methods: {
    // 获取流程详情
    async getFlowDetail() {
      try {
        this.loadingStates.detail = true;
        const id = this.$route.params.id;
        const res = await api.sessionFlow.getDetail(id);
        if (res.code === 200) {
          this.flowDetail = res.data;
        } else {
          this.$showFriendlyError(null, res.message || "获取详情失败");
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$showFriendlyError(null, "获取详情失败");
      } finally {
        this.loadingStates.detail = false;
      }
    },
    // 更新基础配置数据
    updateBasicConfig(config) {
      this.allConfigs.basicConfig = config;
    },

    // 更新转人工配置数据
    updateHumanTransfer(config) {
      this.allConfigs.humanTransferConfig = config;
    },

    // 更新响应配置数据
    updateResponseConfig(config) {
      this.allConfigs.responseConfig = config;
    },

    // 通用保存方法，可以被handleSave和handleDebug调用
    async performSave() {
      // 确保获取所有组件最新数据
      const basicConfig = this.$refs.basicConfig;
      const advancedConfig = this.$refs.advancedConfig;

      // 强制更新基础配置数据（确保最新状态）
      if (basicConfig) {
        basicConfig.updateParentConfig();
      }

              // 如果基础配置组件存在且数据未通过事件更新，则直接获取
        if (basicConfig && !this.allConfigs.basicConfig) {
          this.allConfigs.basicConfig = {
            basicForm: basicConfig.basicForm,
            modelForm: basicConfig.modelForm,
            knowledgeNodeData: basicConfig.knowledgeNodeData,
            pluginNodes: basicConfig.activePlugins,
            newPluginNodes: basicConfig.activeNewPlugins,
            dbNodes: basicConfig.activeDatabaseNodes,
          };
        }

      // 如果高级配置组件存在且数据未通过事件更新，则直接获取
      if (advancedConfig) {
        if (!this.allConfigs.humanTransferConfig) {
          this.allConfigs.humanTransferConfig =
            advancedConfig.humanTransferConfig;
        }
        if (!this.allConfigs.responseConfig) {
          this.allConfigs.responseConfig = advancedConfig.responseConfig;
        }
      }

      // 确保基础配置数据存在
      if (this.allConfigs.basicConfig) {
        const basicData = this.allConfigs.basicConfig;

        // 调试日志：检查模型配置中的声音数据
        console.log('保存前的模型配置:', basicData.modelForm.data.nodeTypeConfig);
        console.log('voiceId:', basicData.modelForm.data.nodeTypeConfig.voiceId);
        console.log('voice:', basicData.modelForm.data.nodeTypeConfig.voice);

        // 封装param
        let param = {
          name: basicData.basicForm.name,
          description: basicData.basicForm.description,
          introduce: basicData.basicForm.introduce,
          profilePhoto: basicData.basicForm.profilePhoto,
          isPublic: basicData.basicForm.isPublic,
          isTrueCustomerServer: basicData.basicForm.isTrueCustomerServer,
          applicationType: basicData.basicForm.applicationType,
          sessionFlowCode: basicData.basicForm.sessionFlowCode,
          sessionFlowSetting: basicData.basicForm.sessionFlowSetting,
          pluginOrKnowledge: basicData.basicForm.pluginOrKnowledge,
          createFlowDetailInput: {
            name: basicData.basicForm.name,
            largeModelNodes: [basicData.modelForm],
            knowledgeNodes: [
              {
                ...basicData.knowledgeNodeData,
                data: {
                  ...basicData.knowledgeNodeData.data,
                  name: "知识库"
                }
              }
            ],
            pluginNodes: basicData.pluginNodes || [],
            newPluginNodes: basicData.newPluginNodes || [],
            dbNodes: basicData.dbNodes || [],
            endNodes: [
              {
                id: "endNodes",
                type: "end",
                position: {
                  x: 0,
                  y: 0,
                },
                data: {
                  label: "string",
                  type: "string",
                  name: "结束节点",
                  description: "string",
                  activeTab: "string",
                  nodeTypeConfig: {
                    name: "string",
                    inputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: true,
                        inputOutputType: "Input",
                      },
                    ],
                    structuredOutput: true,
                    outputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: true,
                        inputOutputType: "Input",
                      },
                    ],
                    responseConfig: {
                      enableSegmentedResponse: this.allConfigs.responseConfig?.enableSegmentedResponse ?? true,
                      enableMergeResponse: this.allConfigs.responseConfig?.enableMergeResponse ?? true,
                      responseDelay: this.allConfigs.responseConfig?.responseDelay ?? 0,
                      voice: basicData.modelForm.data.nodeTypeConfig.voice || "",
                    },
                  },
                },
              },
            ],
            startNodes: [
              {
                id: "startNodes",
                type: "start",
                position: {
                  x: 0,
                  y: 0,
                },
                data: {
                  label: "string",
                  type: "string",
                  name: "开始节点",
                  description: "string",
                  activeTab: "string",
                  nodeTypeConfig: {
                    name: "string",
                    inputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: false,
                        inputOutputType: "Input",
                      },
                    ],
                    structuredOutput: false,
                    outputs: [
                      {
                        sourceType: "NodeOutput",
                        sourceNodeId: "string",
                        field: "string",
                        dataType: "字符串",
                        description: "string",
                        isParameter: false,
                        inputOutputType: "Input",
                      },
                    ],
                    inputSettings: {
                      enableTextInput: false,
                      textInputName: "string",
                      textInputRequired: false,
                      textInputPlaceholder: "string",
                      enableImageInput: false,
                      imageInputName: "string",
                      imageInputRequired: false,
                      imageInputPlaceholder: "string",
                      enableFileInput: false,
                      fileInputName: "string",
                      fileInputRequired: false,
                      fileInputPlaceholder: "string",
                      customVariables: [
                        {
                          displayName: "string",
                          name: "string",
                          type: "text",
                          required: false,
                          placeholder: "string",
                          options: [
                            {
                              label: "string",
                              value: "string",
                            },
                          ],
                          defaultValue: "string",
                        },
                      ],
                    },
                  },
                },
              },
            ],
            // 如果存在转人工配置，则添加到数据中
            humanTransferNodes: this.allConfigs.humanTransferConfig
              ? [
                  {
                    id: "humanTransfer",
                    type: "humanTransfer",
                    position: { x: 0, y: 0 },
                    data: this.allConfigs.humanTransferConfig,
                  },
                ]
              : [],
            edges: [],
            Version: new Date().getTime().toString(),
          },
        };

        // 调试日志：检查endNodes中的voice配置
        console.log('endNodes.responseConfig:', param.createFlowDetailInput.endNodes[0].data.nodeTypeConfig.responseConfig);
        console.log('endNodes.responseConfig.voice:', param.createFlowDetailInput.endNodes[0].data.nodeTypeConfig.responseConfig.voice);
        console.log('完整的保存参数:', param);

        // 获取应用ID，通常从路由参数中获取
        const id = this.$route.params.id;
        // 将id添加到param对象中
        param.id = id;

        // 调用接口更新应用
        const res = await api.sessionFlow.update(param);
        if (res.code === 200) {
          return true; // 保存成功
        } else {
          throw new Error(res.message || "保存失败");
        }
      }
      return false;
    },

    async handleSave() {
      try {
        this.loadingStates.save = true;
        const success = await this.performSave();
        if (success) {
          this.$message.success("保存成功");
          this.$router.push({
            path: "/create/app",
          });
        }
      } catch (error) {
        console.error("保存失败:", error);
      } finally {
        this.loadingStates.save = false;
      }
    },
    async handleDebug() {
      // 判断当前活跃的Tab
      if (this.activeTab === 'basic' || this.activeTab === 'advanced') {
        // 如果调试面板已经打开，直接关闭
        if (this.debugStates[this.activeTab]) {
          this.debugStates[this.activeTab] = false;
          return;
        }

        // 如果调试面板未打开，先保存配置再打开调试面板
        try {
          this.loadingStates.debug = true;
          const success = await this.performSave();
          if (success) {
            this.$message.success("配置已保存，正在启动调试模式");
            // 保存成功后开启调试模式
            this.debugStates[this.activeTab] = true;
          }
        } catch (error) {
          console.error("保存失败，无法启动调试模式:", error);
          this.$showFriendlyError(null, "保存失败，无法启动调试模式");
        } finally {
          this.loadingStates.debug = false;
        }
      }
    },
    handleCloseDebug() {
      // 关闭当前Tab的调试模式
      if (this.activeTab === 'basic' || this.activeTab === 'advanced') {
        this.debugStates[this.activeTab] = false;
      }
    },
    async handleDeleteApp() {
      try {
        const id = this.$route.params.id;

        // 首先校验应用是否被引用
        const checkRes = await this.checkAppReferences(id);
        if (!checkRes.canDelete) {
          this.$showFriendlyError(null, '该应用被其他渠道或工作流引用，无法删除');
          return;
        }

        // 弹窗确认删除操作
        await this.$confirm('确定要删除该应用吗？删除后将无法恢复', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 执行删除操作
        this.loadingStates.delete = true;
        const res = await api.sessionFlow.deleteApp(id);
        if (res.code === 200) {
          this.$message.success('删除成功');
          this.$router.push('/create/app');
        } else {
          this.$showFriendlyError(null, res.message || '删除失败');
        }
      } catch (error) {
        // 用户取消删除操作
        if (error === 'cancel') {
          return;
        }
        console.error('删除应用失败:', error);
        this.$showFriendlyError(null, '删除应用失败，请稍后重试');
      } finally {
        this.loadingStates.delete = false;
      }
    },

    // 校验应用是否被引用（临时方法，等后端接口提供后替换）
    async checkAppReferences(appId) {
      // TODO: 替换为真实的校验接口
      // const res = await api.app.checkReferences(appId);

      // 模拟校验接口调用
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log('校验应用引用情况:', appId);
          // 模拟校验结果，这里可以修改 canDelete 为 false 来测试被引用的情况
          resolve({
            canDelete: true, // 改为 false 可以测试被引用的情况
            references: [] // 引用信息
          });
        }, 500);
              });
      },

    // 处理分享功能
    handleShare() {
      // 获取当前应用的分享链接
      const appId = this.$route.params.id;
      const shareLink = `https://link-ai.tech/app/${appId}`;

      // 复制分享链接到剪贴板
      navigator.clipboard.writeText(shareLink).then(() => {
        this.$message.success('分享链接已复制到剪贴板');
      }).catch(() => {
        // 如果剪贴板API不可用，显示链接让用户手动复制
        this.$alert(`分享链接：${shareLink}`, '分享', {
          confirmButtonText: '复制',
          callback: () => {
            // 这里可以添加复制到剪贴板的备用方案
            this.$message.success('请手动复制分享链接');
          }
        });
      });
    },

    // 切换标签页
    switchTab(tabName) {
      this.activeTab = tabName;
    },

    // 处理返回按钮点击
    handleBack() {
      this.$router.push({
        path: "/create/app",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  background-color: rgba(242, 246, 252, 1);
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.group_1 {
  background-color: rgba(255, 255, 255, 0.95);
  width: 100%;
  height: 60px;
  border: 0.5px solid #e0e0e0;
}

.box_1 {
  width: 100%;
  height: 38px;
  margin: 11px 0 0 0;
  display: flex;
  align-items: center;
  padding-right: 34px;

  .back-button {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 77, 79, 0.1);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    .back-icon {
      font-size: 18px;
      font-weight: 800;
      width: 16px;
      height: 12px;
      background: #000000;
      transform: rotate(180deg);
    }
  }
}

.thumbnail_1 {
  width: 16px;
  height: 13px;
  margin-top: 13px;
}

.text_1 {
  width: 90px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 26px;
  margin: 6px 0 0 18px;
}

.group_2 {
  border-radius: 8px;
  width: 232px;
  height: 38px;
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.text-wrapper_1 {
  height: 30px;
  width: 72px;
  margin: 4px 0 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(244, 246, 248, 1);

  &.active {
    background: linear-gradient(135deg, #256dff 0%, #4a90e2 100%);
  }

  .text_2 {
    width: 56px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 20px;
  }

  &.active .text_2 {
    color: rgba(255, 255, 255, 1);
  }
}

.text-wrapper_2 {
  height: 30px;
  width: 72px;
  margin: 4px 0 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(244, 246, 248, 1);

  &.active {
    background: linear-gradient(135deg, #256dff 0%, #4a90e2 100%);
  }

  .text_3 {
    width: 56px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 20px;
  }

  &.active .text_3 {
    color: rgba(255, 255, 255, 1);
  }
}

.text-wrapper_3 {
  height: 30px;
  width: 72px;
  margin: 4px 4px 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(244, 246, 248, 1);

  &.active {
    background: linear-gradient(135deg, #256dff 0%, #4a90e2 100%);
  }

  .text_4 {
    width: 56px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 20px;
  }

  &.active .text_4 {
    color: rgba(255, 255, 255, 1);
  }
}

.text-wrapper_4 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 4px;
  height: 30px;
  width: 52px;
  margin: 4px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .text_5 {
    width: 28px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 20px;
  }
}

.text-wrapper_5 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 4px;
  height: 30px;
  width: 52px;
  margin: 4px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .text_6 {
    width: 28px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 20px;
  }
}

.text-wrapper_6 {
  background-color: rgba(37, 109, 255, 1);
  border-radius: 4px;
  height: 30px;
  width: 52px;
  margin: 4px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(37, 109, 255, 0.8);
  }

  .text_7 {
    width: 28px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 20px;
  }
}

.label_1 {
  width: 44px;
  height: 30px;
  margin: 4px 0 0 8px;
}

.image_1 {
  width: 100%;
  height: 1px;
  margin-top: 10px;
}

.group_3 {
  position: relative;
  width: 100%;
  height: calc(100vh - 60px);
  margin-bottom: 1px;
}
</style>
