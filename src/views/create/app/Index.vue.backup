<template>
  <div class="app-page">
    <router-view v-if="$route.path !== '/create/app'"></router-view>
    <div class="app-list" v-else>
      <div
        v-if="loading"
        class="loading-container"
        v-loading="loading"
        element-loading-background="rgba(255, 255, 255, 0.7)"
        element-loading-text="加载应用中..."
      ></div>
      <div v-else class="main-content">
        <!-- 页面标题和新建按钮 -->
        <div class="page-header">
          <h1 class="page-title">智能体</h1>
          <div class="create-agent-btn" @click="handleCreate">
            <img src="@/assets/app/add-agent-icon.png" alt="新建" class="btn-icon" />
            <span>新建智能体</span>
          </div>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="search-filter-section">
          <div class="search-box">
            <img src="@/assets/app/search-icon.png" alt="搜索" class="search-icon" />
            <input
              type="text"
              v-model="searchKeyword"
              placeholder="搜索智能体"
              class="search-input"
              @input="handleSearch"
              @keyup.enter="handleSearchImmediate"
            />
          </div>
          <el-select
            v-model="selectedCategory"
            placeholder="全部分类"
            class="category-select"
            @change="handleCategoryChange"
            popper-class="category-dropdown-popper"
          >
            <el-option label="全部分类" :value="null"></el-option>
            <el-option
              v-for="item in applicationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>

        <!-- 智能体卡片网格 -->
        <div class="agents-grid" v-if="apps.length > 0">
          <div
            v-for="(app, index) in apps"
            :key="app.id"
            class="agent-card"
            :style="{ '--card-index': index }"
            @click="handleSettings(app)"
          >
            <div class="card-header">
              <div class="agent-avatar">
                <el-avatar :size="44" :src="app.profilePhoto">{{
                  app.name.charAt(0)
                }}</el-avatar>
              </div>
              <div class="agent-info">
                <h3 class="agent-name">{{ app.name }}</h3>
                <div class="agent-description" v-if="app.introduce">
                  {{ app.introduce }}
                </div>
                <div class="agent-tags">
                  <div class="agent-type-tag" :class="+app.sessionFlowCode===1 ? 'light' : 'knowledge'">
                    <span>{{ getSessionFlowCodeLabel(app.sessionFlowCode) }}</span>
                  </div>
                  <div class="category-tag" v-if="getApplicationTypeLabel(app.applicationType)">
                    <img src="@/assets/app/category-tag-icon.png" alt="分类" class="tag-icon" />
                    <span>{{ getApplicationTypeLabel(app.applicationType) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="update-time">
              最近更新 {{ formatUpdateTime(app.lastModificationTime) }}
            </div>

            <div class="card-divider"></div>

            <div class="card-actions">
              <div class="left-section">
                <img src="@/assets/app/config-icon.png" alt="配置" class="config-icon" @click.stop="handleSettings(app)" />
              </div>
              <div class="right-section">
                <div class="config-btn" @click.stop="handleSettings(app)">
                  <span>配置</span>
                </div>
                <div class="chat-btn" @click.stop="handleChat(app)" :class="{ loading: chatLoadingMap[app.id] }">
                  <i v-if="chatLoadingMap[app.id]" class="el-icon-loading"></i>
                  <span v-else>开始对话</span>
                </div>
                <div class="more-btn" @click.stop="showMoreActions(app, $event)">
                  <span class="dots">•••</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div class="empty-state" v-else-if="!loading">
          <div class="empty-content">
            <div class="empty-icon">📝</div>
            <div class="empty-text">
              <span v-if="searchKeyword || selectedCategory !== null">
                没有找到符合条件的智能体
              </span>
              <span v-else>
                暂无智能体，点击右上角创建您的第一个智能体
              </span>
            </div>
          </div>
        </div>

        <!-- 加载更多提示 -->
        <div class="load-more-section" v-if="apps.length > 0">
          <div class="load-more-indicator" v-if="loadingMore">
            <i class="el-icon-loading"></i>
            <span>加载中...</span>
          </div>
          <div class="no-more-data" v-else-if="noMoreData && apps.length > 0">
            <span>已加载全部数据</span>
          </div>
        </div>

        <!-- 更多操作菜单 -->
        <div class="more-actions-menu" v-show="showMoreMenu" :style="moreMenuStyle">
          <div class="menu-item" @click="handleShare(currentApp)">
            <span>分享</span>
          </div>
          <div class="menu-item" @click="handleCopy(currentApp)">
            <span>创建副本</span>
          </div>
          <div class="menu-item delete" @click="handleDelete(currentApp)">
            <span>删除</span>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title="智能体复制"
      :visible.sync="copyDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      center
    >
      <div class="copy-dialog-content">
        <p>将自动创建一个相同的应用，确认复制吗？</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCopy">确定复制</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="分享智能体"
      :visible.sync="shareDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      center
      custom-class="share-dialog"
    >
      <div class="share-dialog-content">
        <div class="share-header">
          <span class="share-title">分享智能体</span>
          <i class="el-icon-close close-icon" @click="shareDialogVisible = false"></i>
        </div>

        <div class="share-body">
          <div class="link-section">
            <span class="link-title">已为您生成智能体的专属链接</span>
            <div class="link-container">
              <span class="link-text">{{ shareLink }}</span>
              <div class="copy-btn" @click="copyShareLink">
                <span class="copy-text">{{ linkCopied ? '复制成功' : '复制链接' }}</span>
              </div>
            </div>
          </div>

          <div class="qr-section">
            <div class="qr-wrapper">
              <img
                v-if="qrCodeUrl"
                :src="qrCodeUrl"
                alt="二维码"
                class="qr-code"
                @error="handleQRCodeError"
              />
              <div v-else class="qr-loading">
                <i class="el-icon-loading"></i>
                <span>生成二维码中...</span>
              </div>
            </div>
            <span class="qr-text">手机扫码体验</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 删除智能体弹窗 -->
    <el-dialog
      :visible.sync="deleteDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      center
      custom-class="delete-dialog"
    >
      <div class="delete-dialog-content">
        <div class="delete-header">
          <span class="delete-title">删除智能体</span>
          <i class="el-icon-close close-icon" @click="deleteDialogVisible = false"></i>
        </div>

        <div class="delete-body">
          <div class="warning-section">
            <i class="el-icon-warning warning-icon"></i>
            <span class="warning-text">智能体删除后数据将无法恢复，是否确定删除?</span>
          </div>
        </div>

        <div class="delete-footer">
          <div class="cancel-btn" @click="deleteDialogVisible = false" :disabled="deleteLoading">
            <span>取消</span>
          </div>
          <div class="delete-btn" @click="confirmDelete" :class="{ loading: deleteLoading }" :disabled="deleteLoading">
            <span v-if="!deleteLoading">删除</span>
            <i v-else class="el-icon-loading"></i>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 创建智能体弹窗 -->
    <el-dialog
      :visible.sync="showCreateDialog"
      width="800px"
      :close-on-click-modal="false"
      center
      custom-class="create-agent-dialog"
    >
      <div class="create-agent-dialog-content">
        <div class="dialog-header">
          <span class="dialog-title">选择创建智能体</span>
          <img
            src="@/assets/app/close-icon.png"
            alt="关闭"
            class="close-icon"
            @click="showCreateDialog = false"
          />
        </div>

        <div class="agent-type-selection">
          <div class="agent-type-card" @click="selectAgentType('light')" :class="{ active: selectedAgentType === 'light' }">
            <img src="@/assets/app/light-agent-icon.png" alt="轻量智能体" class="agent-type-icon" />
            <span class="agent-type-name">轻量智能体</span>
            <span class="agent-type-desc">适用于简单对话和基础任务处理</span>
          </div>
          <div class="agent-type-card" @click="selectAgentType('knowledge')" :class="{ active: selectedAgentType === 'knowledge' }">
            <img src="@/assets/app/knowledge-agent-icon.png" alt="知识智能体" class="agent-type-icon" />
            <span class="agent-type-name">知识智能体</span>
            <span class="agent-type-desc">适用于查询知识库与数据库能力</span>
          </div>
        </div>

        <div class="form-section">
          <div class="form-item">
            <div class="form-label">
              <span>智能体头像</span>
              <span class="required">*</span>
            </div>
            <div class="avatar-upload">
              <div class="avatar-preview">
                <img v-if="agentForm.avatar" :src="agentForm.avatar" alt="头像" class="avatar-img" />
                <div v-else class="avatar-placeholder">
                  <i class="el-icon-plus"></i>
                </div>
              </div>
              <span class="upload-tip">智能体头像支持 jpg、png 格式，大小不超过 2MB</span>
            </div>
          </div>

          <div class="form-item">
            <div class="form-label">
              <span>智能体名称</span>
              <span class="required">*</span>
            </div>
            <el-input
              v-model="agentForm.name"
              placeholder="请输入智能体名称"
              class="form-input"
            />
          </div>

          <div class="form-item">
            <div class="form-label">
              <span>智能体描述</span>
              <span class="required">*</span>
            </div>
            <div class="description-header">
              <span>AI一键填写</span>
              <span>去模版中心复制</span>
            </div>
            <el-input
              v-model="agentForm.description"
              type="textarea"
              :rows="3"
              placeholder="智能体是做什么的？简单描述它吧～"
              class="form-textarea"
              maxlength="100"
              show-word-limit
            />
          </div>

          <div class="form-item">
            <div class="form-label">
              <span>开场介绍</span>
              <span class="required">*</span>
            </div>
            <div class="intro-tip">
              将在用户开启对话时展示，引导用户快速了解功能并开启对话。例如："需要什么帮助？"
            </div>
            <el-input
              v-model="agentForm.introduction"
              type="textarea"
              :rows="4"
              placeholder="请输入开场介绍"
              class="form-textarea"
              maxlength="1000"
              show-word-limit
            />
          </div>

          <div class="form-item">
            <div class="form-label">
              <span>智能体类型</span>
              <span class="required">*</span>
            </div>
            <el-select
              v-model="agentForm.type"
              placeholder="请选择智能体类型"
              class="form-select"
            >
              <el-option label="轻量智能体" value="light"></el-option>
              <el-option label="知识智能体" value="knowledge"></el-option>
            </el-select>
          </div>

          <div class="form-item">
            <div class="form-label">
              <span>智能体设定</span>
            </div>
            <div class="setting-tip">输入提示词，定义智能体行为</div>
            <el-input
              v-model="agentForm.setting"
              type="textarea"
              :rows="6"
              placeholder="请输入智能体设定"
              class="form-textarea"
              maxlength="20000"
              show-word-limit
            />
          </div>

          <div class="form-item">
            <div class="form-label">
              <span>是否公开</span>
            </div>
            <div class="public-switch">
              <el-switch v-model="agentForm.isPublic" />
              <span class="public-tip">(如选择公开则可在应用广场被搜索和使用)</span>
            </div>
          </div>

          <div class="form-item" v-if="selectedAgentType === 'knowledge'">
            <div class="form-label">
              <span>绑定知识库</span>
            </div>
            <div class="knowledge-section">
              <div class="knowledge-actions">
                <div class="knowledge-action">
                  <img src="@/assets/app/add-agent-icon.png" alt="添加" class="action-icon" />
                  <span>添加知识库</span>
                </div>
                <div class="knowledge-action">
                  <img src="@/assets/app/add-agent-icon.png" alt="添加" class="action-icon" />
                  <span>添加知识库</span>
                </div>
              </div>
              <span class="upload-tip">或直接上传文件</span>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <div class="create-btn" @click="createAgent">
            <img src="@/assets/app/create-agent-btn.png" alt="创建" class="btn-icon" />
            <span>创建智能体</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"
import { copyToClipboard, generateShareLink } from "@/utils"
import { EnumApplicationType, getAgentTypeLabel } from "@/utils/enums"

export default {
  name: "AppPage",
  data() {
    return {
      apps: [],
      allApps: [], // 存储所有应用数据，用于搜索和筛选
      copyDialogVisible: false,
      shareDialogVisible: false,
      deleteDialogVisible: false,
      deleteLoading: false,
      currentApp: null,
      shareLink: "",
      linkCopied: false,
      qrCodeUrl: "",
      loading: false,
      loadingMore: false, // 加载更多数据的状态
      total: 0,
      currentPage: 1, // 当前页码
      pageSize: 20, // 每页数量
      noMoreData: false, // 是否没有更多数据
      chatLoadingMap: {}, // 记录每个应用的聊天loading状态
      showMoreMenu: false, // 控制更多操作菜单显示
      moreMenuStyle: {}, // 更多操作菜单样式
      searchKeyword: '', // 搜索关键词
      selectedCategory: null, // 选中的分类
      searchTimer: null, // 搜索防抖定时器
      applicationTypeOptions: EnumApplicationType, // 应用类型选项
      showCreateDialog: false, // 显示创建智能体弹窗
      selectedAgentType: '', // 选中的智能体类型
      agentForm: {
        avatar: '',
        name: '',
        description: '',
        introduction: '',
        type: '',
        setting: '',
        isPublic: false
      }
    };
  },
  created() {
    this.fetchAppList();
    // 点击其他地方关闭更多操作菜单
    document.addEventListener('click', this.hideMoreMenu);
  },
  mounted() {
    // 添加滚动监听
    this.addScrollListener();
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideMoreMenu);
    // 移除滚动监听
    this.removeScrollListener();
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },
  methods: {
    // 获取sessionFlowCode对应的中文标签
    getSessionFlowCodeLabel(code) {
      // 将字符串转换为数字再获取标签
      const numCode = parseInt(code);
      return getAgentTypeLabel(numCode) || code;
    },
    // 获取applicationType对应的分类名称
    getApplicationTypeLabel(applicationType) {
      if (!applicationType) return '';
      const option = this.applicationTypeOptions.find(item => item.value === applicationType);
      return option ? option.label : '';
    },
    async fetchAppList(isLoadMore = false) {
      try {
        if (isLoadMore) {
          this.loadingMore = true;
        } else {
          this.loading = true;
          this.currentPage = 1;
          this.noMoreData = false;
        }

        let params = {
          sorting: 0,
          skipCount: (this.currentPage - 1) * this.pageSize,
          maxResultCount: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim();
        }

        // 添加分类筛选条件
        if (this.selectedCategory !== null) {
          params.applicationType = this.selectedCategory;
        }

        const response = await api.sessionFlow.getList(params);
        if (response.code === 200 && response.data) {
          this.total = response.data.totalCount;
          const newItems = response.data.items || [];

          if (isLoadMore) {
            // 加载更多时追加数据
            this.allApps = [...this.allApps, ...newItems];
            this.apps = [...this.apps, ...newItems];
          } else {
            // 首次加载或重新搜索时替换数据
            this.allApps = newItems;
            this.apps = [...newItems];
          }

          // 检查是否还有更多数据
          this.noMoreData = this.apps.length >= this.total || newItems.length < this.pageSize;

          if (isLoadMore) {
            this.currentPage++;
          }
        } else {
          throw new Error(response.message || "获取应用列表失败");
        }
      } catch (error) {
        console.error("获取应用列表失败:", error);
        this.$showFriendlyError(error, "获取应用列表失败");
      } finally {
        this.loading = false;
        this.loadingMore = false;
      }
    },
    handleCreate() {
      this.showCreateDialog = true;
    },
    handleKnowledge(app) {
      this.$message.info(`进入知识智能体：${app.name}`);
    },
    async handleChat(app) {
      // 防止重复点击
      if (this.chatLoadingMap[app.id]) {
        return;
      }

      try {
        // 设置loading状态
        this.$set(this.chatLoadingMap, app.id, true);

        // 调用创建SignalR通道的接口
        console.log("开始为应用创建聊天会话:", app.id);
        const result = await this.createSignalRChannel(app.id);

        this.$message.success("聊天会话创建成功，正在打开聊天窗口...");

        // 接口成功后进行页面跳转，传递会话ID
        const sessionId = result.sessionData.sessionId;
        // 使用路由跳转到/access/temporary-chat，并在新标签页打开
        const routeUrl = this.$router.resolve({
          path: "/access/temporary-chat",
          query: {
            id: app.id,
            sourceType: 1,
            sessionId: sessionId,
          },
        });
        window.open(routeUrl.href, "_blank");
      } catch (error) {
        console.error("创建聊天通道失败:", error);
        this.$showFriendlyError(error, "创建聊天通道失败，请重试");
      } finally {
        // 清除loading状态
        this.$set(this.chatLoadingMap, app.id, false);
      }
    },

    // 创建SignalR通道的接口
    async createSignalRChannel(appId) {
      try {
        // 第一步：创建聊天会话
        const sessionResponse = await this.createChatSession(appId);

        if (!sessionResponse.isSuccess) {
          throw new Error(sessionResponse.message || "创建聊天会话失败");
        }

        const sessionData = sessionResponse.data;
        console.log(`为应用 ${appId} 创建聊天会话成功:`, sessionData);

        // 返回会话信息，用于跳转时传递
        return {
          success: true,
          sessionData: sessionData,
          message: "聊天会话创建成功",
        };
      } catch (error) {
        console.error("创建聊天会话失败:", error);
        throw error;
      }
    },

    // 创建聊天会话的接口
    async createChatSession(appId) {
      try {
        const response = await api.chat.createSession({
          clientId: appId,
          sourceType: 1, // 1: 应用
          sessionName: `应用${appId}的聊天会话`,
          isTestSession: true,
        });

        return response;
      } catch (error) {
        console.error("创建聊天会话失败:", error);
        return {
          isSuccess: false,
          message:
            error.response?.data?.message ||
            error.message ||
            "创建聊天会话失败",
        };
      }
    },
    handleCopy(app) {
      this.currentApp = app;
      this.copyDialogVisible = true;
    },
    async confirmCopy() {
      if (this.currentApp) {
        try {
          // 先获取应用详情
          const detailResponse = await api.sessionFlow.getDetail(
            this.currentApp.id
          );
          if (detailResponse.code !== 200 || !detailResponse.data) {
            throw new Error(detailResponse.message || "获取应用详情失败");
          }

          const appDetail = detailResponse.data;
          appDetail.name = `${appDetail.name} - 副本`;
          delete appDetail.id;
          appDetail["createFlowDetailInput"] = appDetail.flowDetailDto;
          delete appDetail.flowDetailDto;

          const res = await api.sessionFlow.create(appDetail);

          if (res.code === 200) {
            this.$message.success("智能体复制成功");
            this.fetchAppList();
          } else {
            throw new Error(res.message || "复制失败");
          }
        } catch (error) {
          console.error("复制应用失败:", error);
          this.$showFriendlyError(error, "复制失败，请稍后重试");
        }
      }
      this.copyDialogVisible = false;
      this.currentApp = null;
    },
    async handleShare(app) {
      this.currentApp = app;
      this.shareDialogVisible = true;
      this.shareLink = generateShareLink(app.id);
      this.linkCopied = false;
      this.qrCodeUrl = "";

      // 生成二维码
      try {
        const { generateQRCode } = await import('@/utils');
        this.qrCodeUrl = await generateQRCode(this.shareLink, {
          width: 140,
          height: 140,
          color: '#000000',
          backgroundColor: '#FFFFFF',
          margin: 2
        });
      } catch (error) {
        console.error('生成二维码失败:', error);
        this.qrCodeUrl = "";
      }
    },
    handleSettings(app) {
      this.$router.push(`/create/app/settings/${app.id}`);
    },
    async copyShareLink() {
      try {
        const success = await copyToClipboard(this.shareLink);
        if (success) {
          this.linkCopied = true;
          this.$message.success("链接已复制到剪贴板");
        } else {
          this.$showFriendlyError(null, "复制失败，请手动复制");
        }
      } catch (err) {
        console.error("复制失败:", err);
        this.$showFriendlyError(err, "复制失败，请手动复制");
      }
    },
    handleQRCodeError() {
      console.error('二维码加载失败');
      this.qrCodeUrl = "";
    },
    // 格式化更新时间
    formatUpdateTime(time) {
      if (!time) return '';
      const date = new Date(time);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 显示更多操作菜单
    showMoreActions(app, event) {
      this.currentApp = app;
      this.showMoreMenu = true;

      // 计算菜单位置
      const rect = event.target.getBoundingClientRect();
      this.moreMenuStyle = {
        position: 'fixed',
        top: rect.bottom + 5 + 'px',
        left: rect.left - 100 + 'px',
        zIndex: 1000
      };
    },
    // 隐藏更多操作菜单
    hideMoreMenu() {
      this.showMoreMenu = false;
    },
    // 删除智能体
    handleDelete() {
      this.deleteDialogVisible = true;
    },
    // 确认删除
    async confirmDelete() {
      if (!this.currentApp) {
        this.$message.error('未找到要删除的智能体');
        return;
      }
      this.deleteLoading = true;
      try {
        // 调用删除接口
        const response = await api.sessionFlow.deleteApp(this.currentApp.id);
        if (response.code === 200) {
          this.$message.success('删除成功');
          this.fetchAppList();
        } else {
          throw new Error(response.message || '删除失败');
        }
      } catch (error) {
        console.error('删除智能体失败:', error);
        this.$showFriendlyError(error, '删除失败，请稍后重试');
      } finally {
        this.deleteDialogVisible = false;
        this.currentApp = null;
        this.hideMoreMenu();
        this.deleteLoading = false;
      }
    },
    // 处理搜索（防抖）
    handleSearch() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.fetchAppList(false);
      }, 500);
    },
    // 立即搜索（回车键触发）
    handleSearchImmediate() {
      // 清除防抖定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      // 立即执行搜索
      this.fetchAppList(false);
    },
    // 处理分类变化
    handleCategoryChange() {
      // 重新从第一页开始加载
      this.fetchAppList(false);
    },
    // 添加滚动监听
    addScrollListener() {
      const appPage = document.querySelector('.app-page');
      if (appPage) {
        appPage.addEventListener('scroll', this.handleScroll);
      }
    },
    // 移除滚动监听
    removeScrollListener() {
      const appPage = document.querySelector('.app-page');
      if (appPage) {
        appPage.removeEventListener('scroll', this.handleScroll);
      }
    },
    // 处理滚动事件
    handleScroll() {
      const appPage = document.querySelector('.app-page');
      if (!appPage || this.loadingMore || this.noMoreData || this.loading) {
        return;
      }

      const scrollTop = appPage.scrollTop;
      const scrollHeight = appPage.scrollHeight;
      const clientHeight = appPage.clientHeight;

      // 当滚动到距离底部100px时开始加载更多
      if (scrollTop + clientHeight >= scrollHeight - 100) {
        this.loadMore();
      }
    },
    // 加载更多数据
    loadMore() {
      if (this.loadingMore || this.noMoreData || this.loading) {
        return;
      }
      this.fetchAppList(true);
    },
    // 选择智能体类型
    selectAgentType(type) {
      this.selectedAgentType = type;
      this.agentForm.type = type;
    },
    // 创建智能体
    async createAgent() {
      // 表单验证
      if (!this.agentForm.name.trim()) {
        this.$message.error('请输入智能体名称');
        return;
      }
      if (!this.agentForm.description.trim()) {
        this.$message.error('请输入智能体描述');
        return;
      }
      if (!this.agentForm.introduction.trim()) {
        this.$message.error('请输入开场介绍');
        return;
      }
      if (!this.agentForm.type) {
        this.$message.error('请选择智能体类型');
        return;
      }

      try {
        // 这里可以调用创建智能体的API
        console.log('创建智能体:', this.agentForm);
        this.$message.success('智能体创建成功');
        this.showCreateDialog = false;
        this.resetForm();
        this.fetchAppList(); // 刷新列表
      } catch (error) {
        console.error('创建智能体失败:', error);
        this.$showFriendlyError(error, '创建智能体失败，请重试');
      }
    },
    // 重置表单
    resetForm() {
      this.selectedAgentType = '';
      this.agentForm = {
        avatar: '',
        name: '',
        description: '',
        introduction: '',
        type: '',
        setting: '',
        isPublic: false
      };
    }
  },
};
</script>

<style lang="scss" scoped>
.app-page {
  width: 100%;
  height: 100vh;
  background-color: #f2f6fc;
  position: relative;
  overflow-y: auto; // 允许Y轴滚动
}

.app-list {
  width: 100%;
  min-height: 100%;
}

.main-content {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 0.5px solid #eee;
  padding: 0.8rem 1.07rem; // 30px 40px转换为rem
  min-height: calc(100vh - 2.67rem); // 100px转换为rem
  box-sizing: border-box;
}

// 页面标题区域
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.83rem; // 31px转换为rem

  .page-title {
    font-size: 0.64rem; // 24px转换为rem
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #000;
    margin: 0;
    line-height: 0.88rem; // 33px转换为rem
  }

  .create-agent-btn {
    background-color: #256dff;
    border-radius: 0.21rem; // 8px转换为rem
    width: 3.09rem; // 116px转换为rem
    height: 0.96rem; // 36px转换为rem
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    gap: 0.27rem; // 10px转换为rem

    .btn-icon {
      width: 0.27rem; // 10px转换为rem
      height: 0.27rem; // 10px转换为rem
    }

    span {
      color: #fff;
      font-size: 0.37rem; // 14px转换为rem
      line-height: 0.53rem; // 20px转换为rem
    }

    &:hover {
      background-color: #1e5ce6;
    }
  }
}

// 搜索筛选区域
.search-filter-section {
  display: flex;
  gap: 0.53rem; // 20px转换为rem
  margin-bottom: 0.85rem; // 32px转换为rem

  .search-box {
    background-color: #f4f6f8;
    border-radius: 8px;
    width: 6.4rem; // 240px转换为rem
    height: 0.96rem; // 36px转换为rem
    display: flex;
    align-items: center;
    padding: 0 0.35rem; // 13px转换为rem
    gap: 0.32rem; // 12px转换为rem

    .search-icon {
      width: 0.4rem; // 15px转换为rem
      height: 0.43rem; // 16px转换为rem
      flex-shrink: 0;
    }

    .search-input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      color: #000;
      font-size: 0.37rem; // 14px转换为rem
      line-height: 0.53rem; // 20px转换为rem

      &::placeholder {
        color: #bababa;
      }
    }
  }

  .category-select {
    width: 4.27rem; // 160px转换为rem

    :deep(.el-input) {
      .el-input__inner {
        background-color: #f4f6f8;
        border: none;
        border-radius: 0.21rem; // 8px转换为rem
        height: 0.96rem; // 36px转换为rem
        line-height: 0.96rem; // 36px转换为rem
        color: #000;
        font-size: 0.37rem; // 14px转换为rem
        padding: 0 0.8rem 0 0.43rem; // 0 30px 0 16px转换为rem

        &::placeholder {
          color: #000;
        }
      }

      .el-input__suffix {
        right: 0.43rem; // 16px转换为rem

        .el-input__suffix-inner {
          .el-select__caret {
            color: #666;
            font-size: 0.32rem; // 12px转换为rem
          }
        }
      }
    }
  }
}

// 下拉选择器弹出层样式
:deep(.category-dropdown-popper) {
  .el-select-dropdown__item {
    font-size: 0.37rem; // 14px转换为rem
    color: #000;

    &.selected {
      color: #256dff;
      font-weight: 500;
    }

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(0.53rem); // 20px转换为rem
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 智能体网格布局
.agents-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.53rem; // 20px转换为rem
  margin-bottom: 0.53rem; // 20px转换为rem

  // 响应式布局
  @media (max-width: 1600px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 8rem; // 300px转换为rem
  margin: 1.07rem 0; // 40px 0转换为rem

  .empty-content {
    text-align: center;

    .empty-icon {
      font-size: 1.28rem; // 48px转换为rem
      margin-bottom: 0.43rem; // 16px转换为rem
    }

    .empty-text {
      color: #999;
      font-size: 0.43rem; // 16px转换为rem
      line-height: 0.64rem; // 24px转换为rem
    }
  }
}

// 智能体卡片样式
.agent-card {
  background-color: #fff;
  border-radius: 8px;
  border: 0.5px solid #ebecf1;
  width: 13.6rem; // 510px转换为rem (510/37.5 = 13.6rem)
  height: 5.07rem; // 190px转换为rem (190/37.5 = 5.07rem)
  padding: 0.53rem 0.43rem; // 20px 16px转换为rem
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;

  &:hover {
    box-shadow: 0px 0px 10px 0px rgba(190, 190, 190, 0.3);
    border-color: #dfe1e8;
  }

  .card-header {
    display: flex;
    gap: 0.32rem; // 12px转换为rem
    margin-bottom: 0.21rem; // 8px转换为rem

    .agent-avatar {
      flex-shrink: 0;

      :deep(.el-avatar) {
        width: 1.17rem; // 44px转换为rem
        height: 1.17rem; // 44px转换为rem
        font-size: 0.43rem; // 16px转换为rem
      }
    }

    .agent-info {
      flex: 1;
      min-width: 0;

      .agent-name {
        font-size: 0.43rem; // 16px转换为rem
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        color: #000;
        margin: 0 0 0.21rem 0; // 0 0 8px 0转换为rem
        line-height: 0.59rem; // 22px转换为rem
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .agent-description {
        color: #666;
        font-size: 0.37rem; // 14px转换为rem
        line-height: 0.53rem; // 20px转换为rem
        margin-bottom: 0.21rem; // 8px转换为rem
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .agent-tags {
        display: flex;
        gap: 0.21rem; // 8px转换为rem

        .agent-type-tag {
          border-radius: 0.11rem; // 4px转换为rem
          height: 0.53rem; // 20px转换为rem
          padding: 0 0.13rem; // 0 5px转换为rem
          display: flex;
          align-items: center;

          &.light {
            background-color: #edf3fe;
            color: #256dff;
          }

          &.knowledge {
            background-color: #fff8e9;
            color: #b58326;
          }

          span {
            font-size: 0.32rem; // 12px转换为rem
            line-height: 0.48rem; // 18px转换为rem
            white-space: nowrap;
          }
        }

        .category-tag {
          background-color: #f4f6f8;
          border-radius: 0.11rem; // 4px转换为rem
          height: 0.53rem; // 20px转换为rem
          padding: 0 0.11rem; // 0 4px转换为rem
          display: flex;
          align-items: center;
          gap: 0.11rem; // 4px转换为rem

          .tag-icon {
            width: 0.32rem; // 12px转换为rem
            height: 0.29rem; // 11px转换为rem
          }

          span {
            font-size: 0.32rem; // 12px转换为rem
            color: #888;
            line-height: 0.48rem; // 18px转换为rem
          }
        }
      }
    }
  }

  .update-time {
    color: #bababa;
    font-size: 0.32rem; // 12px转换为rem
    line-height: 0.53rem; // 20px转换为rem
    margin: 0.21rem 0 0 1.49rem; // 8px 0 0 56px转换为rem
  }

  .card-divider {
    width: 12.59rem; // 472px转换为rem
    height: 0.027rem; // 1px转换为rem
    background: url('@/assets/app/divider-line.png') no-repeat;
    background-size: 100% 100%;
    margin: 0.4rem 0 0.27rem 0; // 15px 0 10px 0转换为rem
  }

  .card-actions {
    width: 12.53rem; // 470px转换为rem
    height: 0.8rem; // 30px转换为rem
    margin: 0.27rem 0 0.32rem 0.48rem; // 10px 0 12px 18px转换为rem
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left-section {
      display: flex;
      align-items: center;

      .config-icon {
        width: 0.53rem; // 20px转换为rem
        height: 0.53rem; // 20px转换为rem
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .right-section {
      display: flex;
      align-items: center;
      gap: 0.21rem; // 8px转换为rem

      .config-btn {
        background-color: rgba(244, 246, 248, 1);
        border-radius: 0.11rem; // 4px转换为rem
        height: 0.8rem; // 30px转换为rem
        width: 1.39rem; // 52px转换为rem
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          color: rgba(0, 0, 0, 1);
          font-size: 0.37rem; // 14px转换为rem
          text-align: center;
          white-space: nowrap;
          line-height: 0.53rem; // 20px转换为rem
        }

        &:hover {
          background-color: #e8eaed;
        }
      }

      .chat-btn {
        height: 0.8rem; // 30px转换为rem
        background-color: #fff;
        border: none;
        width: 2.13rem; // 80px转换为rem
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.11rem; // 4px转换为rem

        span {
          color: rgba(0, 0, 0, 1);
          font-size: 0.37rem; // 14px转换为rem
          text-align: center;
          white-space: nowrap;
          line-height: 0.53rem; // 20px转换为rem
        }

        i {
          color: rgba(0, 0, 0, 1);
          font-size: 0.32rem; // 12px转换为rem
          animation: rotate 1s linear infinite;
        }

        &.loading {
          cursor: not-allowed;
          opacity: 0.8;
        }

        &:hover:not(.loading) {
          background-color: #f5f5f5;
        }
      }

      .more-btn {
        background-color: #fff;
        border-radius: 0.11rem; // 4px转换为rem
        height: 0.8rem; // 30px转换为rem
        width: 0.8rem; // 30px转换为rem
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        .dots {
          color: #666;
          font-size: 0.43rem; // 16px转换为rem
          font-weight: bold;
          line-height: 1;
        }

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

// 加载更多区域
.load-more-section {
  margin-top: 0.53rem; // 20px转换为rem
  padding: 0.53rem 0; // 20px 0转换为rem

  .load-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.21rem; // 8px转换为rem
    color: #666;
    font-size: 0.37rem; // 14px转换为rem

    i {
      font-size: 0.43rem; // 16px转换为rem
      animation: rotate 1s linear infinite;
    }
  }

  .no-more-data {
    display: flex;
    justify-content: center;
    color: #999;
    font-size: 0.37rem; // 14px转换为rem
    padding: 0.27rem 0; // 10px 0转换为rem
  }
}

// 更多操作菜单
.more-actions-menu {
  background-color: #fff;
  border-radius: 0.21rem; // 8px转换为rem
  box-shadow: 0px 0.11rem 0.32rem rgba(0, 0, 0, 0.15); // 4px 12px转换为rem
  border: 1px solid #eee;
  padding: 0.21rem 0; // 8px 0转换为rem
  min-width: 3.2rem; // 120px转换为rem

  .menu-item {
    padding: 0.21rem 0.43rem; // 8px 16px转换为rem
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.37rem; // 14px转换为rem
    color: #000;
    line-height: 0.53rem; // 20px转换为rem

    &:hover {
      background-color: #f5f7fa;
    }

    &.delete {
      color: #f56c6c;

      &:hover {
        background-color: #fef0f0;
      }
    }

    .menu-icon {
      width: 0.37rem; // 14px转换为rem
      height: 0.37rem; // 14px转换为rem
    }
  }
}

.app-card {
  position: relative;
  width: 100%;
  height: 195px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  border: 1px solid #eaeaea;
  box-sizing: border-box;
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
  transform-origin: center;

  &:hover {
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }



}



// 分享对话框样式
:deep(.share-dialog) {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.share-dialog-content {
  background-color: #fff;
  border-radius: 8px;
  width: 100%;
  height: 481px;
  position: relative;

  .share-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    height: 26px;

    .share-title {
      color: #000;
      font-size: 18px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 26px;
    }

    .close-icon {
      width: 12px;
      height: 12px;
      cursor: pointer;
      color: #666;
      font-size: 12px;

      &:hover {
        color: #333;
      }
    }
  }

  .share-body {
    padding: 32px 24px 37px 24px;

    .link-section {
      background-color: #f4f6f8;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 28px;

      .link-title {
        display: block;
        color: #000;
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 20px;
      }

      .link-container {
        background-color: #fff;
        border-radius: 8px;
        border: 0.5px solid #dfe1e8;
        height: 54px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;

        .link-text {
          color: #256dff;
          font-size: 16px;
          line-height: 22px;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .copy-btn {
          background-color: #256dff;
          border-radius: 4px;
          height: 28px;
          width: 72px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background-color 0.3s;

          &:hover {
            background-color: #1e5ce6;
          }

          .copy-text {
            color: #fff;
            font-size: 12px;
            line-height: 18px;
          }
        }
      }
    }

    .qr-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 166px;
      margin: 0 auto;

      .qr-wrapper {
        background-color: #f4f6f8;
        border-radius: 8px;
        height: 166px;
        width: 166px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;

        .qr-code {
          width: 140px;
          height: 140px;
        }

        .qr-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          color: #666;
          font-size: 14px;

          i {
            font-size: 16px;
            animation: rotate 1s linear infinite;
          }
        }
      }

      .qr-text {
        color: #000;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
      }
    }
  }
}

// 删除对话框样式
:deep(.delete-dialog) {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.delete-dialog-content {
  background-color: #fff;
  border-radius: 8px;
  width: 100%;
  height: 260px;
  position: relative;

  .delete-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    height: 26px;

    .delete-title {
      color: #000;
      font-size: 18px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 26px;
    }

    .close-icon {
      width: 12px;
      height: 12px;
      cursor: pointer;
      color: #666;
      font-size: 12px;

      &:hover {
        color: #333;
      }
    }
  }

  .delete-body {
    padding: 32px 24px 80px 24px;

    .warning-section {
      display: flex;
      align-items: center;
      gap: 8px;

      .warning-icon {
        width: 20px;
        height: 20px;
        color: #f56c6c;
        font-size: 20px;
        flex-shrink: 0;
      }

      .warning-text {
        color: #000;
        font-size: 18px;
        line-height: 26px;
      }
    }
  }

  .delete-footer {
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
    padding: 16px 24px;

    .cancel-btn {
      background-color: #fff;
      border-radius: 4px;
      height: 36px;
      width: 92px;
      border: 0.5px solid #256dff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      span {
        color: #256dff;
        font-size: 14px;
        line-height: 20px;
      }
    }

    .delete-btn {
      background-color: #ff0000;
      border-radius: 4px;
      height: 36px;
      width: 92px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #e60000;
      }

      span {
        color: #fff;
        font-size: 14px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        line-height: 20px;
      }
    }
  }
}

// 复制对话框样式
.copy-dialog-content {
  text-align: center;
  padding: 20px 0;
}

// 加载状态
.loading-container {
  width: 100%;
  height: 10.67rem; // 400px转换为rem
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .loading-text {
    color: #909399;
    margin-top: 3.2rem; // 120px转换为rem
    font-size: 0.37rem; // 14px转换为rem
  }
}

// 全局样式
:deep(.el-avatar > img) {
  width: 100%;
  height: 100%;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 创建智能体弹窗样式
:deep(.create-agent-dialog) {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.create-agent-dialog-content {
  background-color: #fff;
  border-radius: 8px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    height: 26px;

    .dialog-title {
      color: #000;
      font-size: 18px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 26px;
    }

    .close-icon {
      width: 12px;
      height: 12px;
      cursor: pointer;
      color: #666;
      font-size: 12px;

      &:hover {
        color: #333;
      }
    }
  }

  .agent-type-selection {
    display: flex;
    gap: 20px;
    padding: 32px 24px;
    border-bottom: 1px solid #f0f0f0;

    .agent-type-card {
      flex: 1;
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        background-color: #f0f2f5;
      }

      &.active {
        border-color: #256dff;
        background-color: #edf3fe;
      }

      .agent-type-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 12px;
      }

      .agent-type-name {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: #000;
        margin-bottom: 8px;
      }

      .agent-type-desc {
        display: block;
        font-size: 14px;
        color: #666;
        line-height: 20px;
      }
    }
  }

  .form-section {
    padding: 24px;

    .form-item {
      margin-bottom: 24px;

      .form-label {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
        color: #000;
        font-weight: 500;

        .required {
          color: #f56c6c;
          margin-left: 4px;
        }
      }

      .avatar-upload {
        .avatar-preview {
          width: 80px;
          height: 80px;
          border-radius: 8px;
          border: 2px dashed #d9d9d9;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #256dff;
          }

          .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
          }

          .avatar-placeholder {
            color: #999;
            font-size: 24px;
          }
        }

        .upload-tip {
          font-size: 12px;
          color: #999;
        }
      }

      .description-header {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;

        span {
          font-size: 12px;
          color: #256dff;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .intro-tip {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        line-height: 16px;
      }

      .setting-tip {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .public-switch {
        display: flex;
        align-items: center;
        gap: 8px;

        .public-tip {
          font-size: 12px;
          color: #666;
        }
      }

      .knowledge-section {
        .knowledge-actions {
          display: flex;
          gap: 12px;
          margin-bottom: 8px;

          .knowledge-action {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background-color: #f0f2f5;
            }

            .action-icon {
              width: 12px;
              height: 12px;
            }

            span {
              font-size: 12px;
              color: #666;
            }
          }
        }

        .upload-tip {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .form-input,
    .form-textarea,
    .form-select {
      width: 100%;

      :deep(.el-input__inner) {
        border-radius: 4px;
        border: 1px solid #d9d9d9;

        &:focus {
          border-color: #256dff;
        }
      }

      :deep(.el-textarea__inner) {
        border-radius: 4px;
        border: 1px solid #d9d9d9;

        &:focus {
          border-color: #256dff;
        }
      }
    }
  }

  .dialog-footer {
    padding: 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;

    .create-btn {
      background-color: #256dff;
      border-radius: 4px;
      height: 40px;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.3s;
      gap: 8px;

      &:hover {
        background-color: #1e5ce6;
      }

      .btn-icon {
        width: 16px;
        height: 16px;
      }

      span {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
</style>
