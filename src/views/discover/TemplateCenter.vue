<template>
  <div class="template-center-container">
    <div class="coming-soon-content">
      <div class="icon-container">
        <i class="iconfont icon-mobanzhongxin"></i>
      </div>
      <h2 class="title">模型仓库</h2>
      <p class="subtitle">正在努力打造中...</p>
      <div class="description">
        <p>我们正在为您精心准备丰富的应用模板</p>
        <p>敬请期待更多精彩内容</p>
      </div>
      <div class="progress-indicator">
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
        <span class="progress-text">开发进度 60%</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplateCenter',
  data() {
    return {}
  },
  mounted() {
    // 可以在这里添加一些动画效果
  }
}
</script>

<style lang="scss" scoped>
.template-center-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40px;
}

.coming-soon-content {
  text-align: center;
  background: white;
  border-radius: 16px;
  padding: 60px 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.icon-container {
  margin-bottom: 24px;

  i {
    font-size: 80px;
    color: #2187FA;
    opacity: 0.8;
  }
}

.title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.subtitle {
  font-size: 20px;
  color: #2187FA;
  margin-bottom: 24px;
  font-weight: 500;
}

.description {
  margin-bottom: 32px;

  p {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.progress-indicator {
  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;

    .progress-fill {
      width: 60%;
      height: 100%;
      background: linear-gradient(90deg, #2187FA 0%, #45BCFD 100%);
      border-radius: 4px;
      animation: progressAnimation 2s ease-in-out infinite alternate;
    }
  }

  .progress-text {
    font-size: 14px;
    color: #2187FA;
    font-weight: 500;
  }
}

@keyframes progressAnimation {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .template-center-container {
    padding: 20px;
  }

  .coming-soon-content {
    padding: 40px 24px;
  }

  .icon-container i {
    font-size: 60px;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 18px;
  }

  .description p {
    font-size: 14px;
  }
}
</style>
