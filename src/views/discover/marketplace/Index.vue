<template>
  <div class="marketplace-page">
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 页面头部 -->
      <div class="header-section">
        <div class="page-header">
          <div class="header-left">
            <div class="title-section">
              <div class="title-row">
                <h1 class="page-title">应用中心</h1>
                <p class="page-desc">发现和使用优质的AI应用</p>
              </div>
            </div>
            <div class="search-section">
              <div class="search-box">
                <img class="search-icon" :src="require('@/assets/marketplace/search-icon.png')" alt="搜索" />
                <span class="search-placeholder">搜索应用名称</span>
              </div>
            </div>
          </div>
          <div class="header-right">
            <div class="promo-banner">
              <div class="promo-text">
                多种应用一键体验，<br />
                快去试试吧～
              </div>
            </div>
          </div>
        </div>

        <!-- 分类筛选栏 -->
        <div class="category-bar">
          <div 
            class="category-item" 
            :class="{ active: category === '' }"
            @click="handleCategoryChange('')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-all.png')" alt="全部" />
            <span>全部</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'sales' }"
            @click="handleCategoryChange('sales')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-sales.png')" alt="销售" />
            <span>销售</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'purchase' }"
            @click="handleCategoryChange('purchase')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-purchase.png')" alt="采购" />
            <span>采购</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'logistics' }"
            @click="handleCategoryChange('logistics')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-logistics.png')" alt="物流" />
            <span>物流</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'finance' }"
            @click="handleCategoryChange('finance')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-finance.png')" alt="财务" />
            <span>财务</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'customer' }"
            @click="handleCategoryChange('customer')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-customer.png')" alt="客服" />
            <span>客服</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'legal' }"
            @click="handleCategoryChange('legal')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-legal.png')" alt="法务" />
            <span>法务</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'quality' }"
            @click="handleCategoryChange('quality')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-quality.png')" alt="质管" />
            <span>质管</span>
          </div>
          <div 
            class="category-item" 
            :class="{ active: category === 'other' }"
            @click="handleCategoryChange('other')"
          >
            <img class="category-icon" :src="require('@/assets/marketplace/category-other.png')" alt="其他" />
            <span>其他</span>
          </div>

          <div class="sort-section">
            <!-- 最热门下拉菜单 -->
            <div class="dropdown-container">
              <div 
                class="sort-item dropdown-trigger" 
                :class="{ active: hotDropdownOpen }"
                @click="toggleHotDropdown"
              >
                <span>{{ selectedHotOption }}</span>
                <i class="el-icon-caret-bottom" :class="{ 'rotate': hotDropdownOpen }"></i>
              </div>
              <div v-if="hotDropdownOpen" class="dropdown-menu">
                <div 
                  class="dropdown-item" 
                  :class="{ active: selectedHotOption === '最热门' }"
                  @click="selectHotOption('最热门')"
                >
                  最热门
                </div>
                <div 
                  class="dropdown-item" 
                  :class="{ active: selectedHotOption === '最新发布' }"
                  @click="selectHotOption('最新发布')"
                >
                  最新发布
                </div>
              </div>
            </div>

            <!-- 全部应用下拉菜单 -->
            <div class="dropdown-container">
              <div 
                class="sort-item dropdown-trigger" 
                :class="{ active: appTypeDropdownOpen }"
                @click="toggleAppTypeDropdown"
              >
                <span>{{ selectedAppType }}</span>
                <i class="el-icon-caret-bottom" :class="{ 'rotate': appTypeDropdownOpen }"></i>
              </div>
              <div v-if="appTypeDropdownOpen" class="dropdown-menu">
                <div 
                  class="dropdown-item" 
                  :class="{ active: selectedAppType === '全部应用' }"
                  @click="selectAppType('全部应用')"
                >
                  全部应用
                </div>
                <div 
                  class="dropdown-item" 
                  :class="{ active: selectedAppType === '轻量智能体' }"
                  @click="selectAppType('轻量智能体')"
                >
                  轻量智能体
                </div>
                <div 
                  class="dropdown-item" 
                  :class="{ active: selectedAppType === '知识智能体' }"
                  @click="selectAppType('知识智能体')"
                >
                  知识智能体
                </div>
                <div 
                  class="dropdown-item" 
                  :class="{ active: selectedAppType === '工作流' }"
                  @click="selectAppType('工作流')"
                >
                  工作流
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 应用展示区域 -->
        <div class="scrollable-content">
          <div class="apps-section">
            <div class="apps-grid">
              <!-- 固定应用 -->
              <div 
                v-for="app in fixedApps" 
                :key="app.id" 
                class="app-card"
                @click="handleDetail(app)"
              >
                <div class="card-image" :style="{ backgroundImage: `url(${app.backgroundImage})` }">
                  <div class="image-overlay">
                    <div class="overlay-content">
                      <div class="user-info">
                        <img :src="app.userAvatar" alt="用户头像" class="user-avatar" />
                        <span class="user-name">{{ app.userName }}</span>
                      </div>
                      <div class="app-tag" :class="app.tagClass">
                        {{ app.tagName }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-content">
                  <h3 class="app-name">{{ app.name }}</h3>
                  <p class="app-desc">{{ app.description }}</p>
                </div>
              </div>

              <!-- 动态应用 -->
              <div 
                v-for="app in dynamicApps" 
                :key="'dynamic-' + app.id" 
                class="app-card"
                @click="handleDetail(app)"
              >
                <div class="card-image" :style="{ backgroundImage: `url(${app.backgroundImage})` }">
                  <div class="image-overlay">
                    <div class="overlay-content">
                      <div class="user-info">
                        <img :src="app.userAvatar" alt="用户头像" class="user-avatar" />
                        <span class="user-name">{{ app.userName }}</span>
                      </div>
                      <div class="app-tag" :class="app.tagClass">
                        {{ app.tagName }}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-content">
                  <h3 class="app-name">{{ app.name }}</h3>
                  <p class="app-desc">{{ app.description }}</p>
                </div>
              </div>
            </div>

            <!-- 移除装饰性滚动条 -->
          </div>
        </div>

        <!-- 固定底部分页 -->
        <div class="pagination-section">
          <el-pagination
            :current-page="pagination.currentPage"
            :page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="allApps.length"
            layout="sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DiscoverMarketplacePage',
  data() {
    return {
      searchKeyword: '',
      category: '',
      sort: 'hot',
      jumpPage: '',
      // 下拉菜单状态
      hotDropdownOpen: false, // 最热门下拉菜单状态（默认闭合）
      appTypeDropdownOpen: false, // 全部应用下拉菜单状态（默认闭合）
      selectedHotOption: '最热门', // 当前选中的热门选项
      selectedAppType: '全部应用', // 当前选中的应用类型
      allApps: [
        // 固定应用
        {
          id: 1,
          name: '行程规划助手',
          description: '帮你推荐景点、规划公共交通、搜索周...',
          backgroundImage: require('@/assets/marketplace/app-bg-1.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'all'
        },
        {
          id: 2,
          name: 'Midjourney绘画',
          description: '使用midjourney进行图片创作',
          backgroundImage: require('@/assets/marketplace/app-bg-2.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '知识智能体',
          tagClass: 'tag-knowledge',
          category: 'all'
        },
        {
          id: 3,
          name: '编程大师',
          description: 'Talk is cheap, show you the code!',
          backgroundImage: require('@/assets/marketplace/app-bg-3.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '工作流',
          tagClass: 'tag-workflow',
          category: 'all'
        },
        {
          id: 4,
          name: '销售助手',
          description: '智能销售数据分析和客户管理工具',
          backgroundImage: require('@/assets/marketplace/app-bg-4.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'sales'
        },
        {
          id: 5,
          name: '采购管理系统',
          description: '智能采购流程管理和供应商评估',
          backgroundImage: require('@/assets/marketplace/app-bg-5.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'purchase'
        },
        {
          id: 6,
          name: '物流追踪助手',
          description: '实时物流信息追踪和路线优化',
          backgroundImage: require('@/assets/marketplace/app-bg-6.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'logistics'
        },
        {
          id: 7,
          name: '财务分析工具',
          description: '智能财务报表分析和预测',
          backgroundImage: require('@/assets/marketplace/app-bg-7.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '知识智能体',
          tagClass: 'tag-knowledge',
          category: 'finance'
        },
        {
          id: 8,
          name: '客服机器人',
          description: '智能客服对话和问题解答系统',
          backgroundImage: require('@/assets/marketplace/app-bg-1.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'customer'
        },
        {
          id: 9,
          name: '法务文档助手',
          description: '法律文档审查和合同分析工具',
          backgroundImage: require('@/assets/marketplace/app-bg-2.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '知识智能体',
          tagClass: 'tag-knowledge',
          category: 'legal'
        },
        {
          id: 10,
          name: '质量检测系统',
          description: '产品质量检测和缺陷识别',
          backgroundImage: require('@/assets/marketplace/app-bg-3.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '工作流',
          tagClass: 'tag-workflow',
          category: 'quality'
        },
        {
          id: 11,
          name: '通用工具集',
          description: '多功能办公辅助工具集合',
          backgroundImage: require('@/assets/marketplace/app-bg-4.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'other'
        },
        {
          id: 12,
          name: 'DeepSeek智能助手',
          description: '能够深度思考的DeepSeek智能助手',
          backgroundImage: require('@/assets/marketplace/app-bg-5.png'),
          userAvatar: require('@/assets/marketplace/user-avatar.png'),
          userName: '蒸汽丝带发明...',
          tagName: '轻量智能体',
          tagClass: 'tag-light',
          category: 'all'
        }
      ],
      apps: [
        {
          id: 1,
          name: '智能文档助手',
          description: '自动生成和编辑各类文档，提高办公效率',
          icon: 'el-icon-document',
          rating: 4.5,
          downloads: '2,345',
          tags: ['办公效率', '文档处理']
        },
        {
          id: 2,
          name: 'AI图像生成器',
          description: '使用AI技术生成和编辑各类图像',
          icon: 'el-icon-picture',
          rating: 4.8,
          downloads: '3,678',
          tags: ['内容创作', '图像处理']
        },
        {
          id: 3,
          name: '数据分析工具',
          description: '智能数据分析和可视化工具',
          icon: 'el-icon-data-line',
          rating: 4.6,
          downloads: '1,890',
          tags: ['数据处理', '可视化']
        },
        {
          id: 4,
          name: '代码助手',
          description: '智能代码生成和优化工具',
          icon: 'el-icon-cpu',
          rating: 4.7,
          downloads: '4,567',
          tags: ['开发工具', '编程']
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 100
      }
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.pagination.total / this.pagination.pageSize)
    },
    filteredApps() {
      let filtered = this.allApps
      
      // 根据分类筛选
      if (this.category && this.category !== '') {
        filtered = filtered.filter(app => app.category === this.category)
      }
      
      // 根据搜索关键词筛选
      if (this.searchKeyword) {
        filtered = filtered.filter(app => 
          app.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          app.description.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      
      return filtered
    },
    fixedApps() {
      // 显示前6个应用作为固定应用
      return this.filteredApps.slice(0, 6)
    },
    dynamicApps() {
      // 显示剩余应用作为动态应用
      return this.filteredApps.slice(6)
    }
  },
  methods: {
    // 分页相关方法
    handleSizeChange(newSize) {
      this.pagination.pageSize = newSize;
      this.pagination.currentPage = 1; // 重置到第一页
    },
    
    handleCurrentChange(newPage) {
      this.pagination.currentPage = newPage;
    },

    // 移除旧的分页方法，保留一些可能需要的方法
    prevPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
      }
    },
    
    nextPage() {
      if (this.pagination.currentPage < this.totalPages) {
        this.pagination.currentPage++;
      }
    },
    
    jumpToPage() {
      if (this.jumpPage >= 1 && this.jumpPage <= this.totalPages) {
        this.pagination.currentPage = this.jumpPage;
      }
      this.jumpPage = '';
    },
    handleInstall(app) {
      this.$message.success(`开始安装：${app.name}`)
    },
    handleDetail(app) {
      this.$message.info(`查看详情：${app.name}`)
    },
    handleCategoryChange(category) {
      this.category = category
      console.log('分类变更:', category)
      // 重置到第一页
      this.pagination.currentPage = 1
    },
    handleSortChange(sort) {
      this.sort = sort
      console.log('排序变更:', sort)
    },
    // 切换最热门下拉菜单
    toggleHotDropdown() {
      this.hotDropdownOpen = !this.hotDropdownOpen
    },
    // 切换应用类型下拉菜单
    toggleAppTypeDropdown() {
      this.appTypeDropdownOpen = !this.appTypeDropdownOpen
    },
    // 选择热门选项
    selectHotOption(option) {
      this.selectedHotOption = option
      this.hotDropdownOpen = false
      console.log('选择热门选项:', option)
    },
    // 选择应用类型
    selectAppType(type) {
      this.selectedAppType = type
      console.log('选择应用类型:', type)
      // 这里可以添加筛选逻辑
    },
    getTagClass(tag) {
      const tagMap = {
        '办公效率': 'tag-office',
        '内容创作': 'tag-content', 
        '数据处理': 'tag-data',
        '开发工具': 'tag-development',
        '客服': 'tag-customer',
        '财务': 'tag-finance',
        '法务': 'tag-legal',
        '质管': 'tag-quality',
        '其他': 'tag-other'
      }
      return tagMap[tag] || 'tag-default'
    }
  }
}
</script>

<style lang="scss" scoped>
.marketplace-page {
  width: 100%;
  height: 100%;
  background: #f3f4f5;
  display: flex;
  flex-direction: column;

  .main-content {
    position: relative;
    width: 100%;
    height: 100%;
    background: url('~@/assets/marketplace/main-background.png') no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;

    // 固定头部区域
    .header-section {
      height: 100%;
      flex-shrink: 0; // 不允许收缩
      padding: 1.43rem 2.14rem 0; // 顶部和左右内边距

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
        margin-bottom: 1.79rem;

        .header-left {
          flex: 0 0 auto;
          width: 35.71rem;

          .title-section {
            margin-bottom: 1.43rem;

            .title-row {
              display: flex;
              align-items: baseline;
              width: 100%;
            }

            .page-title {
              font-size: 2.29rem;
              font-weight: 500;
              color: #000;
              margin: 0;
              font-family: PingFangSC-Medium;
              line-height: 1.2;
            }

            .page-desc {
              font-size: 1.14rem;
              color: #666;
              margin: 0;
              line-height: 1.4;
              margin-left: 1rem;
            }
          }

          .search-section {
            .search-box {
              position: relative;
              width: 28.57rem;
              height: 3.43rem;
              background: white;
              border: 2px solid #256dff;
              border-radius: 0.57rem;
              display: flex;
              align-items: center;
              cursor: pointer;

              .search-icon {
                position: absolute;
                left: 1.07rem;
                width: 1.29rem;
                height: 1.29rem;
                object-fit: contain;
              }

              .search-placeholder {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                padding: 0 1.07rem 0 3.21rem;
                font-size: 1.14rem;
                color: #bbb;
                background: transparent;
                line-height: 1.4;
              }
            }
          }
        }

        .header-right {
          flex: 0 0 auto;
          width: 25rem;

          .promo-banner {
            background: url('~@/assets/marketplace/promo-banner-bg.png') no-repeat center;
            background-size: cover;
            width: 100%;
            height: 8.57rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.57rem;

            .promo-text {
              font-size: 1.29rem;
              font-weight: 700;
              color: #000;
              text-align: center;
              line-height: 1.4;
              font-family: AlimamaShuHeiTi-Bold;
            }
          }
        }
      }

      // 固定的分类栏
      .category-bar {
        display: flex;
        align-items: center;
        gap: 0.86rem;
        margin-bottom: 1.43rem; // 减小底部间距
        flex-wrap: wrap;

        .category-item {
          display: flex;
          align-items: center;
          gap: 0.57rem;
          padding: 0.86rem 1.14rem;
          border-radius: 0.57rem;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 1rem;
          color: #000;
          background: transparent;
          height: auto;
          min-width: 5.71rem;

          &.active {
            background-color: rgba(242, 246, 255, 1);
            color: #000;
          }

          &:hover:not(.active) {
            background-color: #f5f7fa;
          }

          .category-icon {
            width: 1.14rem;
            height: 1.14rem;
            object-fit: contain;
          }

          span {
            line-height: 1.4;
            white-space: nowrap;
          }
        }

        .sort-section {
          margin-left: auto;
          display: flex;
          gap: 1.14rem;

          .dropdown-container {
            position: relative;
            display: inline-block;
          }

          .sort-item {
            display: flex;
            align-items: center;
            gap: 0.57rem;
            padding: 0.86rem 1.14rem;
            background-color: rgba(244, 246, 248, 1);
            border-radius: 0.57rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            color: #000;
            border: 1px solid transparent;
            height: auto;
            min-width: 8.57rem;

            &:hover {
              background-color: #e8ebf0;
            }

            &.active {
              background-color: #fff;
              border: 1px solid rgba(37, 109, 255, 1);
            }

            i {
              font-size: 0.86rem;
              transition: transform 0.3s ease;

              &.rotate {
                transform: rotate(180deg);
              }
            }

            span {
              line-height: 1.4;
            }
          }

          .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background-color: white;
            border: 1px solid #ebecf1;
            border-radius: 0.57rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 10;
            margin-top: 0.57rem;
            padding: 0.57rem 0;

            .dropdown-item {
              padding: 0.86rem 1.14rem;
              font-size: 1rem;
              color: #000;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background-color: #f5f7fa;
              }

              &.active {
                background-color: #f4f6f8;
                color: #000;
              }
            }
          }
        }
      }
    }

    // 可滚动的应用卡片区域
    .scrollable-content {
      flex: 1; // 占据剩余空间
      overflow-y: auto; // 允许垂直滚动
      // padding: 0 2.14rem 6rem; // 底部添加足够的内边距，避免被分页遮挡
      
      .apps-section {
        .apps-grid {
          display: grid;
          grid-template-columns: repeat(6, 1fr);
          gap: 1.43rem;
          padding-bottom: 1.43rem; // 底部留出空间

          .app-card {
            background: white;
            border-radius: 0.86rem;
            border: 1px solid rgba(235, 236, 241, 1);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            height: 15.71rem;

            &:hover {
              box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
              transform: translateY(-2px);
            }

            .card-image {
              height: 11.43rem;
              background-size: cover;
              background-position: center;
              position: relative;
              border-radius: 0.86rem 0.86rem 0 0;

              .image-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: url('~@/assets/marketplace/card-overlay.png') no-repeat;
                background-size: 100% 100%;
                height: 4.29rem;
                padding: 1.14rem;

                .overlay-content {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-end;

                  .user-info {
                    display: flex;
                    align-items: center;
                    gap: 0.57rem;

                    .user-avatar {
                      width: 1.71rem;
                      height: 1.71rem;
                      border-radius: 50%;
                      object-fit: cover;
                    }

                    .user-name {
                      color: white;
                      font-size: 0.86rem;
                      opacity: 0.9;
                      line-height: 1.4;
                    }
                  }

                  .app-tag {
                    padding: 0.29rem 0.86rem;
                    border-radius: 0.29rem;
                    font-size: 0.86rem;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    line-height: 1.4;
                    white-space: nowrap;
                    max-width: 7.14rem;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    &.tag-light {
                      background-color: rgba(237, 243, 254, 1);
                      color: rgba(37, 109, 255, 1);
                    }

                    &.tag-knowledge {
                      background-color: rgba(255, 248, 233, 1);
                      color: rgba(181, 131, 38, 1);
                    }

                    &.tag-workflow {
                      background-color: rgba(255, 237, 239, 1);
                      color: rgba(255, 106, 106, 1);
                    }
                  }
                }
              }
            }

            .card-content {
              padding: 0.86rem 1.14rem;

              .app-name {
                font-size: 1.07rem;
                font-weight: 600;
                color: #000;
                margin: 0 0 0.57rem;
                line-height: 1.3;
                font-family: PingFangSC-Semibold;
              }

              .app-desc {
                font-size: 0.86rem;
                color: #888;
                margin: 0;
                line-height: 1.4;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
    }

    // 固定底部分页
    .pagination-section {
      background: white;
      border-radius: 0; // 移除圆角
      padding: .71rem;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 1px solid #ebecf1; // 添加顶部边框分隔

      // 使用::v-deep来覆盖Element UI的默认样式
      ::v-deep .el-pagination {
        .el-pager li.number.active {
          background-color: rgba(37, 109, 255, 1) !important;
          border-color: rgba(37, 109, 255, 1) !important;
          color: white !important;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1600px) {
    .main-content .scrollable-content .apps-section .apps-grid {
      grid-template-columns: repeat(5, 1fr);
    }
  }

  @media (max-width: 1200px) {
    .main-content .scrollable-content .apps-section .apps-grid {
      grid-template-columns: repeat(4, 1fr);
    }
    
    .main-content .header-section .page-header {
      gap: 2.14rem;
      
      .header-left {
        max-width: 32.14rem;
        
        .search-section .search-box {
          width: 22.86rem;
        }
      }
      
      .header-right .promo-banner {
        width: 18.57rem;
        height: 6.43rem;
      }
    }
  }

  @media (max-width: 900px) {
    .main-content .scrollable-content .apps-section .apps-grid {
      grid-template-columns: repeat(3, 1fr);
    }
    
    .main-content .header-section .page-header {
      flex-direction: column;
      gap: 1.43rem;
      align-items: center;
      
      .header-left {
        max-width: 100%;
        text-align: center;
        
        .search-section .search-box {
          width: 100%;
          max-width: 25rem;
        }
      }
      
      .header-right .promo-banner {
        width: 100%;
        max-width: 25rem;
        height: 5.71rem;
      }
    }
  }

  @media (max-width: 600px) {
    .main-content {
      .header-section {
        padding: 1.07rem 1.07rem 0;
      }
      
      .scrollable-content {
        padding: 0 1.07rem 6rem; // 移动端也保持底部内边距
      }
    }
    
    .main-content .scrollable-content .apps-section .apps-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .main-content .header-section .category-bar .sort-section {
      margin-left: 0;
      margin-top: 0.71rem;
      width: 100%;
    }
    
    .main-content .header-section .page-header {
      .header-left {
        .title-section .page-title {
          font-size: 1.71rem;
        }
        
        .search-section .search-box {
          height: 2.86rem;
        }
      }
    }
  }
}
</style>

