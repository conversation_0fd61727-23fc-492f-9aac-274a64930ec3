<template>
  <div class="page flex-col">
    <div class="section_1 flex-col">
      <div class="block_1 flex-row">
        <div class="group_1 flex-col">
          <div class="box_1 flex-row justify-between">
            <img
              class="image_1"
              referrerpolicy="no-referrer"
              src="./assets/img/logo.png"
            />
            <span class="text_1">元智启AI</span>
            <!-- <span class="text_1">AI</span> -->
          </div>
          <div class="text-wrapper_1">
            <span class="text_2">企业级</span>
            <span class="text_3">AI应用配置平台</span>
          </div>
          <span class="text_4">低成本、无门槛、AI应用轻松配</span>
        </div>
        <div class="group_2 flex-col">
          <!-- <span class="text_5">欢迎登录元智启AI</span> -->
          <span class="text_5">{{
            pageType === "login"
              ? "欢迎登录"
              : pageType === "register"
              ? "欢迎注册"
              : "重置密码"
          }}</span>
          <div
            class="text-wrapper_2 flex-row justify-between"
            v-if="pageType === 'login'"
          >
            <span
              :class="{ active: loginType === 'password' }"
              @click="switchLoginType('password')"
              class="text_6"
              v-if="pageType === 'login'"
              >密码登录</span
            >
            <span
              :class="{ active: loginType === 'sms' }"
              @click="switchLoginType('sms')"
              class="text_7"
              v-if="pageType === 'login'"
              >验证码登录</span
            >
          </div>
          <div class="box_2 flex-col" v-if="pageType === 'login'">
            <div
              class="section_2 flex-col"
              :style="{
                marginLeft: loginType === 'password' ? '109px' : '250px',
              }"
            ></div>
          </div>

          <!-- 表单区域 -->
          <el-form
            :model="loginForm"
            :rules="rules"
            ref="loginForm"
            class="login-form"
          >
            <!-- 用户名/手机号输入 -->
            <el-form-item prop="username" class="text-wrapper_6 flex-col">
              <el-input
                v-model="loginForm.username"
                :placeholder="
                  loginType === 'password' && pageType === 'login'
                    ? '请输入账号'
                    : '请输入手机号'
                "
                class="username-input"
              >
              </el-input>
            </el-form-item>

            <!-- 密码登录 -->
            <el-form-item
              prop="password"
              v-if="loginType === 'password' && pageType === 'login'"
              class="text-wrapper_3 flex-col"
            >
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入账号密码"
                class="password-input"
              >
              </el-input>
            </el-form-item>

            <!-- 注册密码 -->
            <el-form-item
              prop="password"
              v-if="pageType === 'register'"
              class="text-wrapper_3 flex-col"
            >
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                class="password-input"
              >
              </el-input>
            </el-form-item>

            <!-- 忘记密码新密码 -->
            <el-form-item
              prop="newPassword"
              v-if="pageType === 'resetPassword'"
              class="text-wrapper_3 flex-col"
            >
              <el-input
                v-model="loginForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                class="password-input"
              >
              </el-input>
            </el-form-item>

            <!-- 确认密码 (注册或忘记密码时显示) -->
            <el-form-item
              prop="confirmPassword"
              v-if="pageType === 'register' || pageType === 'resetPassword'"
              class="text-wrapper_3 flex-col"
            >
              <el-input
                v-model="loginForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                class="password-input"
              >
              </el-input>
            </el-form-item>

            <!-- 验证码登录或注册验证码或忘记密码验证码 -->
            <el-form-item
              prop="verifyCode"
              v-if="
                loginType === 'sms' ||
                pageType === 'register' ||
                pageType === 'resetPassword'
              "
              class="text-wrapper_3 flex-col"
            >
              <div class="verify-container">
                <el-input
                  v-model="loginForm.verifyCode"
                  placeholder="请输入验证码"
                  class="verify-input"
                  maxlength="6"
                >
                </el-input>
                <el-button
                  type="primary"
                  class="verify-button"
                  :disabled="timer > 0"
                  @click="showSlideCaptcha"
                >
                  {{ timer > 0 ? `${timer}秒后重试` : "获取验证码" }}
                </el-button>
              </div>
            </el-form-item>

            <!-- 登录按钮 -->
            <el-form-item class="login-button-container">
              <div class="text-wrapper_4 flex-col" @click="handleSubmit">
                <span class="text_9">{{
                  loading
                    ? pageType === "login"
                      ? "登录中..."
                      : pageType === "register"
                      ? "注册中..."
                      : "重置密码中..."
                    : pageType === "login"
                    ? "立即登录"
                    : pageType === "register"
                    ? "立即注册"
                    : "立即重置密码"
                }}</span>
              </div>
            </el-form-item>

            <!-- 底部区域 -->
            <div class="box_3 flex-row">
              <div class="section_3 flex-col">
                <!-- 30天自动登录选项 -->
                <div
                  class="block_2 flex-row justify-between"
                  v-if="pageType === 'login'"
                >
                  <div class="box_4 flex-col"></div>
                  <span class="text_10">30天内自动登录</span>
                </div>
              </div>
              <div class="section_4 flex-col">
                <div class="text-wrapper_5">

                  <span
                    class="text_12"
                    @click="switchPageType('register')"
                    v-if="pageType === 'login'"
                    >立即注册</span
                  >
                  <span class="text_11" v-if="pageType === 'register'"
                    >已有账号？</span
                  >
                  <span
                    class="text_12"
                    @click="switchPageType('login')"
                    v-if="pageType !== 'login'"
                    >立即登录</span
                  >
                  <span class="text_11" v-if="pageType === 'login'"></span>
                  <span
                    class="text_12"
                    @click="switchPageType('resetPassword')"
                    v-if="pageType === 'login'"
                    >忘记密码？</span
                  >
                </div>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 滑动验证码弹窗 -->
    <el-dialog
      :title="'安全验证'"
      :visible.sync="slideCaptchaVisible"
      :width="'340px'"
      :show-close="false"
      :close-on-click-modal="false"
      :custom-class="'slide-captcha-dialog'"
    >
      <div class="slide-container" v-loading="loading">
        <img
          :src="captchaData.backgroundImage"
          id="slide-background"
          alt="背景图片"
        />
        <img
          :src="captchaData.sliderImage"
          id="slide-puzzle"
          :style="{ left: puzzleLeft + 'px', top: puzzleTop + 'px' }"
          alt="滑块"
        />
        <div class="slider">
          <div
            class="slider-button"
            id="slider-button"
            @mousedown="dragStart"
            @touchstart="dragStart"
            :style="{ left: sliderLeft + 'px' }"
          ></div>
          <div
            class="slider-track"
            id="slider-track"
            :style="{ width: sliderLeft + 'px' }"
          ></div>
          <div class="slider-hint">向右拖动滑块填充拼图</div>
        </div>
        <div
          id="slide-result"
          :class="['result', resultStatus]"
          v-if="resultMessage"
        >
          {{ resultMessage }}
        </div>
      </div>
      <div class="dialog-footer">
        <el-button @click="resetCaptcha">重新获取</el-button>
        <el-button @click="slideCaptchaVisible = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- 租户选择弹框 -->
    <el-dialog
      title="选择要进入的账号"
      :visible.sync="tenantSelectVisible"
      width="400px"
      :close-on-click-modal="false"
      :show-close="true"
      custom-class="tenant-select-dialog"
    >
      <div class="tenant-list">
        <div
          v-for="tenant in tenantList"
          :key="tenant.tenantId"
          class="tenant-item"
          @click="selectTenant(tenant)"
        >
          <div class="tenant-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="tenant-info">
            <span class="tenant-name">{{ tenant.name || '个人账号' }}</span>
            <span class="tenant-type">{{ tenant.description || '' }}</span>
          </div>
          <div class="tenant-arrow">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";
import { handleLoginSuccess, handleTenantSelection } from "@/utils/auth";

export default {
  name: "LoginPage",
  data() {
    // 添加自定义验证器
    const validateConfirmPassword = (rule, value, callback) => {
      if (
        (this.pageType === "register" && value !== this.loginForm.password) ||
        (this.pageType === "resetPassword" &&
          value !== this.loginForm.newPassword)
      ) {
        callback(new Error("两次输入密码不一致"));
      } else {
        callback();
      }
    };

    // 添加密码格式验证器
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("密码不能为空"));
      } else if (value.length < 6 || value.length > 100) {
        callback(new Error("密码长度必须在6-100个字符之间"));
      } else if (!/^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]*$/.test(value)) {
        callback(new Error("密码只能包含字母、数字和特殊字符"));
      } else if (new Set(value.split('')).size < 4) {
        callback(new Error("密码必须包含至少4个不同的字符"));
      } else if (/(?:012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210)/.test(value)) {
        callback(new Error("密码不能包含连续3位的数字序列"));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      loginType: "password", // 默认密码登录
      pageType: "login", // 页面类型：login、register或forgotPassword
      captchaId: "",
      slideX: 0,
      loginForm: {
        username: "",
        password: "",
        verifyCode: "",
        confirmPassword: "",
        newPassword: "", // 新增忘记密码的新密码字段
      },
      timer: 0, // 倒计时
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { validator: validatePassword, trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { validator: validatePassword, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "请确认密码", trigger: "blur" },
          { validator: validateConfirmPassword, trigger: "blur" },
        ],
        verifyCode: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          { min: 6, max: 6, message: "验证码长度必须为6位", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "验证码必须是6位数字", trigger: "blur" }
        ],
      },
      // 滑动验证码相关数据
      slideCaptchaVisible: false,
      captchaData: {
        id: "",
        backgroundImage: "",
        sliderImage: "",
        hint: "",
        width: 280,
        height: 140,
      },
      isDragging: false,
      startX: 0,
      sliderLeft: 0,
      puzzleLeft: 15, // 初始左边距
      puzzleTop: 0,
      trajectory: [],
      resultStatus: "",
      resultMessage: "",
      slideDisabled: false,
      smsType: "login", // 短信类型，默认为登录
      timerInterval: null, // 添加定时器变量
      // 租户选择相关
      tenantSelectVisible: false,
      tenantList: [],
      pendingToken: null, // 临时保存token
    };
  },

  created() {
    // 组件创建时根据初始登录类型设置验证规则
    this.updateValidationRules(this.loginType);
  },

  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  },

  methods: {
    // 添加now()函数，用于获取当前时间戳
    now() {
      return new Date().getTime();
    },

    // 计算精确的滑动距离和比例
    calculateSlidePosition() {
      const background = document.getElementById("slide-background");
      const puzzle = document.getElementById("slide-puzzle");
      const sliderButton = document.getElementById("slider-button");

      if (!background || !puzzle || !sliderButton) {
        return { slideX: 0, moveRatio: 0 };
      }

      const sliderButtonWidth = sliderButton.offsetWidth;
      const backgroundWidth = background.clientWidth;
      const puzzleWidth = puzzle.clientWidth;

      // 滑块按钮的最大移动距离
      const maxSliderX = backgroundWidth - sliderButtonWidth;

      // 拼图滑块的最大移动距离
      const maxPuzzleX = backgroundWidth - puzzleWidth;

      // 计算移动比例
      const moveRatio = maxSliderX > 0 ? this.sliderLeft / maxSliderX : 0;

      // 计算拼图的实际移动距离
      const puzzleMove = moveRatio * maxPuzzleX;

      return {
        slideX: Math.round(puzzleMove),
        moveRatio: moveRatio,
        maxSliderX: maxSliderX,
        maxPuzzleX: maxPuzzleX,
        puzzleMove: puzzleMove
      };
    },

    // 显示滑动验证码
    showSlideCaptcha() {
      this.$refs.loginForm.validateField("username", async (errorMessage) => {
        if (!errorMessage) {
          // 设置短信类型
          if (this.pageType === "register") {
            this.smsType = "register";
          } else if (this.pageType === "resetPassword") {
            this.smsType = "resetPassword";
          } else {
            this.smsType = this.loginType === "sms" ? "login" : "login";
          }

          this.slideCaptchaVisible = true;
          await this.getCaptcha();
        }
      });
    },

    // 获取验证码图片
    async getCaptcha() {
      try {
        this.loading = true;
        this.hideResult();
        const res = await api.captcha.get({
          width: this.captchaData.width,
          height: this.captchaData.height,
        });

        // 更新验证码数据
        this.captchaData = {
          ...res,
          backgroundImage: res.backgroundImage,
          sliderImage: res.sliderImage,
        };
        this.captchaId = res.id;

        this.resetSlider();

        // 设置图片并计算位置
        this.$nextTick(() => {
          const background = document.getElementById("slide-background");
          const puzzle = document.getElementById("slide-puzzle");
          if (background && puzzle) {
            // 等待图片加载完成
            background.onload = () => {
              puzzle.onload = () => {
                const padding = this.getContainerPadding();

                // 获取图片的自然尺寸
                const bgNaturalWidth = background.naturalWidth;
                const bgNaturalHeight = background.naturalHeight;
                const puzzleNaturalWidth = puzzle.naturalWidth;
                const puzzleNaturalHeight = puzzle.naturalHeight;

                // 计算显示尺寸与自然尺寸的比例
                const scaleX = background.clientWidth / bgNaturalWidth;
                const scaleY = background.clientHeight / bgNaturalHeight;

                // 根据比例调整滑块显示尺寸
                const displayPuzzleWidth = puzzleNaturalWidth * scaleX;
                const displayPuzzleHeight = puzzleNaturalHeight * scaleY;

                // 设置滑块显示尺寸
                puzzle.style.width = displayPuzzleWidth + 'px';
                puzzle.style.height = displayPuzzleHeight + 'px';

                // 计算垂直居中位置
                const initialTop = (background.clientHeight - displayPuzzleHeight) / 2;
                this.puzzleTop = initialTop;
                this.puzzleLeft = padding.left;

                // 确保滑块按钮和轨道也重置到初始位置
                this.$nextTick(() => {
                  const sliderButton = document.getElementById("slider-button");
                  const sliderTrack = document.getElementById("slider-track");

                  if (sliderButton) {
                    sliderButton.style.left = "0px";
                  }

                  if (sliderTrack) {
                    sliderTrack.style.width = "0px";
                  }

                  // 重置滑块位置变量
                  this.sliderLeft = 0;
                });

                console.log("验证码初始化:", {
                  验证码ID: this.captchaData.id,
                  背景图尺寸: {
                    自然宽度: bgNaturalWidth,
                    自然高度: bgNaturalHeight,
                    显示宽度: background.clientWidth,
                    显示高度: background.clientHeight,
                  },
                  滑块尺寸: {
                    自然宽度: puzzleNaturalWidth,
                    自然高度: puzzleNaturalHeight,
                    显示宽度: displayPuzzleWidth,
                    显示高度: displayPuzzleHeight,
                  },
                  缩放比例: { scaleX, scaleY },
                  滑块位置: {
                    top: initialTop,
                    left: padding.left,
                  },
                });
              };
              puzzle.src = this.captchaData.sliderImage;
            };
            background.src = this.captchaData.backgroundImage;
          }
        });
      } catch (error) {
        this.showError("获取验证码失败，请重试");
      } finally {
        this.loading = false;
      }
    },

    // 获取容器内边距
    getContainerPadding() {
      const container = document.querySelector(".slide-container");
      const style = window.getComputedStyle(container);
      return {
        left: parseInt(style.paddingLeft) || 15,
        top: parseInt(style.paddingTop) || 15,
      };
    },

    // 开始拖动
    dragStart(e) {
      if (this.loading || this.slideDisabled) return;
      e.preventDefault();
      this.isDragging = true;
      this.startX = e.type === "mousedown" ? e.clientX : e.touches[0].clientX;

      // 记录滑动轨迹的起点，考虑内边距
      this.trajectory = [
        {
          x: 0,
          y: 0,
          timestamp: this.now(),
        },
      ];

      document.addEventListener("mousemove", this.dragMove);
      document.addEventListener("mouseup", this.dragEnd);
      document.addEventListener("touchmove", this.dragMove);
      document.addEventListener("touchend", this.dragEnd);
    },

            // 拖动中
    dragMove(e) {
      if (!this.isDragging) return;
      e.preventDefault();

      const clientX = e.type === "mousemove" ? e.clientX : e.touches[0].clientX;
      const moveX = clientX - this.startX;
      const padding = this.getContainerPadding();

      // 使用辅助方法计算位置
      this.sliderLeft = moveX; // 临时设置用于计算

      const position = this.calculateSlidePosition();

      // 限制滑块按钮的移动范围
      this.sliderLeft = Math.max(0, Math.min(moveX, position.maxSliderX || 0));

      // 重新计算精确位置
      const finalPosition = this.calculateSlidePosition();

      // 更新滑块按钮位置和轨道宽度
      const sliderButton = document.getElementById("slider-button");
      const sliderTrack = document.getElementById("slider-track");

      if (sliderButton && sliderTrack) {
        sliderButton.style.left = this.sliderLeft + "px";
        sliderTrack.style.width = this.sliderLeft + sliderButton.offsetWidth / 2 + "px";
      }

      // 更新拼图滑块位置，考虑内边距
      this.puzzleLeft = padding.left + finalPosition.puzzleMove;

      // 记录轨迹点（使用拼图的实际移动距离）
      this.trajectory.push({
        x: finalPosition.slideX,
        y: 0,
        timestamp: this.now(),
      });
    },

    // 拖动结束
    async dragEnd() {
      if (!this.isDragging) return;
      this.isDragging = false;

      document.removeEventListener("mousemove", this.dragMove);
      document.removeEventListener("mouseup", this.dragEnd);
      document.removeEventListener("touchmove", this.dragMove);
      document.removeEventListener("touchend", this.dragEnd);

      if (this.sliderLeft > 0) {
                try {
          this.loading = true;
          this.slideDisabled = true;

          // 统一使用相同的滑动距离计算方法
          const padding = this.getContainerPadding();
          this.slideX = Math.round(this.sliderLeft + padding.left);

          console.log("滑动距离计算:", {
            滑块按钮移动距离: this.sliderLeft,
            容器内边距: padding.left,
            最终滑动距离: this.slideX,
            登录类型: this.loginType,
            页面类型: this.pageType
          });

          if (
            this.loginType === "sms" ||
            this.pageType === "register" ||
            this.pageType === "resetPassword"
          ) {
            // 发送短信验证码
            try {
              // 在短信登录和重置密码时，先检查用户是否存在
              if (this.pageType !== "register") {
                try {
                  const userExistsRes = await api.user.checkUserExists(this.loginForm.username);
                  if (!userExistsRes.data) {
                    this.showError("用户不存在");
                    this.$showFriendlyError(null, "用户不存在，请检查手机号码");
                    return;
                  }
                } catch (error) {
                  console.error("检查用户是否存在失败:", error);
                  this.showError("验证失败，请重试");
                  return;
                }
              }

              const smsParams = {
                phoneNumber: this.loginForm.username,
                smsType: this.smsType, // 使用动态类型
                captchaId: this.captchaData.id,
                slideX: this.slideX,
                trajectory: this.trajectory,
              };

              await api.user.sendSms(smsParams);
              this.showSuccess("验证通过！");
              this.$message.success("验证码已发送");
              this.startTimer();

              // 关闭验证码弹窗
              setTimeout(() => {
                this.slideCaptchaVisible = false;
              }, 500);
            } catch (error) {
              this.showError("验证失败，请重试");
              // 验证失败后自动获取新的验证码
              setTimeout(() => {
                this.getCaptcha();
              }, 1000);
            }
          } else {
            // 密码登录验证
            try {
             //执行登录
              await this.handleLoginAfterVerify();
              // 关闭验证码弹窗
              setTimeout(() => {
                this.slideCaptchaVisible = false;
              }, 500);
            } catch (error) {
              this.showError("验证失败，请重试");
              // 验证失败后自动获取新的验证码
              setTimeout(() => {
                this.getCaptcha();
              }, 1000);
            }
          }
        } catch (error) {
          this.showError("验证失败，请重试");
          // 验证失败后自动获取新的验证码
          setTimeout(() => {
            this.getCaptcha();
          }, 1000);
        } finally {
          this.loading = false;
          this.slideDisabled = false;
        }
      } else {
        this.resetSlider();
      }
    },

    // 重置滑块
    resetSlider() {
      this.isDragging = false;
      this.startX = 0;
      this.sliderLeft = 0;
      this.slideDisabled = false;

      const padding = this.getContainerPadding();
      const puzzle = document.getElementById("slide-puzzle");
      const sliderButton = document.getElementById("slider-button");
      const sliderTrack = document.getElementById("slider-track");

      if (puzzle) {
        puzzle.style.transition = "all 0.3s ease";
        puzzle.style.left = `${padding.left}px`;
        puzzle.style.transform = "none";
        // 重置时更新滑块位置
        this.puzzleLeft = padding.left;
      }

      // 重置滑块按钮和轨道
      if (sliderButton) {
        sliderButton.style.left = "0px";
        sliderButton.style.transition = "all 0.3s ease";
      }

      if (sliderTrack) {
        sliderTrack.style.width = "0px";
        sliderTrack.style.transition = "all 0.3s ease";
      }

      // 清除轨迹记录
      this.trajectory = [];
      this.hideResult();

      // 延迟移除过渡效果
      setTimeout(() => {
        if (puzzle) {
          puzzle.style.transition = "";
        }
        if (sliderButton) {
          sliderButton.style.transition = "";
        }
        if (sliderTrack) {
          sliderTrack.style.transition = "";
        }
      }, 300);
    },

    // 重新获取验证码
    resetCaptcha() {
      this.resetSlider();
      this.getCaptcha();
    },

    // 显示成功消息
    showSuccess(message) {
      this.resultMessage = message;
      this.resultStatus = "success";
    },

    // 显示错误消息
    showError(message) {
      this.resultMessage = message;
      this.resultStatus = "error";
    },

    // 隐藏结果消息
    hideResult() {
      this.resultMessage = "";
      this.resultStatus = "";
    },

    // 开始倒计时
    startTimer() {
      // 清除可能存在的定时器
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }

      this.timer = 60;
      this.timerInterval = setInterval(() => {
        if (this.timer > 0) {
          this.timer--;
        } else {
          clearInterval(this.timerInterval);
          this.timerInterval = null;
        }
      }, 1000);
    },

    // 处理提交（登录或注册或忘记密码）
    handleSubmit() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          if (this.pageType === "login") {
            // 登录逻辑
            await this.handleLogin();
          } else if (this.pageType === "register") {
            // 注册逻辑
            await this.handleRegister();
          } else if (this.pageType === "resetPassword") {
            // 忘记密码逻辑
            await this.handleForgotPassword();
          }
        }
      });
    },

    // 登录
    async handleLogin() {
      if (this.loginType === "password") {
        // 密码登录：先进行滑动验证
        this.showSlideCaptcha();
      } else {
        // 验证码登录：直接验证
        try {
          this.loading = true;
          const res = await api.auth.tokenBySms({
            phoneNumber: this.loginForm.username,
            code: this.loginForm.verifyCode,
            captchaId: this.captchaId,
            slideX: this.slideX,
            trajectory: this.trajectory,
          });

                  if (res.isSuccess) {
          // 使用新的登录成功处理函数
          await this.handleLoginWithTenant(res.data.accessToken);
        } else {
          this.$showFriendlyError(null, res.message || "登录失败，请重试");
        }
        } catch (error) {
          console.error("登录失败:", error);
          // 检查是否已在拦截器中处理过错误
          if (!error.alreadyHandled) {
            this.$showFriendlyError(error, "登录失败，请重试");
          }
        } finally {
          this.loading = false;
        }
      }
    },

    // 注册
    async handleRegister() {
      try {
        this.loading = true;
        // 注册需要短信验证码
        if (!this.loginForm.verifyCode) {
          this.$showFriendlyError(null, "请获取并输入短信验证码");
          this.loading = false;
          return;
        }

        // 调用注册接口
        const res = await api.auth.register({
          phoneNumber: this.loginForm.username,
          password: this.loginForm.password,
          code: this.loginForm.verifyCode
        });

        if (res.isSuccess) {
          this.$message.success("注册成功，请登录");
          // 清空表单并切换到登录页
          this.loginForm.password = "";
          this.loginForm.confirmPassword = "";
          this.loginForm.verifyCode = "";
          this.pageType = "login";
          this.timer = 0; // 重置倒计时

          // 清除定时器
          if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
          }
        }
      } catch (error) {
        console.error("注册失败:", error);
        // 检查是否已在拦截器中处理过错误
        if (!error.alreadyHandled) {
          this.$showFriendlyError(error, "注册失败，请重试");
        }
      } finally {
        this.loading = false;
      }
    },

    // 切换登录方式
    switchLoginType(type) {
      this.loginType = type;
      this.loginForm.password = "";
      this.loginForm.verifyCode = "";
      this.$refs.loginForm.clearValidate();

      // 更新验证规则
      this.updateValidationRules(type);
    },

    // 切换页面类型（登录/注册/忘记密码）
    switchPageType(type) {
      this.pageType =
        type || (this.pageType === "login" ? "register" : "login");
      this.loginForm.password = "";
      this.loginForm.newPassword = "";
      this.loginForm.confirmPassword = "";
      this.loginForm.verifyCode = "";
      this.timer = 0; // 重置倒计时

      // 清除定时器
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }

      this.$refs.loginForm.clearValidate();

      // 如果切换到注册页或忘记密码页，设置验证规则
      if (this.pageType === "register" || this.pageType === "resetPassword") {
        this.loginType = "sms";
      }

      // 更新验证规则
      this.updateValidationRules(this.loginType);
    },

    // 更新验证规则
    updateValidationRules(type) {
      if (this.pageType === "login") {
        if (type === "password") {
          // 密码登录时使用用户名验证
          this.rules.username = [
            { required: true, message: "请输入用户名", trigger: "blur" },
          ];
          // 不需要更新密码验证规则，因为已经在data中设置了全局验证器
        } else {
          // 验证码登录时使用手机号验证
          this.rules.username = [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!value) {
                  callback(new Error("请输入手机号"));
                } else if (!/^1[3-9]\d{9}$/.test(value)) {
                  callback(new Error("请输入正确的手机号"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
            { pattern: /^1[3-9]\d{9}$/, message: "手机号必须是11位数字", trigger: "blur" },
            { min: 11, max: 11, message: "手机号必须是11位", trigger: "blur" }
          ];
          // 更新验证码规则
          this.rules.verifyCode = [
            { required: true, message: "请输入验证码", trigger: "blur" },
            { min: 6, max: 6, message: "验证码长度必须为6位", trigger: "blur" },
            { pattern: /^\d{6}$/, message: "验证码必须是6位数字", trigger: "blur" }
          ];
        }
      } else {
        // 注册或忘记密码时使用手机号验证
        this.rules.username = [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error("请输入手机号"));
              } else if (!/^1[3-9]\d{9}$/.test(value)) {
                callback(new Error("请输入正确的手机号"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
          { pattern: /^1[3-9]\d{9}$/, message: "手机号必须是11位数字", trigger: "blur" },
          { min: 11, max: 11, message: "手机号必须是11位", trigger: "blur" }
        ];

        // 更新验证码规则
        this.rules.verifyCode = [
          { required: true, message: "请输入验证码", trigger: "blur" },
          { min: 6, max: 6, message: "验证码长度必须为6位", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "验证码必须是6位数字", trigger: "blur" }
        ];

        // 更新注册或重置密码的密码验证规则
        if (this.pageType === "register") {
          // 不需要更改，使用全局验证器
        } else if (this.pageType === "resetPassword") {
          // 不需要更改，使用全局验证器
        }
      }
    },

    // 滑动验证通过后执行登录
    async handleLoginAfterVerify() {
      try {
        // 使用在dragEnd中已经计算好的滑动距离，确保与验证码登录一致
        const res = await api.auth.tokenByPassword({
          userName: this.loginForm.username,
          password: this.loginForm.password,
          captchaId: this.captchaData.id,
          slideX: this.slideX,
          trajectory: this.trajectory,
        });
        if (res.isSuccess) {
          // 使用新的登录成功处理函数
          await this.handleLoginWithTenant(res.data.accessToken);
        } else {
          this.$showFriendlyError(null, res.message || "登录失败，请重试");
        }
      } catch (error) {
        console.error("登录失败:", error);
        // 检查是否已在拦截器中处理过错误
        if (!error.alreadyHandled) {
          this.$showFriendlyError(error, "登录失败，请重试");
        }
      }
    },

    // 处理登录成功后的逻辑（包含租户获取）
    async handleLoginWithTenant(token) {
      try {
        // 使用auth工具函数处理登录成功逻辑
        const result = await handleLoginSuccess(token);

        if (result.needSelectTenant) {
          // 需要选择租户
          this.pendingToken = token;
          this.tenantList = result.tenants;
          this.tenantSelectVisible = true;
          return; // 等待用户选择租户
        }

        // 不需要选择租户或已选择租户，继续后续流程
        await this.completeLogin();
      } catch (error) {
        console.error("处理登录成功逻辑失败:", error);
        // 检查是否已在拦截器中处理过错误
        if (!error.alreadyHandled) {
          this.$showFriendlyError(error, "登录处理失败，请重试");
        }
        throw error;
      }
    },

    // 选择租户
    async selectTenant(tenant) {
      try {
        // 处理租户选择
        handleTenantSelection(tenant);

        // 关闭弹框
        this.tenantSelectVisible = false;

        // 完成登录流程
        await this.completeLogin();
      } catch (error) {
        console.error("租户选择失败:", error);
        this.$showFriendlyError(error, "租户选择失败，请重试");
      }
    },

    // 完成登录流程
    async completeLogin() {
      try {
        // 保存基本用户信息
        localStorage.setItem(
          "user",
          JSON.stringify({
            name: this.loginForm.username,
          })
        );

        // 获取用户详细信息
        await this.getUserInfo();

        // 跳转到主页
        this.$router.push("/create/app");
        this.$message.success("登录成功");
      } catch (error) {
        console.error("完成登录流程失败:", error);
        this.$showFriendlyError(error, "登录完成失败，请重试");
        throw error;
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await api.auth.getCurrentUser();
        if (response.isSuccess) {
          // 获取现有的用户信息
          let userInfo = {};
          try {
            const existingUserInfo = localStorage.getItem("user");
            if (existingUserInfo) {
              userInfo = JSON.parse(existingUserInfo);
            }
          } catch (err) {
            console.error("解析现有用户信息失败:", err);
            userInfo = {};
          }

          // 合并现有信息和新信息
          const updatedUserInfo = {
            ...userInfo,
            email: response.data.email,
            fullName: response.data.fullName,
            phoneNumber: response.data.phoneNumber,
          };

          // 存储更新后的用户信息
          localStorage.setItem("user", JSON.stringify(updatedUserInfo));
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
      }
    },

    // 忘记密码
    async handleForgotPassword() {
      try {
        this.loading = true;
        // 忘记密码需要短信验证码
        if (!this.loginForm.verifyCode) {
          this.$showFriendlyError(null, "请获取并输入短信验证码");
          this.loading = false;
          return;
        }

        // 调用忘记密码接口
        const res = await api.auth.forgotPassword({
          phoneNumber: this.loginForm.username,
          code: this.loginForm.verifyCode,
          newPassword: this.loginForm.newPassword,
          confirmPassword: this.loginForm.confirmPassword,
        });

        if (res.isSuccess) {
          this.$message.success("密码重置成功，请登录");
          // 清空表单并切换到登录页
          this.loginForm.newPassword = "";
          this.loginForm.confirmPassword = "";
          this.loginForm.verifyCode = "";
          this.switchPageType("login");
          this.timer = 0; // 重置倒计时

          // 清除定时器
          if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
          }
        }
      } catch (error) {
        console.error("密码重置失败:", error);
        // 检查是否已在拦截器中处理过错误
        if (!error.alreadyHandled) {
          this.$showFriendlyError(error, "密码重置失败，请重试");
        }
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  background-color: rgba(243, 245, 248, 1);
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.section_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.block_1 {
  background-image: url("./assets/img/main-bg.png");
  background-size: 42% 100%;
  background-repeat: no-repeat;
  display: flex;
  height: 100%;
  .group_1 {
    width: 42%;
    height: 100%;
    background: url("./assets/img/background.png") 100% no-repeat;
    background-size: 100% 100%;

    .box_1 {
      width: 175px;
      height: 60px;
      margin: 114px 0 0 104px;

      .image_1 {
        width: 60px;
        height: 60px;
      }

      .text_1 {
        width: 95px;
        height: 24px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 24px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 24px;
        margin-top: 18px;
      }
    }

    .text-wrapper_1 {
      width: 557px;
      height: 50px;
      overflow-wrap: break-word;
      font-size: 0;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 50px;
      margin: 48px 0 0 104px;

      .text_2 {
        overflow-wrap: break-word;
        color: rgba(33, 135, 250, 1);
        font-size: 56px;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 50px;
      }

      .text_3 {
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 56px;
        font-family: PingFangSC-Semibold;
        font-weight: 600;
        text-align: left;
        white-space: nowrap;
        line-height: 50px;
      }
    }

    .text_4 {
      width: 390px;
      height: 24px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 28px;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 24px;
      margin: 44px 0 634px 104px;
    }
  }

  .group_2 {
    position: relative;
    width: 58%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    align-items: center;
    background-image: url("./assets/img/sidebar.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .text_5 {
      width: 254px;
      height: 28px;
      overflow-wrap: break-word;
      color: rgba(17, 26, 52, 1);
      font-size: 32px;
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 28px;
      margin-left: 200px;
    }

    .text-wrapper_2 {
      width: 222px;
      height: 18px;
      margin: 60px 0 0 109px;

      .text_6 {
        width: 72px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 18px;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 18px;
        cursor: pointer;

        &.active {
          font-weight: bold;
        }
      }

      .text_7 {
        width: 90px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(78, 89, 105, 1);
        font-size: 18px;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 18px;
        cursor: pointer;

        &.active {
          font-weight: bold;
          color: rgba(0, 0, 0, 1);
        }
      }
    }

    .box_2 {
      height: 1px;
      background: url("./assets/img/line.png") 0px 0px no-repeat;
      background-size: 440px 2px;
      margin-top: 15px;
      width: 440px;
      margin-left: 109px;

      .section_2 {
        background-color: rgba(33, 135, 250, 1);
        width: 72px;
        height: 2px;
        margin: -1px 0 0 0;
        transition: margin-left 0.3s;
      }
    }

    .login-form {
      margin-top: 20px;
      padding-left: 109px;

      .text-wrapper_6 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 52px;
        width: 440px;
        margin-bottom: 20px;
        border: 1px solid rgba(229, 230, 235, 1);

        .el-input {
          height: 100%;

          :deep(.el-input__inner) {
            height: 100%;
            border: none;
            background: transparent;
            padding-left: 20px;
            border-radius: 8px;
            line-height: 50px;
            display: flex;
            align-items: center;

            &::placeholder {
              color: rgba(201, 205, 212, 1);
              font-size: 18px;
              line-height: normal;
            }
          }

          .el-input__prefix {
            display: none;
          }
        }
      }

      .text-wrapper_3 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        height: 52px;
        width: 440px;
        margin-bottom: 20px;
        border: 1px solid rgba(229, 230, 235, 1);

        .el-input {
          height: 100%;

          :deep(.el-input__inner) {
            height: 100%;
            border: none;
            background: transparent;
            padding-left: 20px;
            border-radius: 8px;
            line-height: 50px;
            display: flex;
            align-items: center;

            &::placeholder {
              color: rgba(201, 205, 212, 1);
              font-size: 18px;
              line-height: normal;
            }
          }

          .el-input__prefix {
            display: none;
          }
        }
      }

      .verify-container {
        display: flex;
        align-items: center;
        width: 440px;
        height: 52px;
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        border: 1px solid rgba(229, 230, 235, 1);

        .verify-input {
          flex: 1;
          height: 100%;
          margin-right: 0;

          :deep(.el-input__inner) {
            height: 100%;
            border: none;
            background: transparent;
            padding-left: 20px;
            border-radius: 8px 0 0 8px;
            line-height: 50px;
            display: flex;
            align-items: center;

            &::placeholder {
              color: rgba(201, 205, 212, 1);
              font-size: 18px;
              line-height: normal;
            }
          }
        }

        .verify-button {
          height: 50px;
          margin-right: 1px;
          background-color: transparent;
          border: none;
          border-radius: 0 7px 7px 0;
          font-size: 14px;
          padding: 0 16px;
          white-space: nowrap;
          min-width: 100px;
          color: rgba(33, 135, 250, 1);
          cursor: pointer;

          &:hover {
            background-color: rgba(33, 135, 250, 0.05);
          }

          &:disabled {
            background-color: transparent;
            border: none;
            color: #f56c6c;
            cursor: not-allowed;

            &:hover {
              background-color: transparent;
            }
          }
        }
      }

      .login-button-container {
        margin-top: 32px;

        .text-wrapper_4 {
          background-color: rgba(33, 135, 250, 1);
          border-radius: 8px;
          height: 54px;
          width: 440px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          .text_9 {
            width: 80px;
            height: 20px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 20px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            line-height: 20px;
          }
        }
      }

      .box_3 {
        width: 440px;
        display: flex;
        justify-content: space-between;
        margin-top: 40px;

        .section_3 {
          .block_2 {
            width: 144px;
            height: 16px;
            display: flex;
            align-items: center;

            .box_4 {
              background-color: rgba(255, 255, 255, 1);
              border-radius: 2px;
              width: 16px;
              height: 16px;
              border: 1px solid rgba(201, 205, 212, 1);
              margin-right: 8px;
              cursor: pointer;
            }

            .text_10 {
              width: 116px;
              height: 16px;
              overflow-wrap: break-word;
              color: rgba(102, 111, 131, 1);
              font-size: 16px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 16px;
            }
          }
        }

        .section_4 {
          .text-wrapper_5 {
            width: 160px;
            height: 16px;
            overflow-wrap: break-word;
            text-align: right;
            white-space: nowrap;
            line-height: 16px;

            .text_11 {
              overflow-wrap: break-word;
              color: rgba(134, 144, 156, 1);
              font-size: 16px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 16px;
            }

            .text_12 {
              overflow-wrap: break-word;
              color: rgba(33, 135, 250, 1);
              font-size: 16px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 16px;
              margin-left: 5px;
              cursor: pointer;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
}

// 滑动验证码样式
.slide-captcha-dialog {
  .slide-container {
    position: relative;
    margin: 25px auto;
    overflow: hidden;
    border-radius: 8px;
    width: 280px;

    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(100, 181, 246, 0.25);
    box-sizing: border-box;
    box-shadow: 0 0 20px rgba(100, 181, 246, 0.15);
  }

  #slide-background {
    width: 100%;
    height: 140px; // 设置固定高度
    display: block;
    border-radius: 4px;
    background: linear-gradient(135deg, #fafbfd 0%, #f8f9fc 100%);
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    vertical-align: middle;
    object-fit: cover; // 确保图片填充
  }

  #slide-puzzle {
    position: absolute;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    box-sizing: border-box;
    z-index: 5;
    pointer-events: none;
    margin: 0;
    padding: 0;
    transform-origin: center;
    will-change: transform, left;
    object-fit: contain; // 保持滑块比例
    transition: none; // 默认无过渡效果，拖拽时需要实时响应
    // 移除固定尺寸，改为动态设置
  }

  .slider {
    width: 100%;
    height: 38px;
    background: rgba(255, 255, 255, 0.9);
    position: relative;
    margin-top: 8px;
    border: 1px solid rgba(100, 181, 246, 0.35);
    border-radius: 20px;
    display: flex;
    align-items: center;
  }

  .slider-button {
    width: 52px;
    height: 38px;
    background: linear-gradient(45deg, #64b5f6, #2196f3);
    position: absolute;
    left: 0;
    top: 0.5px;
    cursor: grab;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    transition: none; // 拖拽时不要过渡效果
    user-select: none;

    &:hover {
      background: linear-gradient(45deg, #5fa3e3, #1976d2);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    }

    &:active {
      cursor: grabbing;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      background: linear-gradient(45deg, #5094d4, #1565c0);
    }

    &::after {
      content: "→";
      color: #fff;
      font-size: 16px;
      pointer-events: none;
    }
  }

  .slider-track {
    height: 100%;
    width: 0;
    background: linear-gradient(
      90deg,
      rgba(100, 181, 246, 0.2),
      rgba(33, 150, 243, 0.3)
    );
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    border-radius: 20px;
    transition: width 0.2s;
  }

  .slider-hint {
    color: #607d8b;
    font-size: 13px;
    margin-left: 50px;
    user-select: none;
    font-weight: 500;
  }

  #slide-distance {
    margin-top: 10px;
    font-size: 13px;
    color: #757575;
    text-align: center;
  }

  .result {
    margin-top: 10px;
    text-align: center;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;

    &.success {
      background-color: #e8f5e9;
      color: #2e7d32;
    }

    &.error {
      background-color: #ffebee;
      color: #c62828;
    }
  }
}

.dialog-footer {
  text-align: center;
  margin-top: 20px;
}

// 辅助样式
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}
:deep(.el-form-item__content .el-input__inner) {
  height: 52px;
}
:deep(.el-form-item--small .el-form-item__content, .el-form-item--small .el-form-item__label) {
  height: 52px;
}

// 租户选择弹框样式
:deep(.tenant-select-dialog) {
  .el-dialog__header {
    text-align: center;
    padding: 20px 20px 10px;
    border-bottom: none;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }

  .el-dialog__body {
    padding: 10px 20px 20px;
  }
}

.tenant-list {
  .tenant-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    margin-bottom: 8px;
    border: 1px solid #E5E6EB;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #2187FA;
      background-color: #F7FBFF;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .tenant-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #F0F2F5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 20px;
        color: #666F83;
      }
    }

    .tenant-info {
      flex: 1;
      display: flex;
      flex-direction: column;

      .tenant-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        line-height: 22px;
        margin-bottom: 2px;
      }

      .tenant-type {
        font-size: 14px;
        color: #666F83;
        line-height: 20px;
      }
    }

    .tenant-arrow {
      color: #C9CDD4;
      font-size: 16px;

      i {
        transition: transform 0.3s ease;
      }
    }

    &:hover .tenant-arrow i {
      transform: translateX(4px);
      color: #2187FA;
    }
  }
}
</style>
