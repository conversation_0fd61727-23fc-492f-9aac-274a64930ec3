<template>
  <div class="page flex-col">
    <span class="text_1">为了给您更完美的体验，我们正在做最后的冲刺，很快解锁！</span>
    <img
      class="image_1"
      referrerpolicy="no-referrer"
      :src="getVersionIcon(currentSubscriptionName || '标准版')"
    />
    <div class="error-actions">
      <el-button type="primary" @click="goHome">返回首页</el-button>
      <el-button @click="goBack">返回上一页</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorPage404',
  data() {
    return {
      currentSubscriptionName: ''
    }
  },
  created() {
    this.getCurrentSubscription()
  },
  methods: {
    // 获取当前用户订阅版本
    getCurrentSubscription() {
      try {
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const user = JSON.parse(userStr)
          this.currentSubscriptionName = user.subscriptionPlanName || '标准版'
        }
      } catch (error) {
        console.error('获取用户订阅信息失败:', error)
        this.currentSubscriptionName = '标准版'
      }
    },

    // 根据版本名称获取对应的404图标
    getVersionIcon(versionName) {
      const iconMap = {
        '基础版': require('@/assets/error/404-icon-basic.png'),
        '标准版': require('@/assets/error/404-icon-standard.png'),
        '专业版': require('@/assets/error/404-icon-professional.png'),
        '企业版': require('@/assets/error/404-icon-enterprise.png')
      }
      return iconMap[versionName] || require('@/assets/error/404-icon.png')
    },

    goHome() {
      this.$router.push('/')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .text_1 {
    width: 486px;
    height: 25px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 18px;
    text-align: center;
    white-space: nowrap;
    line-height: 25px;
    margin-bottom: 59px;
  }

  .image_1 {
    width: 200px;
    height: 200px;
    margin-bottom: 40px;
  }

  .error-actions {
    display: flex;
    gap: 16px;
    margin-top: 20px;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
}

// 添加flex工具类
.flex-col {
  display: flex;
  flex-direction: column;
}
</style>
