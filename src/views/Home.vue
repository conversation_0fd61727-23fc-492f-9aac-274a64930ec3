<template>
  <div class="home">
    <h1>用户管理系统</h1>
    <el-row :gutter="20" class="m-4">
      <el-col :span="6" v-for="(item, index) in menuItems" :key="index">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="flex-between">
            <span>{{ item.title }}</span>
            <el-button type="text" @click="handleClick(item)">进入</el-button>
          </div>
          <div class="text-secondary">
            {{ item.description }}
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'HomePage',
  data() {
    return {
      menuItems: [
        {
          title: '应用管理',
          description: '管理所有应用，包括创建、配置和分享',
          path: '/apps'
        },
        {
          title: '知识库管理',
          description: '管理知识库，上传和维护文档',
          path: '/knowledge'
        },
        {
          title: '用户管理',
          description: '管理用户账户和权限设置',
          path: '/users'
        },
        {
          title: '数据看板',
          description: '查看系统使用统计和性能监控',
          path: '/dashboard'
        }
      ]
    }
  },
  methods: {
    handleClick(item) {
      this.$router.push(item.path)
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  padding: 20px;

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
  }

  .box-card {
    margin-bottom: 20px;

    .text-secondary {
      color: var(--text-color-secondary);
      font-size: 14px;
    }
  }
}
</style>
