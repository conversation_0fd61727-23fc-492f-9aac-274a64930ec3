<template>
  <div class="manage-dashboard-page">
    <div class="page-header">
      <h2>数据看板</h2>
      <p class="page-desc">查看系统运行数据和统计信息</p>
    </div>
    <div class="page-content">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(stat, index) in stats" :key="index">
          <el-card class="stat-card">
            <div class="stat-icon">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>API调用趋势</span>
              <el-select v-model="apiChartTimeRange" placeholder="请选择" style="float: right; width: 120px">
                <el-option v-for="item in timeRangeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
            <div ref="apiChart" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>用户活跃度</span>
              <el-select v-model="userChartTimeRange" placeholder="请选择" style="float: right; width: 120px">
                <el-option v-for="item in timeRangeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
            <div ref="userChart" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// 引入echarts
import * as echarts from 'echarts/core'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册需要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  BarChart,
  CanvasRenderer
])

export default {
  name: 'ManageDashboardPage',
  data() {
    return {
      stats: [
        {
          icon: 'el-icon-user',
          value: '1,234',
          label: '总用户数'
        },
        {
          icon: 'el-icon-s-platform',
          value: '56',
          label: '应用数量'
        },
        {
          icon: 'el-icon-data-line',
          value: '98.5%',
          label: '系统可用性'
        },
        {
          icon: 'el-icon-connection',
          value: '10,000+',
          label: 'API调用/天'
        }
      ],
      // 时间范围选项
      timeRangeOptions: [
        { value: '7d', label: '最近7天' },
        { value: '30d', label: '最近30天' },
        { value: '90d', label: '最近90天' }
      ],
      apiChartTimeRange: '7d',
      userChartTimeRange: '7d',
      // 图表实例
      apiChart: null,
      userChart: null,
      // Mock数据 - API调用趋势
      apiCallsData: {
        '7d': {
          dates: ['5-01', '5-02', '5-03', '5-04', '5-05', '5-06', '5-07'],
          values: [8500, 9200, 11500, 10200, 9800, 12500, 14000]
        },
        '30d': {
          dates: Array.from({ length: 30 }, (_, i) => `${4 + Math.floor(i / 30)}-${String(i % 30 + 1).padStart(2, '0')}`),
          values: Array.from({ length: 30 }, () => Math.floor(Math.random() * 6000) + 8000)
        },
        '90d': {
          dates: Array.from({ length: 90 }, (_, i) => `${2 + Math.floor(i / 30)}-${String(i % 30 + 1).padStart(2, '0')}`),
          values: Array.from({ length: 90 }, () => Math.floor(Math.random() * 8000) + 6000)
        }
      },
      // Mock数据 - 用户活跃度
      userActivityData: {
        '7d': {
          categories: ['新用户', '活跃用户', '回访用户'],
          series: [
            [45, 120, 65], // 5-01
            [52, 132, 70], // 5-02
            [58, 140, 75], // 5-03
            [63, 138, 72], // 5-04
            [70, 150, 80], // 5-05
            [75, 160, 85], // 5-06
            [82, 170, 90]  // 5-07
          ],
          dates: ['5-01', '5-02', '5-03', '5-04', '5-05', '5-06', '5-07']
        },
        '30d': {
          categories: ['新用户', '活跃用户', '回访用户'],
          series: Array.from({ length: 30 }, () => [
            Math.floor(Math.random() * 50) + 40,  // 新用户
            Math.floor(Math.random() * 60) + 120, // 活跃用户
            Math.floor(Math.random() * 30) + 60   // 回访用户
          ]),
          dates: Array.from({ length: 30 }, (_, i) => `${4 + Math.floor(i / 30)}-${String(i % 30 + 1).padStart(2, '0')}`)
        },
        '90d': {
          categories: ['新用户', '活跃用户', '回访用户'],
          series: Array.from({ length: 90 }, () => [
            Math.floor(Math.random() * 60) + 30,  // 新用户
            Math.floor(Math.random() * 80) + 100, // 活跃用户
            Math.floor(Math.random() * 40) + 50   // 回访用户
          ]),
          dates: Array.from({ length: 90 }, (_, i) => `${2 + Math.floor(i / 30)}-${String(i % 30 + 1).padStart(2, '0')}`)
        }
      }
    }
  },
  watch: {
    apiChartTimeRange(val) {
      this.updateApiChart(val)
    },
    userChartTimeRange(val) {
      this.updateUserChart(val)
    }
  },
  mounted() {
    // 在DOM加载完成后初始化图表
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    // 初始化图表
    initCharts() {
      // 初始化API调用趋势图
      this.apiChart = echarts.init(this.$refs.apiChart)
      this.updateApiChart(this.apiChartTimeRange)

      // 初始化用户活跃度图
      this.userChart = echarts.init(this.$refs.userChart)
      this.updateUserChart(this.userChartTimeRange)

      // 添加窗口大小变化的监听，以便图表自适应
      window.addEventListener('resize', this.resizeCharts)
    },

    // 更新API调用趋势图
    updateApiChart(timeRange) {
      const data = this.apiCallsData[timeRange]
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>{a}: {c}'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.dates,
          axisLabel: {
            rotate: timeRange === '90d' ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          name: '调用次数'
        },
        series: [
          {
            name: 'API调用次数',
            type: 'line',
            data: data.values,
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 3,
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.6)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ]
              }
            }
          }
        ]
      }
      this.apiChart.setOption(option)
    },

    // 更新用户活跃度图
    updateUserChart(timeRange) {
      const data = this.userActivityData[timeRange]

      // 处理数据格式 - 转换为echarts需要的格式
      const series = data.categories.map((category, index) => {
        return {
          name: category,
          type: 'bar',
          stack: 'total',
          data: data.series.map(dayData => dayData[index])
        }
      })

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: data.categories,
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.dates,
          axisLabel: {
            rotate: timeRange === '90d' ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          name: '用户数'
        },
        series: series,
        color: ['#91CC75', '#5470C6', '#EE6666']
      }
      this.userChart.setOption(option)
    },

    // 调整图表大小
    resizeCharts() {
      this.apiChart && this.apiChart.resize()
      this.userChart && this.userChart.resize()
    }
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.resizeCharts)
    // 销毁图表，释放资源
    this.apiChart && this.apiChart.dispose()
    this.userChart && this.userChart.dispose()
  }
}
</script>

<style lang="scss" scoped>
.manage-dashboard-page {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 10px;
      font-weight: 500;
    }

    .page-desc {
      color: #606266;
      font-size: 14px;
      margin: 0;
    }
  }

  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;

    .stat-icon {
      font-size: 48px;
      color: #409EFF;
      margin-right: 20px;
    }

    .stat-info {
      .stat-value {
        font-size: 24px;
        font-weight: 500;
        color: #303133;
      }

      .stat-label {
        margin-top: 5px;
        font-size: 14px;
        color: #909399;
      }
    }
  }
}
</style>
