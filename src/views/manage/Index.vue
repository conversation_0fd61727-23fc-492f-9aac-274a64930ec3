<template>
  <div class="manage-page">
    <router-view v-if="$route.path !== '/manage'"></router-view>
    <div v-else>
      <h2>系统管理</h2>
      <div class="manage-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="manage-card">
              <div class="manage-header">
                <h3>用户管理</h3>
                <p>管理系统用户和权限</p>
              </div>
              <div class="manage-body">
                <el-button type="text" @click="manageUsers">管理用户</el-button>
                <el-button type="text" @click="manageRoles">角色权限</el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="manage-card">
              <div class="manage-header">
                <h3>应用管理</h3>
                <p>管理所有AI应用</p>
              </div>
              <div class="manage-body">
                <el-button type="text" @click="manageApps">应用列表</el-button>
                <el-button type="text" @click="manageSettings">应用设置</el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="manage-card">
              <div class="manage-header">
                <h3>系统设置</h3>
                <p>基础系统配置</p>
              </div>
              <div class="manage-body">
                <el-button type="text" @click="systemConfig">系统配置</el-button>
                <el-button type="text" @click="systemLogs">系统日志</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { Row, Col, Card, Button } from 'element-ui'

export default {
  name: 'ManagePage',
  components: {
    'el-row': Row,
    'el-col': Col,
    'el-card': Card,
    'el-button': Button
  },
  methods: {
    manageUsers() {
      this.$message.info('用户管理功能开发中...')
    },
    manageRoles() {
      this.$message.info('角色权限功能开发中...')
    },
    manageApps() {
      this.$message.info('应用管理功能开发中...')
    },
    manageSettings() {
      this.$message.info('应用设置功能开发中...')
    },
    systemConfig() {
      this.$message.info('系统配置功能开发中...')
    },
    systemLogs() {
      this.$message.info('系统日志功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.manage-page {
  padding: 20px;

  .manage-content {
    margin-top: 20px;

    .manage-card {
      height: 200px;
      display: flex;
      flex-direction: column;
      padding: 20px;

      .manage-header {
        margin-bottom: 20px;

        h3 {
          margin: 0 0 10px;
          font-size: 18px;
          color: #303133;
        }

        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }

      .manage-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 10px;

        .el-button {
          width: 100%;
          text-align: left;
          padding-left: 0;
        }
      }
    }
  }
}
</style>
