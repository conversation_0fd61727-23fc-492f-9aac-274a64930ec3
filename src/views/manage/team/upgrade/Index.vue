<template>
  <div class="upgrade-page">
    <div class="upgrade-header">
      <div class="header-tabs">
        <el-button
          :type="activeTab === 'upgrade' ? 'primary' : ''"
          class="tab-item"
          :class="{ active: activeTab === 'upgrade' }"
          @click="switchTab('upgrade')"
          >版本升级</el-button
        >
        <el-button
          :type="activeTab === 'points' ? 'primary' : ''"
          class="tab-item"
          :class="{ active: activeTab === 'points' }"
          @click="switchTab('points')"
          >积分充值</el-button
        >
      </div>
    </div>

    <!-- 版本升级内容 -->
    <div class="version-list" v-if="activeTab === 'upgrade'">
      <div
        v-for="plan in subscriptionPlans"
        :key="plan.planName"
        class="version-card"
        :class="{
          selected: selectedVersion === plan.planName,
          recommended: plan.planName === '标准版',
          basic: plan.planName === '基础版',
          standard: plan.planName === '标准版',
          professional: plan.planName === '专业版',
          enterprise: plan.planName === '企业版',
        }"
      >
        <!-- 版本头部 -->
        <div class="version-header">
          <div v-if="plan.planName === '标准版'" class="recommend-tag">
            限时优惠25%
          </div>

          <!-- 顶部彩色区域 -->
          <div class="header-colored-section">
            <div class="header-content">
              <div class="header-left">
                <h3 class="version-title">{{ plan.planName }}</h3>
                <p class="version-desc">{{ plan.description }}</p>
              </div>
              <div class="header-right">
                <img :src="logoImage" alt="logo" class="logo-icon" />
              </div>
            </div>
          </div>

          <!-- 价格区域 -->
          <div class="price-section">
            <template v-if="plan.details && plan.details.length > 0">
              <template
                v-if="
                  plan.details[0].unit === '请咨询客服' ||
                  plan.planName === '企业版'
                "
              >
                <div class="enterprise-contact">
                  <div class="contact-text">专业团队为您服务</div>
                  <div class="contact-text">请咨询售前顾问</div>
                </div>
              </template>
              <template v-else-if="plan.planName === '基础版'">
                <div class="price-display">
                  <span class="price-symbol">¥</span>
                  <span class="price-amount">0</span>
                  <span class="price-unit">/年</span>
                </div>
              </template>
              <template v-else>
                <div class="price-display">
                  <span class="price-symbol">¥</span>
                  <span class="price-amount">{{
                    plan.details[selectedPayType]
                      ? plan.details[selectedPayType].amount
                      : plan.details[0].amount
                  }}</span>
                  <span class="price-unit"
                    >/{{
                      plan.details[selectedPayType]
                        ? plan.details[selectedPayType].unit
                        : plan.details[0].unit
                    }}</span
                  >
                </div>
              </template>
            </template>
          </div>

          <!-- 操作按钮 -->
          <el-button
            class="action-btn"
            :class="{
              'basic-btn': plan.planName === '基础版',
              'standard-btn': plan.planName === '标准版',
              'professional-btn': plan.planName === '专业版',
              'enterprise-btn': plan.planName === '企业版',
            }"
            @click="handleStartUse(plan.planName)"
          >
            {{ getButtonText(plan.planName) }}
          </el-button>
        </div>

        <!-- 功能特性列表 -->
        <div class="version-features">
          <div
            v-for="(feature, featureIndex) in plan.featureLimits"
            :key="`${plan.planName}-${feature.featureKey}-${featureIndex}`"
            class="feature-item"
          >
            <div class="feature-label">
              {{ feature.description }}
            </div>
            <div class="feature-value">
              <div
                v-if="
                  feature.limitType === '布尔' &&
                  feature.featureKey !== 'ChannelAccess'
                "
                class="value-with-tooltip"
              >
                <span
                  v-if="
                    feature.limitValue === 'true' || feature.limitValue === true
                  "
                  class="feature-text"
                  >每日签到</span
                >
                <span v-else class="feature-dash">-</span>
                <el-tooltip
                  v-if="feature.context"
                  effect="dark"
                  :content="feature.context"
                  placement="top"
                >
                  <i class="el-icon-question tooltip-icon"></i>
                </el-tooltip>
              </div>
              <div
                v-else-if="feature.limitType === '整数'"
                class="value-with-tooltip"
              >
                <span class="feature-text"
                  >{{ formatNumber(feature.limitValue)
                  }}{{ getUnit(feature.unit) }}</span
                >
                <el-tooltip
                  v-if="feature.context"
                  effect="dark"
                  :content="feature.context"
                  placement="top"
                >
                  <i class="el-icon-question tooltip-icon"></i>
                </el-tooltip>
              </div>
              <div v-else class="value-with-tooltip">
                <template v-if="feature.featureKey === 'ChannelAccess'">
                  <div class="channel-icons">
                    <img
                      v-for="(icon, index) in parseChannelIcons(
                        feature.context || ''
                      )"
                      :key="index"
                      :src="icon.icon"
                      :alt="icon.name"
                      :title="icon.name"
                      class="channel-icon"
                    />
                  </div>
                </template>
                <template
                  v-else-if="feature.featureKey === 'KnowledgeBaseImportLimit'"
                >
                  <span class="feature-text">{{
                    formatNumber(feature.limitValue)
                  }}</span>
                  <el-popover
                    placement="top"
                    width="300"
                    trigger="hover"
                    popper-class="import-limit-popover"
                  >
                    <div class="import-limit-content">
                      <div
                        v-if="feature.context"
                        v-html="formatImportLimitContext(feature.context)"
                      ></div>
                    </div>
                    <div slot="reference" class="import-limit-reference">
                      <i class="el-icon-question tooltip-icon"></i>
                    </div>
                  </el-popover>
                </template>
                <template v-else-if="typeof feature.limitValue === 'boolean'">
                  <span v-if="feature.limitValue" class="feature-text"
                    >每日签到</span
                  >
                  <span v-else class="feature-dash">-</span>
                </template>
                <template v-else>
                  <span class="feature-text">{{ feature.limitValue }}</span>
                </template>
                <el-tooltip
                  v-if="
                    feature.context &&
                    feature.featureKey !== 'KnowledgeBaseImportLimit'
                  "
                  effect="dark"
                  :content="feature.context"
                  placement="top"
                >
                  <i class="el-icon-question tooltip-icon"></i>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 积分充值内容 -->
    <div class="points-list" v-if="activeTab === 'points'">
      <div
        v-for="pointRule in pointPurchaseRules"
        :key="pointRule.id"
        class="points-card"
      >
        <div class="points-header">{{ pointRule.desc }}</div>
        <div style="background-color: #fff;width:100%;text-align: center;padding:32px">
          <div class="points-amount">
            {{ formatPointAmount(pointRule.point) }}积分
          </div>
          <div class="points-price">¥ {{ pointRule.price }}</div>
        </div>
        <el-button
          type="primary"
          class="points-btn"
          @click="handleBuyPoints(pointRule)"
        >
          立即购买
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request";
import logoImage from "@/assets/manage/update_logo.png";

export default {
  name: "UpgradePage",
  data() {
    return {
      activeTab: "upgrade", // 默认显示升级版本的内容
      selectedVersion: "",
      selectedPayType: 0, // 0表示按月，1表示按年
      subscriptionPlans: [],
      currentSubscriptionId: null, // 添加当前订阅ID
      currentUserSubscriptionId: null, // 添加用户订阅ID
      pointPurchaseRules: [],
      logoImage,
    };
  },
  created() {
    const query = this.$route.query;
    if (query.type === "points") {
      this.activeTab = "points";
    } else {
      this.activeTab = "upgrade";
    }
    this.fetchSubscriptionPlans();
    this.fetchCurrentSubscription(); // 添加获取当前订阅信息的方法调用
    this.fetchPointPurchaseRules();
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    },
    async fetchCurrentSubscription() {
      try {
        const res = await api.subscription.getValid(); // 调用获取有效订阅的API
        console.log("获取当前有效订阅信息:", res);
        if (res.code === 200 && res.data) {
          // 从API响应中获取相关ID
          const { id, subscriptionId } = res.data;

          // 保存相关ID，后续用于匹配
          this.currentUserSubscriptionId = id;
          this.currentSubscriptionId = subscriptionId;

          console.log("用户订阅ID:", this.currentUserSubscriptionId);
          console.log("订阅计划ID:", this.currentSubscriptionId);
          // 将订阅信息存储到localStorage的user中

          localStorage.setItem(
            "user",
            JSON.stringify({
              ...JSON.parse(localStorage.getItem("user")),
              id: res.data.userId,
            })
          );

          // 如果已经加载了订阅计划，则进行匹配
          if (this.subscriptionPlans.length > 0) {
            this.matchSubscriptionPlan();
          }
        }
      } catch (error) {
        console.error("获取当前订阅信息失败:", error);
      }
    },
    // 根据订阅ID匹配订阅计划
    matchSubscriptionPlan() {
      if (
        (!this.currentSubscriptionId && !this.currentUserSubscriptionId) ||
        !this.subscriptionPlans.length
      )
        return;

      console.log("开始匹配订阅计划");
      // 打印所有计划的ID和名称，方便调试
      console.log(
        "可用的订阅计划:",
        this.subscriptionPlans.map((p) => ({ id: p.id, name: p.planName }))
      );

      // 尝试多种方式匹配计划
      let matchedPlan = null;

      // 1. 先用subscriptionId匹配
      if (this.currentSubscriptionId) {
        matchedPlan = this.subscriptionPlans.find(
          (plan) =>
            plan.featureLimits[0].subscriptionId === this.currentSubscriptionId
        );
        if (matchedPlan) {
          console.log("通过subscriptionId匹配到计划:", matchedPlan.planName);
        }
      }

      // 2. 如果没找到，尝试用用户订阅ID匹配
      if (!matchedPlan && this.currentUserSubscriptionId) {
        matchedPlan = this.subscriptionPlans.find(
          (plan) => plan.id === this.currentUserSubscriptionId
        );
        if (matchedPlan) {
          console.log(
            "通过userSubscriptionId匹配到计划:",
            matchedPlan.planName
          );
        }
      }

      // 设置选中的版本
      if (matchedPlan) {
        this.selectedVersion = matchedPlan.planName;
      }
    },
    async fetchSubscriptionPlans() {
      try {
        const res = await api.subscription.getList();
        if (res.code === 200) {
          console.log("订阅计划列表:", res.data);

          // 按照指定顺序排序，保留所有版本
          this.subscriptionPlans = res.data.items
            .sort((a, b) => {
              const order = ["基础版", "标准版", "专业版", "企业版"];
              return order.indexOf(a.planName) - order.indexOf(b.planName);
            })
            .map((plan) => ({
              ...plan,
              featureLimits: this.sortFeatures(plan.featureLimits || []),
            }));

          console.log(
            "处理后的订阅计划:",
            this.subscriptionPlans.map((p) => ({ id: p.id, name: p.planName }))
          );

          // 匹配当前订阅计划
          if (this.currentSubscriptionId || this.currentUserSubscriptionId) {
            this.matchSubscriptionPlan();
          } else {
            // 默认选中专业版（仅在未获取到当前订阅时）
            this.selectedVersion = "";
          }
        }
      } catch (error) {
        console.error("获取订阅计划失败:", error);
      }
    },
    async fetchPointPurchaseRules() {
      try {
        const res = await api.points.getPointPurchaseRulesList();
        if (res.code === 200) {
          console.log("积分购买规则列表:", res.data);
          // 按sort字段排序，从大到小
          this.pointPurchaseRules = res.data.sort((a, b) => b.sort - a.sort);
        }
      } catch (error) {
        console.error("获取积分购买规则失败:", error);
      }
    },
    handleBuyPoints(pointRule) {
      this.$message.info(
        `正在购买${pointRule.point}积分，价格：¥${pointRule.price}`
      );
      // 这里添加积分购买的逻辑
    },
    sortFeatures(features) {
      const order = [
        "ProfessionalBenefits", // 专业版所有权益
        "AIPoints", // AI积分
        "CustomAIModel", // 自定义AI大模型
        "AI_LLM", // 自定义AI大模型(新名称)
        "ApplicationCount", // 应用
        "KnowledgeBaseStorage", // 知识库文件容量
        "KnowledgeBaseEntries", // 知识库条数
        "KnowledgeBaseImportLimit", // 知识库导入限制
        "InternalDatabaseRows", // 内置数据库行数
        "RemoteDatabase", // 远程数据库
        "CustomPlugins", // 自定义插件
        "Workflows", // 工作流
        "DataDashboard", // 数据看板
        "ChannelAccess", // 渠道接入
        "ChannelCustomerManagement", // 接入渠道客户管理
        "ClientManagement", // 客户端管理
        "ConversationRateLimit", // 对话限流配置
        "SmartHumanTransfer", // 智能转人工
        "AggregateConversationManagement", // 聚合对话管理
        "TeamSpace", // 团队空间
        "SpaceMemberPermission", // 空间成员权限管理
        "CustomSpaceMemberCount", // 按需定制空间成员数量
        "AdvancedOpenAPI", // 开放API（高级）
        "OpenAPI", // 开放API（基础）
        "CustomEnterpriseLogo", // 自定义企业LOGO
        "CustomEnterpriseAppStore", // 自定义企业应用广场
        "CustomFunctionCapacity", // 按需定制功能权益容量
        "HighestPriorityPerformance", // 最高优先级性能保障
        "AITrainingOptimization", // 智能体训练调优服务
        "PrivateDeployment", // 可支持私有化部署
        "CustomerService", // 服务
      ];

      return features.sort((a, b) => {
        const indexA = order.indexOf(a.featureKey);
        const indexB = order.indexOf(b.featureKey);
        // 如果找不到的字段放到最后
        if (indexA === -1) return 1;
        if (indexB === -1) return -1;
        return indexA - indexB;
      });
    },
    handlePayTypeChange(version, type) {
      this.selectedPayType = type;
    },
    handleStartUse(version) {
      this.selectedVersion = version;
      switch (version) {
        case "基础版":
          this.$message.info("开始使用基础版");
          break;
        case "标准版":
          this.$message.info("开始使用标准版");
          break;
        case "专业版":
          this.$message.info("继续使用专业版");
          break;
        case "企业版":
          this.$message.info("正在为您转接客服...");
          break;
      }
    },
    getButtonText(version) {
      // 企业版无论是否选中都显示"联系咨询"
      if (version === "企业版") {
        return "联系咨询";
      }

      // 基础版显示"去使用"
      if (version === "基础版") {
        return "去使用";
      }

      // 如果当前版本被选中，显示"继续使用"，否则显示"开始使用"
      return this.selectedVersion === version ? "继续使用" : "开始使用";
    },
    formatNumber(value) {
      const num = parseInt(value);
      if (num >= 10000) {
        return num / 10000 + "万";
      }
      return num;
    },
    getUnit(unit) {
      if (unit.includes("容量") && unit.includes("G")) {
        return "G";
      } else if (unit.includes("容量") && unit.includes("M")) {
        return "M";
      } else if (unit.includes("条数")) {
        return "条";
      } else if (unit.includes("行数")) {
        return "万行";
      } else if (unit.includes("个")) {
        return "个";
      } else if (unit.includes("人")) {
        return "人";
      } else if (unit.includes("万")) {
        return "万";
      }
      return "";
    },
    parseChannelIcons(context) {
      if (!context) return [];

      // 定义渠道图标映射
      const channelMap = {
        "微信公众号（个人）": {
          name: "微信公众号",
          icon: "/icons/wechat-mp.png",
          order: 1,
        },
        "微信公众号（企业）": {
          name: "微信公众号",
          icon: "/icons/wechat-person.png",
          order: 1,
        },
        钉钉: {
          name: "钉钉",
          icon: "/icons/dingtalk.png",
          order: 2,
        },
        飞书: {
          name: "飞书",
          icon: "/icons/feishu.png",
          order: 3,
        },
        企业微信自建应用: {
          name: "企业微信",
          icon: "/icons/wework.png",
          order: 4,
        },
        企业微信: {
          name: "企业微信",
          icon: "/icons/wework.png",
          order: 4,
        },
        微信客服: {
          name: "微信客服",
          icon: "/icons/wechat-service.png",
          order: 5,
        },
        微信: {
          name: "微信",
          icon: "/icons/wechat.png",
          order: 6,
        },
      };

      const icons = new Set();

      // 按照字符长度从长到短排序，确保优先匹配最长的渠道名称
      Object.keys(channelMap)
        .sort((a, b) => b.length - a.length)
        .forEach((channel) => {
          if (context.includes(channel)) {
            icons.add(channelMap[channel]);
          }
        });

      // 将Set转换为数组并按order排序
      return (
        Array.from(icons)
          .sort((a, b) => a.order - b.order)
          // 去重（比如微信公众号个人版和企业版使用同一个图标）
          .filter(
            (icon, index, self) =>
              index === self.findIndex((t) => t.name === icon.name)
          )
      );
    },
    formatImportLimitContext(context) {
      if (!context) return "";
      // 将逗号分隔的内容转换为HTML格式的列表
      return context
        .split(",")
        .map((item) => `<div class="import-limit-item">${item.trim()}</div>`)
        .join("");
    },
    // 解析知识库导入限制对象
    parseImportLimit(limitValue) {
      if (!limitValue) return {};
      try {
        return JSON.parse(limitValue);
      } catch (error) {
        console.error("解析知识库导入限制失败:", error);
        return {};
      }
    },
    formatPointAmount(point) {
      const num = parseInt(point);
      if (num >= 10000) {
        return num / 10000 + "万";
      }
      return num;
    },
  },
};
</script>

<style lang="scss" scoped>
.upgrade-page {
  background: #f5f7fa;
  min-height: calc(100vh - 60px);

  .upgrade-header {
    margin-bottom: 32px;
    text-align: center;

    .header-tabs {
      display: inline-flex;
      padding: 4px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .tab-item {
        padding: 12px 32px;
        font-size: 16px;
        font-weight: 500;
        border: none;
        background: transparent;
        border-radius: 6px;
        color: #606266;

        &.active {
          background: #409eff;
          color: #fff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
        }

        & + .tab-item {
          margin-left: 4px;
        }
      }
    }
  }

  .version-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;

    .version-card {
      border-radius: 12px;
      padding: 0;
      position: relative;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      border: 1px solid #e4e7ed;
      overflow: hidden;
      min-height: 600px;

      // 基础版样式
      &.basic {
        background: #fff;

        .header-colored-section {
          background: linear-gradient(166deg, #e9effb 0%, #d1d8ea 100%);

          .version-title {
            color: #000000 !important;
          }

          .version-desc {
            color: #606266 !important;
          }
        }

        .logo-icon {
          filter: brightness(0) saturate(100%) invert(60%) sepia(0%)
            saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%);
        }
      }

      // 标准版样式 - 推荐版本
      &.standard {
        background: #fff;
        box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);

        .header-colored-section {
          background: linear-gradient(166deg, #d5e9ff 0%, #86bffe 100%);

          .version-title {
            color: #278bfa !important;
          }
        }

        .recommend-tag {
          position: absolute;
          top: 16px;
          right: 16px;
          background: #ff4757;
          color: #fff;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          z-index: 10;
        }

        .logo-icon {
          filter: brightness(0) saturate(100%) invert(100%) sepia(0%)
            saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
        }
      }

      // 专业版样式
      &.professional {
        background: #fff;

        .header-colored-section {
          background: linear-gradient(167deg, #faedd2 0%, #f0d49d 100%);

          .version-title {
            color: #b58326 !important;
          }
        }

        .logo-icon {
          filter: brightness(0) saturate(100%) invert(100%) sepia(0%)
            saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
        }
      }

      // 企业版样式
      &.enterprise {
        background: #fff;

        .header-colored-section {
          background: linear-gradient(166deg, #7c7c90 0%, #51516e 100%);
        }

        .logo-icon {
          filter: brightness(0) saturate(100%) invert(100%) sepia(0%)
            saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
        }
      }

      .version-header {
        padding: 0;
        position: relative;
        background: transparent;

        .header-colored-section {
          padding: 20px;
          margin-bottom: 16px;
          border-radius: 12px 12px 0 0;

          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            min-height: 60px;

            .header-left {
              flex: 1;
              text-align: left;

              .version-title {
                font-size: 20px;
                font-weight: 600;
                margin: 0 0 6px;
                color: #fff;
              }

              .version-desc {
                font-size: 13px;
                margin: 0;
                line-height: 1.3;
                max-width: 180px;
              }
            }

            .header-right {
              flex-shrink: 0;
              margin-left: 12px;

              .logo-icon {
                width: 65px;
                height: 65px;
                object-fit: contain;
                opacity: 0.8;
              }
            }
          }
        }

        .price-section {
          padding: 0 20px;
          margin-bottom: 20px;
          min-height: 60px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          text-align: left;

          .price-display {
            display: flex;
            align-items: baseline;
            justify-content: flex-start;
            gap: 1px;

            .price-symbol {
              font-size: 20px;
              color: #303133;
              font-weight: 600;
            }

            .price-amount {
              font-size: 48px;
              font-weight: 700;
              color: #303133;
              line-height: 1;
            }

            .price-unit {
              font-size: 16px;
              color: #606266;
              font-weight: 500;
            }
          }

          .enterprise-contact {
            text-align: left;

            .contact-text {
              font-size: 16px;
              color: #303133;
              line-height: 1.4;
              margin-bottom: 2px;
              font-weight: 800;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        .action-btn {
          width: calc(100% - 40px);
          height: 44px;
          font-size: 15px;
          font-weight: 500;
          border-radius: 6px;
          border: none;
          margin: 0 20px 16px;

          &.basic-btn {
            background: #ffffff;
            color: #666;
            border: 1px solid #e3e3e3;
            &:hover {
              background: #337ecc;
            }
          }

          &.standard-btn {
            background: #409eff;
            color: #fff;

            &:hover {
              background: #337ecc;
            }
          }

          &.professional-btn {
            background: #ccaa68;
            color: #fff;

            &:hover {
              background: #ccaa68;
            }
          }

          &.enterprise-btn {
            background: #474665;
            color: #fff;

            &:hover {
              background: #5a6268;
            }
          }
        }
      }

      .version-features {
        flex: 1;
        padding: 0 20px 20px;
        background: rgba(255, 255, 255, 0.9);

        .feature-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid rgba(240, 242, 245, 0.8);
          font-size: 13px;

          &:last-child {
            border-bottom: none;
          }

          .feature-label {
            color: #606266;
            flex: 1;
            text-align: left;
            font-weight: 400;
          }

          .feature-value {
            color: #303133;
            font-weight: 500;
            text-align: right;
            min-width: 80px;

            .value-with-tooltip {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              gap: 4px;
            }

            .feature-text {
              color: #303133;
              font-weight: 500;
            }

            .feature-dash {
              color: #c0c4cc;
              font-size: 14px;
            }

            .tooltip-icon {
              color: #c0c4cc;
              cursor: help;
              font-size: 12px;

              &:hover {
                color: #409eff;
              }
            }

            .channel-icons {
              display: flex;
              align-items: center;
              gap: 3px;

              .channel-icon {
                width: 18px;
                height: 18px;
                object-fit: contain;
              }
            }
          }
        }
      }

      &:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-4px);
      }

      &.selected {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  // 积分充值卡片样式
  .points-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    max-width: 1200px;
    margin: 0 auto;

    .points-card {
      background: linear-gradient(166deg, #f3f7ff 0%, #e3e8f2 100%);
      border-radius: 8px;
      padding: 24px 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      transition: all 0.3s ease;
      border: none;
      position: relative;
      min-height: 200px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      .points-header {
        color: #303133;
        font-size: 14px;
        margin-bottom: 40px;
        margin-top: 40px;
        font-weight: 500;
        line-height: 1.2;
      }

      .points-amount {
        color: #303133;
        font-size: 28px;
        font-weight: 700;

        line-height: 1;
      }

      .points-price {
        color: #909399;
        font-size: 14px;
        font-weight: 400;

      }

      .points-btn {
        margin-top: 50px;
        width: calc(100% - 8px);
        height: 36px;
        font-size: 13px;
        font-weight: 500;
        border-radius: 4px;
        background: #fff;

        color: #409eff;

        &:hover {
          background: #f0f9ff;
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .upgrade-page {
    .version-list,
    .points-list {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
  }
}

@media screen and (max-width: 768px) {
  .upgrade-page {
    padding: 16px;

    .version-list,
    .points-list {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .version-card {
      .version-header {
        padding: 24px 20px;

        .version-title {
          font-size: 24px;
        }

        .price-section .price-display .price-amount {
          font-size: 36px;
        }
      }

      .version-features {
        padding: 0 20px 20px;
      }
    }

    .points-list {
      .points-card {
        padding: 20px 12px;
        min-height: 180px;

        .points-header {
          font-size: 13px;
          margin-bottom: 16px;
        }

        .points-amount {
          font-size: 24px;
          margin-bottom: 6px;
        }

        .points-price {
          font-size: 13px;
          margin-bottom: 20px;
        }

        .points-btn {
          width: calc(100% - 4px);
          height: 32px;
          font-size: 12px;
        }
      }
    }
  }
}

.import-limit-reference {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: var(--el-color-primary);
  cursor: pointer;
  font-size: 14px;

  &:hover {
    opacity: 0.8;
  }

  i {
    font-size: 14px;
  }
}

:deep(.import-limit-popover) {
  padding: 0;
  .el-popover__title {
    margin: 0;
    padding: 10px;
    font-weight: bold;
    border-bottom: 1px solid #ebeef5;
  }
}

:deep(.import-limit-content) {
  padding: 10px;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;

  .import-limit-item {
    margin-bottom: 8px;
    padding-left: 8px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 8px;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: #606266;
    }
  }
}
</style>
