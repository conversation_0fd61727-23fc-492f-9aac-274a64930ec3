<template>
  <div class="team-page">
    <template v-if="$route.path === '/manage/team'">
      <!-- 检查是否有团队 -->
      <div v-if="hasTeam">
        <!-- 团队成员管理界面 -->
        <div class="team-header">
          <div class="team-info">
            <div class="team-name-section">
              <h2 v-if="!isEditingTeamName" @click="startEditTeamName" class="editable-title">
                {{ currentTeam && currentTeam.name || '团队空间' }}
                <i class="el-icon-edit edit-icon"></i>
              </h2>
              <div v-else class="team-name-edit">
                <el-input
                  v-model="editTeamName"
                  @blur="handleInputBlur"
                  @keyup.enter="saveTeamName"
                  @keyup.esc="cancelEditTeamName"
                  ref="teamNameInput"
                  size="medium"
                  style="width: 300px;"
                />
                <el-button type="primary" size="small" @click="saveTeamName" style="margin-left: 8px;">保存</el-button>
                <el-button size="small" @click="handleCancelEdit">取消</el-button>
              </div>
            </div>
            <p class="team-desc">管理团队成员，设置权限和角色</p>
          </div>
          <div class="team-actions">
            <el-button type="primary" @click="showInviteDialog">邀请新成员</el-button>
          </div>
        </div>

        <!-- 成员列表 -->
        <div class="members-section">
          <el-card>
            <div slot="header" class="card-header">
              <span>成员管理</span>
              <div class="header-actions">
                <el-input
                  v-model="searchKeyword"
                  placeholder="输入成员名称或手机号搜索"
                  prefix-icon="el-icon-search"
                  style="width: 300px;"
                  @input="handleSearch"
                />
              </div>
            </div>

            <el-table
              :data="filteredMembers"
              v-loading="membersLoading"
              style="width: 100%"
            >
              <el-table-column prop="name" label="成员名称" min-width="200">
                <template slot-scope="scope">
                  <div class="member-info">
                    <el-avatar :size="32" :src="scope.row.avatar">
                      {{ scope.row.name ? scope.row.name.charAt(0) : '?' }}
                    </el-avatar>
                    <span class="member-name">{{ scope.row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="role" label="角色" min-width="100">
                <template slot-scope="scope">
                  <el-tag :type="getRoleTagType(scope.row.role)">
                    {{ getRoleText(scope.row.role) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="phone" label="手机号" min-width="150" />
              <el-table-column prop="joinTime" label="加入时间" min-width="180">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.joinTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="120">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="removeMember(scope.row)"
                    style="color: #f56c6c;"
                  >
                    移出团队
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </div>

      <!-- 没有团队时显示原来的介绍页面 -->
      <div v-else class="team-intro-page">
        <!-- 顶部介绍 -->
        <div class="intro-header">
          <h1 class="intro-title">团队空间</h1>
          <p class="intro-desc">
            创建团队/企业空间，邀请团队成员、企业员工加入、设置不同权限的角色并为成员授
            权，助力团队协作的同时满足企业精细化的管理控制。
          </p>
          <el-button type="primary" size="large" @click="handleUpgrade" class="upgrade-btn">
            前往升级
          </el-button>
        </div>

        <!-- 功能介绍 -->
        <div class="features-section">
          <div class="features-header">
            <h2 class="features-title">— 功能简介 —</h2>
          </div>

          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-badge professional">专业版</div>
              <div class="feature-icon-wrapper">
                <img src="@/assets/manage/kongjianyuchengyuanguanli.png" alt="空间与成员管理" class="feature-icon">
              </div>
              <h3 class="feature-title">空间与成员管理</h3>
              <p class="feature-description">
                一键创建专属空间，共享空间内容，<br>
                团队协作，高效管理
              </p>
            </div>

            <div class="feature-card">
              <div class="feature-badge enterprise">企业版</div>
              <div class="feature-icon-wrapper">
                <img src="@/assets/manage/jifenpeieguanli.png" alt="积分配额管理" class="feature-icon">
              </div>
              <h3 class="feature-title">积分配额管理</h3>
              <p class="feature-description">
                企业积分池按周期自动为成员配额，<br>
                可手动调整企业积分池，可按需充值个人积分
              </p>
            </div>

            <div class="feature-card">
              <div class="feature-badge enterprise">企业版</div>
              <div class="feature-icon-wrapper">
                <img src="@/assets/manage/jiaosequanxianshezhi.png" alt="角色权限设置" class="feature-icon">
              </div>
              <h3 class="feature-title">角色权限设置</h3>
              <p class="feature-description">
                按需创建多种用户角色，灵活设置角色的功能与权限，<br>
                权限控制按需多方设置
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>
    <router-view v-else></router-view>

    <!-- 邀请新成员弹窗 -->
    <el-dialog
      title="邀请新成员"
      :visible.sync="inviteDialogVisible"
      width="580px"
      @close="handleInviteDialogClose"
      class="invite-dialog"
    >
      <div class="invite-content">
        <div class="role-section">
          <div class="section-title">
            <i class="el-icon-user"></i>
            <span>角色</span>
            <span class="required">*</span>
          </div>
          <el-select v-model="inviteForm.role" placeholder="请选择角色" style="width: 100%;" size="medium">
            <el-option label="成员" value="member">
              <div class="role-option">
                <span class="role-name">成员</span>
                <span class="role-desc">可以使用基础功能</span>
              </div>
            </el-option>
            <el-option label="管理员" value="admin">
              <div class="role-option">
                <span class="role-name">管理员</span>
                <span class="role-desc">拥有完整管理权限</span>
              </div>
            </el-option>
          </el-select>
        </div>

        <div class="invite-link-section">
          <div class="section-title">
            <i class="el-icon-link"></i>
            <span>邀请链接</span>
            <el-button type="primary" size="small" @click="copyInviteLink" :loading="inviteLoading">
              {{ inviteLoading ? '生成中...' : '复制' }}
            </el-button>
          </div>
          <div class="invite-link-content">
            <el-input
              v-model="inviteLink"
              readonly
              type="textarea"
              :rows="4"
              placeholder="正在生成邀请链接..."
              class="invite-link-input"
            />
          </div>
          <div class="invite-tip">
            <i class="el-icon-info"></i>
            <span>通过该链接注册或登录，即可加入团队</span>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="inviteDialogVisible = false" size="medium">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api/request'

export default {
  name: 'ManageTeamPage',
  data() {
    return {
      hasTeam: false,
      currentTeam: null,
      members: [],
      filteredMembers: [],
      membersLoading: false,
      searchKeyword: '',
      inviteDialogVisible: false,
      inviteForm: {
        role: 'member'
      },
      inviteRules: {
        role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      },
      inviteLink: '',
      inviteLoading: false,
      isEditingTeamName: false,
      editTeamName: ''
    }
  },
  mounted() {
    this.checkTeamStatus()
  },
  methods: {
    // 检查团队状态
    async checkTeamStatus() {
      try {
        // 从localStorage获取用户信息，检查teamSpace字段
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const userData = JSON.parse(userStr)
          this.hasTeam = userData.teamSpace === true

          if (this.hasTeam) {
            // 如果有团队权限，直接加载团队成员列表
            this.loadMembers()
          }
        } else {
          this.hasTeam = false
        }
      } catch (error) {
        console.error('检查团队状态失败:', error)
        this.hasTeam = false
      }
    },

    // 加载团队成员
    async loadMembers() {
      try {
        this.membersLoading = true
        const response = await api.team.getMembers()

        if (response.isSuccess && response.data && response.data.length > 0) {
          // 从第一条数据获取团队名称
          this.currentTeam = {
            name: response.data[0].teamSpaceName || '团队空间'
          }

          // 映射成员数据
          this.members = response.data.map(member => ({
            id: member.userId,
            name: member.fullName || member.userName,
            role: member.isMainAccount ? 'admin' : 'member',
            phone: member.userName,
            joinTime: member.creationTime,
            avatar: member.avatarUrl || ''
          }))

          // 应用搜索过滤
          this.handleSearch()
        }
      } catch (error) {
        console.error('加载团队成员失败:', error)
      } finally {
        this.membersLoading = false
      }
    },

    // 搜索成员
    handleSearch() {
      if (this.searchKeyword.trim()) {
        this.filteredMembers = this.members.filter(member =>
          (member.name && member.name.includes(this.searchKeyword)) ||
          (member.phone && member.phone.includes(this.searchKeyword))
        )
      } else {
        this.filteredMembers = [...this.members]
      }
    },

    // 显示邀请弹窗
    showInviteDialog() {
      this.inviteDialogVisible = true
      this.generateInviteLink()
    },

    // 生成邀请链接
    async generateInviteLink() {
      try {
        this.inviteLoading = true
        const response = await api.team.generateInviteCode()
        if (response.isSuccess && response.data) {
          let baseUrl
          if (process.env.NODE_ENV === 'production') {
            baseUrl = 'http://www.yuanzhiqi.com:3002'
          } else {
            baseUrl = process.env.VUE_APP_BASE || window.location.origin
          }
          this.inviteLink = `${baseUrl}/create/app?inviteCode=${response.data}`
        } else {
          this.$showFriendlyError('生成邀请码失败')
          this.inviteLink = ''
        }
      } catch (error) {
        console.error('生成邀请码失败:', error)
        this.inviteLink = ''
      } finally {
        this.inviteLoading = false
      }
    },

    // 复制邀请链接
    copyInviteLink() {
      if (this.inviteLink) {
        try {
          const textArea = document.createElement('textarea')
          textArea.value = this.inviteLink
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.$message.success('邀请链接已复制到剪贴板')
        } catch (err) {
          console.error('复制失败:', err)
          this.$showFriendlyError(null, '复制失败，请手动复制')
        }
      }
    },

    // 关闭邀请弹窗
    handleInviteDialogClose() {
      // 重置表单数据
      this.inviteForm.role = 'member'
      this.inviteLink = ''
      this.inviteLoading = false
    },

    // 移除成员
    async removeMember(member) {
      this.$confirm(`确定要移除成员 ${member.name} 吗？`, '确认移除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 调用API移除成员
          const response = await api.team.removeMember(member.id)

          if (response.isSuccess) {
            this.$message.success('成员移除成功')
            // 重新加载成员列表
            this.loadMembers()
          }
        } catch (error) {
          console.error('移除成员失败:', error)
        }
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 获取角色标签类型
    getRoleTagType(role) {
      const typeMap = {
        'admin': 'danger',
        'member': 'success'
      }
      return typeMap[role] || 'info'
    },

    // 获取角色文本
    getRoleText(role) {
      const textMap = {
        'admin': '管理员',
        'member': '成员'
      }
      return textMap[role] || '未知'
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      try {
        // 处理 "2025-06-04T06:18:34:08.00" 格式
        const date = new Date(dateString)
        if (isNaN(date.getTime())) {
          return dateString // 如果解析失败，返回原字符串
        }
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        console.error('日期格式化失败:', error)
        return dateString
      }
    },

    handleUpgrade() {
      this.$router.push('/manage/team/upgrade').catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          throw err
        }
      })
    },

    startEditTeamName() {
      if (!this.currentTeam) return
      this.isEditingTeamName = true
      this.editTeamName = this.currentTeam.name || '团队空间'
      this.$nextTick(() => {
        if (this.$refs.teamNameInput) {
          this.$refs.teamNameInput.focus()
        }
      })
    },

    async saveTeamName() {
      if (!this.editTeamName.trim()) {
        this.$message.warning('团队名称不能为空')
        return
      }

      if (!this.currentTeam) {
        this.$showFriendlyError(null, '团队信息不存在')
        return
      }

      try {
        const response = await api.team.updateTeamSpaceName(this.editTeamName.trim())
        if (response.isSuccess) {
          this.currentTeam.name = this.editTeamName.trim()
          this.isEditingTeamName = false
        }
      } catch (error) {
        console.error('修改团队名称失败:', error)
      }
    },

         cancelEditTeamName() {
       // ESC键取消编辑，不调用API，恢复原始名称
       this.isEditingTeamName = false
       this.editTeamName = this.currentTeam && this.currentTeam.name || '团队空间'
     },

     // 处理输入框失焦事件
     handleInputBlur() {
       // 延迟执行，让点击事件先执行
       setTimeout(() => {
         if (this.isEditingTeamName) {
           this.saveTeamName()
         }
       }, 100)
     },

     // 处理取消编辑
     handleCancelEdit() {
       // 直接取消编辑，不调用API，恢复原始名称
       this.isEditingTeamName = false
       this.editTeamName = this.currentTeam && this.currentTeam.name || '团队空间'
     },


  }
}
</script>

<style lang="scss" scoped>
.team-page {
  max-width: 1440px;
  margin: 0 auto;
  background: #f7f8fa;
  min-height: calc(100vh - 60px);

  .team-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding: 32px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .team-info {
      .team-name-section {
        margin-bottom: 8px;

        .editable-title {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          cursor: pointer;
          display: inline-flex;
          align-items: center;
          transition: all 0.3s ease;

          &:hover {
            color: var(--el-color-primary);

            .edit-icon {
              opacity: 1;
            }
          }

          .edit-icon {
            margin-left: 8px;
            font-size: 18px;
            opacity: 0.6;
            transition: opacity 0.3s ease;
          }
        }

        .team-name-edit {
          display: flex;
          align-items: center;
        }
      }

      .team-desc {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }
    }

    .team-actions {
      .el-button {
        padding: 12px 24px;
        font-size: 16px;
        border-radius: 8px;
      }
    }
  }

  .members-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }

    .member-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .member-name {
        font-weight: 500;
      }
    }
  }

  .invite-content {
    .role-section {
      margin-bottom: 32px;
    }

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-weight: 500;
      color: #303133;
      font-size: 16px;

      i {
        margin-right: 8px;
        color: var(--el-color-primary);
        font-size: 18px;
      }

      .required {
        color: #f56c6c;
        margin-left: 4px;
      }

      .el-button {
        margin-left: auto;
      }
    }

    .role-option {
      display: flex;
      flex-direction: column;

      .role-name {
        font-weight: 500;
        color: #303133;
      }

      .role-desc {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    .invite-link-section {
      padding: 24px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 1px solid #e4e7ed;

      .invite-link-content {
        margin-bottom: 16px;

        .invite-link-input {
          ::v-deep .el-textarea__inner {
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            resize: none;
          }
        }
      }

      .invite-tip {
        display: flex;
        align-items: center;
        color: #909399;
        font-size: 14px;
        background: #fff;
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;

        i {
          margin-right: 8px;
          color: var(--el-color-info);
        }
      }
    }
  }



  .page-header {
    margin-bottom: 48px;
    text-align: center;
    padding: 40px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    h2 {
      margin: 0 0 24px;
      font-size: 32px;
      font-weight: 600;
      background: linear-gradient(120deg, var(--el-color-primary), #409EFF);
      -webkit-background-clip: text;
      color: transparent;
    }

    .page-desc {
      color: #606266;
      font-size: 16px;
      margin: 0 auto 32px;
      max-width: 800px;
      line-height: 1.8;
    }

    .el-button {
      padding: 12px 32px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .features {
    margin-bottom: 48px;

    .features-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 32px;
      text-align: center;
      color: #303133;
      position: relative;
      padding-bottom: 16px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 48px;
        height: 4px;
        background: var(--el-color-primary);
        border-radius: 2px;
      }
    }

    .features-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 32px;
      margin: 0 auto;
      max-width: 1200px;

      .feature-item {
        flex: 1;
        background: #fff;
        border-radius: 16px;
        padding: 32px;
        display: flex;
        gap: 24px;
        align-items: flex-start;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        cursor: pointer;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }

        &.selected {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);

          &:hover {
            transform: none;
          }

          .feature-icon {
            background: var(--el-color-primary);

            i {
              color: #fff;
            }
          }
        }

        .feature-icon {
          width: 56px;
          height: 56px;
          border-radius: 16px;
          background: var(--el-color-primary-light-9);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          i {
            font-size: 28px;
            color: var(--el-color-primary);
            transition: all 0.3s ease;
          }
        }

        &:hover .feature-icon {
          background: var(--el-color-primary);

          i {
            color: #fff;
          }
        }

        .feature-content {
          flex: 1;

          h4 {
            margin: 0 0 16px;
            font-size: 18px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 0 0 20px;
            color: #606266;
            font-size: 14px;
            line-height: 1.8;
          }

          .feature-version {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            background: var(--el-color-success-light-9);
            color: var(--el-color-success);
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              background: var(--el-color-success-light-8);
            }
          }
        }
      }
    }
  }

  // 团队介绍页面样式
  .team-intro-page {
    min-height: 100vh;
    background-size: cover;
    padding: 20px;
    position: relative;
    overflow: hidden;

    .intro-header {
      background: #ffffff url('@/assets/manage/teambg.png') no-repeat 100% 100%;
      text-align: left;
      padding: 48px 70px;
      margin-bottom: 64px;
      position: relative;
      z-index: 1;

      .intro-title {
        font-size: 48px;
        font-weight: 700;
        color: #409EFF;
        margin: 0 0 24px;
        letter-spacing: -0.5px;
      }

      .intro-desc {
        font-size: 16px;
        color: #606266;
        line-height: 1.8;
        margin: 0 0 40px;
        max-width: 600px;
      }

      .upgrade-btn {
        background: #409EFF;
        border: none;
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #337ecc;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(64, 158, 255, 0.3);
        }
      }
    }

    .features-section {
      position: relative;
      z-index: 1;

      .features-header {
        text-align: center;
        margin-bottom: 48px;

        .features-title {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin: 0;
          letter-spacing: 1px;
        }
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;
        margin: 0 auto;

        .feature-card {
          background: #ffffff;
          border-radius: 12px;
          padding: 32px 24px;
          text-align: center;
          position: relative;
          transition: all 0.3s ease;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
          border: 1px solid #f0f2f5;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }

          .feature-badge {
            position: absolute;
            top: 0px;
            right: 0px;
            padding: 6px 16px;
            border-radius: 0px 8px 0px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #ffffff;

            &.professional {
              background: #409EFF;
            }

            &.enterprise {
              background: #E6A23C;
            }
          }

          .feature-icon-wrapper {
            margin: 16px 0 24px;

            .feature-icon {
              width: 48px;
              height: 48px;
              object-fit: contain;
              transition: all 0.3s ease;
            }
          }

          .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 12px;
          }

          .feature-description {
            font-size: 13px;
            color: #666666;
            line-height: 1.5;
            margin: 0;
          }

          // 图标样式优化
          .feature-icon {
            display: block;
            margin: 0 auto;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .team-page {
    padding: 24px;

    .team-intro-page {
      padding: 40px 24px;

      .features-section .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
      }
    }

    .features {
      .features-list {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .team-page {
    padding: 16px;

    .team-intro-page {
      padding: 30px 16px;

      .intro-header {
        .intro-title {
          font-size: 36px;
        }

        .intro-desc {
          font-size: 14px;
        }
      }

      .features-section .features-grid {
        grid-template-columns: 1fr;
        gap: 24px;
      }
    }

    .page-header {
      padding: 24px;

      h2 {
        font-size: 24px;
      }
    }

    .features {
      .features-list {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
