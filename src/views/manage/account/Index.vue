<template>
  <div class="manage-account-page">
    <!-- 个人信息 -->
    <el-card class="account-card">
      <div class="account-header">
        <div class="header-left">
          <h2>个人信息</h2>
        </div>
        <!-- <div class="header-right">
          <el-button type="primary" size="small" icon="el-icon-share">分享</el-button>
        </div> -->
      </div>
      <div class="account-info">
        <div class="info-left">
          <div class="avatar-section">
            <el-avatar
              :size="60"
              :src="require('@/assets/userAvatar.png')"
            ></el-avatar>
          </div>
          <div class="user-details">
            <div class="user-name">{{ userInfo.name }}</div>
            <div class="user-email">{{ userInfo.email }}</div>
            <div class="user-actions">
              <el-tag size="mini">{{ userInfo.role }}</el-tag>
              <el-tag type="success" size="mini">{{ userInfo.plan }}</el-tag>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div class="balance-section">
            <!-- <div class="balance-item">
              <div class="balance-label">余额</div>
              <div class="balance-value">{{ userInfo.balance }}</div>
            </div> -->
            <div class="balance-item">
              <div class="balance-label">积分</div>
              <div class="balance-value">{{ userInfo.points }}</div>
              <el-button
                type="text"
                icon="el-icon-plus"
                class="charge-btn"
                @click="handleCharge"
                >充值</el-button
              >
              <el-button
                type="text"
                icon="el-icon-document"
                class="charge-btn"
                @click="showBillingRules"
                >计费规则</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 积分变更 -->
    <el-card class="points-card">
      <div class="card-header">
        <div class="header-left">
          <h2>积分变更</h2>
        </div>
        <div class="header-right">
          <el-button type="text" icon="el-icon-refresh" @click="fetchUserPoints"
            >刷新</el-button
          >
        </div>
      </div>
      <el-table
        :data="pointsHistory"
        style="width: 100%"
        v-loading="pointsLoading"
      >
        <el-table-column
          prop="source"
          label="来源"
          width="180"
        ></el-table-column>
        <el-table-column prop="points" label="数值"></el-table-column>
        <el-table-column prop="source" label="类型" width="100">
        </el-table-column>
        <el-table-column prop="creationTime" label="时间">
          <template slot-scope="scope">
            {{ formatDate(scope.row.creationTime) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="pointsParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pointsParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pointsTotal"
          @size-change="handlePointsSizeChange"
          @current-change="handlePointsCurrentChange"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 订单记录 -->
    <el-card class="order-card">
      <div class="card-header">
        <div class="header-left">
          <h2>订单记录</h2>
        </div>
        <div class="header-right">
          <el-button
            type="text"
            icon="el-icon-refresh"
            @click="fetchOrderRecords"
            >刷新</el-button
          >
        </div>
      </div>
      <el-table
        :data="orderHistory"
        style="width: 100%"
        v-loading="orderLoading"
      >
        <el-table-column
          prop="orderNo"
          label="订单号"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="source"
          label="来源"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="amount"
          label="金额"
          width="100"
        ></el-table-column>
        <el-table-column prop="remark" label="备注"></el-table-column>
        <el-table-column prop="creationTime" label="时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.creationTime) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="orderParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="orderParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="orderTotal"
          @size-change="handleOrderSizeChange"
          @current-change="handleOrderCurrentChange"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 使用记录详情弹窗 -->
    <el-dialog
      title="使用记录详情"
      :visible.sync="detailDialogVisible"
      :close-on-click-modal="false"
      width="80%"
      :before-close="handleDetailDialogClose"
      custom-class="detail-dialog"
      top="5vh"
    >
      <div v-loading="detailLoading">
        <div v-if="recordDetail.length > 0">
          <!-- 类型选择步骤条 -->
          <div class="node-steps-container">
            <div class="node-type-selector">
              <div
                v-for="(group, index) in groupedDetails"
                :key="index"
                class="type-item"
                :class="{ active: currentId === group.id }"
                @click="switchType(group.id, group.type)"
              >
                <div class="type-icon" :class="getIconColorClass(group.type)">
                  {{ group.icon }}
                  <div class="type-badge">{{ group.records.length }}</div>
                </div>
                <div class="type-name">{{ group.typeName }}</div>
              </div>
            </div>
          </div>

          <!-- 当前选中类型的详情记录 -->
          <div class="type-details" v-if="groupedDetails.length > 0">
            <el-timeline v-if="activeTypeIndex < groupedDetails.length">
              <el-timeline-item
                v-for="(item, itemIndex) in getCurrentTypeRecords()"
                :key="itemIndex"
                :timestamp="formatDetailDate(item)"
                placement="top"
                :class="{ 'is-collapsed': activeTimelineItem !== itemIndex }"
              >
                <div
                  class="timeline-header"
                  @click="toggleTimelineItem(itemIndex)"
                >
                  <span class="timeline-title"
                    >节点: {{ getDetailNodeType(item) }}</span
                  >
                  <i
                    :class="
                      activeTimelineItem === itemIndex
                        ? 'el-icon-arrow-up'
                        : 'el-icon-arrow-down'
                    "
                  ></i>
                </div>
                <el-card
                  class="detail-card"
                  v-show="activeTimelineItem === itemIndex"
                >
                  <!-- Embedding和OptimizeAgent类型的特殊展示 -->
                  <div v-if="['Embedding', 'OptimizeAgent'].includes(item.nodeType)" class="special-node-info">
                    <div class="info-row">
                      <span class="info-label">类型：</span>
                      <span class="info-value">{{ getDetailNodeType(item) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">时间：</span>
                      <span class="info-value">{{ formatDetailDate(item) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">模型：</span>
                      <span class="info-value" v-if="item.other" v-html="item.other"></span>
                      <span class="info-value" v-else>暂无数据</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">Token数量：</span>
                      <span class="info-value">{{ getDetailTokenNum(item) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">消耗积分：</span>
                      <span class="info-value">{{ getDetailPoints(item) }}</span>
                    </div>
                  </div>
                  
                  <!-- 其他类型的原有展示方式 -->
                  <div v-else>
                    <div class="detail-item">
                      <div class="detail-title">问题：</div>
                      <div
                        class="detail-content"
                        v-html="getChangeDetail(item, 'question')"
                      ></div>
                      <div class="detail-usage-info">
                        <span>消耗积分：{{ getDetailRequestPoints(item) }}</span>
                        <span
                          >Token数量：{{ getDetailRequestTokenNum(item) }}</span
                        >
                      </div>
                    </div>
                    <div class="detail-item" v-if="hasDetailResponse(item)">
                      <div class="detail-title">回答：</div>
                      <div
                        class="detail-content"
                        v-html="getChangeDetail(item, 'answer')"
                      ></div>
                      <div class="detail-usage-info">
                        <span>消耗积分：{{ getDetailResponsePoints(item) }}</span>
                        <span
                          >Token数量：{{ getDetailResponseTokenNum(item) }}</span
                        >
                      </div>
                    </div>
                    <div class="detail-item" v-if="['intentBranch', 'logicBranch'].includes(item.nodeType)">
                      <div class="detail-title">输出分支：</div>
                      <div
                        class="detail-content"
                        v-html="item.other"
                      ></div>
                    </div>
                    <div class="detail-info">
                      <span>节点类型：{{ getDetailNodeType(item) }}</span>
                      <!-- <span>总消耗积分：{{ getDetailPoints(item) }}</span>
                      <span>总Token数量：{{ getDetailTokenNum(item) }}</span> -->
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
        <div v-else class="empty-data">暂无详细数据</div>
      </div>
    </el-dialog>

    <!-- 使用记录 -->
    <el-card class="usage-card">
      <div class="card-header">
        <div class="header-left">
          <h2>使用记录</h2>
        </div>
        <div class="header-right">
          <el-select
            v-model="searchParams.flowDetailType"
            placeholder="请选择类型"
            class="filter-item"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="智能体" value="SessionFlow"></el-option>
            <el-option label="工作流" value="WorkFlow"></el-option>
            <el-option label="知识库写入" value="Embedding"></el-option>
            <el-option label="AI优化" value="OptimizeAgent"></el-option>
          </el-select>
          <el-date-picker
            v-model="searchParams.startTime"
            type="datetime"
            placeholder="开始时间"
            class="filter-item"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
          <el-date-picker
            v-model="searchParams.endTime"
            type="datetime"
            placeholder="结束时间"
            class="filter-item"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </div>
      </div>
      <el-table
        :data="usageHistory"
        style="width: 100%"
        v-loading="usageLoading"
      >
        <el-table-column
          prop="sessionID"
          label="会话ID"
          width="380"
        ></el-table-column>
        <el-table-column prop="flowDetailType" label="类型" width="100">
          <template slot-scope="scope">
            {{
              scope.row.flowDetailType === "WorkFlow" ? "工作流" : 
              scope.row.flowDetailType === "Embedding" ? "知识库写入" : 
              scope.row.flowDetailType === "OptimizeAgent" ? "AI优化" : "智能体"
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="问题"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <!-- 由于接口返回的数据中没有问题字段，暂时显示为空或者显示会话ID -->
            {{ scope.row.question  }}
          </template>
        </el-table-column>
        <el-table-column
          label="回答"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <!-- 由于接口返回的数据中没有问题字段，暂时显示为空或者显示会话ID -->
            {{ scope.row.answer  }}
          </template>
        </el-table-column>
        <el-table-column
          prop="tokenNum"
          label="Token长度"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="points"
          label="消耗积分"
          width="100"
        ></el-table-column>
        <el-table-column prop="triggerTime" label="使用时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.triggerTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="viewUsageDetail(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="usageParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="usageParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="usageTotal"
          @size-change="handleUsageSizeChange"
          @current-change="handleUsageCurrentChange"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 计费规则弹框 -->
    <el-dialog
      title="积分计费规则"
      :visible.sync="billingRulesDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-loading="billingRulesLoading" class="billing-rules-content">
        <div v-if="billingRules.length === 0 && !billingRulesLoading" class="no-data">
          暂无计费规则
        </div>
        <div v-else class="billing-rules-list">
          <div
            v-for="rule in billingRules"
            :key="rule.id"
            class="billing-rule-item"
          >
            <span class="rule-name">{{ rule.moduleName }}</span>
            <span class="rule-cost">{{ rule.pointCost }} 积分 = {{ rule.billingQty }} {{ rule.billingMethodUnit }}</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="billingRulesDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request";
import { formatOutputContent } from "@/utils";

export default {
  name: "ManageAccountPage",
  data() {
    return {
      searchParams: {
        flowDetailType: "",
        startTime: "",
        endTime: "",
      },
      userInfo: {
        name: "",
        email: "",
        phone: "",
        avatar:
          "https://cube.elemecdn.com/3/7c/********************************.png",
        role: "管理员",
        plan: "专业版",
        balance: "￥1000.00",
        points: "0",
      },
      pointsHistory: [],
      usageHistory: [],
      loading: false,
      pointsLoading: false,
      usageLoading: false,
      // 积分分页参数
      pointsParams: {
        page: 1,
        pageSize: 10,
      },
      pointsTotal: 0,
      // 使用记录分页参数
      usageParams: {
        page: 1,
        pageSize: 10,
      },
      usageTotal: 0,
      detailDialogVisible: false,
      detailLoading: false,
      recordDetail: [],
      currentRecordId: "",
      orderHistory: [],
      orderLoading: false,
      orderParams: {
        page: 1,
        pageSize: 10,
      },
      orderTotal: 0,
      activeTypeIndex: 0,
      groupedDetails: [],
      currentId: "",
      activeTimelineItem: 0,
      billingRulesDialogVisible: false,
      billingRulesLoading: false,
      billingRules: [],
    };
  },
  mounted() {
    this.fetchUserPoints();
    this.fetchPointsHistory();
    this.fetchUsageRecords();
    this.fetchOrderRecords();
    this.fetchUserRole();
  },
  methods: {
    handleCharge() {
      this.$router.push("/manage/team/upgrade?type=points");
    },

    async fetchUserPoints() {
      try {
        this.loading = true;
        const response = await api.points.getPoints();

        if (response && response.isSuccess) {
          this.userInfo.points = response.data.toString();

          // 获取本地存储的用户信息
          const userStr = localStorage.getItem("user");
          if (userStr) {
            const user = JSON.parse(userStr);
            this.userInfo.name = user.fullName || "";
            this.userInfo.email = user.email || "";
            this.userInfo.phone = user.phoneNumber || "";
          }
        } else {
          this.$showFriendlyError(null, response.message || "积分查询失败");
        }
      } catch (error) {
        console.error("获取积分失败:", error);
        this.$showFriendlyError(error, "获取积分失败，请稍后重试");
      } finally {
        this.loading = false;
      }
    },

    // 获取用户角色
    async fetchUserRole() {
      try {
        const response = await api.team.getUserRole();
        if (response && response.isSuccess) {
          this.userInfo.role = response.data ? '管理员' : '成员';
        } else {
          console.warn('获取用户角色失败:', response.message);
        }
      } catch (error) {
        console.error('获取用户角色失败:', error);
      }
    },

    // 节点类型转换中文
    nodeTypeToChinese(type) {
      if (!type) return "未知节点";

      // 对于applicationnode类型的特殊处理
      if (type.startsWith("applicationnode-")) {
        const match = type.match(/applicationnode-([^:]+):(.+)/);
        if (match && match[1] && match[2]) {
          const subType = match[2];
          return "智能体节点：" + this.getSubNodeTypeName(subType);
        }
        return "智能体节点";
      }

      // 完整显示类型的中文名，不进行合并
      switch (type) {
        case "start":
          return "开始节点";
        case "end":
          return "结束节点";
        case "knowledge":
          return "知识库节点";
        case "largeModel":
          return "大模型节点";
        case "intentBranch":
          return "意图分支节点";
        case "logicBranch":
          return "逻辑分支节点";
        case "database":
          return "数据库节点";
        case "code":
          return "代码节点";
        case "fixedContent":
          return "固定内容节点";
        case "plugin":
          return "接口插件节点";
        case "newPlugin":
          return "其它插件节点";
        case "humanTransfer":
          return "人工转接节点";
        case "custom":
          return "自定义节点";
        case "application:start":
          return "智能体开始节点";
        case "application:end":
          return "智能体结束节点";
        case "workflow:start":
          return "工作流开始节点";
        case "workflow:end":
          return "工作流结束节点";
        case "OptimizeAgent":
          return "AI优化";
        case "Embedding":
          return "知识库写入";
        default:
          return type;
      }
    },

    // 获取节点类型图标
    getNodeTypeIcon(type) {
      if (!type) return "el-icon-question";

      // 对于applicationnode类型使用特定图标
      if (type.startsWith("applicationnode-")) {
        return "el-icon-s-grid";
      }

      switch (type) {
        case "start":
        case "application:start":
        case "workflow:start":
          return "▶"; // 开始节点使用 ▶
        case "end":
        case "application:end":
        case "workflow:end":
          return "●"; // 结束节点使用 ●
        case "knowledge":
          return "📚"; // 知识库节点使用 📚
        case "largeModel":
          return "🧠"; // 大模型节点使用 🧠
        case "database":
          return "🗄️"; // 数据库节点使用 🗄️
        case "code":
          return "💻"; // 代码节点使用 💻
        case "fixedContent":
          return "📝"; // 固定内容节点使用 📝
        case "plugin":
          return "🔌"; // 接口插件节点使用 🔌
        case "newPlugin":
          return "🧩"; // 其它插件节点使用 🧩
        case "humanTransfer":
          return "👤"; // 转人工节点使用 👤
        case "intentBranch":
          return "⎈"; // 意图分支节点使用 ⎈
        case "logicBranch":
          return "⎇"; // 逻辑分支节点使用 ⎇
        case "application":
          return "🚀"; // 智能体节点使用 🚀
        case "OptimizeAgent":
          return "🤖"; // 提示词优化使用 🤖
        case "Embedding":
          return "🔍"; // 知识库写入使用 🔍
        default:
          return "el-icon-s-help";
      }
    },

    // 将详情记录按类型分组
    groupDetailsByType() {
      if (!this.recordDetail || this.recordDetail.length === 0) return [];

      const groups = {};
      const originalOrder = []; // 记录原始顺序

      // 按类型分组，只合并applicationnode
      this.recordDetail.forEach((item) => {
        if (!item) return;

        const type = item.nodeType || "未知";

        // 判断是否是 applicationnode 类型
        if (type.startsWith("applicationnode-")) {
          // 使用统一的key进行合并
          const groupKey = "applicationnode";

          if (!groups[groupKey]) {
            groups[groupKey] = {
              id: item.id,
              type: groupKey,
              typeName: "智能体节点",
              icon: "🚀",
              records: [],
            };
            // 记录首次出现的位置
            originalOrder.push(groupKey);
          }

          groups[groupKey].records.push(item);
          return; // 已处理完当前项，跳过下面的普通处理
        }

        // 非 applicationnode 类型，使用完整的原始类型作为键
        // 确保每个非applicationnode的节点都有自己的唯一分组
        const uniqueKey = type + "-" + item.id;
        if (!groups[uniqueKey]) {
          groups[uniqueKey] = {
            id: item.id,
            type: type,
            typeName: this.nodeTypeToChinese(type),
            icon: this.getNodeTypeIcon(type),
            records: [],
          };
          // 记录首次出现的位置
          originalOrder.push(uniqueKey);
        }

        groups[uniqueKey].records.push(item);
      });

      // 按照原始顺序返回结果
      return originalOrder.map((key) => groups[key]);
    },

    // 获取积分历史记录
    async fetchPointsHistory() {
      try {
        this.pointsLoading = true;
        const params = {
          skipCount: (this.pointsParams.page - 1) * this.pointsParams.pageSize,
          maxResultCount: this.pointsParams.pageSize,
        };

        const response = await api.points.getRecordList(params);

        if (response && response.isSuccess) {
          this.pointsHistory = response.data.items;
          this.pointsTotal = response.data.totalCount;
        }
      } catch (error) {
        console.error("获取积分历史失败:", error);
      } finally {
        this.pointsLoading = false;
      }
    },

    // 获取使用记录
    async fetchUsageRecords() {
      try {
        this.usageLoading = true;
        const params = {
          skipCount: (this.usageParams.page - 1) * this.usageParams.pageSize,
          maxResultCount: this.usageParams.pageSize,
        };

        // 添加查询条件

        params.flowDetailType = this.searchParams.flowDetailType;

        params.startTime = this.searchParams.startTime;

        params.endTime = this.searchParams.endTime;

        const response = await api.mainRequest.getList(params);

        if (response && response.isSuccess) {
          this.usageHistory = response.data.items;
          this.usageTotal = response.data.totalCount;
        }
      } catch (error) {
        console.error("获取使用记录失败:", error);
      } finally {
        this.usageLoading = false;
      }
    },

    // 查看使用记录详情
    async viewUsageDetail(row) {
      this.detailDialogVisible = true;
      this.detailLoading = true;
      this.currentRecordId = row.id;
      this.activeTimelineItem = 0;

      try {
        const response = await api.mainRequest.getDetail(this.currentRecordId);

        if (response && response.isSuccess) {
          this.recordDetail = response.data;
          this.groupedDetails = this.groupDetailsByType();
          this.activeTypeIndex = 0;
        } else {
          this.recordDetail = [];
          this.groupedDetails = [];
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.recordDetail = [];
        this.groupedDetails = [];
      } finally {
        this.detailLoading = false;
      }
    },

    // 关闭详情弹窗
    handleDetailDialogClose() {
      this.detailDialogVisible = false;
      this.recordDetail = [];
      this.groupedDetails = [];
      this.activeTypeIndex = 0;
    },

    // 格式化日期参数
    formatDateParam(date) {
      if (!date) return "";
      const d = new Date(date);
      return `${d.getFullYear()}-${(d.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${d.getDate().toString().padStart(2, "0")}`;
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return `${date.getFullYear()}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date
        .getHours()
        .toString()
        .padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    },

    // 积分分页大小变化
    handlePointsSizeChange(size) {
      this.pointsParams.pageSize = size;
      this.fetchPointsHistory();
    },

    // 积分当前页变化
    handlePointsCurrentChange(page) {
      this.pointsParams.page = page;
      this.fetchPointsHistory();
    },

    // 使用记录分页大小变化
    handleUsageSizeChange(size) {
      this.usageParams.pageSize = size;
      this.fetchUsageRecords();
    },

    // 使用记录当前页变化
    handleUsageCurrentChange(page) {
      this.usageParams.page = page;
      this.fetchUsageRecords();
    },

    // 查询按钮
    handleSearch() {
      this.usageParams.page = 1; // 重置到第一页
      this.fetchUsageRecords();
    },

    // 获取订单记录
    async fetchOrderRecords() {
      try {
        this.orderLoading = true;
        const params = {
          skipCount: (this.orderParams.page - 1) * this.orderParams.pageSize,
          maxResultCount: this.orderParams.pageSize,
        };

        const response = await api.points.getOrderRecordList(params);

        if (response && response.isSuccess) {
          this.orderHistory = response.data.items;
          this.orderTotal = response.data.totalCount;
        }
      } catch (error) {
        console.error("获取订单记录失败:", error);
      } finally {
        this.orderLoading = false;
      }
    },

    // 订单记录分页大小变化
    handleOrderSizeChange(size) {
      this.orderParams.pageSize = size;
      this.fetchOrderRecords();
    },

    // 订单记录当前页变化
    handleOrderCurrentChange(page) {
      this.orderParams.page = page;
      this.fetchOrderRecords();
    },

    // 获取当前类型记录
    getCurrentTypeRecords() {
      if (
        !this.groupedDetails ||
        this.groupedDetails.length === 0 ||
        this.activeTypeIndex >= this.groupedDetails.length
      ) {
        return [];
      }
      return this.groupedDetails[this.activeTypeIndex].records || [];
    },

    // 获取详情节点类型
    getDetailNodeType(item) {
      if (!item || !item.nodeType) return "未知节点";

      const type = item.nodeType;

      // 对于 applicationnode 类型显示更简洁的信息
      if (type.startsWith("applicationnode-")) {
        const match = type.match(/applicationnode-([^:]+):(.+)/);
        if (match && match[1] && match[2]) {
          const subType = match[2];
          return "智能体节点：" + this.getSubNodeTypeName(subType);
        }
      }

      return this.nodeTypeToChinese(type);
    },

    // 获取子节点类型名称
    getSubNodeTypeName(subType) {
      switch (subType) {
        case "start":
          return "开始";
        case "end":
          return "结束";
        case "knowledge":
          return "知识库";
        case "largeModel":
          return "大模型";
        case "intentBranch":
          return "意图分支";
        case "logicBranch":
          return "逻辑分支";
        case "database":
          return "数据库";
        case "code":
          return "代码";
        case "fixedContent":
          return "固定内容";
        case "plugin":
          return "插件";
        case "humanTransfer":
          return "人工转接";
        case "custom":
          return "自定义";
        case "OptimizeAgent":
          return "AI优化";
        case "Embedding":
          return "知识库写入";
        default:
          return subType;
      }
    },

    // 解析问答内容
    parseContentBlocks(content) {
      if (!content) return [];

      try {
        // 尝试解析为JSON
        const contentArray = JSON.parse(content);
        if (Array.isArray(contentArray)) {
          // 处理JSON数组格式
          return contentArray.map((item) => {
            return {
              content: item.内容 || "",
              score: item.得分 || 0,
              source: item.来源 || "",
            };
          });
        }
        return [];
      } catch (e) {
        // 如果不是JSON格式，则按纯文本处理
        return [
          {
            content: content,
            score: 1,
            source: "",
          },
        ];
      }
    },

    // 处理详情内容
    getChangeDetail(item, type) {
      if (!item) return "";

      const str = type === "question" ? item.question : item.answer;
      if (!str) return "";

      // 判断是否是JSON格式的内容块
      try {
        const contentBlocks = this.parseContentBlocks(str);
        if (contentBlocks.length > 0) {
          // 合并所有内容块的内容
          const formattedContent = contentBlocks
            .map((block) => block.content)
            .join("\n\n");
          return formatOutputContent(formattedContent);
        }
      } catch (e) {
        console.error("解析内容块失败:", e);
      }

      // 默认返回原始格式
      return formatOutputContent(str);
    },

    // 获取详情消耗积分
    getDetailPoints(item) {
      return (item.inPoints || 0) + (item.outPoints || 0);
    },

    // 获取详情Token数量
    getDetailTokenNum(item) {
      return (item.inTokens || 0) + (item.outTokens || 0);
    },

    // 获取问题消耗积分
    getDetailRequestPoints(item) {
      return item.inPoints || 0;
    },

    // 获取问题Token数量
    getDetailRequestTokenNum(item) {
      return item.inTokens || 0;
    },

    // 获取回答消耗积分
    getDetailResponsePoints(item) {
      return item.outPoints || 0;
    },

    // 获取回答Token数量
    getDetailResponseTokenNum(item) {
      return item.outTokens || 0;
    },

    // 获取详情日期
    formatDetailDate(item) {
      return this.formatDate(item.triggerTime);
    },

    // 判断是否有详情回答
    hasDetailResponse(item) {
      return item.answer && item.answer.trim() !== "";
    },

    // 获取图标颜色类
    getIconColorClass(type) {
      if (!type) return "";

      switch (type) {
        case "start":
        case "application:start":
        case "workflow:start":
          return "icon-start";
        case "end":
        case "application:end":
        case "workflow:end":
          return "icon-end";
        case "knowledge":
          return "icon-knowledge";
        case "largeModel":
          return "icon-model";
        case "database":
          return "icon-database";
        case "code":
          return "icon-code";
        case "fixedContent":
          return "icon-fixed";
        case "plugin":
          return "icon-plugin";
        case "humanTransfer":
          return "icon-human";
        case "intentBranch":
          return "icon-intent";
        case "logicBranch":
          return "icon-logic";
        case "applicationnode":
          return "icon-app";
        case "Embedding":
          return "icon-embedding";
        case "OptimizeAgent":
          return "icon-optimize";
        default:
          return "";
      }
    },

    // 切换类型
    switchType(id) {
      if (this.currentId === id) return; // 如果点击的是当前已选中的类型，不做任何操作
      this.currentId = id;
      // 找到当前选中类型的索引
      this.activeTypeIndex = this.groupedDetails.findIndex(
        (item) => item.id === id
      );
    },

    // 切换时间线项的展开状态
    toggleTimelineItem(index) {
      this.activeTimelineItem = this.activeTimelineItem === index ? -1 : index;
    },

    // 显示计费规则弹框
    showBillingRules() {
      this.billingRulesDialogVisible = true;
      this.fetchBillingRules();
    },

    // 获取计费规则
    async fetchBillingRules() {
      try {
        this.billingRulesLoading = true;
        const response = await api.points.getPointCostRulesList();

        if (response && response.isSuccess) {
          this.billingRules = response.data;
        }
      } catch (error) {
        console.error("获取计费规则失败:", error);
      } finally {
        this.billingRulesLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.manage-account-page {
  padding: 20px;
  background: #f5f7fa;

  .account-card,
  .points-card,
  .usage-card,
  .order-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      h2 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .account-header {
    @extend .card-header;
  }

  .account-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;

    .info-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .avatar-section {
        .el-avatar {
          border: 2px solid #e4e7ed;
        }
      }

      .user-details {
        .user-name {
          font-size: 18px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }

        .user-email {
          font-size: 14px;
          color: #606266;
          margin-bottom: 8px;
        }

        .user-actions {
          display: flex;
          gap: 8px;

          .el-tag {
            padding: 2px 8px;
            font-size: 12px;
            line-height: 12px;
          }
        }
      }
    }

    .info-right {
      .balance-section {
        display: flex;
        gap: 40px;

        .balance-item {
          text-align: center;

          .balance-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
          }

          .balance-value {
            font-size: 24px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 8px;
          }

          .charge-btn {
            color: var(--el-color-primary);
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
    }
  }

  .table-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .filter-item {
    margin-right: 12px;
    width: 160px;
  }

  .date-range {
    width: 240px;
  }
}

.detail-card {
  margin-bottom: 10px;

  .detail-item {
    margin-bottom: 10px;

    .detail-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .detail-content {
      word-break: break-word;
      white-space: pre-wrap;
      color: #606266;
    }
  }

  .detail-info {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: #909399;
    font-size: 12px;

    span {
      margin-right: 15px;
    }
  }
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}

.detail-dialog {
  .el-dialog__body {
    max-height: 75vh;
    overflow-y: auto;
  }
}

.node-steps-container {
  background: #fafafa;
  border-radius: 12px;
  padding: 20px 30px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.node-type-selector {
  display: flex;
  justify-content: space-between;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 21px;
    left: 70px;
    right: 70px;
    height: 2px;
    background: linear-gradient(80deg, #e0e0e0, #409eff, #e0e0e0);
    background-size: 200% 100%;
    animation: flowAnimation 5s infinite ease-in-out;
    z-index: 1;
  }

  .type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    cursor: pointer;
    padding: 0 10px;
    flex: 1;
    z-index: 2;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    &:hover {
      transform: translateY(-3px);

      .type-icon {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .type-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 10px;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      font-size: 26px;

      .type-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #909399;
        color: white;
        min-width: 18px;
        height: 18px;
        border-radius: 9px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 5px;
        transition: all 0.3s ease;
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
      }
    }

    .type-name {
      font-size: 13px;
      color: #606266;
      text-align: center;
      transition: all 0.3s ease;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 80px;
    }

    &.active {
      .type-icon {
        border-color: #409eff;
        box-shadow: 0 0 14px rgba(64, 158, 255, 0.5);
        transform: scale(1.15);

        &::after {
          content: "";
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          border-radius: 50%;
          border: 1px solid rgba(64, 158, 255, 0.3);
          animation: pulse 1.5s infinite;
        }
      }

      .type-name {
        color: #409eff;
        font-weight: bold;
        transform: scale(1.05);
      }

      .type-badge {
        background: #409eff;
        transform: scale(1.2);
      }
    }
  }
}

.type-details {
  margin-top: 30px;
  animation: fadeIn 0.5s ease;

  .el-timeline {
    padding-left: 20px;
  }

  .el-timeline-item {
    margin-bottom: 25px;
    position: relative;
    transition: all 0.3s ease;

    &:hover {
      .detail-card {
        transform: translateX(5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
      }
    }

    &.is-collapsed {
      margin-bottom: 15px;

      .el-timeline-item__content {
        min-height: 32px;
      }
    }

    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f5f7fa;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 10px;
      transition: all 0.3s ease;

      &:hover {
        background: #ecf5ff;
      }

      .timeline-title {
        font-weight: 500;
        color: #303133;
      }

      i {
        color: #909399;
        transition: transform 0.3s ease;
      }
    }
  }
}

.detail-card {
  margin-bottom: 10px;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;

  .detail-item {
    margin-bottom: 10px;

    .detail-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    .detail-content {
      word-break: break-word;
      white-space: pre-wrap;
      color: #606266;
    }
  }

  .detail-info {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: #909399;
    font-size: 12px;

    span {
      margin-right: 15px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.detail-usage-info {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.detail-info {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  color: #909399;
  font-size: 12px;

  span {
    margin-right: 15px;
  }
}

.special-node-info {
  padding: 16px;

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      margin-right: 12px;
    }

    .info-value {
      color: #303133;
      flex: 1;
    }
  }
}

.billing-rules-content {
  .no-data {
    text-align: center;
    color: #909399;
    padding: 20px;
    font-size: 14px;
  }

  .billing-rules-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .billing-rule-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 2px;
      background-color: transparent;
      color: #303133;
      font-size: 14px;
      transition: all 0.2s ease;
      border-radius: 4px;

      &:hover {
        background-color: #f5f7fa;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .rule-name {
        flex: 1;
        text-align: left;
        color: #303133;
        font-weight: 500;
      }

      .rule-cost {
        flex-shrink: 0;
        text-align: right;
        color: #606266;
        margin-left: 16px;
      }
    }
  }
}
</style>

<style>
/* 使用非scoped样式处理深度选择器和图标颜色 */
.detail-dialog .detail-content .markdown-content {
  line-height: 1.6;
}

.detail-dialog .detail-content .markdown-content h1,
.detail-dialog .detail-content .markdown-content h2,
.detail-dialog .detail-content .markdown-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
}

.detail-dialog .detail-content .markdown-content code.inline-code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.detail-dialog .detail-content .markdown-content .code-container {
  margin: 10px 0;
  border-radius: 5px;
  overflow: hidden;
}

.detail-dialog .detail-content .markdown-content .code-container .code-header {
  background-color: #f1f1f1;
  padding: 8px 12px;
  font-size: 12px;
  color: #606266;
}

.detail-dialog .detail-content .markdown-content .code-container .code-block {
  margin: 0;
  padding: 12px;
  background-color: #f8f8f8;
  overflow-x: auto;
}

.detail-dialog .detail-content .markdown-content ul,
.detail-dialog .detail-content .markdown-content ol {
  padding-left: 20px;
  margin: 10px 0;
}

.detail-dialog .detail-content .markdown-content blockquote {
  border-left: 4px solid #dfe2e5;
  padding-left: 16px;
  color: #6a737d;
  margin: 10px 0;
}

/* 图标颜色样式 */
.node-type-selector .type-item .type-icon.icon-start {
  color: #67c23a;
}
.node-type-selector .type-item .type-icon.icon-end {
  color: #ee371e;
}
.node-type-selector .type-item .type-icon.icon-knowledge {
  color: #e6a23c;
}
.node-type-selector .type-item .type-icon.icon-model {
  color: #9254de;
}
.node-type-selector .type-item .type-icon.icon-database {
  color: #f56c6c;
}
.node-type-selector .type-item .type-icon.icon-code {
  color: #00b3a4;
}
.node-type-selector .type-item .type-icon.icon-fixed {
  color: #909399;
}
.node-type-selector .type-item .type-icon.icon-plugin {
  color: #ff9900;
}
.node-type-selector .type-item .type-icon.icon-human {
  color: #ff6b6b;
}
.node-type-selector .type-item .type-icon.icon-intent {
  color: #36cfc9;
}
.node-type-selector .type-item .type-icon.icon-logic {
  color: #7367f0;
}
.node-type-selector .type-item .type-icon.icon-app {
  color: #1890ff;
}
.node-type-selector .type-item .type-icon.icon-embedding {
  color: #52c41a;
}
.node-type-selector .type-item .type-icon.icon-optimize {
  color: #722ed1;
}

/* 新增 Timeline 相关样式 */
.el-timeline-item__timestamp {
  color: #606266;
  font-size: 13px;
  transition: all 0.3s ease;
  padding: 2px 8px;
  border-radius: 4px;
}

.el-timeline-item__node {
  transition: all 0.3s ease;
}

.el-timeline-item__tail {
  transition: all 0.3s ease;
}

.el-timeline-item__content {
  width: 100%;
}

/* 添加悬停效果 */
.type-details .el-timeline-item:hover .el-timeline-item__timestamp {
  transform: translateY(-5px);
  color: #409eff;
  font-weight: bold;
}

.type-details .el-timeline-item:hover .el-timeline-item__node {
  transform: scale(1.2);
  background-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
}

.type-details .el-timeline-item:hover .el-timeline-item__tail {
  border-left-color: #409eff;
}

/* 动画效果 */
.detail-dialog {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 添加流动线条动画效果 */
@keyframes flowAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* .node-type-selector::after {
  content: '';
  position: absolute;
  top: 21px;
  left: 70px;
  right: 70px;
  height: 2px;
  background: linear-gradient(90deg, #e0e0e0, #409eff, #e0e0e0);
  background-size: 200% 100%;
  animation: flowAnimation 3s infinite ease-in-out;
  z-index: 1;
} */

/* .node-type-selector .type-item.active::before {
  content: '';
  position: absolute;
  top: 45px;
  left: 25px;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #409eff, transparent);
  background-size: 200% 100%;
  animation: flowAnimation 2s infinite ease-in-out;
  z-index: 1;
} */
</style>
