<template>
  <div class="access-page">
    <router-view v-if="$route.path !== '/access'"></router-view>
    <div v-else>
      <h2>接入管理</h2>
      <div class="access-content">
        <el-card class="access-card">
          <div class="access-header">
            <h3>API 接入</h3>
            <p>通过API接口接入您的应用</p>
          </div>
          <el-button type="primary" @click="showApiDocs">查看文档</el-button>
        </el-card>

        <el-card class="access-card">
          <div class="access-header">
            <h3>SDK 接入</h3>
            <p>使用SDK快速接入您的应用</p>
          </div>
          <el-button type="primary" plain @click="showSdkDocs">获取SDK</el-button>
        </el-card>

        <el-card class="access-card">
          <div class="access-header">
            <h3>插件接入</h3>
            <p>使用插件方式接入您的应用</p>
          </div>
          <el-button type="primary" plain @click="showPluginDocs">浏览插件</el-button>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AccessPage',
  methods: {
    showApiDocs() {
      this.$message.info('API文档功能开发中...')
    },
    showSdkDocs() {
      this.$message.info('SDK文档功能开发中...')
    },
    showPluginDocs() {
      this.$message.info('插件文档功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.access-page {
  padding: 20px;
  height: 100%;
  .access-content {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;

    .access-card {
      height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 20px;

      .access-header {
        h3 {
          margin: 0 0 10px;
          font-size: 18px;
          color: #303133;
        }

        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
