<template>
  <div class="temporary-chat-page">
    <SimpleChatIndex :sourceType="1" />
  </div>
</template>

<script>
import SimpleChatIndex from './components/SimpleChatIndex.vue'

export default {
  name: 'TemporaryChat',
  components: {
    SimpleChatIndex
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.temporary-chat-page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #f5f7fa;
  border-radius: 12px;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;

  &:hover {
    background: rgba(0, 0, 0, 0.2);
  }
}
</style>
