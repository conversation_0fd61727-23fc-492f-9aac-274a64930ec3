<template>
  <div class="plugin-page">
    <div class="construction-container">
      <div class="construction-icon">
        <i class="el-icon-set-up"></i>
      </div>
      <h3>插件</h3>
      <p>正在努力建设中</p>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <div class="construction-text">
        我们正在为您开发强大的插件系统，让您的应用更加智能！
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PluginPage'
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.plugin-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f5f5;

  .construction-container {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 16px;
    box-shadow: $box-shadow-light;
    max-width: 400px;

    .construction-icon {
      margin-bottom: 24px;

      i {
        font-size: 80px;
        color: #667eea;
        opacity: 0.8;
      }
    }

    h3 {
      font-size: 24px;
      color: $text-color-primary;
      margin-bottom: 16px;
      font-weight: 600;
    }

    p {
      font-size: 18px;
      color: #667eea;
      margin-bottom: 24px;
      font-weight: 500;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: $border-color-light;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 24px;

      .progress-fill {
        width: 45%;
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        animation: progress-animation 2s ease-in-out infinite;
      }
    }

    .construction-text {
      font-size: 14px;
      color: $text-color-secondary;
      line-height: 1.6;
    }
  }
}

@keyframes progress-animation {
  0% {
    width: 25%;
  }
  50% {
    width: 65%;
  }
  100% {
    width: 45%;
  }
}
</style>
