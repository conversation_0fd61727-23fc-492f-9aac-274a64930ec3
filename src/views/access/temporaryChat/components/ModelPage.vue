<template>
  <div class="model-page">
    <div class="construction-container">
      <div class="construction-icon">
        <i class="el-icon-cpu"></i>
      </div>
      <h3>模型</h3>
      <p>正在努力建设中</p>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <div class="construction-text">
        我们正在集成更多先进的AI模型，为您提供更强大的智能服务！
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModelPage'
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.model-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

  .construction-container {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 16px;
    box-shadow: $box-shadow-light;
    max-width: 400px;

    .construction-icon {
      margin-bottom: 24px;

      i {
        font-size: 80px;
        color: #ff9a9e;
        opacity: 0.8;
      }
    }

    h3 {
      font-size: 24px;
      color: $text-color-primary;
      margin-bottom: 16px;
      font-weight: 600;
    }

    p {
      font-size: 18px;
      color: #ff9a9e;
      margin-bottom: 24px;
      font-weight: 500;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: $border-color-light;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 24px;

      .progress-fill {
        width: 75%;
        height: 100%;
        background: linear-gradient(90deg, #ff9a9e, #fecfef);
        animation: progress-animation 2s ease-in-out infinite;
      }
    }

    .construction-text {
      font-size: 14px;
      color: $text-color-secondary;
      line-height: 1.6;
    }
  }
}

@keyframes progress-animation {
  0% {
    width: 55%;
  }
  50% {
    width: 85%;
  }
  100% {
    width: 75%;
  }
}
</style>
