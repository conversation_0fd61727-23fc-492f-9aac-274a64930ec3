<template>
  <div class="simple-workflow-chat-panel">
    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>正在加载工作流信息...</span>
      </div>
    </div>

    <!-- 工作流面板内容 - 左中右三栏布局 -->
    <template v-else-if="isPanelReady">
      <el-row :gutter="10" class="full-height">
        <!-- 中间表单区域 -->
        <el-col :span="14" class="full-height">
          <div class="form-panel">
            <div class="panel-header">
              <h3>
                {{ (appInfo && appInfo.name) || "工作流执行" }}
                <el-tag v-if="appInfo && currentSessionId" type="info" size="mini">
                  ID: {{ currentSessionId }}
                </el-tag>
              </h3>
              <div v-if="showControls" class="header-actions">
                <el-button
                  size="mini"
                  icon="el-icon-refresh"
                  circle
                  @click="refreshChat"
                ></el-button>
                <el-button
                  v-if="showCloseButton"
                  size="mini"
                  icon="el-icon-back"
                  circle
                  @click="closeChat"
                ></el-button>
              </div>
            </div>

            <div class="form-container">
              <el-form :model="formData" ref="formRef" :disabled="isExecuting" :rules="formRules">
                <!-- 文本输入 - 如果配置中启用 -->
                <el-form-item v-if="enableTextInput" :label="textInputName" prop="question" :required="startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.textInputRequired">
                  <div class="input-container">
                    <el-input
                      type="textarea"
                      :rows="4"
                      :placeholder="(startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.textInputPlaceholder) || `请输入${textInputName}`"
                      v-model="formData.question"
                      :disabled="isExecuting"
                    ></el-input>
                  </div>
                </el-form-item>

                <!-- 简化模式下的问题描述输入 -->
                <el-form-item v-if="isSimpleMode" prop="question">
                  <div class="input-container">
                    <el-input
                      type="textarea"
                      :rows="4"
                      placeholder="请输入问题描述"
                      v-model="formData.question"
                      :disabled="isExecuting"
                    ></el-input>
                  </div>
                </el-form-item>

                <!-- 简化模式下的图片上传 - 如果imageInputRequired为true -->
                <el-form-item v-if="isSimpleMode && startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.imageInputRequired === true" :label="imageInputName" prop="imageFileList">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    :file-list="formData.imageFileList || []"
                    :on-change="handleImageChange"
                    :on-exceed="handleImageExceed"
                    :limit="1"
                    accept="image/*"
                    :disabled="isExecuting"
                  >
                    <el-button type="primary" :disabled="isExecuting">点击上传图片</el-button>
                    <div slot="tip" class="el-upload__tip">
                      {{ (startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.imageInputPlaceholder) || '只能上传jpg/png文件，且不超过10MB，仅支持单张图片' }}
                    </div>
                  </el-upload>
                </el-form-item>

                <!-- 图片上传 - 如果配置中启用 -->
                <el-form-item v-if="enableImageInput" :label="imageInputName" prop="imageFileList" :required="startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.imageInputRequired">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    :file-list="formData.imageFileList || []"
                    :on-change="handleImageChange"
                    :on-exceed="handleImageExceed"
                    :limit="1"
                    accept="image/*"
                    :disabled="isExecuting"
                  >
                    <el-button type="primary" :disabled="isExecuting">点击上传图片</el-button>
                    <div slot="tip" class="el-upload__tip">
                      {{ (startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.imageInputPlaceholder) || '只能上传jpg/png文件，且不超过10MB，仅支持单张图片' }}
                    </div>
                  </el-upload>
                </el-form-item>

                <!-- 文件上传 - 如果配置中启用 -->
                <el-form-item v-if="enableFileInput" :label="fileInputName" prop="fileList" :required="startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.fileInputRequired">
                  <el-upload
                    class="upload-demo"
                    action="#"
                    :auto-upload="false"
                    :file-list="formData.fileList"
                    :on-change="handleFileChange"
                    :disabled="isExecuting"
                  >
                    <el-button type="primary" :disabled="isExecuting">点击上传文件</el-button>
                    <div slot="tip" class="el-upload__tip">
                      {{ startNodeConfig && startNodeConfig.inputSettings && startNodeConfig.inputSettings.fileInputPlaceholder || '上传文件大小不超过10MB' }}
                    </div>
                  </el-upload>
                </el-form-item>

                <!-- 自定义变量字段 - 根据工作流配置动态生成 -->
                <template v-if="customVariables && customVariables.length > 0">
                  <template v-for="variable in customVariables">
                    <el-form-item
                      v-if="variable.name !== 'sys_current_time'"
                      :key="variable.name"
                      :label="variable.displayName"
                      :prop="'customVariables.' + variable.name"
                      :required="variable.required"
                    >
                      <!-- 文本类型输入框 -->
                      <el-input
                        v-if="variable.type === 1"
                        v-model="formData.customVariables[variable.name]"
                        :placeholder="variable.placeholder || '请输入' + variable.displayName"
                        :disabled="isExecuting"
                      ></el-input>

                      <!-- 选择类型 -->
                      <el-select
                        v-else-if="variable.type === 2"
                        v-model="formData.customVariables[variable.name]"
                        :placeholder="variable.placeholder || '请选择' + variable.displayName"
                        style="width: 100%"
                        :disabled="isExecuting"
                      >
                        <el-option
                          v-for="option in variable.options"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </template>
              </el-form>
            </div>

            <!-- 表单底部执行按钮区域 -->
            <div class="form-bottom-actions">
              <div class="left-actions">
                <!-- 清除记忆按钮 -->
                <el-button
                  type="warning"
                  size="small"
                  @click="clearChatMemory"
                  :disabled="isExecuting || !hasResult"
                  title="清除记忆"
                >
                  <i class="el-icon-delete"></i>
                  清除记忆
                </el-button>
              </div>
              <div class="right-actions">
                <el-button
                  type="primary"
                  class="execute-btn"
                  @click="executeWorkflow"
                  :loading="isExecuting"
                  :disabled="!isFormValid"
                >
                  {{ isExecuting ? '执行中...' : '开始运行' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 右侧结果区域 -->
        <el-col :span="10" class="full-height">
          <div class="result-panel">
            <div class="panel-header">
              <h3>执行结果</h3>
              <div class="header-actions">
                <el-button size="mini" icon="el-icon-refresh" circle @click="refreshResult"></el-button>
              </div>
            </div>

            <div class="result-container" ref="resultContainer">
              <div v-if="!hasResult" class="empty-result">
                <i class="el-icon-warning-outline"></i>
                <p>请点击"开始运行"按钮执行工作流</p>
              </div>
              <div v-else>
                <!-- 右上角时间戳 -->
                <div class="result-time">{{ latestResult.time }}</div>

                <!-- 用户输入 -->
                <div class="chat-message user-message">
                  <div class="avatar user-avatar">
                    <img
                      src="@/assets/userAvatar.png"
                      @error="handleUserAvatarError"
                      alt="用户"
                    >
                  </div>
                  <div class="message-content">
                    <div class="message-text">
                      <!-- 显示文本输入 -->
                      <div v-if="latestResult.textInput">{{ latestResult.textInput }}</div>

                      <!-- 显示自定义变量 -->
                      <div v-if="hasValidCustomVariables" class="custom-variables">
                        <template v-for="(value, key) in latestResult.customVariables">
                          <div v-if="key !== 'sys_current_time' && value" :key="key" class="variable-item">
                            <span class="variable-name">{{ getVariableDisplayName(key) }}:</span>
                            <span class="variable-value">{{ value }}</span>
                          </div>
                        </template>
                      </div>

                      <!-- 显示文件和图片上传情况 -->
                      <div v-if="latestResult.hasImage" class="uploaded-files">
                        <div class="file-info">已上传图片: 1个</div>
                        <!-- 图片预览 -->
                        <div v-if="latestResult.imageUrl" class="image-preview">
                          <img :src="latestResult.imageUrl" alt="上传的图片" @click="previewImage(latestResult.imageUrl)" />
                        </div>
                      </div>
                      <div v-if="latestResult.hasFile" class="uploaded-files">
                        <div class="file-info">已上传文件: 1个</div>
                        <!-- 文件信息 -->
                        <div v-if="latestResult.fileName" class="file-name">
                          <i class="el-icon-document"></i>
                          {{ latestResult.fileName }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 系统回复 -->
                <div class="chat-message system-message">
                  <div class="avatar system-avatar">
                    <img
                      :src="(appInfo && appInfo.profilePhoto) || ''"
                      @error="handleSystemAvatarError"
                      alt="系统"
                    >
                  </div>
                  <div class="message-content">
                    <div class="message-wrapper">
                      <!-- 消息操作按钮 - 右上角 -->
                      <div v-if="latestResult.message" class="message-actions-top">
                        <!-- 语音朗读按钮 -->
                        <button
                          class="action-btn voice-read-btn"
                          @click="readMessage(latestResult.message, 0)"
                          :disabled="isLoadingAudio && currentReadingMessageIndex !== 0"
                          :title="getVoiceReadButtonTitle(0)"
                        >
                          <i :class="getVoiceReadButtonIcon(0)"></i>
                        </button>
                        <!-- 一键复制按钮 -->
                        <button
                          class="action-btn copy-btn"
                          @click="copyMessage(latestResult.message)"
                          title="复制内容"
                        >
                          <i class="el-icon-document-copy"></i>
                        </button>
                      </div>
                      <!-- 工作流执行过程展示 - 优先显示动态步骤，否则显示静态流程 -->
                      <div v-if="latestResult.executionSteps && latestResult.executionSteps.length > 0" class="workflow-execution-steps">
                        <div class="execution-steps-list">
                          <div
                            v-for="(step, stepIndex) in latestResult.executionSteps"
                            :key="'step_' + stepIndex"
                            class="execution-step"
                            :class="{ 'current': step.isCurrent, 'completed': step.isCompleted }"
                          >
                            <div class="step-indicator">
                              <i v-if="step.isCompleted" class="el-icon-check"></i>
                              <i v-else-if="step.isCurrent" class="el-icon-loading"></i>
                              <span v-else class="step-number">{{ stepIndex + 1 }}</span>
                            </div>
                            <div class="step-content">
                              <span class="step-label">{{ getStepDisplayName(step.nodeType) }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- 静态工作流展示 - 当没有动态步骤时显示 -->
                      <div v-else class="message-flow">
                        <div class="flow-node" v-for="(node, index) in workflowNodes" :key="index">
                          <div class="node-icon" :class="{'node-active': index <= currentNodeIndex}">
                            <i :class="getNodeIcon(node)"></i>
                          </div>
                          <div class="node-connector" v-if="index < workflowNodes.length - 1"></div>
                        </div>
                      </div>
                      <div class="message-text" v-html="latestResult.message"></div>
                      <!-- 流式传输指示器 -->
                      <div v-if="isExecuting && latestResult.status === 'streaming'" class="streaming-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </template>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="el-icon-chat-line-square"></i>
      <p>请选择一个会话开始工作流</p>
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request";
import { formatOutputContent } from '@/utils';

export default {
  name: "SimpleWorkflowChatPanel",
  props: {
    // 应用信息
    appInfo: {
      type: Object,
      default: () => ({})
    },
    // 会话ID
    sessionId: {
      type: String,
      default: null
    },
    // 应用ID
    appId: {
      type: [String, Number],
      default: null
    },
    // 来源类型
    sourceType: {
      type: Number,
      default: 2 // 工作流默认为2
    },
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 工作流详情
      workflowDetail: null,
      // 表单数据
      formData: {
        question: '',
        fileList: [],
        imageFileList: [],
        customVariables: {}
      },
      // 表单验证规则
      formRules: {
        question: [
          { required: false, message: '请输入问题描述', trigger: 'blur' }
        ],
        imageFileList: [
          {
            required: false,
            message: '请上传图片',
            trigger: 'change',
            validator: (rule, value, callback) => {
              // 如果是简化模式且图片上传必填
              if (this.isSimpleMode && this.startNodeConfig && this.startNodeConfig.inputSettings && this.startNodeConfig.inputSettings.imageInputRequired === true) {
                if (!value || value.length === 0) {
                  callback(new Error(`请上传${this.imageInputName}`));
                  return;
                }
              }
              // 如果是非简化模式且图片上传必填
              if (!this.isSimpleMode && this.enableImageInput && this.startNodeConfig && this.startNodeConfig.inputSettings && this.startNodeConfig.inputSettings.imageInputRequired === true) {
                if (!value || value.length === 0) {
                  callback(new Error(`请上传${this.imageInputName}`));
                  return;
                }
              }
              callback();
            }
          }
        ],
        fileList: [
          { required: false, message: '请上传文件', trigger: 'change' }
        ]
      },
      // 是否正在执行
      isExecuting: false,
      // 是否正在加载
      isLoading: false,
      // 面板是否准备好
      isPanelReady: false,
      // 当前会话ID
      currentSessionId: null,
      // 是否有结果
      hasResult: false,
      // 最新结果数据
      latestResult: {
        status: '',
        time: '',
        message: '',
        textInput: '',
        customVariables: {},
        hasImage: false,
        hasFile: false,
        executionSteps: []
      },
      // 工作流节点
      workflowNodes: ['开始', '固定节点', '插件', '结束'],
      // 当前节点索引
      currentNodeIndex: -1,

      // 语音朗读相关数据
      speechSynthesis: null, // 语音合成对象（本地朗读备用）
      isReading: false, // 是否正在朗读
      currentReadingMessageIndex: -1, // 当前朗读的消息索引
      currentAudio: null, // 当前播放的音频对象
      isLoadingAudio: false, // 是否正在加载音频

      // 流式输出相关数据
      streamAbortController: null, // 流式请求控制器，用于取消请求
      streamingMessage: '', // 当前流式接收的消息内容
      isStreamCompleted: false, // 流式响应是否完成

    };
  },
  computed: {
    // 获取工作流开始节点配置
    startNodeConfig() {
      if (!this.workflowDetail || !this.workflowDetail.flowDetailDto || !this.workflowDetail.flowDetailDto.startNodes || !this.workflowDetail.flowDetailDto.startNodes.length) {
        return null;
      }

      const startNode = this.workflowDetail.flowDetailDto.startNodes[0];
      return startNode.data && startNode.data.nodeTypeConfig ? startNode.data.nodeTypeConfig : null;
    },

    // 获取自定义变量列表
    customVariables() {
      if (!this.startNodeConfig || !this.startNodeConfig.inputSettings || !this.startNodeConfig.inputSettings.customVariables) {
        return [];
      }

      return this.startNodeConfig.inputSettings.customVariables;
    },

    // 是否启用文本输入
    enableTextInput() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings &&
             this.startNodeConfig.inputSettings.enableTextInput;
    },

    // 获取文本输入名称
    textInputName() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings ?
             this.startNodeConfig.inputSettings.textInputName || '问题描述' :
             '问题描述';
    },

    // 是否启用图片上传
    enableImageInput() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings &&
             this.startNodeConfig.inputSettings.enableImageInput;
    },

    // 获取图片上传名称
    imageInputName() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings ?
             this.startNodeConfig.inputSettings.imageInputName || '上传图片' :
             '上传图片';
    },

    // 是否启用文件上传
    enableFileInput() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings &&
             this.startNodeConfig.inputSettings.enableFileInput;
    },

    // 获取文件上传名称
    fileInputName() {
      return this.startNodeConfig &&
             this.startNodeConfig.inputSettings ?
             this.startNodeConfig.inputSettings.fileInputName || '上传文件' :
             '上传文件';
    },

    // 判断是否为简化模式
    isSimpleMode() {
      // 如果工作流详情不存在，进入简化模式
      if (!this.workflowDetail?.flowDetailDto) {
        return true;
      }

      // 如果图片上传是必需的，不进入简化模式
      if (this.startNodeConfig?.inputSettings?.imageInputRequired === true) {
        return false;
      }

      // 原有的简化模式判断逻辑
      return (this.enableFileInput === false &&
        this.enableImageInput === false &&
        this.enableTextInput === false &&
        (!this.customVariables || this.customVariables.length === 0));
    },

    // 判断表单是否有效，可以提交
    isFormValid() {
      // 简化模式下的验证
      if (this.isSimpleMode) {
        // 简化模式下，问题描述通常是必填的
        const hasQuestionInput = this.formData.question && this.formData.question.trim();

        // 如果配置了必填图片上传
        if (this.startNodeConfig?.inputSettings?.imageInputRequired === true) {
          const hasRequiredImage = this.formData.imageFileList && this.formData.imageFileList.length > 0;
          // 简化模式下，如果需要图片，则问题描述和图片都需要
          return hasQuestionInput && hasRequiredImage;
        }

        // 简化模式下，只需要问题描述
        return hasQuestionInput;
      }

      // 完整模式下的验证
      let isValid = true;

      // 检查文本输入
      if (this.enableTextInput && this.startNodeConfig?.inputSettings?.textInputRequired) {
        const hasTextInput = this.formData.question && this.formData.question.trim();
        if (!hasTextInput) {
          isValid = false;
        }
      }

      // 检查图片上传
      if (this.enableImageInput && this.startNodeConfig?.inputSettings?.imageInputRequired) {
        const hasImageInput = this.formData.imageFileList && this.formData.imageFileList.length > 0;
        if (!hasImageInput) {
          isValid = false;
        }
      }

      // 检查文件上传
      if (this.enableFileInput && this.startNodeConfig?.inputSettings?.fileInputRequired) {
        const hasFileInput = this.formData.fileList && this.formData.fileList.length > 0;
        if (!hasFileInput) {
          isValid = false;
        }
      }

      // 检查自定义变量
      if (this.customVariables && this.customVariables.length > 0) {
        for (const variable of this.customVariables) {
          if (variable.required && variable.name !== 'sys_current_time') {
            const value = this.formData.customVariables[variable.name];
            if (!value || (typeof value === 'string' && !value.trim())) {
              isValid = false;
              break;
            }
          }
        }
      }

      // 如果没有配置任何必填项，至少需要有一个输入
      if (isValid && !this.hasAnyRequiredField) {
        // 检查是否至少有一个字段有值
        const hasQuestion = this.formData.question && this.formData.question.trim();
        const hasImage = this.formData.imageFileList && this.formData.imageFileList.length > 0;
        const hasFile = this.formData.fileList && this.formData.fileList.length > 0;
        const hasCustomVariables = this.customVariables && this.customVariables.some(v => {
          const value = this.formData.customVariables[v.name];
          return value && (typeof value !== 'string' || value.trim());
        });

        isValid = hasQuestion || hasImage || hasFile || hasCustomVariables;
      }

      return isValid;
    },

    // 检查是否有任何必填字段
    hasAnyRequiredField() {
      // 简化模式下，问题描述通常是必填的，或者图片上传是必填的
      if (this.isSimpleMode) {
        return true; // 简化模式下至少问题描述是必填的
      }

      // 检查完整模式下的必填字段
      const hasRequiredText = this.enableTextInput && this.startNodeConfig?.inputSettings?.textInputRequired;
      const hasRequiredImage = this.enableImageInput && this.startNodeConfig?.inputSettings?.imageInputRequired;
      const hasRequiredFile = this.enableFileInput && this.startNodeConfig?.inputSettings?.fileInputRequired;
      const hasRequiredVariables = this.customVariables && this.customVariables.some(v => v.required && v.name !== 'sys_current_time');

      return hasRequiredText || hasRequiredImage || hasRequiredFile || hasRequiredVariables;
    },

    // 检查是否有有效的自定义变量值
    hasValidCustomVariables() {
      if (!this.latestResult.customVariables || Object.keys(this.latestResult.customVariables).length === 0) {
        return false;
      }

      // 检查是否有任何非空的自定义变量值（排除系统时间变量）
      return Object.keys(this.latestResult.customVariables).some(key => {
        if (key === 'sys_current_time') return false;
        const value = this.latestResult.customVariables[key];
        return value && (typeof value !== 'string' || value.trim());
      });
    },


  },
  watch: {
    // 监听会话ID变化
    sessionId: {
      handler(newSessionId, oldSessionId) {
        if (newSessionId && newSessionId !== oldSessionId) {
          console.log("SimpleWorkflowChatPanel: 会话ID变化:", oldSessionId, "->", newSessionId);
          this.handleSessionChange(newSessionId);
        }
      },
      immediate: true
    },
    // 监听应用ID变化
    appId: {
      handler(newAppId) {
        if (newAppId) {
          this.initializePanel();
        }
      },
      immediate: true
    }
  },
  created() {
    // 初始化语音功能
    this.initSpeechSynthesis();
  },
  beforeDestroy() {
    // 清理语音相关资源
    this.cleanupVoiceFeatures();
    // 清理流式请求
    this.cleanupStreamRequest();
  },
  methods: {
    // 初始化面板
    async initializePanel() {
      if (!this.appId) {
        console.warn("SimpleWorkflowChatPanel: appId 未提供");
        return;
      }

      console.log("SimpleWorkflowChatPanel 初始化:", {
        appId: this.appId,
        sessionId: this.sessionId,
        sourceType: this.sourceType
      });

      this.isLoading = true;

      try {
        // 获取工作流详情
        await this.getWorkflowDetail();
        this.isPanelReady = true;
      } catch (error) {
        console.error("初始化工作流面板失败:", error);
        this.$showFriendlyError(error, "加载工作流配置失败，请重试");
      } finally {
        this.isLoading = false;
      }
    },

    // 获取工作流详情
    async getWorkflowDetail() {
      try {
        console.log("获取工作流详情，ID:", this.appId);
        const result = await api.workflow.getDetail(this.appId);

        if (result && result.code === 200 && result.data) {
          this.workflowDetail = result.data;
          console.log("工作流详情获取成功:", this.workflowDetail);

          // 初始化自定义变量
          this.initializeCustomVariables();
          // 更新表单验证规则
          this.updateFormRules();
        } else {
          throw new Error(result?.message || "获取工作流详情失败");
        }
      } catch (error) {
        console.error("获取工作流详情失败:", error);
        throw error;
      }
    },

    // 初始化自定义变量
    initializeCustomVariables() {
      if (this.customVariables && this.customVariables.length > 0) {
        this.customVariables.forEach(variable => {
          if (variable.name !== 'sys_current_time') {
            // 设置默认值
            this.$set(this.formData.customVariables, variable.name, variable.defaultValue || '');
          }
        });
      }
    },

    // 更新表单验证规则
    updateFormRules() {
      // 重置表单规则
      this.formRules = {
        question: [
          { required: false, message: '请输入问题描述', trigger: 'blur' }
        ],
        imageFileList: [
          { required: false, message: '请上传图片', trigger: 'change' }
        ],
        fileList: [
          { required: false, message: '请上传文件', trigger: 'change' }
        ]
      };

      // 根据工作流配置动态设置必填项规则
      if (this.startNodeConfig && this.startNodeConfig.inputSettings) {
        // 文本输入字段
        if (this.enableTextInput && this.startNodeConfig.inputSettings.textInputRequired) {
          this.formRules.question = [
            { required: true, message: `请输入${this.textInputName}`, trigger: 'blur' }
          ];
        }

        // 图片上传字段
        if (this.enableImageInput && this.startNodeConfig.inputSettings.imageInputRequired) {
          this.formRules.imageFileList = [
            { required: true, message: `请上传${this.imageInputName}`, trigger: 'change' }
          ];
        }

        // 文件上传字段
        if (this.enableFileInput && this.startNodeConfig.inputSettings.fileInputRequired) {
          this.formRules.fileList = [
            { required: true, message: `请上传${this.fileInputName}`, trigger: 'change' }
          ];
        }
      }

      // 设置自定义变量的验证规则
      if (this.customVariables && this.customVariables.length > 0) {
        this.formRules.customVariables = {};

        this.customVariables.forEach(variable => {
          if (variable.required) {
            const fieldName = variable.name;
            const displayName = variable.displayName;
            const type = variable.type;

            this.formRules.customVariables[fieldName] = [
              {
                required: true,
                message: `请${type === 1 ? '输入' : '选择'}${displayName}`,
                trigger: type === 1 ? 'blur' : 'change'
              }
            ];
          }
        });
      }
    },

    // 处理会话变化
    handleSessionChange(newSessionId) {
      console.log("SimpleWorkflowChatPanel: 处理会话变化:", newSessionId);

      // 设置新的会话ID
      this.currentSessionId = newSessionId;

      // 重置表单和结果
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.formData.question = '';
      this.hasResult = false;
      this.currentNodeIndex = -1;
      this.latestResult = {
        status: '',
        time: '',
        message: '',
        textInput: ''
      };
    },

        // 表单验证
    async validateForm() {
      if (!this.$refs.formRef) return false;

      try {
        await this.$refs.formRef.validate();
        return true;
      } catch (error) {
        console.warn("表单验证失败:", error);
        return false;
      }
    },

    // 执行工作流
    async executeWorkflow() {
      // 表单验证
      const isValid = await this.validateForm();
      if (!isValid || this.isExecuting) return;

      const sessionId = this.currentSessionId;

      if (!sessionId) {
        this.$showFriendlyError(null, "会话未初始化，无法执行工作流");
        return;
      }

      // 检查必填字段
      if (this.isSimpleMode && !this.formData.question.trim()) {
        this.$showFriendlyError(null, "请输入问题描述");
        return;
      }

      if (this.enableTextInput && this.startNodeConfig?.inputSettings?.textInputRequired && !this.formData.question.trim()) {
                  this.$showFriendlyError(null, `请输入${this.textInputName}`);
        return;
      }

      // 检查简化模式下的图片上传必填项
      if (this.isSimpleMode && this.startNodeConfig?.inputSettings?.imageInputRequired === true) {
        if (!this.formData.imageFileList || this.formData.imageFileList.length === 0) {
          this.$showFriendlyError(null, `请上传${this.imageInputName}`);
          return;
        }
      }

      // 检查非简化模式下的图片上传必填项
      if (!this.isSimpleMode && this.enableImageInput && this.startNodeConfig?.inputSettings?.imageInputRequired === true) {
        if (!this.formData.imageFileList || this.formData.imageFileList.length === 0) {
          this.$showFriendlyError(null, `请上传${this.imageInputName}`);
          return;
        }
      }

      this.isExecuting = true;
      this.currentNodeIndex = 0; // 开始执行

      try {
        // 准备执行参数
        const params = {
          sessionId: sessionId,
          flowDetailType: "WorkFlow",
          flowId: this.appId,
          textInput: this.formData.question || "",
          imageInput: "",
          fileInput: "",
          customVariables: {}
        };

        // 处理图片上传
        if (this.formData.imageFileList?.length > 0) {
          const imageFile = this.formData.imageFileList[0];
          console.log("图片文件对象:", imageFile);
          console.log("图片文件URL:", imageFile.url);
          console.log("图片文件响应:", imageFile.response);
          console.log("图片文件状态:", imageFile.status);

          // 检查图片是否正在上传
          if (imageFile.status === 'uploading') {
            this.$message.warning('图片正在上传中，请稍后再试');
            this.isExecuting = false;
            return;
          }

          // 检查图片上传是否失败
          if (imageFile.status === 'error') {
            this.$showFriendlyError(null, '图片上传失败，请重新上传');
            this.isExecuting = false;
            return;
          }

          // 获取图片URL
          if (imageFile.url) {
            params.imageInput = imageFile.url;
            console.log("包含图片URL:", imageFile.url);
          } else if (imageFile.response && imageFile.response.data && imageFile.response.data.fileUrl) {
            params.imageInput = imageFile.response.data.fileUrl;
            console.log("包含图片URL（从响应获取）:", imageFile.response.data.fileUrl);
          } else {
            // 如果图片需要但没有有效URL，提示用户
            if ((this.isSimpleMode && this.startNodeConfig?.inputSettings?.imageInputRequired === true) ||
                (!this.isSimpleMode && this.enableImageInput && this.startNodeConfig?.inputSettings?.imageInputRequired === true)) {
              this.$showFriendlyError(null, '图片上传未完成或失败，请重新上传');
              this.isExecuting = false;
              return;
            }
            console.warn("图片文件没有找到有效的URL");
          }
        }

        // 简化模式和非简化模式下的图片处理逻辑已经统一在上面处理了，这里不需要重复处理

        // 处理文件上传
        if (this.enableFileInput && this.formData.fileList?.length > 0) {
          const uploadFile = this.formData.fileList[0];
          if (uploadFile.raw) {
            // 这里需要实现文件上传的具体逻辑
            params.fileInput = uploadFile.raw;
            console.log("包含文件上传:", uploadFile.name);
          }
        }

        // 处理自定义变量
        if (this.customVariables && this.customVariables.length > 0) {
          params.customVariables = { ...this.formData.customVariables };

          // 添加系统时间变量
          const timeVariable = this.customVariables.find(v => v.name === 'sys_current_time');
          if (timeVariable) {
            params.customVariables['sys_current_time'] = new Date().toISOString();
          }
        }

        console.log("执行工作流API调用:", params);

        // 触发消息发送事件
        this.$emit('message-sent', {
          content: this.formData.question,
          sessionId: sessionId,
          time: this.formatTime(new Date())
        });

        // 使用流式执行
        await this.executeWorkflowStream(params, sessionId);

      } catch (error) {
        console.error("执行工作流失败:", error);

        // 显示错误结果
        this.latestResult = {
          status: 'error',
          time: this.formatTime(new Date()),
          message: `执行失败，请重试`,
          textInput: this.formData.question,
          customVariables: { ...this.formData.customVariables },
          hasImage: this.formData.imageFileList?.length > 0,
          hasFile: this.formData.fileList?.length > 0,
          imageUrl: this.formData.imageFileList?.length > 0 ? (this.formData.imageFileList[0].url || (this.formData.imageFileList[0].response && this.formData.imageFileList[0].response.data && this.formData.imageFileList[0].response.data.fileUrl)) : null,
          fileName: this.formData.fileList?.length > 0 ? this.formData.fileList[0].name : null
        };
        this.hasResult = true;
        this.currentNodeIndex = -1; // 重置节点状态
      }
    },

    // 流式执行工作流
    async executeWorkflowStream(params, sessionId) {
      try {
        // 创建取消控制器
        this.streamAbortController = new AbortController();

        // 初始化流式状态
        this.streamingMessage = '';
        this.isStreamCompleted = false;

        // 初始化结果数据
        this.latestResult = {
          status: 'streaming',
          time: this.formatTime(new Date()),
          message: '',
          textInput: this.formData.question,
          customVariables: { ...this.formData.customVariables },
          hasImage: this.formData.imageFileList?.length > 0,
          hasFile: this.formData.fileList?.length > 0,
          imageUrl: this.formData.imageFileList?.length > 0 ? (this.formData.imageFileList[0].url || (this.formData.imageFileList[0].response && this.formData.imageFileList[0].response.data && this.formData.imageFileList[0].response.data.fileUrl)) : null,
          fileName: this.formData.fileList?.length > 0 ? this.formData.fileList[0].name : null,
          executionSteps: []
        };
        this.hasResult = true;

        await api.workflow.executeStream(
          params,
          // onMessage 回调
          (messageData) => {
            console.log('流式消息:', messageData);

            // 处理执行步骤信息
            if (messageData.NodeType && messageData.NodeId) {
              // 更新工作流执行步骤
              this.updateWorkflowExecutionSteps(messageData);
              console.log("更新工作流执行步骤:", messageData);
            }

            // 处理新接口的响应格式
            if (messageData.Output) {
              // 检查是否是错误消息
              if (messageData.Type === 'error') {
                console.error('工作流执行错误:', messageData.Output);

                // 重置执行状态，让表单重新可操作
                this.isExecuting = false;

                this.latestResult.status = 'error';
                this.latestResult.message = formatOutputContent(messageData.Output);
                this.currentNodeIndex = -1;
              } else if (messageData.Type === 'end') {
                // 流式结束
                console.log('流式结束消息');
              } else {
                // 只有当 IsEnd 为 true 时才显示输出内容
                if (messageData.IsEnd === true) {
                  this.streamingMessage = messageData.Output; // 直接使用最终输出，不累积

                  console.log("最终响应内容:", { response: messageData.Output, fullResponse: this.streamingMessage });

                  // 更新显示的消息为最终结果
                  this.latestResult.message = formatOutputContent(this.streamingMessage);

                  // 滚动到底部
                  this.$nextTick(() => {
                    this.scrollToBottom();
                  });
                } else {
                  // 对于中间步骤的输出，不更新消息内容，保持原有状态
                  console.log("中间步骤输出（不显示）:", messageData.Output);
                }
              }
            }
          },
          // onError 回调
          (error) => {
            console.error('流式执行出错:', error);

            // 重置执行状态，让表单重新可操作
            this.isExecuting = false;

            this.latestResult.status = 'error';
            this.latestResult.message = `执行失败，请重试`;
            this.currentNodeIndex = -1;
          },
          // onComplete 回调
          () => {
            console.log('流式执行完成');
            this.isStreamCompleted = true;

            // 重置执行状态，让表单重新可操作
            this.isExecuting = false;

            // 只有在不是错误状态时才设置为成功
            if (this.latestResult.status !== 'error') {
              this.currentNodeIndex = this.workflowNodes.length - 1;
              this.latestResult.status = 'success';

              // 标记所有执行步骤为已完成
              if (this.latestResult.executionSteps && this.latestResult.executionSteps.length > 0) {
                this.latestResult.executionSteps.forEach(step => {
                  step.isCurrent = false;
                  step.isCompleted = true;
                });
              }

              // 触发消息接收事件
              this.$emit('message-received', {
                content: this.streamingMessage,
                sessionId: sessionId,
                time: this.formatTime(new Date())
              });
            }

            // 滚动到底部
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          },
          // signal (取消控制)
          this.streamAbortController.signal
        );

      } catch (error) {
        console.error('流式执行失败:', error);

        // 重置执行状态，让表单重新可操作
        this.isExecuting = false;

        this.latestResult = {
          status: 'error',
          time: this.formatTime(new Date()),
          message: `执行失败，请重试`,
          textInput: this.formData.question,
          customVariables: { ...this.formData.customVariables },
          hasImage: this.formData.imageFileList?.length > 0,
          hasFile: this.formData.fileList?.length > 0,
          imageUrl: this.formData.imageFileList?.length > 0 ? (this.formData.imageFileList[0].url || (this.formData.imageFileList[0].response && this.formData.imageFileList[0].response.data && this.formData.imageFileList[0].response.data.fileUrl)) : null,
          fileName: this.formData.fileList?.length > 0 ? this.formData.fileList[0].name : null
        };
        this.hasResult = true;
        this.currentNodeIndex = -1;
        throw error;
      }
    },

    // 刷新结果
    refreshResult() {
      if (!this.hasResult) {
        this.$message.info('请先执行工作流获取结果');
        return;
      }

      this.$message.success('结果已刷新');
    },

    // 刷新聊天
    refreshChat() {
      if (this.hasResult && confirm("确定要清除当前执行记录吗？")) {
        this.resetForm();
      }
    },

    // 关闭聊天
    closeChat() {
      this.$emit('close-chat');
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const resultContainer = this.$refs.resultContainer;
        if (resultContainer) {
          resultContainer.scrollTop = resultContainer.scrollHeight;
        }
      });
    },

    // 根据节点类型获取图标
    getNodeIcon(nodeType) {
      switch(nodeType) {
        case '开始':
          return 'el-icon-s-promotion';
        case '固定节点':
          return 'el-icon-notebook-1';
        case '插件':
          return 'el-icon-s-tools';
        case '结束':
          return 'el-icon-success';
        default:
          return 'el-icon-more';
      }
    },

    // 处理用户头像加载错误
    handleUserAvatarError(e) {
      e.target.src = 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png';
    },

    // 处理系统头像加载错误
    handleSystemAvatarError(e) {
      e.target.src = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png';
    },

    // 处理图片上传
    handleImageChange(file, fileList) {
      // 限制只能有一张图片，如果上传新图片则替换原有图片
      if (fileList.length > 1) {
        // 只保留最新上传的图片
        this.formData.imageFileList = [fileList[fileList.length - 1]];
      } else {
        this.formData.imageFileList = fileList;
      }

      // 如果是新上传的文件，自动上传到OSS
      if (file.status === 'ready') {
        this.uploadFileToOss(file.raw, 'image');
      }
    },

    // 处理图片上传超出限制
    handleImageExceed(files) {
      this.$message.warning('只能上传一张图片，新图片将替换原有图片');

      // 构造符合 el-upload 格式的文件对象
      const newFile = {
        name: files[0].name,
        size: files[0].size,
        type: files[0].type,
        raw: files[0],
        status: 'ready',
        uid: Date.now() + Math.random() // 生成唯一ID
      };

      // 使用新上传的图片替换现有图片
      this.formData.imageFileList = [newFile];

      // 自动上传新图片
      this.uploadFileToOss(files[0], 'image');
    },

    // 处理文件上传
    handleFileChange(file, fileList) {
      this.formData.fileList = fileList;
      // 如果是新上传的文件，自动上传到OSS
      if (file.status === 'ready') {
        this.uploadFileToOss(file.raw, 'file');
      }
    },

    // 上传文件到OSS
    async uploadFileToOss(file, type) {
      if (!file) return;

      // 找到对应的文件列表项
      let fileListItem = null;
      if (type === 'image') {
        fileListItem = this.formData.imageFileList.find(item => item.raw === file);
      } else {
        fileListItem = this.formData.fileList.find(item => item.raw === file);
      }

      try {
        const uploadParams = {
          file: file,
          fileType: type === 'image' ? 'image' : 'file',
          folder: 'workflow_inputs'
        };

        if (fileListItem) {
          this.$set(fileListItem, 'status', 'uploading');
        }

        const uploadResult = await api.oss.upload(uploadParams);

        if (uploadResult && uploadResult.data && uploadResult.data.fileUrl) {
          if (fileListItem) {
            this.$set(fileListItem, 'status', 'success');
            this.$set(fileListItem, 'url', uploadResult.data.fileUrl);
            this.$set(fileListItem, 'response', uploadResult);
            console.log(`${type === 'image' ? '图片' : '文件'}上传成功，URL:`, uploadResult.data.fileUrl);
          }
          this.$message.success(`${type === 'image' ? '图片' : '文件'}上传成功!`);
          return uploadResult.data.fileUrl;
        } else {
          if (fileListItem) {
            this.$set(fileListItem, 'status', 'error');
          }
          throw new Error('上传成功但未返回有效URL');
        }
      } catch (error) {
        if (fileListItem) {
          this.$set(fileListItem, 'status', 'error');
        }
        console.error(`${type === 'image' ? '图片' : '文件'}上传失败:`, error);
        this.$showFriendlyError(error, `${type === 'image' ? '图片' : '文件'}上传失败，请重试`);
        return null;
      }
    },

    // 根据变量名获取显示名称
    getVariableDisplayName(variableName) {
      const variable = this.customVariables.find(v => v.name === variableName);
      return variable ? variable.displayName : variableName;
    },

    // 格式化时间
    formatTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
    },

    // ===== 语音功能相关方法 =====

    // 初始化语音合成功能
    initSpeechSynthesis() {
      try {
        if ('speechSynthesis' in window) {
          this.speechSynthesis = window.speechSynthesis;
          console.log('语音合成功能已初始化');
        } else {
          console.warn('浏览器不支持语音合成功能');
        }
      } catch (error) {
        console.error('初始化语音合成失败:', error);
      }
    },

    // 清理语音功能资源
    cleanupVoiceFeatures() {
      try {
        // 停止语音朗读
        this.stopReading();
        this.isReading = false;
        this.currentReadingMessageIndex = -1;
        this.isLoadingAudio = false;
      } catch (error) {
        console.error('清理语音功能资源失败:', error);
      }
    },

    // 清理流式请求
    cleanupStreamRequest() {
      try {
        if (this.streamAbortController) {
          this.streamAbortController.abort();
          this.streamAbortController = null;
        }
        this.streamingMessage = '';
        this.isStreamCompleted = false;
      } catch (error) {
        console.error('清理流式请求失败:', error);
      }
    },

    // 语音朗读消息
    async readMessage(content, messageIndex) {
      try {
        // 如果正在朗读同一条消息，则停止朗读
        if (this.isReading && this.currentReadingMessageIndex === messageIndex) {
          this.stopReading();
          return;
        }

        // 停止当前朗读（如果有）
        this.stopReading();

        // 提取纯文本内容（去除HTML标签）
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';

        if (!textContent.trim()) {
          this.$message.warning('该消息没有可朗读的文本内容');
          return;
        }

        // 设置朗读状态
        this.isReading = true;
        this.currentReadingMessageIndex = messageIndex;
        this.isLoadingAudio = true;

        try {
          // 优先使用服务器文字转语音接口
          const ttsResult = await this.callTextToSpeech(textContent);

          if (ttsResult && ttsResult.data && ttsResult.data.obsUrl) {
            // 播放服务器返回的音频
            await this.playAudioFromUrl(ttsResult.data.obsUrl);
          } else {
            // 服务器接口失败，降级到本地语音合成
            console.warn('服务器文字转语音失败，降级到本地语音合成');
            this.fallbackToLocalTTS(textContent);
          }
        } catch (error) {
          console.error('服务器文字转语音失败:', error);
          // 降级到本地语音合成
          this.fallbackToLocalTTS(textContent);
        } finally {
          this.isLoadingAudio = false;
        }

      } catch (error) {
        console.error('语音朗读失败:', error);
        this.$showFriendlyError(error, '语音朗读功能出现错误');
        this.stopReading();
      }
    },

    // 停止语音朗读
    stopReading() {
      try {
        // 停止音频播放
        if (this.currentAudio) {
          this.currentAudio.pause();
          this.currentAudio.currentTime = 0;
          this.currentAudio = null;
        }

        // 停止本地语音合成
        if (this.speechSynthesis && this.speechSynthesis.speaking) {
          this.speechSynthesis.cancel();
        }

        this.isReading = false;
        this.currentReadingMessageIndex = -1;
        this.isLoadingAudio = false;
      } catch (error) {
        console.error('停止朗读失败:', error);
      }
    },

    // 调用服务器文字转语音接口
    async callTextToSpeech(text) {
      try {
        const params = {
          text: text,
          sessionID: this.currentSessionId,
          flowType: 'WorkFlow',
          appId: this.appId
        };

        console.log('调用服务器文字转语音接口:', params);
        const result = await api.voice.textToSpeech(params);
        console.log('服务器文字转语音结果:', result);

        if (result && result.isSuccess && result.data) {
          return result;
        } else {
          throw new Error(result?.message || '服务器文字转语音失败');
        }
      } catch (error) {
        console.error('调用服务器文字转语音接口失败:', error);
        throw error;
      }
    },

    // 播放服务器返回的音频URL
    async playAudioFromUrl(audioUrl) {
      return new Promise((resolve, reject) => {
        try {
          // 创建音频对象
          const audio = new Audio(audioUrl);
          this.currentAudio = audio;

          // 设置音频事件监听
          audio.oncanplaythrough = () => {
            console.log('音频可以播放');
          };

          audio.onplay = () => {
            console.log('音频开始播放');
          };

          audio.onended = () => {
            console.log('音频播放结束');
            this.isReading = false;
            this.currentReadingMessageIndex = -1;
            this.currentAudio = null;
            resolve();
          };

          audio.onerror = (error) => {
            console.error('音频播放错误:', error);
            this.isReading = false;
            this.currentReadingMessageIndex = -1;
            this.currentAudio = null;
            reject(new Error('音频播放失败'));
          };

          // 开始播放
          audio.play().catch(error => {
            console.error('音频播放启动失败:', error);
            this.isReading = false;
            this.currentReadingMessageIndex = -1;
            this.currentAudio = null;
            reject(error);
          });

        } catch (error) {
          console.error('创建音频播放器失败:', error);
          this.isReading = false;
          this.currentReadingMessageIndex = -1;
          this.currentAudio = null;
          reject(error);
        }
      });
    },

    // 降级到本地语音合成
    fallbackToLocalTTS(textContent) {
      try {
        if (!this.speechSynthesis) {
          this.$message.warning('当前浏览器不支持语音朗读功能');
          this.stopReading();
          return;
        }

        console.log('使用本地语音合成');

        // 创建语音合成实例
        const utterance = new SpeechSynthesisUtterance(textContent);

        // 设置语音参数
        utterance.lang = 'zh-CN'; // 中文
        utterance.rate = 1; // 语速
        utterance.pitch = 1; // 音调
        utterance.volume = 1; // 音量

        // 朗读开始事件
        utterance.onstart = () => {
          console.log('本地语音合成开始朗读');
        };

        // 朗读结束事件
        utterance.onend = () => {
          this.isReading = false;
          this.currentReadingMessageIndex = -1;
          console.log('本地语音合成朗读结束');
        };

        // 朗读错误事件
        utterance.onerror = (event) => {
          console.error('本地语音合成朗读出错:', event);
          this.isReading = false;
          this.currentReadingMessageIndex = -1;
          this.$showFriendlyError(null, '语音朗读失败');
        };

        // 开始朗读
        this.speechSynthesis.speak(utterance);

      } catch (error) {
        console.error('本地语音合成失败:', error);
        this.stopReading();
        this.$showFriendlyError(error, '语音朗读失败');
      }
    },

    // 复制消息内容
    copyMessage(content) {
      try {
        // 提取纯文本内容（去除HTML标签）
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';

        if (!textContent.trim()) {
          this.$message.warning('该消息没有可复制的文本内容');
          return;
        }

        // 尝试使用现代剪贴板API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(textContent)
            .then(() => {
              this.$message.success('内容已复制到剪贴板');
            })
            .catch(() => {
              this.fallbackCopyTextToClipboard(textContent);
            });
        } else {
          // 使用备用复制方法
          this.fallbackCopyTextToClipboard(textContent);
        }

      } catch (error) {
        console.error('复制消息失败:', error);
        this.$showFriendlyError(error, '复制失败，请手动复制');
      }
    },

    // 备用的复制方法
    fallbackCopyTextToClipboard(text) {
      try {
        // 创建临时DOM元素
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 确保元素不可见
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '-9999px';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // 尝试执行复制命令
        const successful = document.execCommand('copy');
        if (successful) {
          this.$message.success("复制成功");
        } else {
          this.$message.warning("复制失败，请手动复制");
        }

        // 清理
        document.body.removeChild(textArea);
      } catch (err) {
        this.$showFriendlyError(err, "复制失败，请手动复制");
        console.error('复制失败:', err);
      }
    },

    // 清除聊天记忆
    async clearChatMemory() {
      try {
        await this.$confirm('确定要清除当前工作流的执行结果和记忆吗？此操作不可恢复。', '确认清除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        // 如果有会话ID，调用清除记忆接口
        if (this.currentSessionId) {
          try {
            const result = await api.llmService.clearMemory({
              sessionId: this.currentSessionId
            });

            if (result && result.isSuccess) {
              if (result.data && result.data.success) {
                console.log('清除会话记忆成功');
              } else {
                console.warn('清除会话记忆失败:', result.data?.message || '未知错误');
                // 即使服务端清除失败，也继续清理本地数据
              }
            } else {
              console.warn('清除记忆接口调用失败:', result?.message || '未知错误');
              // 即使接口调用失败，也继续清理本地数据
            }
          } catch (error) {
            console.error('调用清除记忆接口失败:', error);
            // 即使接口调用失败，也继续清理本地数据
            this.$message.warning('清除服务端记忆失败，但将继续清理本地数据');
          }
        }

        // 清空结果和表单
        this.hasResult = false;
        this.latestResult = {
          status: '',
          time: '',
          message: '',
          textInput: '',
          customVariables: {},
          hasImage: false,
          hasFile: false,
          executionSteps: []
        };
        this.currentNodeIndex = -1;

        // 停止当前的语音朗读
        this.stopReading();

        // 重置表单数据
        this.formData.question = '';
        this.formData.fileList = [];
        this.formData.imageFileList = [];
        this.formData.customVariables = {};

        // 重新初始化自定义变量
        this.initializeCustomVariables();

        this.$message.success('工作流执行结果和记忆已清除');

      } catch (error) {
        if (error !== 'cancel') {
          console.error('清除工作流执行结果失败:', error);
          this.$showFriendlyError(error, '清除执行结果失败');
        }
      }
    },

    // 获取语音朗读按钮的图标
    getVoiceReadButtonIcon(messageIndex) {
      if (this.isLoadingAudio && this.currentReadingMessageIndex === messageIndex) {
        return 'el-icon-loading';
      } else if (this.isReading && this.currentReadingMessageIndex === messageIndex) {
        return 'el-icon-video-pause';
      } else {
        return 'el-icon-video-play';
      }
    },

    // 获取语音朗读按钮的提示文字
    getVoiceReadButtonTitle(messageIndex) {
      if (this.isLoadingAudio && this.currentReadingMessageIndex === messageIndex) {
        return '正在加载音频...';
      } else if (this.isReading && this.currentReadingMessageIndex === messageIndex) {
        return '停止朗读';
      } else {
        return '语音朗读';
      }
    },

    // 获取步骤显示名称
    getStepDisplayName(nodeType) {
      const stepNames = {
        'StartNode': '开始',
        'start': '开始',
        'EndNode': '结束',
        'end': '结束',
        'LargeModeNode': '模型',
        'largeMode': '模型',
        'KnowledgeNode': '知识',
        'knowledge': '知识',
        'IntentBranchNode': '意图',
        'intentBranch': '意图',
        'LogicBranchNode': '逻辑',
        'logicBranch': '逻辑',
        'ApplicationNode': '应用',
        'application': '应用',
        'PluginNode': '插件',
        'plugin': '插件',
        'NewPluginNode': '插件',
        'newPlugin': '插件',
        'HumanTransferNode': '人工',
        'humanTransfer': '人工',
        'FixedContentNode': '固定',
        'fixedContent': '固定',
        'CodeNode': '代码',
        'code': '代码'
      };
      const displayName = stepNames[nodeType] || nodeType;
      // 确保显示为两个字，如果超过两个字则截取前两个字
      return displayName.length > 2 ? displayName.substring(0, 2) : displayName;
    },

    // 更新工作流执行步骤
    updateWorkflowExecutionSteps(stepData) {
      if (!this.latestResult) {
        return;
      }

      // 初始化执行步骤数组
      if (!this.latestResult.executionSteps) {
        this.$set(this.latestResult, 'executionSteps', []);
      }

      // 查找是否已存在相同的步骤
      const existingStepIndex = this.latestResult.executionSteps.findIndex(step => step.nodeId === stepData.NodeId);

      if (existingStepIndex >= 0) {
        // 更新现有步骤
        const existingStep = this.latestResult.executionSteps[existingStepIndex];
        existingStep.isCurrent = stepData.CurrentType === 1;
        existingStep.isCompleted = stepData.IsEnd || stepData.CurrentType === 2;
      } else {
        // 添加新步骤
        const newStep = {
          nodeId: stepData.NodeId,
          nodeType: stepData.NodeType,
          isCurrent: stepData.CurrentType === 1,
          isCompleted: stepData.IsEnd || stepData.CurrentType === 2
        };
        this.latestResult.executionSteps.push(newStep);
      }

      // 更新所有之前的步骤为已完成状态
      if (stepData.CurrentType === 1 || stepData.IsEnd) {
        const currentStepIndex = this.latestResult.executionSteps.findIndex(step => step.nodeId === stepData.NodeId);
        for (let i = 0; i < currentStepIndex; i++) {
          this.latestResult.executionSteps[i].isCurrent = false;
          this.latestResult.executionSteps[i].isCompleted = true;
        }
      }

      // 强制更新Vue组件
      this.$forceUpdate();
    },

    // ============ 图片预览相关方法 ============

    // 预览图片
    previewImage(imageUrl) {
      if (!imageUrl) return;

      // 创建图片预览对话框
      this.$alert('', '图片预览', {
        dangerouslyUseHTMLString: true,
        message: `<div style="text-align: center;">
          <img src="${imageUrl}" style="max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" alt="图片预览" />
        </div>`,
        confirmButtonText: '关闭',
        customClass: 'image-preview-dialog'
      });
    },

    // ============ 语音输入相关方法 ============

    // 初始化语音输入管理器

  },
};
</script>

<style lang="scss" scoped>
.simple-workflow-chat-panel {
  height: 100%;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #606266;

  i {
    font-size: 24px;
    margin-bottom: 10px;
    animation: rotate 2s linear infinite;
  }

  span {
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes streamingDot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.full-height {
  height: 100%;
}

.form-panel,
.result-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.form-panel {
  position: relative; /* 为绝对定位的按钮提供参考 */
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    .el-tag {
      margin-left: 8px;
    }
  }

  .header-actions {
    display: flex;
    gap: 5px;
  }
}

/* 表单区域样式 */
.form-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px 20px;
  padding-bottom: 70px; /* 为底部按钮腾出空间 */

  /* 语音输入相关样式 */
  .input-container {
    position: relative;
    display: flex;
    align-items: stretch;
    gap: 8px;

    .el-input {
      flex: 1;

      :deep(.el-input__inner),
      :deep(.el-textarea__inner) {
        min-height: 44px;
        box-sizing: border-box;
        border-radius: 12px;
        border: 1px solid #e1e4e8;
        transition: all 0.2s ease;

        &:focus {
          border-color: #0969da;
          box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
        }
      }
    }

  }
}

.form-bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .left-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .right-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .el-button {
    height: 44px;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }

    &.el-button--primary {
      background-color: #0969da;
      border-color: #0969da;
      min-width: 120px;
      padding: 0 20px;
      font-size: 14px;

      &:hover {
        background-color: #0860ca;
        border-color: #0860ca;
        box-shadow: 0 3px 12px rgba(9, 105, 218, 0.25);
      }

      &:disabled {
        opacity: 0.6;
        background-color: #94a3b8;
        border-color: #94a3b8;

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }

    &.el-button--warning {
      background-color: #fd7e14;
      border-color: #fd7e14;

      &:hover {
        background-color: #e8590c;
        border-color: #e8590c;
        box-shadow: 0 3px 12px rgba(253, 126, 20, 0.25);
      }

      &:disabled {
        opacity: 0.6;
        background-color: #94a3b8;
        border-color: #94a3b8;

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }

    &.el-button--danger {
      background-color: #d1242f;
      border-color: #d1242f;

      &:hover {
        background-color: #a21e2a;
        border-color: #a21e2a;
        box-shadow: 0 3px 12px rgba(209, 36, 47, 0.25);
      }
    }
  }
}

.execute-btn {
  min-width: 120px;
  height: 44px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 12px;
}

/* 结果区域样式 */
.result-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  background-color: #fafbfc;
}

.result-time {
  text-align: right;
  padding: 12px 20px;
  color: #656d76;
  font-size: 11px;
  opacity: 0.8;
}

.chat-message {
  display: flex;
  margin-bottom: 24px;
  padding: 0 20px;
}

.user-message {
  flex-direction: row-reverse;
}

.system-message {
  flex-direction: row;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.message-content {
  max-width: calc(100% - 60px);
  margin: 0 10px;

  .message-wrapper {
    position: relative;
    padding: 16px 20px;
    border-radius: 12px;
    background-color: #ffffff;
    border: 1px solid #f0f0f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .message-actions-top {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      gap: 6px;
      z-index: 1;
      opacity: 1;

      .action-btn {
        background: #ffffff;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 6px 8px;
        font-size: 12px;
        color: #666666;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        &:hover:not(:disabled) {
          background-color: #f8f9fa;
          border-color: #d0d7de;
          color: #0969da;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.voice-read-btn {
          &:hover:not(:disabled) {
            color: #28a745;
            border-color: #28a745;
          }
        }

        &.copy-btn {
          &:hover:not(:disabled) {
            color: #fd7e14;
            border-color: #fd7e14;
          }
        }

        i {
          font-size: 13px;

          &.el-icon-loading {
            animation: rotate 2s linear infinite;
          }
        }
      }
    }



    .message-flow {
      padding-right: 90px; // 为右上角按钮留出空间
      margin-bottom: 12px;
    }

    .message-text {
      word-break: break-word;
      line-height: 1.6;
      color: #24292f;
      font-size: 14px;
      padding-right: 90px; // 为右上角按钮留出空间
    }

    // 流式传输指示器
    .streaming-indicator {
      display: inline-flex;
      align-items: center;
      margin-top: 8px;
      margin-left: 4px;

      span {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #0969da;
        margin-right: 3px;
        animation: streamingDot 1.5s infinite;

        &:nth-child(2) {
          animation-delay: 0.3s;
        }

        &:nth-child(3) {
          animation-delay: 0.6s;
        }
      }
    }
  }
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .message-text {
    padding: 16px 20px;
    border-radius: 12px;
    background: #f5f5f5;
    color: black;
    word-break: break-word;
    line-height: 1.6;
    font-size: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.message-flow {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.flow-node {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.node-icon {
  width: 28px;
  height: 28px;
  background-color: #f6f8fa;
  color: #656d76;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #e1e4e8;
  transition: all 0.2s ease;
  font-size: 12px;
}

.node-active {
  background-color: #0969da;
  color: white;
  border-color: #0969da;
  box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
}

.node-connector {
  width: 24px;
  height: 2px;
  background-color: #e1e4e8;
  margin: 0 4px;
  border-radius: 1px;
}

.empty-result,
.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;

  i {
    font-size: 32px;
    margin-bottom: 10px;
  }

  p {
    font-size: 14px;
  }
}

.empty-state {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 自定义变量显示样式 */
.custom-variables {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.variable-item {
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.variable-name {
  font-weight: 500;
  color: #303133;
  margin-right: 4px;
}

.variable-value {
  color: #606266;
}

/* 上传文件显示样式 */
.uploaded-files {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(103, 194, 58, 0.1);
  border-radius: 4px;
  border-left: 3px solid #67C23A;
  font-size: 13px;
  color: #606266;

  .file-info {
    margin-bottom: 8px;
    font-weight: 500;
  }

  .image-preview {
    margin-top: 8px;
    text-align: center;

    img {
      max-width: 200px;
      max-height: 150px;
      border-radius: 6px;
      border: 1px solid #e1e4e8;
      cursor: pointer;
      transition: all 0.2s ease;
      object-fit: cover;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: #67C23A;
      }
    }
  }

  .file-name {
    margin-top: 6px;
    padding: 4px 8px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;

    i {
      color: #67C23A;
    }
  }
}

/* 上传组件样式 */
.upload-demo {
  .el-upload__tip {
    margin-top: 5px;
    color: #909399;
    font-size: 12px;
  }
}

/* 图片预览对话框样式 */
:deep(.image-preview-dialog) {
  .el-message-box {
    max-width: 80vw;
    max-height: 80vh;
  }

  .el-message-box__content {
    padding: 20px;
  }

  .el-message-box__message {
    margin: 0;
  }
}

// 工作流执行步骤样式
.workflow-execution-steps {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  .execution-steps-list {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0;

    .execution-step {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      flex: 1;
      max-width: 80px;

      .step-indicator {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: 500;
        border: 2px solid #dee2e6;
        background-color: #ffffff;
        color: #6c757d;
        transition: all 0.3s ease;
        margin-bottom: 6px;
        z-index: 2;

        .step-number {
          font-size: 10px;
          line-height: 1;
        }

        i {
          font-size: 11px;

          &.el-icon-loading {
            animation: rotate 1s linear infinite;
          }
        }
      }

      .step-content {
        text-align: center;

        .step-label {
          font-size: 11px;
          color: #495057;
          line-height: 1.2;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 60px;
        }
      }

      // 连接线
      &::after {
        content: '';
        position: absolute;
        top: 12px;
        left: calc(50% + 12px);
        right: calc(-50% + 12px);
        height: 2px;
        background-color: #dee2e6;
        z-index: 1;
        transition: all 0.3s ease;
      }

      // 最后一个步骤不显示连接线
      &:last-child::after {
        display: none;
      }

      // 当前执行的步骤
      &.current {
        .step-indicator {
          border-color: #409eff;
          background-color: #409eff;
          color: #ffffff;
          animation: step-pulse 1.5s infinite ease-in-out;
        }

        .step-content .step-label {
          color: #409eff;
          font-weight: 500;
        }

        &::after {
          background-color: #409eff;
        }
      }

      // 已完成的步骤
      &.completed {
        .step-indicator {
          border-color: #67c23a;
          background-color: #67c23a;
          color: #ffffff;
        }

        .step-content .step-label {
          color: #67c23a;
          font-weight: 400;
        }

        &::after {
          background-color: #67c23a;
        }
      }

      // 未开始的步骤
      &:not(.current):not(.completed) {
        opacity: 0.7;
      }
    }
  }
}

// 步骤脉冲动画
@keyframes step-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}
</style>
