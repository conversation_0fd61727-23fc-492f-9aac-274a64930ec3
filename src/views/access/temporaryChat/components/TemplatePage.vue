<template>
  <div class="template-page">
    <div class="construction-container">
      <div class="construction-icon">
        <i class="el-icon-document"></i>
      </div>
      <h3>模板</h3>
      <p>正在努力建设中</p>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <div class="construction-text">
        我们正在为您准备丰富的应用模板，帮您快速构建智能应用！
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplatePage'
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.template-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  .construction-container {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 16px;
    box-shadow: $box-shadow-light;
    max-width: 400px;

    .construction-icon {
      margin-bottom: 24px;

      i {
        font-size: 80px;
        color: #a8edea;
        opacity: 0.8;
      }
    }

    h3 {
      font-size: 24px;
      color: $text-color-primary;
      margin-bottom: 16px;
      font-weight: 600;
    }

    p {
      font-size: 18px;
      color: #40a9ff;
      margin-bottom: 24px;
      font-weight: 500;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: $border-color-light;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 24px;

      .progress-fill {
        width: 35%;
        height: 100%;
        background: linear-gradient(90deg, #a8edea, #fed6e3);
        animation: progress-animation 2s ease-in-out infinite;
      }
    }

    .construction-text {
      font-size: 14px;
      color: $text-color-secondary;
      line-height: 1.6;
    }
  }
}

@keyframes progress-animation {
  0% {
    width: 15%;
  }
  50% {
    width: 55%;
  }
  100% {
    width: 35%;
  }
}
</style>
