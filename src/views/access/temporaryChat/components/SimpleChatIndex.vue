<template>
  <div class="simple-chat-container">
    <!-- 双栏布局：显示会话历史 + 聊天面板 -->
    <el-row :gutter="10" class="full-height">
      <!-- 左侧聊天历史列表 -->
      <el-col :span="4" class="full-height">
        <SimpleChatHistory
          :source-type="sourceType"
          :current-session-id="currentSessionId"
          :app-id="appId"
          :searchable="true"
          @session-selected="handleSessionSelected"
          @sessions-loaded="handleSessionsLoaded"
          @loading-changed="handleHistoryLoadingChanged"
        />
      </el-col>

      <!-- 右侧聊天面板 -->
      <el-col :span="20" class="full-height">
        <!-- 应用聊天面板 (sourceType = 1) -->
        <SimpleChatPanel
          v-if="activePanelType === 1 && currentSessionId"
          :app-info="appInfo"
          :session-id="currentSessionId"
          :app-id="appId"
          :source-type="sourceType"
          :show-controls="true"
          :show-close-button="true"
          :is-allow-image-upload="currentSessionConfig.isAllowImageUpload"
          @message-sent="handleMessageSent"
          @message-received="handleMessageReceived"
          @close-chat="handleCloseChat"
        />

        <!-- 工作流聊天面板 (sourceType = 2) -->
        <SimpleWorkflowChatPanel
          v-else-if="activePanelType === 2 && currentSessionId"
          :app-info="appInfo"
          :session-id="currentSessionId"
          :app-id="appId"
          :source-type="sourceType"
          :show-controls="true"
          :show-close-button="true"
          :is-allow-image-upload="currentSessionConfig.isAllowImageUpload"
          @message-sent="handleMessageSent"
          @message-received="handleMessageReceived"
          @close-chat="handleCloseChat"
        />

        <!-- 无会话时的占位内容 -->
        <div v-else class="no-session-placeholder">
          <div class="placeholder-content">
            <i class="el-icon-chat-dot-round"></i>
            <p>请从左侧选择一个会话开始聊天</p>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import SimpleChatHistory from './SimpleChatHistory.vue'
import SimpleChatPanel from './SimpleChatPanel.vue'
import SimpleWorkflowChatPanel from './SimpleWorkflowChatPanel.vue'

export default {
  name: "SimpleChatIndex",
  components: {
    SimpleChatHistory,
    SimpleChatPanel,
    SimpleWorkflowChatPanel
  },
  data() {
    return {
      // 路由参数
      appId: null,
      sourceType: null,
      routeSessionId: null,

      // 当前状态
      currentSessionId: null,
      appInfo: {},
      currentSessionType: null, // 新增：当前会话的实际类型

      // 会话列表
      chatSessions: [],

      // 状态标志
      isHistoryLoading: false,
      isInitializing: true,

      // 当前会话配置
      currentSessionConfig: {
        isAllowImageUpload: false, // 新增：当前会话是否允许图片上传
      },

      // sourceType配置映射
      sourceTypeConfig: {
        1: { name: '应用聊天' },
        2: { name: '工作流聊天' },
      }
    };
  },
  computed: {
    // 当前配置信息
    currentConfig() {
      return this.sourceTypeConfig[this.sourceType] || { name: '默认聊天' };
    },

    // 计算当前应该使用的面板类型：优先使用会话实际类型，其次使用URL的sourceType
    activePanelType() {
      return this.currentSessionType || this.sourceType;
    }
  },
  created() {
    // 从路由获取参数
    this.appId = this.$route.query.id;  // 修正：从query中获取id，不是params
    this.sourceType = parseInt(this.$route.query.sourceType) || 1;
    this.routeSessionId = this.$route.query.sessionId;

    console.log(`简化聊天组件初始化 - sourceType: ${this.sourceType} (${this.currentConfig.name})`);

    // 等待会话列表加载完成后在 handleSessionsLoaded 中处理
  },
  methods: {
    // 设置默认应用信息
    setDefaultAppInfo() {
      this.appInfo = {
        id: this.appId,
        name: this.currentConfig.name,
        description: "我是一个AI智能助手，可以帮您解答各种问题。",
        profilePhoto: "",
        introduce: "有什么可以帮助您的？",
      };
      // 重置会话配置为默认值
      this.resetSessionConfig();
    },

    // 初始化会话
    initializeSession() {
      // 尝试从本地存储恢复会话ID
      const routeKey = `${this.appId}_${this.sourceType}`;
      const savedSessionData = localStorage.getItem("simpleChatSessionData");

      if (savedSessionData) {
        try {
          const sessionData = JSON.parse(savedSessionData);
          if (sessionData.routeKey === routeKey) {
            this.currentSessionId = sessionData.sessionId;
            return;
          }
        } catch (error) {
          console.error("解析本地会话数据失败:", error);
        }
      }

      // 生成新的会话ID
      this.currentSessionId = this.generateSessionId();

      // 保存到本地存储
      this.saveSessionData();
    },

    // 生成会话ID
    generateSessionId() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    // 保存会话数据到本地存储
    saveSessionData() {
      if (!this.currentSessionId) return;

      try {
        const routeKey = `${this.appId}_${this.sourceType}`;
        const sessionData = {
          routeKey: routeKey,
          sessionId: this.currentSessionId,
          timestamp: new Date().getTime(),
        };
        localStorage.setItem("simpleChatSessionData", JSON.stringify(sessionData));
      } catch (error) {
        console.error("保存会话数据失败:", error);
      }
    },

    // 处理会话选择
    handleSessionSelected(sessionData) {
      // 如果传入null，清空当前会话状态
      if (!sessionData) {
        console.log("清空会话选择");
        this.currentSessionId = null;
        this.currentSessionType = null;
        // 重置会话配置 - 确保图片上传权限被正确重置
        this.resetSessionConfig();
        // 更新URL参数，移除sessionId
        this.updateRouteParams(this.appId, this.sourceType, null);
        // 设置默认应用信息
        this.setDefaultAppInfo();
        return;
      }

      console.log("会话切换:", sessionData.sessionId, "->", sessionData.appName);

      // 第一步：立即更新URL参数
      const newAppId = sessionData.clientId || sessionData.id;
      const newSourceType = sessionData.sourceType || this.detectSessionType(sessionData);
      const newSessionId = sessionData.sessionId;

      this.updateRouteParams(newAppId, newSourceType, newSessionId);

      // 第二步：更新组件内部状态
      this.currentSessionId = newSessionId;
      this.appId = newAppId;
      this.sourceType = newSourceType;
      this.currentSessionType = newSourceType;

      // 第三步：更新会话配置 - 根据会话数据动态设置，不依赖缓存
      this.updateSessionConfig(sessionData);

      // 第四步：更新应用信息
      this.appInfo = {
        id: sessionData.id || sessionData.clientId,
        name: `${sessionData.appName || "应用"} - ${sessionData.channelName || "渠道"}`,
        profilePhoto: sessionData.profilePhoto,
        introduce: sessionData.introduce,
        description: sessionData.lastMessage || sessionData.description,
      };

      // 第五步：保存会话数据
      this.saveSessionData();
    },

    // 新增：重置会话配置
    resetSessionConfig() {
      // 总是重置为默认状态，不依赖任何缓存
      this.currentSessionConfig = {
        isAllowImageUpload: false
      };
      console.log("重置会话配置 - 图片上传权限:", this.currentSessionConfig.isAllowImageUpload);
    },

    // 新增：更新会话配置
    updateSessionConfig(sessionData) {
      // 每次都重新计算配置，不依赖之前的状态
      const newConfig = {
        isAllowImageUpload: false // 默认为false
      };

      // 根据会话数据动态判断是否允许图片上传
      if (sessionData && sessionData.isAllowImageUpload === true) {
        newConfig.isAllowImageUpload = true;
      }
      // 可以在这里添加其他判断逻辑，比如根据sourceType、channelType等

      // 更新配置
      this.currentSessionConfig = newConfig;
      console.log("更新会话配置 - 图片上传权限:", this.currentSessionConfig.isAllowImageUpload);
    },

    // 新增：动态更新路由参数
    updateRouteParams(appId, sourceType, sessionId) {
      const newQuery = {
        ...this.$route.query,
        id: appId,  // 修正：id应该在query中，不是params中
        sourceType: sourceType
      };

      // 如果sessionId不为空，则添加到query中；否则从query中删除
      if (sessionId) {
        newQuery.sessionId = sessionId;
      } else {
        // 删除sessionId参数
        delete newQuery.sessionId;
      }

      // temporary-chat路由没有路径参数，所以params保持不变
      const newParams = {
        ...this.$route.params
      };

      // 确保类型一致的比较
      const currentRouteId = this.$route.query.id ? this.$route.query.id.toString() : '';  // 修正：从query中获取id
      const newAppIdStr = appId ? appId.toString() : '';
      const currentSourceType = parseInt(this.$route.query.sourceType) || 0;
      const currentSessionId = this.$route.query.sessionId || '';
      const newSessionIdStr = sessionId ? sessionId.toString() : '';

      // 只有当参数真正发生变化时才更新路由
      const shouldUpdateRoute =
        currentRouteId !== newAppIdStr ||
        currentSourceType !== sourceType ||
        currentSessionId !== newSessionIdStr;

      if (shouldUpdateRoute) {
        console.log("更新URL参数:", { id: newAppIdStr, sourceType: sourceType, sessionId: sessionId });

        this.$router.replace({
          name: this.$route.name,
          params: newParams,
          query: newQuery
        }).catch(err => {
          // 忽略导航重复错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('路由更新失败:', err);
          }
        });
      }
    },

    // 新增：检测会话的实际类型
    detectSessionType(sessionData) {
      // 方法1：通过flowDetailType字段判断
      if (sessionData.flowDetailType === 'WorkFlow') {
        return 2; // 工作流
      } else if (sessionData.flowDetailType && sessionData.flowDetailType !== 'WorkFlow') {
        return 1; // 应用
      }

      // 方法2：通过channelName判断（备用方案）
      if (sessionData.channelName) {
        const channelName = sessionData.channelName.toLowerCase();
        if (channelName.includes('workflow') || channelName.includes('工作流')) {
          return 2; // 工作流
        }
      }

      // 方法3：通过其他字段判断或默认值
      // 如果无法判断，返回当前URL的sourceType作为默认值
      return this.sourceType;
    },

    // 处理会话列表加载完成
    handleSessionsLoaded(sessions) {
      this.chatSessions = sessions;

      // 正在初始化时才处理会话选择
      if (this.isInitializing) {
        this.isInitializing = false;

        if (this.routeSessionId) {
          // 根据路由sessionId查找对应会话
          const foundSession = sessions.find(session => session.sessionId === this.routeSessionId);

          if (foundSession) {
            this.handleSessionSelected(foundSession);
          } else {
            // 如果没找到匹配的会话，尝试通过appId自动选择
            this.selectCurrentSessionInList();
          }
        } else {
          // 没有routeSessionId，尝试通过appId自动选择会话
          this.selectCurrentSessionInList();
        }
      }
    },

    // 在列表中选中当前会话
    selectCurrentSessionInList() {
      if (!this.appId || this.chatSessions.length === 0) {
        // 设置默认应用信息
        this.setDefaultAppInfo();
        // 如果没有routeSessionId，生成新的会话ID
        if (!this.currentSessionId) {
          this.initializeSession();
        }
        return;
      }

      // 根据路由参数ID查找匹配的列表项
      let foundSession = null;

      // 首先尝试通过路由ID匹配clientId或id字段
      foundSession = this.chatSessions.find(
        (chat) => {
          const matchClientId = chat.clientId && (chat.clientId.toString() === this.appId.toString());
          const matchId = chat.id && (chat.id.toString() === this.appId.toString());
          return matchClientId || matchId;
        }
      );

      // 如果找到了对应的会话，则选中它
      if (foundSession) {
        this.handleSessionSelected(foundSession);
      } else {
        // 设置默认应用信息
        this.setDefaultAppInfo();
        // 如果没有currentSessionId，生成新的会话ID
        if (!this.currentSessionId) {
          this.initializeSession();
        }
      }
    },

    // 处理历史加载状态变化
    handleHistoryLoadingChanged(isLoading) {
      this.isHistoryLoading = isLoading;
    },

    // 处理消息发送
    handleMessageSent(messageData) {
      console.log("消息发送:", messageData);
      // 可以在这里添加发送统计或其他逻辑
    },

    // 处理消息接收
    handleMessageReceived(messageData) {
      console.log("消息接收:", messageData);
      // 可以在这里添加接收统计或其他逻辑
    },

        // 处理关闭聊天
    handleCloseChat() {
      // 可以添加关闭聊天的处理逻辑，比如返回上一页
      if (this.$router) {
        this.$router.go(-1);
      } else if (window) {
        window.close();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.simple-chat-container {
  height: 100%;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.full-height {
  height: 100%;
}

.el-row {
  height: 100%;
}

.el-col {
  height: 100%;
}

/* 无会话时的占位样式 */
.no-session-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.placeholder-content {
  text-align: center;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}
</style>
