<template>
  <div class="simple-chat-history">
    <!-- 创建按钮 -->
    <div class="create-button-container">
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="navigateToCreateApp"
      >
        创建
      </el-button>
    </div>

    <!-- 聊天列表容器 -->
    <div class="list-container" ref="listContainer" @scroll="handleScroll">
      <!-- 初次加载状态 -->
      <div v-if="isLoading && chatHistory.length === 0" class="loading-container">
        <div class="loading-spinner">
          <i class="el-icon-loading"></i>
          <span>正在加载聊天列表...</span>
        </div>
      </div>

      <!-- 聊天列表 -->
      <div v-else-if="chatHistory.length > 0" class="chat-list">
        <div
          v-for="(chat, index) in chatHistory"
          :key="chat.id || index"
          :class="['chat-list-item', { active: currentChatIndex === index }]"
          @click="selectChat(index)"
          @mouseenter="setHoveredIndex(index)"
          @mouseleave="setHoveredIndex(-1)"
        >
          <div class="chat-preview">
            <el-avatar
              class="chat-avatar"
              :size="40"
              :src="chat.profilePhoto"
              icon="el-icon-user-solid"
            ></el-avatar>

            <div class="chat-info">
              <div class="chat-name">
                {{ chat.name || `${chat.appName || "应用"} - ${chat.channelName || "渠道"}` }}
              </div>
              <div class="chat-last-message">
                {{ chat.lastMessage || "暂无消息" }}
              </div>
              <div class="chat-time-container">
                <span class="chat-time">{{ chat.time }}</span>
                <el-tag
                  :type="getChannelTagType(chat.channelName)"
                  size="mini"
                  class="channel-tag"
                >
                  {{ chat.channelName || "未知渠道" }}
                </el-tag>
              </div>
            </div>

            <!-- 删除按钮 -->
            <div
              v-if="hoveredIndex === index"
              class="delete-button"
              @click.stop="handleDeleteChat(chat, index)"
            >
              <i class="el-icon-delete"></i>
            </div>
          </div>
        </div>

        <!-- 加载更多按钮 -->
        <div v-if="hasMore && !isLoadingMore" class="load-more-container">
          <el-button
            type="text"
            @click="loadMore"
            class="load-more-btn"
          >
            加载更多
          </el-button>
        </div>

        <!-- 加载更多状态 -->
        <div v-if="isLoadingMore" class="loading-more-container">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>

        <!-- 没有更多数据提示 -->
        <div v-if="!hasMore && chatHistory.length > 0" class="no-more-container">
          <span>没有更多数据了</span>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="el-icon-chat-dot-round"></i>
        <p>暂无聊天记录</p>
      </div>
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request";

export default {
  name: "SimpleChatHistory",
  props: {
    // 应用来源类型
    sourceType: {
      type: Number,
      required: true
    },
    // 当前会话ID
    currentSessionId: {
      type: String,
      default: null
    },
    // 应用ID（用于自动选中）
    appId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      // 会话列表数据
      chatHistory: [],
      // 当前选中的会话索引
      currentChatIndex: -1,
      // 加载状态
      isLoading: false,
      // 防抖相关
      lastUpdateTime: 0,
      updateTimer: null,

      // 瀑布流加载相关
      skipCount: 0,           // 跳过的数量
      maxResultCount: 100,    // 每次加载的数量，修改为100
      totalCount: 0,          // 总数量
      hasMore: true,          // 是否还有更多数据
      isLoadingMore: false,   // 是否正在加载更多

      // 鼠标悬浮状态
      hoveredIndex: -1,       // 当前悬浮的会话索引
    };
  },
  watch: {
    // 监听currentSessionId变化，更新选中状态
    currentSessionId: {
      handler(newSessionId) {
        this.updateSelectedSession(newSessionId);
      },
      immediate: true
    },
    // 监听appId变化，更新选中状态
    appId: {
      handler() {
        this.updateSelectedSession(this.currentSessionId);
      },
      immediate: true
    }
  },
  mounted() {
    // 组件挂载时加载数据
    this.fetchChatSessions(true);
  },
  methods: {
    // 导航到创建页面（根据sourceType判断应用还是工作流）
    navigateToCreateApp() {
      if (this.sourceType === 1) {
        // 应用聊天，跳转到应用创建页面
        this.$router.push('/create/app');
      } else if (this.sourceType === 2) {
        // 工作流聊天，跳转到工作流创建页面
        this.$router.push('/create/workflow');
      } else {
        // 默认跳转到应用创建页面
        this.$router.push('/create/app');
      }
    },

    // 获取聊天会话列表
    fetchChatSessions(isRefresh = false) {
      // 如果是刷新，重置分页参数
      if (isRefresh) {
        this.skipCount = 0;
        this.hasMore = true;
        this.chatHistory = [];
      }

      // 防抖处理
      if (this.isLoading && !this.isLoadingMore) {
        return;
      }

      // 防抖逻辑只在首次加载时生效
      if (!this.isLoadingMore) {
        const currentTime = new Date().getTime();
        if (currentTime - this.lastUpdateTime < 2000) {
          if (this.updateTimer) {
            clearTimeout(this.updateTimer);
          }
          this.updateTimer = setTimeout(() => {
            this._fetchChatSessionsActual();
          }, 2000);
          return;
        }
      }

      this._fetchChatSessionsActual();
    },

    // 实际获取聊天列表的方法
    _fetchChatSessionsActual() {
      this.lastUpdateTime = new Date().getTime();

      // 设置加载状态
      if (!this.isLoadingMore) {
        this.isLoading = true;
        // 通知父组件开始加载
        this.$emit("loading-changed", true);
      }

      // 获取认证令牌
      const token = localStorage.getItem("token");
      if (!token) {
        this.$message.warning("用户未登录，无法获取聊天历史");
        this.isLoading = false;
        this.isLoadingMore = false;
        this.$emit("loading-changed", false);
        return;
      }

      // 发起API请求获取聊天会话列表，传入分页参数
      api.chat.getSessions({
            skipCount: this.skipCount,
            maxResultCount: this.maxResultCount,
            SourceTypes: [1, 2],
            IsTestSession: true
          })
        .then((result) => {
          if (
            result &&
            result.code === 200 &&
            result.data &&
            result.data.items
          ) {
            // 处理返回的聊天会话数据
            const newChatHistory = result.data.items.map((session) => {
              // 转换日期格式为显示时间
              const creationTime = new Date(session.creationTime);
              const now = new Date();
              let timeDisplay = "";

              // 格式化显示时间: 今天显示小时:分钟，其他日期显示月-日
              if (creationTime.toDateString() === now.toDateString()) {
                timeDisplay = `${creationTime
                  .getHours()
                  .toString()
                  .padStart(2, "0")}:${creationTime
                  .getMinutes()
                  .toString()
                  .padStart(2, "0")}`;
              } else {
                timeDisplay = `${(creationTime.getMonth() + 1)
                  .toString()
                  .padStart(2, "0")}-${creationTime
                  .getDate()
                  .toString()
                  .padStart(2, "0")}`;
              }

              return {
                id: session.id,
                clientId: session.clientId || session.id,
                sessionId: session.sessionID,
                name: session.appName,
                avatar: "",
                profilePhoto: session.profilePhoto,
                introduce: session.introduce,
                lastMessage: session.description || "无消息",
                time: timeDisplay,
                appName: session.appName,
                messages: [],
                channelName: session.channelName,
                flowDetailType: session.flowDetailType,
                sourceType: session.sourceType,
                // 添加图片上传权限字段
                isAllowImageUpload: session.isAllowImageUpload || false,
              };
            });

            // 更新分页信息
            this.totalCount = result.data.totalCount || 0;
            this.hasMore = (this.skipCount + this.maxResultCount) < this.totalCount;

            // 保存现有当前选中的会话ID
            const currentSelectedSessionId =
              this.currentChatIndex >= 0 &&
              this.chatHistory[this.currentChatIndex]
                ? this.chatHistory[this.currentChatIndex].sessionId
                : null;

            // 如果是加载更多，追加到现有列表；如果是刷新，替换列表
            if (this.isLoadingMore) {
              this.chatHistory = [...this.chatHistory, ...newChatHistory];
            } else {
              this.chatHistory = newChatHistory;
            }

            // 恢复之前选中的会话
            if (currentSelectedSessionId && !this.isLoadingMore) {
              const newIndex = this.chatHistory.findIndex(
                (chat) => chat.sessionId === currentSelectedSessionId
              );
              if (newIndex >= 0) {
                this.currentChatIndex = newIndex;
              }
            }

            // 根据传入的currentSessionId更新选中状态（只在首次加载时处理）
            if (!this.isLoadingMore) {
              this.updateSelectedSession(this.currentSessionId, true);
              // 通知父组件会话列表已加载
              this.$emit("sessions-loaded", this.chatHistory);
            }
          } else {
            this.$message.warning("获取聊天历史失败");
            console.error("获取聊天历史返回数据格式错误:", result);
          }
        })
        .catch((error) => {
          this.$showFriendlyError(error, "获取聊天历史失败");
          console.error("获取聊天历史失败:", error);
        })
        .finally(() => {
          this.isLoading = false;
          this.isLoadingMore = false;
          this.$emit("loading-changed", false);
        });
    },

    // 选择聊天会话
    selectChat(index) {
      if (this.currentChatIndex === index) {
        return;
      }

      this.currentChatIndex = index;
      const chat = this.chatHistory[index];

      if (chat) {
        // 通知父组件会话被选中
        this.$emit("session-selected", chat);
      }
    },

    // 更新选中的会话（根据sessionId）
    updateSelectedSession(sessionId, shouldEmitEvent = false) {
      if (!sessionId && !this.appId) {
        return;
      }

      if (this.chatHistory.length === 0) {
        return;
      }

      let foundIndex = -1;

      // 首先尝试通过sessionId匹配（优先级更高，因为sessionId更精确）
      if (sessionId) {
        foundIndex = this.chatHistory.findIndex(
          (chat) => chat.sessionId === sessionId
        );
      }

      // 如果通过sessionId没找到，再尝试通过appId匹配
      if (foundIndex < 0 && this.appId) {
        foundIndex = this.chatHistory.findIndex(
          (chat) => {
            const matchClientId = chat.clientId && (chat.clientId.toString() === this.appId.toString());
            const matchId = chat.id && (chat.id.toString() === this.appId.toString());
            return matchClientId || matchId;
          }
        );
      }

      // 总是更新选中状态（UI同步）
      if (foundIndex >= 0 && this.currentChatIndex !== foundIndex) {
        this.currentChatIndex = foundIndex;
      }

      // 只有在明确需要触发事件时才触发（避免重复事件）
      if (shouldEmitEvent && foundIndex >= 0) {
        const selectedChat = this.chatHistory[foundIndex];
        if (selectedChat) {
          this.$emit("session-selected", selectedChat);
        }
      }
    },

    // 刷新聊天列表
    refresh() {
      this.fetchChatSessions(true); // 传入true表示刷新，重置分页
    },

    // 获取选中的会话数据
    getSelectedSession() {
      if (this.currentChatIndex >= 0 && this.chatHistory[this.currentChatIndex]) {
        return this.chatHistory[this.currentChatIndex];
      }
      return null;
    },

    // 加载更多
    loadMore() {
      if (this.isLoadingMore || !this.hasMore) {
        return;
      }

      this.isLoadingMore = true;
      this.skipCount += this.maxResultCount;
      this._fetchChatSessionsActual();
    },

    // 处理滚动事件
    handleScroll() {
      const container = this.$refs.listContainer;
      if (container) {
        const scrollTop = container.scrollTop;
        const scrollHeight = container.scrollHeight;
        const clientHeight = container.clientHeight;

        // 距离底部100px时触发加载更多
        if (scrollTop + clientHeight >= scrollHeight - 100 && this.hasMore && !this.isLoadingMore) {
          this.loadMore();
        }
      }
    },

    // 根据channelName获取标签类型
    getChannelTagType(channelName) {
      if (!channelName) return '';

      const lowerChannelName = channelName.toLowerCase();

      // 根据渠道名称设置不同的标签颜色
      if (lowerChannelName.includes('app') || lowerChannelName.includes('应用')) {
        return 'primary'; // 蓝色
      } else if (lowerChannelName.includes('workflow') || lowerChannelName.includes('工作流')) {
        return 'success'; // 绿色
      } else if (lowerChannelName.includes('api')) {
        return 'warning'; // 橙色
      } else if (lowerChannelName.includes('web') || lowerChannelName.includes('网页')) {
        return 'info'; // 灰色
      } else {
        return ''; // 默认颜色
      }
    },

    // 设置悬浮状态
    setHoveredIndex(index) {
      this.hoveredIndex = index;
    },

    // 处理删除聊天会话
    async handleDeleteChat(chat, index) {
      try {
        // 显示确认对话框
        await this.$confirm(
          `确定要删除会话"${chat.name || chat.appName}"的历史记录吗？此操作不可撤销。`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        );

        // 记录删除前的状态，用于后续自动选中逻辑
        const isCurrentSelected = this.currentChatIndex === index;
        const totalCount = this.chatHistory.length;

        // 调用删除接口
        const result = await api.chat.deleteHistory(chat.sessionId);

        if (result && (result.code === 200 || result.isSuccess)) {
          // 清除localStorage中对应的会话数据
          this.clearLocalStorageForSession(chat.sessionId);

          // 从列表中移除该会话
          this.chatHistory.splice(index, 1);

          // 处理选中状态和自动选中逻辑
          if (isCurrentSelected) {
            // 如果删除的是当前选中的会话，根据规则自动选中下一个
            this.handleAutoSelectAfterDelete(index, totalCount);
          } else if (this.currentChatIndex > index) {
            // 如果删除的会话在当前选中会话之前，索引需要减1
            this.currentChatIndex--;
          }

          // 重置悬浮状态
          this.hoveredIndex = -1;

          this.$message.success('删除成功');
        } else {
          throw new Error(result?.message || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除会话失败:', error);
          this.$showFriendlyError(error, '删除失败');
        }
      }
    },

    // 删除会话后的自动选中逻辑
    handleAutoSelectAfterDelete(deletedIndex, originalTotalCount) {
      // 如果删除后列表为空，清空选中状态
      if (this.chatHistory.length === 0) {
        this.currentChatIndex = -1;
        this.$emit("session-selected", null);
        return;
      }

      // 如果删除的是最后一个会话（原来的列表长度为1），不自动选中
      if (originalTotalCount === 1) {
        this.currentChatIndex = -1;
        this.$emit("session-selected", null);
        return;
      }

      let newSelectedIndex = -1;

      // 先向下找：如果删除的不是最后一个，选中下一个（删除后的相同位置）
      if (deletedIndex < this.chatHistory.length) {
        newSelectedIndex = deletedIndex;
      }
      // 向上找：如果删除的是最后一个，选中上一个
      else if (deletedIndex > 0) {
        newSelectedIndex = deletedIndex - 1;
      }

      // 执行自动选中
      if (newSelectedIndex >= 0 && newSelectedIndex < this.chatHistory.length) {
        this.currentChatIndex = newSelectedIndex;
        const selectedChat = this.chatHistory[newSelectedIndex];
        this.$emit("session-selected", selectedChat);
        console.log(`自动选中下一个会话: ${selectedChat.name || selectedChat.appName}`);
      } else {
        // 如果无法找到合适的会话，清空选中状态
        this.currentChatIndex = -1;
        this.$emit("session-selected", null);
      }
    },

    // 清除localStorage中的会话相关数据
    clearLocalStorageForSession(sessionId) {
      if (!sessionId) return;

      // 清除会话消息历史记录
      const messageKeys = [
        `chatMessages_${sessionId}`,
        `simple_chat_messages_${sessionId}`,
        `workflow_messages_${sessionId}`
      ];

      messageKeys.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn(`清除localStorage键 ${key} 失败:`, error);
        }
      });

      // 清除会话数据
      const sessionKeys = [
        'chatSessionData',
        'simpleChatSessionData',
        'workflowSessionData'
      ];

      sessionKeys.forEach(key => {
        try {
          const savedData = localStorage.getItem(key);
          if (savedData) {
            const sessionData = JSON.parse(savedData);
            if (sessionData.sessionId === sessionId) {
              localStorage.removeItem(key);
            }
          }
        } catch (error) {
          console.warn(`处理localStorage键 ${key} 失败:`, error);
        }
      });

      console.log(`已清除会话 ${sessionId} 的本地数据`);
    }
  },
  beforeDestroy() {
    // 清理计时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }
  }
};
</script>

<style lang="scss" scoped>
/* 左侧聊天列表样式 */
.simple-chat-history {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.create-button-container {
  padding: 15px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: center;

  .el-button {
    width: 100%;
    font-weight: 500;
  }
}

.list-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #606266;

  i {
    font-size: 24px;
    margin-bottom: 10px;
    animation: rotate 2s linear infinite;
  }

  span {
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.chat-list-item {
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    background-color: #ecf5ff;
    border-color: #409eff;
  }

  .chat-preview {
    display: flex;
    align-items: center;
    position: relative;

    .chat-avatar {
      margin-right: 10px;
    }

    .chat-info {
      flex: 1;
      overflow: hidden;

      .chat-name {
        font-weight: 500;
        margin-bottom: 3px;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-last-message {
        color: #909399;
        font-size: 12px;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-time-container {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .chat-time {
          color: #c0c4cc;
          font-size: 11px;
        }

        .channel-tag {
          flex-shrink: 0;
          font-size: 10px;
          height: 16px;
          line-height: 16px;
          padding: 0 4px;
          margin-left: 8px;
        }
      }
    }

    .delete-button {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #f56c6c;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      z-index: 10;

      &:hover {
        background-color: #f78989;
        transform: translateY(-50%) scale(1.1);
      }

      i {
        font-size: 12px;
      }
    }
  }
}

.empty-state {
  text-align: center;
  color: #909399;
  margin-top: 50px;

  i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  margin-top: 10px;
}

.load-more-btn {
  padding: 8px 20px;
  font-size: 13px;
  color: #409eff;
  border: 1px solid #409eff;
  background-color: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #409eff;
    color: #fff;
  }
}

.loading-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  color: #606266;
  font-size: 13px;
  margin-top: 10px;

  i {
    margin-right: 6px;
    animation: rotate 1s linear infinite;
  }
}

.no-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  color: #c0c4cc;
  font-size: 12px;
  margin-top: 10px;
}
</style>
