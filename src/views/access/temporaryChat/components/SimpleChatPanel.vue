<template>
  <div class="simple-chat-panel">
    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>正在加载聊天信息...</span>
      </div>
    </div>

    <!-- 聊天面板内容 -->
    <template v-else-if="isPanelReady">
      <!-- 面板头部 -->
      <div class="panel-header">
        <h3>
          {{ (appInfo && appInfo.name) || "智能客服助手" }}
          <el-tag v-if="appInfo && currentSessionId" type="info" size="mini">
            ID: {{ currentSessionId }}
          </el-tag>
        </h3>
        <div v-if="showControls" class="header-actions">
          <el-button
            size="mini"
            icon="el-icon-refresh"
            circle
            @click="refreshChat"
          ></el-button>
          <el-button
            v-if="showCloseButton"
            size="mini"
            icon="el-icon-back"
            circle
            @click="closeChat"
          ></el-button>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-container">
        <div class="chat-messages" ref="messagesContainer">
          <!-- 消息列表 -->
          <div
            v-for="(message, index) in messages"
            :key="index"
            :class="[
              'chat-message',
              message.role === 'user' ? 'user-message' : 'system-message',
            ]"
          >
            <!-- 系统消息：头像在左侧 -->
            <div
              v-if="message.role !== 'user'"
              class="avatar system-avatar"
            >
              <img
                :src="(appInfo && appInfo.profilePhoto) || ''"
                onerror="this.src='https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'"
                alt="系统"
              />
            </div>
            <div v-if="message.role !== 'user'" class="message-content">
              <div class="message-wrapper">
                <!-- 消息操作按钮 - 右上角 -->
                <div v-if="!message.isStreaming && !message.hasError" class="message-actions-top">
                  <!-- 语音朗读按钮 -->
                  <button
                    class="action-btn voice-read-btn"
                    @click="readMessage(message.content, index)"
                    :disabled="isLoadingAudio && currentReadingMessageIndex !== index"
                    :title="getVoiceReadButtonTitle(index)"
                  >
                    <i :class="getVoiceReadButtonIcon(index)"></i>
                  </button>
                  <!-- 一键复制按钮 -->
                  <button
                    class="action-btn copy-btn"
                    @click="copyMessage(message.content)"
                    title="复制内容"
                  >
                    <i class="el-icon-document-copy"></i>
                  </button>
                </div>
                <div class="message-text" :class="{ 'streaming': message.isStreaming, 'error': message.hasError }">
                  <p v-html="message.content"></p>
                  <!-- 流式传输指示器 -->
                  <div v-if="message.isStreaming" class="streaming-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
                <!-- 知识库文件标签 -->
                <div v-if="message.knowledgeFiles && message.knowledgeFiles.length > 0" class="knowledge-files">
                  <div class="knowledge-files-header">
                    <i class="el-icon-folder-opened"></i>
                    <span>参考资料</span>
                  </div>
                  <div class="knowledge-files-list">
                    <el-tag
                      v-for="file in message.knowledgeFiles"
                      :key="file.fileId"
                      type="info"
                      size="small"
                      class="knowledge-file-tag"
                      :title="`文件ID: ${file.fileId}`"
                    >
                      <i class="el-icon-document"></i>
                      {{ file.fileName }}
                    </el-tag>
                  </div>
                </div>
              </div>
              <div class="message-info">
                <span>{{ message.time }}</span>
                <span v-if="message.isStreaming" class="streaming-text">正在输入...</span>
                <span v-if="message.hasError" class="error-text">发送失败</span>
              </div>
            </div>

            <!-- 用户消息：头像在右侧 -->
            <div v-if="message.role === 'user'" class="message-content user-message-content">
              <div class="user-message-wrapper">
                <!-- 如果有上传的图片，显示图片 -->
                <div v-if="message.imageUrl" class="message-image">
                  <img :src="message.imageUrl" alt="用户发送的图片" @click="previewImage(message.imageUrl)" />
                </div>
                <div class="message-text">
                  <p v-html="message.content"></p>
                </div>
              </div>
              <div class="message-info">
                <span>{{ message.time }}</span>
              </div>
            </div>
            <div v-if="message.role === 'user'" class="avatar user-avatar">
              <img
                src="@/assets/userAvatar.png"
                onerror="this.src='https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'"
                alt="用户"
              />
            </div>
          </div>

          <!-- 正在输入提示 -->
          <div class="typing-indicator" v-if="isTyping">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <!-- 输入框区域 -->
        <div class="chat-input">
          <!-- 语音输入状态提示 - 移到输入框上方 -->
          <div v-if="voiceStatus" class="voice-status" :class="voiceStatusType">
            <i :class="voiceStatusIcon"></i>
            <span>{{ voiceStatus }}</span>
          </div>
          <!-- 已上传图片预览 -->
          <div v-if="uploadedImageUrl && isAllowImageUpload" class="uploaded-image-preview">
            <div class="image-container">
              <img :src="uploadedImageUrl" alt="上传的图片" class="preview-image" />
              <button
                type="button"
                class="remove-image-btn"
                @click="removeUploadedImage"
                title="移除图片"
              >
                <i class="el-icon-close"></i>
              </button>
            </div>
          </div>
          <!-- 输入区域和按钮区域 -->
          <div class="input-and-actions">
            <div class="input-container">
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="2"
                placeholder="请输入消息..."
                @keyup.enter.native.exact="sendMessage"
                :disabled="isTyping || hasStreamingMessage"
              ></el-input>
              <!-- 语音输入按钮 - 仅在智能体类型(sourceType=1)时显示 -->
              <button
                v-if="sourceType === 1"
                type="button"
                class="voice-input-btn"
                :class="{ 'recording': isRecording }"
                @mousedown="handleVoiceMouseDown"
                @mouseup="handleVoiceMouseUp"
                @mouseleave="handleVoiceMouseUp"
                @touchstart="handleVoiceTouchStart"
                @touchend="handleVoiceTouchEnd"
                :disabled="isTyping || hasStreamingMessage || !canRecord"
                :title="getVoiceInputTitle()"
              >
                <i :class="getVoiceInputIcon()"></i>
              </button>
              <!-- 图片上传按钮 - 当启用图片上传时显示 -->
              <el-upload
                v-if="isAllowImageUpload"
                class="image-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
                :http-request="handleImageUpload"
                accept="image/*"
                :disabled="isTyping || hasStreamingMessage"
              >
                <button
                  type="button"
                  class="image-upload-btn"
                  :disabled="isTyping || hasStreamingMessage"
                  title="上传图片"
                >
                  <i class="el-icon-picture"></i>
                </button>
              </el-upload>
            </div>
            <div class="input-actions">

              <!-- 停止生成按钮 - 流式输出时显示 -->
              <el-button
                v-if="hasStreamingMessage"
                type="danger"
                @click="stopStreaming"
                title="停止生成"
              >
                <i class="el-icon-video-pause"></i>
                停止
              </el-button>
              <!-- 发送按钮 - 非流式输出时显示 -->
              <el-button
                v-else
                type="primary"
                :disabled="!inputMessage.trim() || isTyping"
                @click="sendMessage"
              >
                发送
              </el-button>
              <!-- 清除记忆按钮 -->
              <el-button
                v-if="!hasStreamingMessage"
                type="warning"
                size="small"
                @click="clearChatMemory"
                :disabled="isTyping || hasStreamingMessage || messages.length === 0"
                title="清除记忆"
              >
                <i class="el-icon-delete"></i>
                清除记忆
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="el-icon-chat-line-square"></i>
      <p>请选择一个会话开始聊天</p>
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request"
import { clearChatMemory, formatOutputContent } from '@/utils'
import { VoiceInputManager, VoiceUtils } from '@/utils/voice'

export default {
  name: "SimpleChatPanel",
  props: {
    // 应用信息
    appInfo: {
      type: Object,
      default: () => ({})
    },
    // 会话ID
    sessionId: {
      type: String,
      default: null
    },
    // 应用ID
    appId: {
      type: [String, Number],
      default: null
    },
    // 来源类型
    sourceType: {
      type: Number,
      default: 1
    },
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      default: true
    },
    // 是否允许图片上传
    isAllowImageUpload: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 消息列表
      messages: [],
      // 输入消息
      inputMessage: "",
      // 是否正在输入
      isTyping: false,
      // 是否正在流式输出
      isStreaming: false,
      // 流式输出控制器
      streamController: null,
      // 是否正在加载
      isLoading: false,
      // 面板是否准备好
      isPanelReady: false,
      // 当前会话ID
      currentSessionId: null,
      // 聊天消息缓存键名前缀
      chatMessagesStoragePrefix: "simple_chat_messages_",

      // 语音播报相关 - 使用公共语音工具
      voiceTTS: null, // 语音播报工具实例

      // 语音输入相关
      voiceInputManager: null, // 语音输入管理器
      voiceStatus: '', // 语音状态提示
      voiceStatusType: '', // 状态类型：success, error, loading
      voiceStatusIcon: '', // 状态图标
      recordingStartTime: null, // 录音开始时间

      // 图片上传相关
      uploadedImageUrl: null, // 上传的图片URL
    };
  },
  computed: {
    // 检查是否有流式消息
    hasStreamingMessage() {
      return this.messages.length > 0 && this.messages.some(message => message.isStreaming === true);
    },

    // 获取朗读状态 - 使用公共方法
    isReading() {
      return this.voiceTTS ? this.voiceTTS.getState().isReading : false;
    },

    currentReadingMessageIndex() {
      return this.voiceTTS ? this.voiceTTS.getState().currentReadingMessageIndex : -1;
    },

    isLoadingAudio() {
      return this.voiceTTS ? this.voiceTTS.getState().isLoadingAudio : false;
    },

    // 语音输入相关状态
    isRecording() {
      return this.voiceInputManager ? this.voiceInputManager.getState().isRecording : false;
    },

    canRecord() {
      return this.voiceInputManager ? this.voiceInputManager.getState().canRecord : false;
    }
  },
  watch: {
    // 监听会话ID变化
    sessionId: {
      handler(newSessionId, oldSessionId) {
        if (newSessionId && newSessionId !== oldSessionId) {
          console.log("SimpleChatPanel: 会话ID变化:", oldSessionId, "->", newSessionId);
          this.handleSessionChange(newSessionId);
        }
      },
      immediate: true
    },
    // 监听应用ID变化
    appId: {
      handler(newAppId) {
        if (newAppId) {
          this.initializePanel();
        }
      },
      immediate: true
    },
    // 监听图片上传权限变化
    isAllowImageUpload: {
      handler(newValue, oldValue) {
        // 当图片上传权限从允许变为不允许时，清理已上传的图片
        if (oldValue === true && newValue === false) {
          console.log("SimpleChatPanel: 图片上传权限被禁用，清理已上传的图片");
          this.uploadedImageUrl = null;
        }
      },
      immediate: false // 不需要立即执行，只监听变化
    }
  },
  mounted() {
    this.scrollToBottom();
  },
  updated() {
    this.scrollToBottom();
  },
  beforeDestroy() {
    // 清理流式控制器
    if (this.streamController) {
      this.streamController.abort();
      this.streamController = null;
    }
    // 清理语音相关资源
    this.cleanupVoiceFeatures();
    // 清理语音输入资源
    if (this.voiceInputManager) {
      this.voiceInputManager.cleanup();
    }
    // 重置录音状态
    this.recordingStartTime = null;
  },
  created() {
    // 清理本地存储中的重复消息
    this.cleanupDuplicateMessages();
    // 初始化语音功能
    this.initVoiceFeatures();
    // 初始化语音输入功能
    this.initVoiceInputManager();
  },
  methods: {
    // 初始化面板
    initializePanel() {
      if (!this.appId) {
        console.warn("SimpleChatPanel: appId 未提供");
        return;
      }


      this.isPanelReady = true;
    },

    // 处理会话变化
    handleSessionChange(newSessionId) {

      // 清空消息列表
      this.messages = [];

      // 清理图片上传状态 - 确保会话切换时不残留图片
      this.uploadedImageUrl = null;

      // 重置输入状态
      this.inputMessage = "";

      // 设置新的会话ID
      this.currentSessionId = newSessionId;

      // 加载历史消息
      const hasLoadedMessages = this.loadChatMessages(newSessionId);

      // 只有在没有加载到历史消息时才添加欢迎消息
      if (!hasLoadedMessages) {
        this.addWelcomeMessage();
      }
    },

    // 添加欢迎消息
    addWelcomeMessage() {
      if (this.appInfo && (this.appInfo.introduce || this.appInfo.description)) {
        this.addMessage({
          role: "assistant",
          content: formatOutputContent(this.appInfo.introduce || this.appInfo.description),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });
      } else {
        this.addMessage({
          role: "assistant",
          content: formatOutputContent("有什么可以帮助您的？"),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });
      }
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping || this.hasStreamingMessage) return;

      const message = this.inputMessage.trim();
      const sessionId = this.currentSessionId;
      const imageUrl = this.uploadedImageUrl; // 保存图片URL到临时变量

      // 添加用户消息（如果有图片，也添加到消息中用于显示）
      const userMessage = {
        role: "user",
        content: formatOutputContent(message),
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(),
      };

      // 如果有上传的图片，添加图片URL到用户消息中用于显示
      if (imageUrl) {
        userMessage.imageUrl = imageUrl;
      }

      this.addMessage(userMessage);

      // 清空输入框和图片
      this.inputMessage = "";
      this.uploadedImageUrl = null;

      // 根据智能体类型设置状态
      if (this.sourceType === 1) {
        // 智能体类型使用流式输出
        this.isStreaming = true;
        this.isTyping = false;
      } else {
        // 工作流类型使用传统的输入状态
        this.isTyping = true;
        this.isStreaming = false;
      }

      if (!sessionId) {
        this.$showFriendlyError(null, "会话未初始化，无法发送消息");
        this.isTyping = false;
        this.isStreaming = false;
        return;
      }

      try {
        // 触发消息发送事件
        this.$emit('message-sent', {
          content: message,
          sessionId: sessionId,
          time: this.formatTime(new Date())
        });

        // 执行工作流
        const workflowResult = await this.executeWorkflow(message, sessionId, imageUrl);
        console.log("工作流执行成功", workflowResult);

        // 对于流式响应，executeWorkflow 已经处理了消息的添加和更新
        // 对于非流式响应（sourceType === 2），才需要手动添加消息
        if (workflowResult && this.sourceType === 2) {
          this.addMessage(workflowResult);
        }

      } catch (error) {
        console.error("发送消息失败:", error);

        // 检查是否为用户主动中止的AbortError
        if (error.name === 'AbortError' || error.message.includes('aborted')) {
          console.log("消息发送被用户主动中止，不显示错误消息");
          return; // 直接返回，不添加错误消息
        }

        this.addMessage({
          role: "assistant",
          content: formatOutputContent(`发送失败，请重试`),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
          hasError: true
        });
      } finally {
        // 只清理 isTyping 状态，isStreaming 状态由流式回调处理
        this.isTyping = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 执行工作流
    async executeWorkflow(message, sessionId, imageUrl) {
      if (!this.appId) {
        throw new Error("应用ID不存在");
      }

      // 根据sourceType决定调用不同的API接口
      if (this.sourceType === 1) {
        // 应用调用 - 以流式方式执行工作流
        const params = {
          sessionId: sessionId,
          flowDetailType: "SessionFlow",
          flowId: this.appId,
          textInput: message,
          imageInput: imageUrl || "",
          fileInput: "",
          customVariables: {},
        };

        console.log("执行应用API调用（流式）:", params);

        // 创建一个助手消息对象用于实时更新
        const assistantMessage = {
          role: "assistant",
          content: '<div class="markdown-content">正在思考中...</div>',
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
          fromWorkflow: true,
          isStreaming: true,
          knowledgeFiles: [] // 添加知识库文件列表
        };

        // 先添加空的助手消息到界面
        this.addMessage(assistantMessage);

        return new Promise((resolve, reject) => {
          let fullResponse = "";
          let hasError = false;
          let isCompleted = false;

          console.log("开始调用 api.workflow.stream");

          // 创建流式控制器
          if (this.streamController) {
            this.streamController.abort(); // 中止之前的请求
          }
          this.streamController = new AbortController();

          // 设置超时保护
          const timeoutId = setTimeout(() => {
            if (!hasError && !isCompleted) {
              console.warn("流式响应超时，强制完成");
              isCompleted = true;
              assistantMessage.content = formatOutputContent(fullResponse || "响应超时，请重试。");
              assistantMessage.isStreaming = false;

              if (typeof this.updateMessage === 'function') {
                this.updateMessage(assistantMessage.messageId, assistantMessage);
              }

              this.streamController = null;
              resolve(assistantMessage);
            }
          }, 60000); // 60秒超时

          const streamPromise = api.workflow.stream(
            params,
            // onMessage 回调
            (messageData) => {
              try {
                console.log('SimpleChatPanel 收到流式消息:', messageData);

                // 处理知识库节点数据 - 检查NodeType是否包含'knowledge'
                if (messageData.NodeType && messageData.NodeType.includes('knowledge') && messageData.Output && Array.isArray(messageData.Output)) {
                  console.log('检测到知识库节点，处理文件信息:', messageData.Output);
                  console.log('当前已有文件数量:', assistantMessage.knowledgeFiles.length);

                  // 创建当前所有文件ID的Set用于去重
                  const existingFileIds = new Set(assistantMessage.knowledgeFiles.map(f => f.fileId));

                  messageData.Output.forEach((item, index) => {
                    console.log(`处理第${index + 1}个文件:`, {
                      FileId: item.FileId,
                      FileName: item.FileName,
                      exists: existingFileIds.has(item.FileId)
                    });

                    if (item.FileId && item.FileName && !existingFileIds.has(item.FileId)) {
                      const newFile = {
                        fileId: item.FileId,
                        fileName: item.FileName,
                        score: item.Score || 0,
                        ossUrl: item.OssUrl || ''
                      };
                      assistantMessage.knowledgeFiles.push(newFile);
                      existingFileIds.add(item.FileId);
                      console.log('添加新文件:', newFile);
                    } else {
                      console.log('跳过文件（已存在或无效）:', item.FileId, item.FileName);
                    }
                  });

                  console.log('更新后的知识库文件列表:', assistantMessage.knowledgeFiles);
                  console.log('文件数量:', assistantMessage.knowledgeFiles.length);

                  // 强制更新消息，确保界面显示
                  this.updateMessage(assistantMessage.messageId, {
                    knowledgeFiles: [...assistantMessage.knowledgeFiles],
                    isStreaming: true
                  });

                  // 知识库节点不输出文本内容，直接返回
                  return;
                }

                let responseContent = '';
                let isFinal = false;

                // 检查是否为流式结束信号
                if (messageData.Type === 'end') {
                  // 流式结束，标记完成
                  isFinal = true;
                } else if (messageData.Type === 'streaming') {
                  // 流式内容，检查 Output 字段
                  responseContent = messageData.Output || '';
                  isFinal = messageData.IsEnd === true;
                }
                // 兼容旧格式：检查 Data.Response 和 Data.Output 字段
                else if (messageData.IsSuccess && messageData.Data) {
                  isFinal = messageData.Data.IsFinal || false;

                  // 优先检查 Response 字段
                  if (messageData.Data.Response) {
                    responseContent = messageData.Data.Response;
                  }
                  // 当 CurrentType === 2 时，检查 Output 字段
                  else if (messageData.Data.CurrentType === 2 && messageData.Data.Output) {
                    responseContent = messageData.Data.Output;
                  }
                }
                // 兼容直接有 Output 字段但没有 Type 的情况
                else if (messageData.Output !== undefined) {
                  responseContent = messageData.Output;
                }

                // 如果有内容，累积并更新
                if (responseContent) {
                  // 累积响应内容
                  fullResponse += responseContent;

                  // 实时更新消息内容 - 流式过程中也应用格式化
                  assistantMessage.content = formatOutputContent(fullResponse);
                  assistantMessage.isStreaming = !isFinal;

                  // 更新界面显示
                  this.updateMessage(assistantMessage.messageId, assistantMessage);

                  // 发送消息更新事件
                  this.$emit('message-updated', assistantMessage);
                }

                // 如果是最后一条消息或收到结束信号，标记完成
                if (isFinal && !isCompleted) {
                  isCompleted = true;
                  clearTimeout(timeoutId);
                  console.log("流式响应完成，最终内容:", fullResponse);

                  // 确保最终状态正确设置
                  assistantMessage.isStreaming = false;
                  this.updateMessage(assistantMessage.messageId, assistantMessage);

                  this.streamController = null;
                  resolve(assistantMessage);
                }

                // 处理错误响应
                if (messageData.ErrorCode !== null && messageData.ErrorCode !== undefined) {
                  // 处理错误响应
                  if (!hasError && !isCompleted) {
                    hasError = true;
                    clearTimeout(timeoutId);
                    const errorMessage = messageData.Message || "流式响应出错";
                    console.error("流式响应错误:", messageData);

                    assistantMessage.content = `抱歉，处理您的请求时出现了错误，请重试`;
                    assistantMessage.isStreaming = false;
                    assistantMessage.hasError = true;

                    this.updateMessage(assistantMessage.messageId, assistantMessage);
                    this.streamController = null;
                    reject(new Error(errorMessage));
                  }
                }
              } catch (error) {
                if (!hasError && !isCompleted) {
                  console.error("处理流式消息时出错:", error);

                  if (error.name === 'AbortError' || error.message.includes('aborted')) {
                    console.log("流式请求被用户主动中止");
                    hasError = true;
                    isCompleted = true;
                    clearTimeout(timeoutId);
                    this.streamController = null;
                    resolve(assistantMessage);
                    return;
                  }

                  hasError = true;
                  clearTimeout(timeoutId);

                  assistantMessage.content = "抱歉，处理消息时出现了错误。";
                  assistantMessage.isStreaming = false;
                  assistantMessage.hasError = true;

                  this.updateMessage(assistantMessage.messageId, assistantMessage);
                  this.streamController = null;
                  reject(error);
                }
              }
            },
            // onError 回调
            (error) => {
              if (!hasError && !isCompleted) {
                console.error("流式请求出错:", error);

                if (error.name === 'AbortError' || error.message.includes('aborted')) {
                  console.log("流式请求被用户主动中止");
                  hasError = true;
                  isCompleted = true;
                  clearTimeout(timeoutId);
                  this.streamController = null;
                  resolve(assistantMessage);
                  return;
                }

                hasError = true;
                clearTimeout(timeoutId);
                assistantMessage.content = "抱歉，处理您的请求时出现了错误。";
                assistantMessage.isStreaming = false;
                assistantMessage.hasError = true;

                this.updateMessage(assistantMessage.messageId, assistantMessage);
                this.streamController = null;
                reject(error);
              }
            },
            // onComplete 回调
            () => {
              if (!hasError && !isCompleted) {
                isCompleted = true;
                clearTimeout(timeoutId);
                console.log("流式响应完成，最终内容:", fullResponse);
                // 只有在内容还没有被格式化的情况下才格式化
                if (!assistantMessage.content.includes('formatOutputContent') &&
                    !assistantMessage.content.includes('<a href=')) {
                  assistantMessage.content = formatOutputContent(fullResponse || "我已收到您的消息，但暂时无法处理。");
                }
                assistantMessage.isStreaming = false;

                this.updateMessage(assistantMessage.messageId, assistantMessage);
                this.streamController = null;
                resolve(assistantMessage);
              }
            },
            // 传递AbortController的signal
            this.streamController ? this.streamController.signal : null
          );

          // 处理 stream Promise 的结果
          streamPromise.then((result) => {
            console.log("api.workflow.stream Promise resolved:", result);
          }).catch((error) => {
            console.error("api.workflow.stream Promise rejected:", error);
            if (!hasError && !isCompleted) {

              if (error.name === 'AbortError' || error.message.includes('aborted')) {
                console.log("流式请求被用户主动中止");
                hasError = true;
                isCompleted = true;
                clearTimeout(timeoutId);
                this.streamController = null;
                resolve(assistantMessage);
                return;
              }

              hasError = true;
              clearTimeout(timeoutId);
              assistantMessage.content = `抱歉，处理您的请求时出现了错误，请重试`;
              assistantMessage.isStreaming = false;
              assistantMessage.hasError = true;

              this.updateMessage(assistantMessage.messageId, assistantMessage);
              this.streamController = null;
              reject(error);
            }
          });
        });

      } else if (this.sourceType === 2) {
        // 工作流调用 - 执行工作流（非流式）
        const params = {
          sessionId: sessionId,
          flowDetailType: "WorkFlow",
          flowId: this.appId,
          textInput: message,
          imageInput: imageUrl || "", // 添加图片URL参数
          fileInput: "",
          customVariables: {},
        };

        console.log("执行工作流API调用:", params);
        const result = await api.workflow.execute(params);

        if (result && result.code === 200 && result.data) {
          return {
            role: "assistant",
            content: formatOutputContent(result.data.output || "我已收到您的消息，但暂时无法处理。"),
            time: this.formatTime(new Date()),
            messageId: this.generateGuid(),
            fromWorkflow: true
          };
        } else {
          throw new Error(result?.message || "工作流执行失败");
        }

      } else {
        throw new Error(`不支持的sourceType: ${this.sourceType}`);
      }
    },

    // 添加消息
    addMessage(message) {
      // 检查是否已存在相同的消息
      if (message.messageId && this.messages.some(msg => msg.messageId === message.messageId)) {
        console.warn("消息已存在，跳过添加:", message.messageId);
        return;
      }

      console.log("addMessage 被调用:", message);
      this.messages.push(message);

      // 检查是否需要保存到localStorage
      const shouldSave = !message.isStreaming &&
                        !message.hasError &&
                        !this.isErrorMessage(message);

      if (shouldSave) {
        this.saveChatMessages();
      }

      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 更新消息（用于流式响应）
    updateMessage(messageId, updatedMessage) {
      console.log("updateMessage 被调用:", { messageId, updatedMessage });
      const index = this.messages.findIndex(msg => msg.messageId === messageId);

      if (index !== -1) {
        const oldMessage = this.messages[index];
        const newMessage = { ...oldMessage, ...updatedMessage };

        // 使用Vue.set确保响应式更新
        this.$set(this.messages, index, newMessage);

        // 如果流式传输完成且不是错误消息，保存到localStorage
        if (!updatedMessage.isStreaming) {
          const shouldSave = !newMessage.hasError && !this.isErrorMessage(newMessage);
          if (shouldSave) {
            this.saveChatMessages();
          }
        }

        if (!updatedMessage.isStreaming) {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } else {
        console.error("未找到要更新的消息:", messageId);
      }
    },

    // 检查是否为错误消息
    isErrorMessage(message) {
      if (!message || !message.content) return false;

      const errorKeywords = [
        '抱歉，处理您的请求时出现了错误',
        '发送失败',
        '连接未建立',
        '会话未初始化',
        '无法发送消息',
        '处理消息时出现了错误',
        '工作流执行失败',
        '流式响应出错'
      ];

      const content = message.content.toLowerCase();
      return errorKeywords.some(keyword => content.includes(keyword.toLowerCase()));
    },

    // 保存聊天消息到本地存储
    saveChatMessages() {
      if (!this.currentSessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + this.currentSessionId;

        // 过滤消息：去重处理 + 排除错误消息
        const validMessages = [];
        const seenMessageIds = new Set();

        for (const message of this.messages) {
          // 跳过重复消息
          if (message.messageId && seenMessageIds.has(message.messageId)) {
            continue;
          }

          // 跳过错误消息
          if (message.hasError || this.isErrorMessage(message)) {
            continue;
          }

          if (message.messageId) {
            seenMessageIds.add(message.messageId);
          }
          validMessages.push(message);
        }

        // 只保存最新的200条有效消息
        const messagesToSave = validMessages.slice(-200);
        localStorage.setItem(storageKey, JSON.stringify(messagesToSave));
        console.log(`已保存${messagesToSave.length}条有效消息到本地存储`);

      } catch (error) {
        console.error("保存聊天消息失败:", error);
      }
    },

    // 从本地存储加载聊天消息
    loadChatMessages(sessionId) {
      if (!sessionId) {
        sessionId = this.currentSessionId;
      }
      if (!sessionId) return false;

      try {
        const storageKey = this.chatMessagesStoragePrefix + sessionId;
        const savedMessagesJson = localStorage.getItem(storageKey);

        if (savedMessagesJson) {
          const savedMessages = JSON.parse(savedMessagesJson);
          if (Array.isArray(savedMessages) && savedMessages.length > 0) {
            this.messages = savedMessages.map(msg => ({
              ...msg,
              content: formatOutputContent(msg.content),
              // 清除历史消息的流式状态
              isStreaming: false,
              // 确保知识库文件信息被保留
              knowledgeFiles: msg.knowledgeFiles || []
            }));
            console.log(`已从本地存储加载${savedMessages.length}条消息`);

            this.$nextTick(() => {
              this.scrollToBottom();
            });
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error("加载聊天消息失败:", error);
        return false;
      }
    },

    // 清除指定会话的聊天消息缓存
    clearChatMessages(sessionId) {
      if (!sessionId) {
        sessionId = this.currentSessionId;
      }
      if (!sessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + sessionId;
        localStorage.removeItem(storageKey);
        console.log(`已清除会话 ${sessionId} 的本地消息缓存`);
      } catch (error) {
        console.error("清除聊天消息失败:", error);
      }
    },

    // 刷新聊天
    refreshChat() {
      if (this.messages.length > 0 && confirm("确定要清除当前对话记录吗？")) {
        this.clearChatMessages();
        this.messages = [];
        this.addWelcomeMessage();
      } else if (this.messages.length === 0) {
        this.addWelcomeMessage();
      }
    },

    // 关闭聊天
    closeChat() {
      this.$emit('close-chat');
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const messagesContainer = this.$refs.messagesContainer;
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
      });
    },

    // 停止流式输出
    stopStreaming() {
      console.log("用户请求停止流式输出");

      // 中止流式请求
      if (this.streamController) {
        this.streamController.abort();
        this.streamController = null;
        console.log("流式控制器已中止");
      }

      // 重置流式状态
      this.isStreaming = false;
      this.isTyping = false;

      // 重置录音状态（如果正在录音）
      if (this.recordingStartTime) {
        this.recordingStartTime = null;
        if (this.voiceInputManager && this.voiceInputManager.isRecording) {
          this.voiceInputManager.stopRecording();
        }
      }

      // 找到正在流式传输的消息并停止
      const streamingMessage = this.messages.find(msg => msg.isStreaming);
      if (streamingMessage) {
        streamingMessage.isStreaming = false;
        // 在内容末尾添加停止标记
        if (!streamingMessage.content.includes('已停止生成')) {
          streamingMessage.content += '\n\n<div style="color: #999; font-style: italic;">已停止生成</div>';
        }
        this.updateMessage(streamingMessage.messageId, streamingMessage);
        console.log("已停止流式消息:", streamingMessage.messageId);
      }

      this.$message.info('已停止生成');
    },

    // 清理本地存储中的重复消息
    cleanupDuplicateMessages() {
      const prefix = this.chatMessagesStoragePrefix;
      let cleanedCount = 0;

      try {
        // 遍历所有localStorage的键
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key && key.startsWith(prefix)) {
            const savedData = localStorage.getItem(key);
            if (savedData) {
              try {
                const messages = JSON.parse(savedData);
                if (Array.isArray(messages)) {
                  // 去重处理
                  const uniqueMessages = [];
                  const seenMessageIds = new Set();

                  for (const message of messages) {
                    if (message.messageId && seenMessageIds.has(message.messageId)) {
                      continue;
                    }
                    if (message.messageId) {
                      seenMessageIds.add(message.messageId);
                    }
                    uniqueMessages.push(message);
                  }

                  // 如果发现重复消息，更新存储
                  if (uniqueMessages.length !== messages.length) {
                    localStorage.setItem(key, JSON.stringify(uniqueMessages));
                    cleanedCount++;
                  }
                }
              } catch (error) {
                console.error(`清理会话 ${key} 的消息时出错:`, error);
              }
            }
          }
        }

        if (cleanedCount > 0) {
          console.log(`共清理了 ${cleanedCount} 个会话的重复消息`);
        }
      } catch (error) {
        console.error("清理重复消息失败:", error);
      }
    },

    // 生成GUID
    generateGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    // 格式化时间
    formatTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
    },

    // ===== 语音功能相关方法 =====

    // 初始化语音功能
    initVoiceFeatures() {
      try {
        // 创建语音播报工具实例
        const voiceUtils = new VoiceUtils();
        this.voiceTTS = voiceUtils.createTTS({
          preferServer: true,
          fallbackToLocal: true,
          onStart: (messageIndex) => {
            console.log('语音朗读开始:', messageIndex);
          },
          onStop: () => {
            console.log('语音朗读结束');
          },
          onError: (error, messageIndex) => {
            console.error('语音朗读错误:', error, messageIndex);
          }
        });

        console.log('语音播报功能已初始化');
      } catch (error) {
        console.error('初始化语音功能失败:', error);
      }
    },

    // 清理语音功能资源
    cleanupVoiceFeatures() {
      try {
        if (this.voiceTTS) {
          this.voiceTTS.cleanup();
          this.voiceTTS = null;
        }
        // 重置语音输入相关状态
        this.recordingStartTime = null;
        this.voiceStatus = '';
        this.voiceStatusType = '';
        this.voiceStatusIcon = '';
        console.log('语音功能资源已清理');
      } catch (error) {
        console.error('清理语音功能资源失败:', error);
      }
    },

    // 语音朗读消息 - 使用公共方法
    async readMessage(content, messageIndex) {
      try {
        if (!this.voiceTTS) {
          this.$showFriendlyError(null, '语音播报功能未初始化');
          return;
        }

        // 准备TTS参数
        const ttsParams = {
          sessionID: this.currentSessionId,
          flowType: 'SessionFlow',
          appId: this.appId
        };

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg)
        };

        // 调用公共语音朗读方法
        await this.voiceTTS.readMessage(content, messageIndex, ttsParams, api, messageHandler);

      } catch (error) {
        console.error('语音朗读失败:', error);
        this.$showFriendlyError(error, '语音朗读功能出现错误');
      }
    },

    // 停止语音朗读 - 使用公共方法
    stopReading() {
      try {
        if (this.voiceTTS) {
          this.voiceTTS.stopReading();
        }
      } catch (error) {
        console.error('停止朗读失败:', error);
      }
    },

    // 获取语音朗读按钮的图标 - 使用公共方法
    getVoiceReadButtonIcon(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonIcon(messageIndex);
      }
      return 'el-icon-video-play';
    },

    // 获取语音朗读按钮的提示文字 - 使用公共方法
    getVoiceReadButtonTitle(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonTitle(messageIndex);
      }
      return '语音朗读';
    },



    // 复制消息内容 - 使用公共方法
    async copyMessage(content) {
      try {
        // 提取纯文本内容
        const textContent = VoiceUtils.extractTextContent(content);

        if (!textContent.trim()) {
          this.$message.warning('该消息没有可复制的文本内容');
          return;
        }

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg)
        };

        // 使用公共复制方法
        await VoiceUtils.copyToClipboard(textContent, messageHandler);

      } catch (error) {
        console.error('复制消息失败:', error);
        this.$showFriendlyError(error, '复制失败，请手动复制');
      }
    },

    // 清除聊天记忆
    async clearChatMemory() {
      try {
        const result = await clearChatMemory({
          sessionId: this.currentSessionId,
          api: api,
          messageHandler: {
            confirm: (message, title, options) => this.$confirm(message, title, options),
            success: (message) => this.$message.success(message),
            error: (message) => this.$showFriendlyError(null, message)
          },
          clearLocalMessages: () => {
            // 清空聊天消息和输入框
            this.messages = [];
            this.inputMessage = "";

            // 清除本地存储的消息
            if (this.currentSessionId) {
              this.clearChatMessages(this.currentSessionId);
            }
          },
          stopStreaming: () => {
            // 停止流式输出（如果有）
            if (this.streamController) {
              this.streamController.abort();
              this.streamController = null;
            }
            this.isStreaming = false;
            this.isTyping = false;
          },
          stopReading: () => {
            // 停止当前的语音朗读
            this.stopReading();
          },
          addWelcomeMessage: () => {
            // 添加新的欢迎消息
            this.addWelcomeMessage();
          }
        });

        console.log('清除记忆操作结果:', result);
      } catch (error) {
        console.error('清除聊天记忆失败:', error);
        this.$showFriendlyError(error, '清除聊天记忆失败');
      }
    },

    // ============ 语音输入相关方法 ============

    // 初始化语音输入管理器
    async initVoiceInputManager() {
      try {
        console.log('正在初始化语音输入管理器...');

        this.voiceInputManager = new VoiceInputManager({
          sessionId: this.currentSessionId,
          appId: this.appId,
          api: api,
          onStatusChange: (status) => {
            this.voiceStatus = status.status;
            this.voiceStatusType = status.type;
            this.voiceStatusIcon = status.icon;
          },
          onTextResult: (text) => {
            // 将识别到的文本设置到输入框
            this.inputMessage = text;
          },
          onAutoSend: async () => {
            // 自动发送消息
            await this.sendMessage();
          }
        });

        await this.voiceInputManager.initialize();
        console.log('语音输入管理器初始化成功');
      } catch (error) {
        console.error('语音输入管理器初始化失败:', error);
      }
    },

    // 语音按钮鼠标按下事件
    handleVoiceMouseDown() {
      // 只在触摸事件中阻止默认行为，鼠标事件不需要
      this.startRecording();
    },

    // 语音按钮鼠标松开事件
    handleVoiceMouseUp() {
      // 只在触摸事件中阻止默认行为，鼠标事件不需要
      this.stopRecording();
    },

    // 语音按钮触摸开始事件
    handleVoiceTouchStart(event) {
      event.preventDefault(); // 触摸事件需要阻止默认行为
      this.startRecording();
    },

    // 语音按钮触摸结束事件
    handleVoiceTouchEnd(event) {
      event.preventDefault(); // 触摸事件需要阻止默认行为
      this.stopRecording();
    },

    // 开始录音
    startRecording() {
      if (!this.voiceInputManager || this.isTyping || this.hasStreamingMessage) return;

      // 记录录音开始时间
      this.recordingStartTime = Date.now();

      // 更新配置
      this.voiceInputManager.updateOptions({
        sessionId: this.currentSessionId,
        appId: this.appId
      });

      this.voiceInputManager.startRecording();
    },

    // 停止录音
    stopRecording() {
      if (!this.voiceInputManager) return;

      // 检查录音时间间隔
      if (this.recordingStartTime) {
        const recordingDuration = Date.now() - this.recordingStartTime;
        const minRecordingTime = 1000; // 最小录音时间1秒

        if (recordingDuration < minRecordingTime) {
          // 重置录音状态
          this.recordingStartTime = null;

          // 设置标志阻止语音识别处理，然后停止录音
          try {
            if (this.voiceInputManager) {
              // 设置标志表示这是一个太短的录音，不应该处理
              this.voiceInputManager.shouldIgnoreResult = true;

              if (this.voiceInputManager.isRecording) {
                this.voiceInputManager.stopRecording();
              }
            }
          } catch (error) {
            console.error('停止录音时出错:', error);
          }

          // 立即清除所有语音状态，防止显示"正在识别"
          this.voiceStatus = '';
          this.voiceStatusType = '';
          this.voiceStatusIcon = '';

          // 短暂延迟后显示错误提示，确保状态已清除
          setTimeout(() => {
            this.voiceStatus = '请长按录音，识别语音太短';
            this.voiceStatusType = 'error';
            this.voiceStatusIcon = 'el-icon-warning';

            // 3秒后清除提示
            setTimeout(() => {
              this.voiceStatus = '';
              this.voiceStatusType = '';
              this.voiceStatusIcon = '';
            }, 3000);
          }, 50); // 减少延迟时间

          return; // 直接返回，不执行后续的语音识别处理
        }
      }

      // 正常停止录音并处理语音识别
      this.voiceInputManager.stopRecording();
      this.recordingStartTime = null;
    },

    // 获取语音输入按钮标题
    getVoiceInputTitle() {
      if (!this.voiceInputManager) {
        return '语音功能未初始化';
      }
      return this.voiceInputManager.getInputTitle(this.isTyping || this.hasStreamingMessage);
    },

    // 获取语音输入按钮图标
    getVoiceInputIcon() {
      if (!this.voiceInputManager) {
        return 'el-icon-microphone-off';
      }
      return this.voiceInputManager.getInputIcon();
    },

    // 图片上传前的预处理
    beforeImageUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!');
      }
      return isLt2M;
    },

    // 处理图片上传
    async handleImageUpload(options) {
      const file = options.file;
      let loadingMessage = null;

      try {
        // 显示上传中提示
        loadingMessage = this.$message({
          message: "图片上传中...",
          type: "info",
          duration: 0,
        });

        // 构建上传参数
        const uploadParams = {
          file: file,
          fileType: "image",
          folder: "chat_images",
        };

        // 使用upload方法上传文件
        const uploadRes = await api.oss.upload(uploadParams);

        // 关闭加载提示
        if (loadingMessage) {
          loadingMessage.close();
        }

        if (uploadRes.data && uploadRes.data.fileUrl) {
          // 设置图片URL
          this.uploadedImageUrl = uploadRes.data.fileUrl;
          this.$message.success("图片上传成功");
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        // 关闭加载提示
        if (loadingMessage) {
          loadingMessage.close();
        }

        console.error("上传图片失败:", error);
        this.$showFriendlyError(error, "图片上传失败，请稍后重试");
      }
    },

    // 移除已上传的图片
    removeUploadedImage() {
      this.uploadedImageUrl = null;
      this.$message.info("已移除上传的图片");
    },

    // 预览图片
    previewImage(imageUrl) {
      // 创建一个简单的图片预览弹窗
      this.$msgbox({
        title: '图片预览',
        message: `<img src="${imageUrl}" style="max-width: 100%; max-height: 400px; object-fit: contain;" />`,
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        confirmButtonText: '关闭'
      }).catch(() => {
        // 用户点击关闭按钮
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.simple-chat-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #606266;

  i {
    font-size: 24px;
    margin-bottom: 10px;
    animation: rotate 2s linear infinite;
  }

  span {
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background-color: #fafbfc;

  .chat-message {
    display: flex;
    margin-bottom: 24px;

    &.user-message {
      justify-content: flex-end;

      .user-message-content {
        max-width: 70%;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-right: 10px;

        .user-message-wrapper {
          background: linear-gradient(135deg, #667eea 0%, #4892f3 100%);
          border-radius: 12px;
          padding: 16px 20px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }

          .message-image {
            margin-bottom: 8px;

            img {
              max-width: 200px;
              max-height: 150px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                transform: scale(1.02);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              }
            }
          }

          .message-text {
            p {
              margin: 0;
              line-height: 1.6;
              word-wrap: break-word;
              color: #ffffff;
              font-size: 14px;
            }
          }
        }

        .message-info {
          margin-top: 8px;
          font-size: 11px;
          opacity: 0.6;
          color: #656d76;
          text-align: right;
        }
      }
    }

    &.system-message {
      justify-content: flex-start;

      .message-content {
        color: #303133;
        margin-left: 10px;
      }
    }
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-content {
    max-width: 70%;

    .message-wrapper {
      position: relative;
      border-radius: 12px;
      padding: 16px 20px;
      background-color: #ffffff;
      border: 1px solid #f0f0f0;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      .message-actions-top {
        position: absolute;
        top: 12px;
        right: 12px;
        display: flex;
        gap: 6px;
        z-index: 1;
        opacity: 1;

        .action-btn {
          background: #ffffff;
          border: 1px solid #e8e8e8;
          border-radius: 6px;
          padding: 6px 8px;
          font-size: 12px;
          color: #666666;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

          &:hover:not(:disabled) {
            background-color: #f8f9fa;
            border-color: #d0d7de;
            color: #0969da;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &.voice-read-btn {
            &:hover:not(:disabled) {
              color: #28a745;
              border-color: #28a745;
            }
          }

          &.copy-btn {
            &:hover:not(:disabled) {
              color: #fd7e14;
              border-color: #fd7e14;
            }
          }

          i {
            font-size: 13px;

            &.el-icon-loading {
              animation: rotate 2s linear infinite;
            }
          }
        }
      }



      .message-text {
        position: relative;
        padding-right: 90px; // 为右上角按钮留出空间

        p {
          margin: 0;
          line-height: 1.6;
          word-wrap: break-word;
          color: #24292f;
          font-size: 14px;
        }

        // 流式传输状态样式
        &.streaming {
          border-left: 3px solid #0969da;
          padding-left: 12px;
          background-color: rgba(9, 105, 218, 0.03);
          border-radius: 0 12px 12px 0;
        }

        // 错误状态样式
        &.error {
          border-left: 3px solid #d1242f;
          padding-left: 12px;
          background-color: rgba(209, 36, 47, 0.03);
          border-radius: 0 12px 12px 0;
        }

        // 流式传输指示器
        .streaming-indicator {
          display: inline-flex;
          align-items: center;
          margin-left: 8px;

          span {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: #0969da;
            margin-right: 3px;
            animation: streamingDot 1.5s infinite;

            &:nth-child(2) {
              animation-delay: 0.3s;
            }

            &:nth-child(3) {
              animation-delay: 0.6s;
            }
          }
        }
      }
             // 知识库文件标签样式
       .knowledge-files {
         margin-top: 12px;
         padding: 8px 12px;
         background-color: #f8f9fa;
         border: 1px solid #e9ecef;
         border-radius: 6px;
         font-size: 12px;

         .knowledge-files-header {
           display: flex;
           align-items: center;
           gap: 6px;
           margin-bottom: 8px;
           font-weight: 500;
           color: #495057;

           i {
             font-size: 14px;
             color: #6c757d;
           }
         }

         .knowledge-files-list {
           display: flex;
           flex-wrap: wrap;
           gap: 6px;

           :deep(.knowledge-file-tag) {
             display: inline-flex;
             align-items: center;
             gap: 4px;
             background-color: #e3f2fd;
             color: #1565c0;
             border: 1px solid #bbdefb;
             border-radius: 4px;
             padding: 4px 8px;
             font-size: 11px;
             line-height: 1.2;
             cursor: pointer;
             transition: all 0.2s ease;

             &:hover {
               background-color: #bbdefb;
               border-color: #90caf9;
               transform: translateY(-1px);
               box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
             }

             i {
               font-size: 12px;
             }
           }
         }
       }
    }

    .message-info {
      margin-top: 8px;
      font-size: 11px;
      opacity: 0.6;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 4px;
      color: #656d76;

      .streaming-text {
        color: #0969da;
        font-style: italic;
      }

      .error-text {
        color: #d1242f;
        font-style: italic;
      }
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 10px 20px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #999;
    margin-right: 5px;
    animation: typing 1.5s infinite;

    &:nth-child(2) {
      animation-delay: 0.5s;
    }

    &:nth-child(3) {
      animation-delay: 1s;
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes streamingDot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-input {
  padding: 20px 24px;
  border-top: 1px solid #e1e4e8;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .input-and-actions {
    display: flex;
    align-items: stretch;
    gap: 12px;
    min-height: 44px;
  }

  .input-container {
    display: flex;
    align-items: stretch;
    gap: 8px;
    position: relative;
    flex: 1;

    .el-input {
      flex: 1;

      :deep(.el-textarea__inner) {
        border-radius: 12px;
        border: 1px solid #e1e4e8;
        padding: 12px 16px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        transition: all 0.2s ease;
        min-height: 44px;
        box-sizing: border-box;

        &:focus {
          border-color: #0969da;
          box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
        }
      }
    }

    .voice-input-btn {
      width: 44px;
      height: 44px;
      border-radius: 12px;
      border: 1px solid #e1e4e8;
      background: #ffffff;
      color: #666666;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      user-select: none;
      flex-shrink: 0;

      &:hover:not(:disabled) {
        background-color: #f8f9fa;
        border-color: #0969da;
        color: #0969da;
        box-shadow: 0 2px 8px rgba(9, 105, 218, 0.15);
      }

      &:active:not(:disabled) {
        transform: scale(0.98);
      }

      &.recording {
        background: linear-gradient(135deg, #ff3838, #c0392b);
        border-color: #ff3838;
        color: white;
        animation: pulse 1.5s infinite;
        box-shadow: 0 2px 12px rgba(255, 56, 56, 0.3);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f5f5f5;
        border-color: #e1e4e8;
        color: #999999;

        &:hover {
          transform: none;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
      }

      i {
        font-size: 16px;
      }
    }

    .image-uploader {
      .image-upload-btn {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        border: 1px solid #e1e4e8;
        background: #ffffff;
        color: #666666;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        user-select: none;
        flex-shrink: 0;

        &:hover:not(:disabled) {
          background-color: #f8f9fa;
          border-color: #0969da;
          color: #0969da;
          box-shadow: 0 2px 8px rgba(9, 105, 218, 0.15);
        }

        &:active:not(:disabled) {
          transform: scale(0.98);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background-color: #f5f5f5;
          border-color: #e1e4e8;
          color: #999999;

          &:hover {
            transform: none;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }
        }

        i {
          font-size: 16px;
        }
      }
    }
  }

  .voice-status {
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
    animation: fadeIn 0.3s ease;

    i {
      font-size: 14px;

      &.el-icon-loading {
        animation: rotate 2s linear infinite;
      }
    }

    &.success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }

    &.error {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }

    &.loading {
      background-color: #d1ecf1;
      border: 1px solid #bee5eb;
      color: #0c5460;
    }
  }

  .uploaded-image-preview {
    margin-bottom: 8px;
    padding: 10px;
    background-color: #f0f9eb;
    border: 1px solid #67c23a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    .image-container {
      position: relative;
      width: 40px;
      height: 40px;
      overflow: hidden;
      border-radius: 6px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .remove-image-btn {
        position: absolute;
        top: 2px;
        right: 2px;
        background-color: rgba(0, 0, 0, 0.5);
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        font-size: 12px;
        z-index: 1;
        opacity: 0.8;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .input-actions {
    display: flex;
    align-items: stretch;
    gap: 8px;
    flex-shrink: 0;

    .el-button {
      height: 44px;
      padding: 0 16px;
      transition: all 0.2s ease;
      white-space: nowrap;
      border-radius: 12px;
      font-weight: 500;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: auto;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      i {
        margin-right: 4px;
        font-size: 14px;
      }

      // 只有图标的按钮样式调整
      &.icon-only {
        min-width: 44px;
        padding: 0;

        i {
          margin-right: 0;
        }
      }
    }

    // 停止生成按钮样式
    .el-button--danger {
      background-color: #d1242f;
      border-color: #d1242f;
      color: white;

      &:hover {
        background-color: #a21e2a;
        border-color: #a21e2a;
        box-shadow: 0 3px 12px rgba(209, 36, 47, 0.25);
      }
    }

    // 发送按钮增强样式
    .el-button--primary {
      background-color: #0969da;
      border-color: #0969da;
      min-width: 80px;

      &:hover {
        background-color: #0860ca;
        border-color: #0860ca;
        box-shadow: 0 3px 12px rgba(9, 105, 218, 0.25);
      }

      &:disabled {
        opacity: 0.6;
        background-color: #94a3b8;
        border-color: #94a3b8;

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }

    // 清除记忆按钮样式
    .el-button--warning {
      background-color: #fd7e14;
      border-color: #fd7e14;

      &:hover {
        background-color: #e8590c;
        border-color: #e8590c;
        box-shadow: 0 3px 12px rgba(253, 126, 20, 0.25);
      }

      &:disabled {
        opacity: 0.6;
        background-color: #94a3b8;
        border-color: #94a3b8;

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;

  i {
    font-size: 64px;
    margin-bottom: 20px;
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}
</style>
