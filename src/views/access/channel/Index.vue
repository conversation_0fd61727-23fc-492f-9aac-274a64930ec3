<template>
  <div class="access-channel-page">
    <div class="page-header">
      <h2>API</h2>
      <p class="page-desc">配置和管理AI应用的API接入</p>
    </div>
    <div class="page-content">
      <el-row :gutter="20">
        <!-- API 部分 -->
        <el-col :span="8">
          <el-card class="channel-card">
            <div class="channel-icon">
              <i class="el-icon-chat-dot-round"></i>
            </div>
            <h3>对话 API</h3>
            <p>将应用或工作流的对话能力接入你的应用</p>
            <div class="channel-actions">
              <el-button type="text">文档</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="channel-card">
            <div class="channel-icon">
              <i class="el-icon-connection"></i>
            </div>
            <h3>工作流运行 API</h3>
            <p>可传入工作流变量运行工作流</p>
            <div class="channel-actions">
              <el-button type="text">文档</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="channel-card">
            <div class="channel-icon">
              <i class="el-icon-notebook-2"></i>
            </div>
            <h3>带记忆的对话 API</h3>
            <p>兼容应用和工作流中的上下文记忆设置，无需单独传入上下文</p>
            <div class="channel-actions">
              <el-button type="text">文档</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="section-header">
        <h3>API Key 管理</h3>
        <el-button type="primary" @click="handleCreateKey">创建API Key</el-button>
      </div>

      <!-- 添加 API Key 表格 -->
      <el-table :data="apiKeys" style="width: 100%">
        <el-table-column prop="key" label="API Key" min-width="200">
          <template #default="{ row }">
            <span>{{ maskApiKey(row.key) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180"></el-table-column>
        <el-table-column prop="expireTime" label="过期时间" min-width="180"></el-table-column>
        <el-table-column label="操作" width="180" align="right">
          <template #default="{ row }">
            <el-button type="text" @click="handleCopyKey(row)">复制</el-button>
            <el-button type="text" class="delete-btn" @click="handleDeleteKey(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="section-header">
        <h3>网站集成</h3>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="channel-card">
            <div class="channel-icon">
              <i class="el-icon-monitor"></i>
            </div>
            <h3>网页嵌入</h3>
            <p>打造你的网站客服机器人</p>
            <div class="channel-actions">
              <el-button type="primary" @click="showWebEmbedDialog">接入</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="section-header">
        <h3>客户运营</h3>
      </div>
      <el-row :gutter="20">
        <el-col :span="8" v-for="channel in wechatChannels" :key="channel.type">
          <el-card class="channel-card">
            <div class="channel-icon">
              <i class="el-icon-chat-round"></i>
            </div>
            <h3>{{ channel.name }}</h3>
            <p>{{ channel.description }}</p>
            <div class="channel-actions">
              <el-button type="primary">接入</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 网页嵌入配置弹窗 -->
    <el-dialog
      title="配置网页嵌入"
      :visible.sync="webEmbedDialogVisible"
      width="650px"
      :close-on-click-modal="false"
      custom-class="web-embed-dialog"
    >
      <el-steps :active="activeStep" finish-status="success" class="embed-steps" align-center>
        <el-step title="基本信息"></el-step>
        <el-step title="配置客户端"></el-step>
      </el-steps>

      <div v-if="activeStep === 1" class="step-content">
        <div class="form-item">
          <div class="form-label">客户端名称<span class="required">*</span></div>
          <el-input v-model="embedConfig.clientName" placeholder="请输入自定义名称"></el-input>
        </div>

        <div class="form-item">
          <div class="form-label">接入应用<span class="required">*</span></div>
          <el-select v-model="embedConfig.application" placeholder="选择你的默认应用" class="full-width">
            <el-option-group label="应用">
              <el-option
                v-for="item in applicationList"
                :key="'app_' + item.id"
                :label="item.name"
                :value="'app_' + item.id">
              </el-option>
            </el-option-group>
            <el-option-group label="工作流">
              <el-option
                v-for="item in workflowList"
                :key="'workflow_' + item.id"
                :label="item.name"
                :value="'workflow_' + item.id">
              </el-option>
            </el-option-group>
          </el-select>
        </div>
      </div>

      <div class="dialog-footer">
        <el-button @click="webEmbedDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleNextStep">下一步</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api/request'

export default {
  name: 'AccessChannelPage',
  data() {
    return {
      wechatChannels: [
        {
          type: 'wechat_official',
          name: '微信公众号（企业）',
          description: '在企业公众号中申请入驻回复'
        },
        {
          type: 'wechat_personal',
          name: '微信公众号（个人）',
          description: '在个人订阅号中申请入驻回复'
        },
        {
          type: 'wechat_service',
          name: '微信客服',
          description: '打造你的微信客服管家'
        },
        {
          type: 'wechat_work',
          name: '企业微信',
          description: '打造你的企业微信数字员工'
        },
        {
          type: 'wechat',
          name: '微信',
          description: '打造你的微信下单机器人'
        }
      ],
      apiKeys: [
        {
          key: 'Lin...6BF',
          createTime: '2025-03-08 11:15:36',
          expireTime: '2025-03-08 11:15:36'
        },
        {
          key: 'Lin...mxg',
          createTime: '2025-03-12 14:07:52',
          expireTime: '2025-03-12 14:07:52'
        },
        {
          key: 'Lin...o6A',
          createTime: '2025-03-12 14:23:13',
          expireTime: '2025-03-12 14:23:13'
        }
      ],
      webEmbedDialogVisible: false,
      activeStep: 1,
      embedConfig: {
        clientName: '',
        application: ''
      },
      applicationList: [],
      workflowList: []
    }
  },
  created() {
    this.fetchApplications()
    this.fetchWorkflows()
  },
  methods: {
    handleCreateKey() {
      this.$message.success('API Key创建成功')
    },
    maskApiKey(key) {
      return key
    },
    handleCopyKey({ key }) {
      navigator.clipboard.writeText(key).then(() => {
        this.$message.success('API Key已复制到剪贴板')
      }).catch(() => {
        this.$showFriendlyError(null, '复制失败')
      })
    },
    handleDeleteKey({ key }) {
      this.$confirm('确定要删除该API Key吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('删除key:', key)
        this.$message.success('删除成功')
      }).catch(() => {})
    },
    showWebEmbedDialog() {
      this.webEmbedDialogVisible = true
      this.activeStep = 1
      this.embedConfig = {
        clientName: '',
        application: ''
      }
    },
    handleNextStep() {
      if (this.activeStep === 1) {
        if (!this.embedConfig.clientName || !this.embedConfig.application) {
          this.$message.warning('请填写必填项')
          return
        }

        // 解析选择的应用或工作流
        const appData = this.embedConfig.application.split('_')
        const sourceType = appData[0] === 'app' ? 1 : 2
        const correspondingAppId = appData[1]
        // 调用API创建客户端
        api.client.create({
          channelType: 1, // 网页嵌入类型
          clientName: this.embedConfig.clientName,
          correspondingAppId: correspondingAppId,
          sourceType: sourceType
        }).then(res => {
          if (res && res.isSuccess) {
            this.$message.success('客户端创建成功')
            this.webEmbedDialogVisible = false
            // 获取返回的客户端ID并跳转到配置页面
            this.$router.push(`/access/client`)
          } else {
            this.$showFriendlyError({ message: res.message }, '创建失败')
          }
        }).catch(error => {
          this.$showFriendlyError(error, '创建客户端失败，请重试')
        })
      }
    },
    async fetchApplications() {
      try {
        const res = await api.sessionFlow.getList()
        if (res && res.isSuccess && res.data) {
          this.applicationList = res.data.items
        }
      } catch (error) {
        console.error('获取应用列表失败', error)
                  this.$showFriendlyError(error, '获取应用列表失败')
      }
    },
    async fetchWorkflows() {
      try {
        const res = await api.workflow.getList()
        if (res && res.isSuccess && res.data) {
          this.workflowList = res.data.items
        }
      } catch (error) {
        console.error('获取工作流列表失败', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.access-channel-page {
  padding: 20px;

  .page-header {
    margin-bottom: 30px;

    h2 {
      margin: 0 0 10px;
      font-weight: 500;
    }

    .page-desc {
      color: #606266;
      font-size: 14px;
      margin: 0;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0 20px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .channel-card {
    height: 100%;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .channel-icon {
      font-size: 40px;
      color: #409EFF;
      margin: 20px 0;
    }

    h3 {
      margin: 0 0 10px;
      font-size: 16px;
      color: #303133;
    }

    p {
      margin: 0 0 20px;
      color: #606266;
      font-size: 14px;
      min-height: 40px;
    }

    .channel-actions {
      margin-top: 20px;
    }
  }

  :deep(.el-table) {
    margin-bottom: 30px;

    .delete-btn {
      color: #f56c6c;
      margin-left: 15px;
    }
  }

  .embed-steps {
    margin-bottom: 30px;
    padding: 0 40px;
  }

  .step-content {
    padding: 0 40px;
  }

  .form-item {
    margin-bottom: 20px;
  }

  .form-label {
    margin-bottom: 8px;
    font-size: 14px;

    .required {
      color: #f56c6c;
      margin-left: 4px;
    }
  }

  .full-width {
    width: 100%;
  }

  .dialog-footer {
    text-align: center;
    margin-top: 30px;
  }
}
</style>
