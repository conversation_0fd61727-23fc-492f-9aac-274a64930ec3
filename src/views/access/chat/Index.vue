<template>
  <div class="execute-workflow-page">
    <!-- 恢复左右分栏布局 -->
    <el-row :gutter="10" class="full-height">
      <!-- 左侧聊天历史列表 -->
      <el-col :span="6" class="full-height">
        <div class="left-panel">
                    <!-- 搜索框 -->
          <div class="search-container" v-if="!isSelectMode">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索智能体名称..."
              size="small"
              clearable
              @keyup.enter.native="handleSearch"
              @clear="handleSearchClear"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="handleSearch"
                :loading="isSearching"
              ></el-button>
            </el-input>

            <!-- 导出按钮 -->
            <el-dropdown @command="handleExportCommand" class="export-dropdown">
              <el-button size="small" type="primary" class="export-btn">
                导出<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="exportAll">导出全部会话</el-dropdown-item>
                <el-dropdown-item command="exportSelected">导出指定会话</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>

          <!-- 选择模式操作栏 -->
          <div class="select-mode-container" v-if="isSelectMode">
            <div class="select-info">
              已选择 {{ selectedSessions.length }} 个会话
            </div>
            <div class="select-actions">
              <el-button size="small" @click="selectAllSessions">全选</el-button>
              <el-button size="small" @click="clearSelection">清空</el-button>
              <el-button size="small" type="primary" @click="confirmExportSelected" :disabled="selectedSessions.length === 0">确认导出</el-button>
              <el-button size="small" @click="exitSelectMode">取消</el-button>
            </div>
          </div>

          <!-- 顶部选项卡 -->
          <div class="tabs-container">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
              <el-tab-pane name="all">
                <span slot="label" class="tab-label">
                  全部
                  <span v-if="hasUnreadSessions" class="tab-unread-dot"></span>
                </span>
              </el-tab-pane>
              <el-tab-pane label="已读" name="read"></el-tab-pane>
              <el-tab-pane name="unread">
                <span slot="label" class="tab-label">
                  未读
                  <span v-if="hasUnreadInCurrentTab" class="tab-unread-dot"></span>
                </span>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div class="list-container">
            <!-- 左侧聊天列表加载状态 -->
            <div v-if="isLoadingChatList || isRefreshingList" class="chat-list-loading">
              <div class="loading-content">
                <i class="el-icon-loading loading-icon"></i>
                <p>正在加载会话列表...</p>
              </div>
            </div>

            <!-- 聊天列表 -->
            <div
              v-else
              ref="listContainer"
              class="chat-list-scroll"
              @scroll="handleScroll"
            >
              <!-- 当有数据时显示列表 -->
              <template v-if="filteredChats.length > 0">
                <div
                  v-for="(chat, index) in filteredChats"
                  :key="index"
                  class="chat-list-item"
                  :class="{ active: currentChatIndex === index, 'select-mode': isSelectMode }"
                  @click="handleChatItemClick(chat, index)"
                >
                  <div class="chat-preview">
                    <!-- 选择模式下的复选框 -->
                    <div v-if="isSelectMode" class="chat-checkbox">
                      <el-checkbox
                        :value="selectedSessions.includes(chat.sessionId)"
                        @change="toggleSessionSelection(chat.sessionId)"
                        @click.native.stop
                      ></el-checkbox>
                    </div>

                    <div class="chat-avatar">
                      <el-avatar :size="32" :src="chat.profilePhoto || ''">{{
                        chat.appName ? chat.appName.charAt(0) : "U"
                      }}</el-avatar>
                    </div>
                    <div class="chat-info">
                      <div class="chat-name">{{ chat.appName || "用户" }}</div>
                      <div class="chat-last-message">
                        {{ chat.lastMessage || "" }}
                      </div>
                      <div class="chat-time">{{ chat.time }}</div>
                      <div class="chat-app-info" v-if="chat.appName">
                                              <!-- 根据appSourceType显示不同类型的标签 -->
                        <el-tag
                          size="mini"
                          :type="chat.appSourceType === 1 ? 'primary' : chat.appSourceType === 2 ? 'success' : 'info'"
                        >
                          {{ chat.appSourceType === 1 ? '应用' : chat.appSourceType === 2 ? '工作流' : '渠道' }}
                        </el-tag>
                        <!-- 显示在线状态 -->
                        <el-tag
                          size="mini"
                          :type="chat.isOnline ? 'success' : 'info'"
                        >
                          {{ chat.isOnline ? '在线' : '离线' }}
                        </el-tag>
                      </div>
                    </div>

                    <!-- 未读消息红点 -->
                    <div class="chat-unread-indicator">
                      <span v-if="chat.hasUnreadMessages" class="chat-unread-dot"></span>
                    </div>
                  </div>
                </div>

                <!-- 加载更多按钮 -->
                <div v-if="hasMore && !isLoadingMoreList" class="load-more-container">
                  <el-button
                    type="text"
                    @click="loadMoreSessions"
                    class="load-more-btn"
                  >
                    加载更多
                  </el-button>
                </div>

                <!-- 加载更多状态 -->
                <div v-if="isLoadingMoreList" class="loading-more-container">
                  <i class="el-icon-loading"></i>
                  <span>加载中...</span>
                </div>

                <!-- 没有更多数据提示 -->
                <div v-if="!hasMore" class="no-more-container">
                  <span>没有更多数据了</span>
                </div>
              </template>

              <!-- 当没有数据时显示空状态 -->
              <div v-else class="empty-list">
                <i class="el-icon-warning-outline"></i>
                <p>暂无聊天记录</p>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧聊天内容区域 -->
      <el-col :span="18" class="full-height">
        <div class="result-panel">
          <!-- 添加加载指示器 -->
          <div v-if="isLoading" class="loading-container" v-loading="isLoading" element-loading-text="正在加载聊天信息...">
          </div>

          <!-- 未选择会话时的提示 -->
          <div v-if="rightPanelReady && currentChatIndex === -1" class="no-session-selected">
            <i class="el-icon-s-comment empty-chat-icon"></i>
            <h3>请选择一个会话开始聊天</h3>
            <p>从左侧会话列表中选择一个会话进行对话</p>
          </div>

          <!-- 使用v-if控制右侧面板的显示，只有在准备好后才显示 -->
          <template v-if="rightPanelReady && currentChatIndex !== -1">
            <div class="panel-header">
              <h3>
                {{ (appInfo && appInfo.name) || "智能客服助手" }}
                <el-tag v-if="appInfo && appInfo.id" type="info"
                  >ID: {{ sessionStatus.sessionId }}</el-tag
                >
              </h3>
              <div class="header-actions">
                <el-button
                  v-if="!sessionStatus.isConnected && currentChatIndex!==-1"
                  type="warning"
                  @click="reconnectToSignalR"
                  >重连</el-button
                >
                <el-button
                v-if="!sessionStatus.isConnected && currentChatIndex!==-1"
                  size="mini"
                  icon="el-icon-refresh"
                  circle
                  @click="refreshChat"
                ></el-button>
                <el-button
                v-if="!sessionStatus.isConnected && currentChatIndex!==-1"
                  size="mini"
                  icon="el-icon-back"
                  circle
                  @click="closeChat"
                ></el-button>
              </div>
            </div>

            <div class="result-container">
              <div class="chat-messages" ref="messagesContainer">
                <!-- 加载更多消息按钮 -->
                <div class="load-more-container" v-if="messages.length > 0 && !isLoadingMore && hasMoreMessages">
                  <el-button type="text" @click="loadMoreMessages">查看更多历史消息</el-button>
                </div>
                <!-- 加载中指示器 -->
                <div class="load-more-loading" v-if="isLoadingMore">
                  <i class="el-icon-loading"></i>
                  <span>加载历史消息中...</span>
                </div>



                <!-- 消息列表 -->
                <div
                  v-for="(message, index) in messages"
                  :key="index"
                  :class="[
                    'chat-message',
                    message.role === 'user' ? 'user-message' :
                    message.role === 'human' ? 'human-message' : 'system-message',
                  ]"
                >
                  <!-- 头像 -->
                  <div
                    :class="[
                      'avatar',
                      message.role === 'user' ? 'user-avatar' : 'system-avatar'
                    ]"
                  >
                    <img
                      v-if="message.role === 'user'"
                      src="@/assets/userAvatar.png"
                      onerror="this.src='https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'"
                      alt="用户"
                    />
                    <img
                      v-else
                      :src="(appInfo && appInfo.profilePhoto) || ''"
                      onerror="this.src='https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'"
                      :alt="message.role === 'human' ? '人工客服' : '系统'"
                    />
                  </div>

                  <!-- 消息内容 -->
                  <div class="message-content">
                    <div class="message-text">
                      <p v-html="message.content"></p>
                    </div>
                    <!-- 消息操作按钮 - 只有非用户消息才显示 -->
                    <div v-if="message.role !== 'user'" class="message-actions">
                      <!-- 语音朗读按钮 -->
                      <button
                        class="action-btn voice-read-btn"
                        @click="readMessage(message.content, index)"
                        :disabled="isLoadingAudio && currentReadingMessageIndex !== index"
                        :title="getVoiceReadButtonTitle(index)"
                      >
                        <i :class="getVoiceReadButtonIcon(index)"></i>
                      </button>
                      <!-- 一键复制按钮 -->
                      <button
                        class="action-btn copy-btn"
                        @click="copyMessage(message.content)"
                        title="复制内容"
                      >
                        <i class="el-icon-document-copy"></i>
                      </button>
                    </div>
                    <div class="message-info">
                      <span>{{ message.time }}</span>
                    </div>
                  </div>
                </div>

                <!-- 正在输入提示 -->
                <div class="typing-indicator" v-if="isTyping">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>

              <!-- 输入框区域 -->
              <div class="chat-input">
                <el-input
                  v-model="inputMessage"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入消息..."
                  @keyup.enter.native.exact="sendMessage"
                  :disabled="isTyping"
                ></el-input>
                <!-- 清除记忆按钮 -->
                <el-button
                  v-if="sessionStatus.isConnected && sessionStatus.sessionId"
                  type="warning"
                  @click="clearChatMemory"
                  :disabled="isTyping || !sessionStatus.isConnected"
                  title="清除记忆"
                  style="margin-left: 10px"
                >
                  <i class="el-icon-delete"></i>
                  清除记忆
                </el-button>
                <el-button
                  type="primary"
                  :disabled="
                    !inputMessage.trim() ||
                    isTyping ||
                    !sessionStatus.isConnected
                  "
                  @click="sendMessage"
                  :title="!sessionStatus.isConnected ? '当前处于离线状态' : ''"
                  style="margin-left: 10px; height: 100%"
                  >发送</el-button
                >
              </div>
            </div>
          </template>
        </div>
      </el-col>
    </el-row>

    <!-- 导出弹框 -->
    <el-dialog
      title="导出会话"
      :visible.sync="exportDialogVisible"
      width="520px"
      :close-on-click-modal="false"
    >
      <div class="export-dialog-content">
        <el-alert
          title="为避免数据量过大，请按会话时间范围导出，时间跨度须在30天内。"
          type="primary"
          :closable="false"
          show-icon
        ></el-alert>

        <div class="date-range-section">
          <label class="date-label">会话时间 <span class="required">*</span></label>
          <el-date-picker
            v-model="exportDateRange"
            type="daterange"
            range-separator="→"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="datePickerOptions"
            style="width: 100%"
            @change="handleDateRangeChange"
          >
          </el-date-picker>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入SignalR客户端
import * as signalR from "@microsoft/signalr"
// 引入API接口
import { api } from "@/api/request"
// 导入格式化工具
import { clearChatMemory, formatOutputContent } from '@/utils'
// 导入语音工具
import { VoiceUtils } from '@/utils/voice'

export default {
  name: "AppChat",
  data() {
    return {
      appInfo: {},
      inputMessage: "",
      messages: [],
      isTyping: false,
      userAvatar: "",
      appId: null,
      // SignalR连接对象
      hubConnection: null,
      // 全局通知连接对象
      globalNotificationConnection: null,
      // 会话状态
      sessionStatus: {
        isConnected: false,
        sessionId: null,
        userName: "",
      },
      // 加载状态
      isLoading: true,
      // 是否有结果
      hasResult: false,
      // 聊天历史记录
      chatHistory: [],
      // 当前选中的聊天索引
      currentChatIndex: -1,
      // 应用来源类型
      sourceType: null,
      // 添加列表更新防抖
      chatListUpdateTimer: null,
      // 是否正在加载列表
      isLoadingChatList: false,
      // 是否正在刷新列表（用于切换选项卡时的loading显示）
      isRefreshingList: false,
      // 最后一次更新时间
      lastChatListUpdateTime: 0,
      // 聊天消息缓存键名前缀
      chatMessagesStoragePrefix: "chat_messages_",
      // 右侧面板是否已准备好
      rightPanelReady: false,
      // 历史消息分页参数
      historyMessageParams: {
        skipCount: 0,
        maxResultCount: 10
      },
      // 是否正在加载更多消息
      isLoadingMore: false,
      // 是否还有更多历史消息
      hasMoreMessages: true,
      // 是否需要自动滚动到底部
      shouldScrollToBottom: true,
      // 当前激活的选项卡
      activeTab: "all",

      // 瀑布流加载相关
      skipCount: 0,           // 跳过的数量
      maxResultCount: 10,     // 每次加载的数量
      totalCount: 0,          // 总数量
      hasMore: true,          // 是否还有更多数据
      isLoadingMoreList: false, // 是否正在加载更多列表数据

      // 未读状态
      hasUnreadSessions: false, // 是否有未读会话

      // 搜索相关
      searchKeyword: "", // 搜索关键词
      isSearching: false, // 是否正在搜索

      // 导出选择模式
      isSelectMode: false, // 是否处于选择模式
      selectedSessions: [], // 已选择的会话ID列表

      // 导出弹框
      exportDialogVisible: false, // 导出弹框是否显示
      exportType: '', // 导出类型：'all' 或 'selected'
      exportDateRange: [], // 导出日期范围
      firstSelectedDate: null, // 第一个选择的日期，用于限制第二个日期的选择范围

      // 刷新定时器
      refreshTimer: null, // 会话列表刷新防抖定时器

      // 全局通知相关
      unreadUpdateDebounce: null, // 防抖处理
      lastUnreadUpdate: null, // 防抖数据缓存
      // 防重复通知
      lastNotificationTime: null, // 最后一次通知时间
      lastNotificationSessionId: null, // 最后一次通知的会话ID

      // 语音播报相关 - 使用公共语音工具
      voiceTTS: null, // 语音播报工具实例
    };
  },
  computed: {
    chatPageStyle() {
      return {
        height: "100%",
        maxHeight: "100%",
      };
    },
    // 过滤后的聊天列表
    filteredChats() {
      // 现在主要通过接口过滤，这里保持原始数据
      // 如果需要额外的前端过滤，可以在这里添加
      return this.chatHistory;
    },
    // 当前选项卡是否有未读消息
    hasUnreadInCurrentTab() {
      if (this.activeTab === 'unread') {
        // 未读选项卡：检查当前显示的列表中是否有未读消息
        return this.filteredChats.some(chat => chat.hasUnreadMessages);
      }
      // 其他选项卡使用全局未读状态
      return this.hasUnreadSessions;
    },

    // 日期选择器配置
    datePickerOptions() {
      const today = new Date();
      today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻

      return {
        disabledDate: (time) => {
          // 不能选择今天之后的日期
          if (time.getTime() > today.getTime()) {
            return true;
          }

          // 如果正在选择日期范围且已经有了第一个日期
          if (this.firstSelectedDate) {
            const diffTime = Math.abs(time.getTime() - this.firstSelectedDate.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            // 禁用超过30天的日期
            return diffDays > 30;
          }

          return false;
        },
        onPick: ({ maxDate, minDate }) => {
          // 记录第一个选择的日期，用于限制第二个日期的选择范围
          if (minDate && !maxDate) {
            this.firstSelectedDate = new Date(minDate);
          } else if (maxDate && minDate) {
            // 选择完成后清除第一个日期的记录
            this.firstSelectedDate = null;

            // 检查最终选择的日期范围是否超过30天
            const diffTime = Math.abs(maxDate.getTime() - minDate.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (diffDays > 30) {
              this.$message.warning('时间跨度不能超过30天');
              this.exportDateRange = [];
            }
          }
        }
      };
    },

    // 获取朗读状态 - 使用公共方法
    isReading() {
      return this.voiceTTS ? this.voiceTTS.getState().isReading : false;
    },

    currentReadingMessageIndex() {
      return this.voiceTTS ? this.voiceTTS.getState().currentReadingMessageIndex : -1;
    },

    isLoadingAudio() {
      return this.voiceTTS ? this.voiceTTS.getState().isLoadingAudio : false;
    },
  },
  created() {
    // 获取路由参数中的clientId
    this.appId = this.$route.query.clientId || null;

    // 初始化时不建立连接，只加载左侧边栏数据
    this.fetchChatSessions();

    // 建立全局通知连接以接收实时通知
    this.establishGlobalNotificationConnection();

    // 初始化语音播报功能
    this.initVoiceTTS();
  },
  mounted() {
    this.scrollToBottom();
    // 不再检查登录和初始化聊天
    // 只加载左侧列表数据
  },
  updated() {
    // 只有在需要时才滚动到底部
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
    }
  },
  beforeDestroy() {
    // 组件销毁前关闭连接
    this.stopConnection();
    this.stopGlobalNotificationConnection();

    // 清理定时器
    if (this.chatListUpdateTimer) {
      clearTimeout(this.chatListUpdateTimer);
    }
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    if (this.unreadUpdateDebounce) {
      clearTimeout(this.unreadUpdateDebounce);
    }

    // 清理防重复通知变量
    this.lastNotificationTime = null;
    this.lastNotificationSessionId = null;

    // 清理语音播报资源
    if (this.voiceTTS) {
      this.voiceTTS.cleanup();
      this.voiceTTS = null;
    }
  },
  methods: {
        // 处理选项卡切换
    handleTabClick() {
      // 切换选项卡时清除当前选中的会话，显示"未选择会话时的提示"
      this.currentChatIndex = -1;
      this.messages = [];
      this.sessionStatus.sessionId = null;
      this.appInfo = {};

      // 断开当前的SignalR连接
      this.stopConnection();

      // 设置刷新状态，显示loading效果
      this.isRefreshingList = true;

      // 切换选项卡时重新加载数据
      this.refreshSessions();
    },

    // 加载更多会话
    loadMoreSessions() {
      if (this.isLoadingMoreList || !this.hasMore) {
        return;
      }

      this.isLoadingMoreList = true;
      this.skipCount += this.maxResultCount;
      this._fetchChatSessionsActual();
    },

    // 处理滚动事件
    handleScroll() {
      const container = this.$refs.listContainer;
      if (container) {
        const scrollTop = container.scrollTop;
        const scrollHeight = container.scrollHeight;
        const clientHeight = container.clientHeight;

        // 距离底部100px时触发加载更多
        if (scrollTop + clientHeight >= scrollHeight - 100 && this.hasMore && !this.isLoadingMoreList) {
          this.loadMoreSessions();
        }
      }
    },

    // 刷新会话列表
    refreshSessions() {
      this.fetchChatSessions(true); // 传入true表示刷新，重置分页
    },

    // 生成GUID方法
    generateGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },
    // 加载聊天历史记录
    fetchChatSessions(isRefresh = false) {
      // 如果是刷新，重置分页参数
      if (isRefresh) {
        this.skipCount = 0;
        this.hasMore = true;
        this.chatHistory = [];
      }

      // 如果正在加载中，直接返回
      if (this.isLoadingChatList) {
        return;
      }

      // 立即设置loading状态，确保用户能看到loading效果
      if (!this.isLoadingMoreList) {
        this.isLoadingChatList = true;
        // 在加载左侧栏前，重置右侧面板状态
        this.rightPanelReady = false;
      }

      // 如果距离上次更新时间不足2秒，使用防抖延迟加载（仅在非加载更多时）
      if (!this.isLoadingMoreList) {
        const currentTime = new Date().getTime();
        if (currentTime - this.lastChatListUpdateTime < 2000) {
          // 清除旧的计时器
          if (this.chatListUpdateTimer) {
            clearTimeout(this.chatListUpdateTimer);
          }

          // 设置新的延迟加载
          this.chatListUpdateTimer = setTimeout(() => {
            this._fetchChatSessionsActual();
          }, 2000);
          return;
        }
      }

      // 正常加载
      this._fetchChatSessionsActual();
    },

    // 实际加载聊天列表的方法
    _fetchChatSessionsActual() {
      // 更新最后一次加载时间
      this.lastChatListUpdateTime = new Date().getTime();

      // 确保loading状态已设置（防抖延迟调用时可能需要重新设置）
      if (!this.isLoadingMoreList && !this.isLoadingChatList) {
        this.isLoadingChatList = true;
        // 在加载左侧栏前，重置右侧面板状态
        this.rightPanelReady = false;
      }

      // 先不设置全局loading状态，避免整个页面被loading覆盖导致闪烁
      // 我们只在首次加载时才显示全局loading
      const isFirstLoad = this.chatHistory.length === 0;
      if (isFirstLoad) {
        this.isLoading = true;
      }

      // 获取认证令牌
      const token = localStorage.getItem("token");
      if (!token) {
        this.$message.warning("用户未登录，无法获取聊天历史");
        this.isLoadingChatList = false;
        this.isLoadingMoreList = false;
        this.isLoading = false;
        return;
      }

      // 发起API请求获取聊天会话列表，传入分页参数
      api.chat
        .getSessions({
          skipCount: this.skipCount,
          maxResultCount: this.maxResultCount,
          // 根据当前选项卡添加过滤条件
          ...(this.activeTab === 'all' && { ReadStatus: 0 }),
          ...(this.activeTab === 'read' && { ReadStatus: 1 }),
          ...(this.activeTab === 'unread' && { ReadStatus: 2 }),
          // Index.vue 中传 false，表示非测试会话
          IsTestSession: false,
          // 添加搜索关键词参数
          ...(this.searchKeyword && this.searchKeyword.trim() && { sessionName: this.searchKeyword.trim() }),
          // 可以根据需要添加其他过滤条件
          // SourceType: this.sourceType,
          // IsOnline: true,
          // Sorting: "lastMessage_desc"
        })
        .then((result) => {
          if (
            result &&
            result.code === 200 &&
            result.data &&
            result.data.items
          ) {
            // 提取未读状态
            this.hasUnreadSessions = result.data.hasUnreadSessions || false;

            // 处理返回的聊天会话数据
            const newChatHistory = result.data.items.map((session) => {
              // 使用lastMessageSentAt字段格式化时间为 xxx-xx-xx xx:xx:xx 格式
              let timeDisplay = "";
              if (session.lastMessageSentAt) {
                const lastMessageTime = new Date(session.lastMessageSentAt);
                const year = lastMessageTime.getFullYear();
                const month = (lastMessageTime.getMonth() + 1).toString().padStart(2, "0");
                const day = lastMessageTime.getDate().toString().padStart(2, "0");
                const hours = lastMessageTime.getHours().toString().padStart(2, "0");
                const minutes = lastMessageTime.getMinutes().toString().padStart(2, "0");
                const seconds = lastMessageTime.getSeconds().toString().padStart(2, "0");
                timeDisplay = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
              } else {
                // 如果没有lastMessageSentAt，使用lastModificationTime作为备选
                const lastModificationTime = new Date(session.lastModificationTime);
                const year = lastModificationTime.getFullYear();
                const month = (lastModificationTime.getMonth() + 1).toString().padStart(2, "0");
                const day = lastModificationTime.getDate().toString().padStart(2, "0");
                const hours = lastModificationTime.getHours().toString().padStart(2, "0");
                const minutes = lastModificationTime.getMinutes().toString().padStart(2, "0");
                const seconds = lastModificationTime.getSeconds().toString().padStart(2, "0");
                timeDisplay = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
              }

              return {
                id: session.id,
                clientId: session.clientId || session.id, // 添加clientId字段，如果API中不存在则使用ID
                sessionId: session.sessionID,
                name: session.sessionName,
                appId: session.appId,
                avatar: "",
                profilePhoto: session.profilePhoto, // 添加服务头像
                introduce: session.introduce, // 添加介绍信息
                lastMessage: session.lastMessageSnippet || "",
                time: timeDisplay,
                appName: session.appName,
                messages: [],
                channelName: session.channelName,
                hasUnreadMessages: session.hasUnreadMessages || false, // 添加未读消息标识
                appSourceType: session.appSourceType || 1, // 添加appSourceType字段，默认为1（应用）
                isOnline: session.isOnline || false, // 添加在线状态字段，默认为离线
              };
            });

            // 更新分页信息
            this.totalCount = result.data.totalCount || 0;
            this.hasMore = (this.skipCount + this.maxResultCount) < this.totalCount;

            // 保存现有当前选中的会话ID
            const currentSelectedSessionId =
              this.currentChatIndex >= 0 &&
              this.chatHistory[this.currentChatIndex]
                ? this.chatHistory[this.currentChatIndex].sessionId
                : null;

            // 如果是加载更多，追加到现有列表；如果是刷新，替换列表
            if (this.isLoadingMoreList) {
              this.chatHistory = [...this.chatHistory, ...newChatHistory];
            } else {
              this.chatHistory = newChatHistory;
            }

            // 聊天会话列表加载完成

            // 恢复之前选中的会话（只在首次加载时处理）
            if (!this.isLoadingMoreList) {
              if (currentSelectedSessionId) {
                // 如果有之前选中的会话，继续保持选中
                const newIndex = this.chatHistory.findIndex(
                  (chat) => chat.sessionId === currentSelectedSessionId
                );
                if (newIndex >= 0) {
                  this.currentChatIndex = newIndex;
                }
              }
              // 移除自动选择逻辑，改为纯手动选择模式

              // 右侧面板准备就绪
              this.rightPanelReady = true;
            }
          } else {
            this.$message.warning("获取聊天历史失败");
            console.error("获取聊天历史返回数据格式错误:", result);
            // 即使失败，也标记右侧面板准备完成
            this.rightPanelReady = true;
          }
        })
        .catch((error) => {
          this.$showFriendlyError(error, "获取聊天历史失败");
          console.error("获取聊天历史出错:", error);
          // 即使失败，也标记右侧面板准备完成
          this.rightPanelReady = true;
        })
        .finally(() => {
          this.isLoadingChatList = false;
          this.isLoadingMoreList = false;
          this.isRefreshingList = false; // 重置刷新状态
          this.isSearching = false; // 重置搜索状态
          // 重置loading状态
          if (isFirstLoad) {
            this.isLoading = false;
          }
        });
    },

    // 选择聊天
    selectChat(index) {
      // 如果已经选中了该聊天，不做任何操作
      if (this.currentChatIndex === index) {
        return;
      }

      this.currentChatIndex = index;
      const chat = this.chatHistory[index];
      // 重置消息列表
      this.messages = [];

      // 重置历史消息分页参数
      this.historyMessageParams = {
        skipCount: 0,
        maxResultCount: 10
      };
      this.hasMoreMessages = true;

      // 设置当前会话ID为选中的聊天会话ID
      if (chat.sessionId) {
        this.sessionStatus.sessionId = chat.sessionId;
        // 会话ID设置完成

        // 更新localStorage中保存的会话ID
        const routeKey = `${chat.clientId}_${chat.id}`;
        const sessionData = {
          routeKey: routeKey,
          sessionId: chat.sessionId,
          timestamp: new Date().getTime(),
        };
        localStorage.setItem("chatSessionData", JSON.stringify(sessionData));

        // 清空当前消息，准备加载历史消息
        this.messages = [];

        // 调用接口获取会话历史消息
        this.loadSessionMessages(chat.sessionId, chat);
      }

      // 更新应用信息标题和ID
      this.appInfo = {
        name: chat.appName,
        id: chat.id,
        profilePhoto: chat.profilePhoto || "", // 添加服务头像
        introduce: chat.introduce || "", // 添加介绍信息
        description: chat.description || "",
      };

      // 如果从聊天列表中选择了聊天，则使用列表中的clientId
      if (chat.clientId) {
        this.appId = chat.clientId;
      } else if (chat.id) {
        // 兼容处理：如果没有clientId，则尝试使用id
        this.appId = chat.id;
      }

      // 获取应用来源类型
      this.sourceType = chat.appSourceType || 1; // 默认使用类型1

      // 立即在本地标记该会话为已读，提升用户体验
      if (chat.hasUnreadMessages) {
        this.$set(this.chatHistory[index], 'hasUnreadMessages', false);


        // 更新全局未读状态
        this.updateGlobalUnreadStatus();
      }

      // 选择聊天时才创建连接
      this.checkLoginAndCreateConnection();
    },

    // 加载会话历史消息
    async loadSessionMessages(sessionId, chat) {
      if (!sessionId) {
        console.warn("会话ID为空，无法加载历史消息");
        return;
      }



      try {
        // 调用getSessionMessages接口获取会话历史消息
        const result = await api.chat.getSessionMessages(sessionId, {
          skipCount: 0,
          maxResultCount: 10 // 初次加载与loadMoreMessages保持一致的数量
        });

        // 初次加载后更新分页参数，为后续"加载更多"做准备
        this.historyMessageParams.skipCount = 10;

        if (result && result.code === 200 && result.data) {


          // 处理返回的消息数据
          if (result.data.items && Array.isArray(result.data.items) && result.data.items.length > 0) {
            // 调用现有的处理方法来显示历史消息
            this.handleReceiveSessionHistory(result.data.items);
          } else if (Array.isArray(result.data) && result.data.length > 0) {
            // 兼容直接返回数组的情况
            this.handleReceiveSessionHistory(result.data);
          } else {
            // 如果没有历史消息但有介绍信息，显示欢迎语
            if (chat && chat.introduce) {
              this.addMessage({
                role: "assistant",
                content: formatOutputContent(chat.introduce || "请问有什么可以帮助您？"),
                time: this.formatTime(new Date()),
                isWelcomeMessage: true, // 标记为欢迎消息
              });
            }
          }
        } else {
          console.warn("获取会话历史消息返回数据格式异常:", result);
          // 如果接口调用失败，显示欢迎消息作为备选
          if (chat && chat.introduce) {
            this.addMessage({
              role: "assistant",
              content: formatOutputContent(chat.introduce || "请问有什么可以帮助您？"),
              time: this.formatTime(new Date()),
              isWelcomeMessage: true,
            });
          }
        }
      } catch (error) {
        console.error("加载会话历史消息失败:", error);
        console.warn("加载历史消息失败，将使用SignalR获取: " + (error.message || "未知错误"));

        // 出错时显示欢迎消息作为备选
        if (chat && chat.introduce) {
          this.addMessage({
            role: "assistant",
            content: formatOutputContent(chat.introduce || "请问有什么可以帮助您？"),
            time: this.formatTime(new Date()),
            isWelcomeMessage: true,
          });
        }
      }
    },

    // 检查登录并创建连接
    async checkLoginAndCreateConnection() {
      try {
        // 获取用户信息
        const userStr = localStorage.getItem("user");
        if (!userStr) {
          this.$message.warning("用户未登录，部分功能可能受限");
          return;
        }

        const user = JSON.parse(userStr);
        this.sessionStatus.userName = user.name || "用户";

        // 创建连接
        await this.createConnection();
      } catch (error) {
        console.error("检查登录状态失败:", error);
        this.$message.warning("初始化聊天失败，请刷新页面重试");
      } finally {
        // 无论如何，都关闭加载状态
        this.isLoading = false;
      }
    },

    // 创建与服务器的SignalR连接
    async createConnection() {
      try {
        // 先清理旧的连接，确保每次切换会话都重新建立连接
        await this.cleanupConnection();

        // 使用SignalR专用的环境变量
        const signalRUrl = process.env.VUE_APP_SIGNALR_URL || 'http://localhost:5280';

        // 构建查询参数
        const queryParams = new URLSearchParams({
          sessionId: this.sessionStatus.sessionId || '',
          clientId: this.appId || '',
          sourceType: this.sourceType || 1
        });

        // 使用正确的SignalR端点路径
        const serverUrl = `${signalRUrl}/signalr-hubs/jwt?${queryParams.toString()}`;

        // 获取认证令牌
        const token = localStorage.getItem("token");

        if (!token) {
          this.$message.warning("用户未登录，无法建立聊天连接");
          return;
        }

        // 创建连接配置
        const connectionOptions = {
          accessTokenFactory: () => token,
          transport: signalR.HttpTransportType.WebSockets |
                    signalR.HttpTransportType.ServerSentEvents |
                    signalR.HttpTransportType.LongPolling,
          skipNegotiation: false,
          timeout: 30000,
          keepAliveIntervalInMilliseconds: 15000,
          serverTimeoutInMilliseconds: 30000
        };

        // 创建连接
        this.hubConnection = new signalR.HubConnectionBuilder()
          .withUrl(serverUrl, connectionOptions)
          .withAutomaticReconnect([0, 2000, 10000, 30000])
          .configureLogging(signalR.LogLevel.Warning)
          .build();

        // 设置连接事件处理器
        this.setupConnectionEvents();

        // 启动连接
        this.startConnection();
      } catch (error) {
        console.error("❌ 创建连接失败:", error);
        console.error("错误详情:", error.message);
        console.error("错误堆栈:", error.stack);

        this.$showFriendlyError(null, "连接服务器失败，请稍后重试");
      }
    },

    // 启动连接
    async startConnection() {
      if (!this.hubConnection) {
        return;
      }

      if (this.hubConnection.state === signalR.HubConnectionState.Connected) {
        this.sessionStatus.isConnected = true;
        return;
      }

      try {
        await this.hubConnection.start();
        this.sessionStatus.isConnected = true;

        // 连接成功后自动标记已读（延时确保连接稳定）
        setTimeout(() => {
          this.markSessionAsRead();
        }, 1000);
      } catch (error) {
        console.error("❌ SignalR连接启动失败:", error);
        console.error("错误详情:", error.message);
        console.error("错误堆栈:", error.stack);
        this.sessionStatus.isConnected = false;

        // 对404等特定错误不显示用户提示，避免过多干扰
        if (error.message && error.message.includes("404")) {
          return; // 不显示错误消息，不进行手动重试
        }

        // 只对非404错误显示提示信息
        let errorMessage = "连接聊天服务失败";
        if (error.message) {
          if (error.message.includes("401")) {
            errorMessage = "认证失败，请重新登录";
          } else if (error.message.includes("WebSocket")) {
            errorMessage = "网络连接问题，请检查网络";
          } else {
            errorMessage = "连接失败: " + error.message;
          }
        }

        // 只对严重错误显示消息提示
        if (!error.message || (!error.message.includes("404") && !error.message.includes("Not Found"))) {
          this.$showFriendlyError(error, errorMessage);
        }
      }
    },

    // 清理旧连接
    async cleanupConnection() {
      if (this.hubConnection) {
        try {
          // 先移除所有事件监听器
          this.hubConnection.off("ReceiveMessage");
          this.hubConnection.off("ReceiveSessionHistory");
          this.hubConnection.off("UserStatusChanged");
          this.hubConnection.off("SystemNotification");
          this.hubConnection.off("ReceiveSystemNotification");
          this.hubConnection.off("SessionCreated");
          this.hubConnection.off("SessionUpdated");
          this.hubConnection.off("SessionListChanged");

          // 如果连接处于活跃状态，先断开连接
          if (this.hubConnection.state === signalR.HubConnectionState.Connected ||
              this.hubConnection.state === signalR.HubConnectionState.Connecting) {
            await this.hubConnection.stop();
          }
        } catch (error) {
          console.warn("清理旧连接出错:", error);
        } finally {
        this.hubConnection = null;
          this.sessionStatus.isConnected = false;
        }
        }
    },

    // 设置连接事件处理器
    setupConnectionEvents() {
      if (!this.hubConnection) return;

      // 连接关闭事件
      this.hubConnection.onclose((error) => {
        this.sessionStatus.isConnected = false;
        if (error) {
          console.error("连接意外关闭:", error.message);
          // 对404错误特殊处理，不显示错误消息
          if (!error.message.includes("404") && !error.message.includes("Not Found")) {
            this.$showFriendlyError(error, "连接已断开，请重试");
          }
        }
      });

      // 重连中事件
      this.hubConnection.onreconnecting((error) => {
        this.sessionStatus.isConnected = false;
        if (error && !error.message.includes("404")) {
          this.$message.warning("连接断开，正在尝试重新连接...");
        }
      });

      // 重连成功事件
      this.hubConnection.onreconnected(() => {
        this.$message.success("已重新连接到服务器");
        this.sessionStatus.isConnected = true;

        // 重连成功后也自动标记已读
        setTimeout(() => {
          this.markSessionAsRead();
        }, 1000);
      });

      // 注册接收消息事件
      this.hubConnection.on("ReceiveMessage", (message) => {
        this.handleReceiveMessage(message);
      });

      // 注册接收会话历史消息事件
      this.hubConnection.on("ReceiveSessionHistory", (messages) => {
        this.handleReceiveSessionHistory(messages);
      });

      // 注册系统通知事件（静默处理）
      this.hubConnection.on("SystemNotification", () => {
        // 系统通知静默处理，不打印到控制台
      });

      // 统一的系统通知处理（静默处理）
      this.hubConnection.on("ReceiveSystemNotification", () => {
        // 系统通知静默处理，不打印到控制台
      });

      // 用户状态变更事件（静默处理）
      this.hubConnection.on("UserStatusChanged", () => {
        // 用户状态变更静默处理，不打印到控制台
      });

      // 监听新会话创建事件
      this.hubConnection.on("SessionCreated", (sessionData) => {
        this.handleNewSessionCreated(sessionData);
      });

      // 监听会话更新事件
      this.hubConnection.on("SessionUpdated", (sessionData) => {
        this.handleSessionUpdated(sessionData);
      });

      // 监听会话列表变更事件
      this.hubConnection.on("SessionListChanged", () => {
        this.refreshSessionList();
      });
    },

    // 重连SignalR
    async reconnectToSignalR() {
      await this.stopConnection();
      setTimeout(() => {
        this.createConnection();
      }, 1000);
    },

    // 加入会话
    async joinSession() {
      if (!this.sessionStatus.sessionId || !this.appId || !this.hubConnection ||
          this.hubConnection.state !== signalR.HubConnectionState.Connected) {
        return;
      }

      try {
        const clientId = `${this.appId}`;
        const sourceType = this.sourceType || 1;

        // 使用SignalR加入会话
        await this.hubConnection.invoke(
          "JoinSession",
          this.sessionStatus.sessionId,
          clientId,
          sourceType,
          this.sessionStatus.sessionId // customSenderId
        );

      } catch (error) {
        console.error("加入会话失败:", error);
        // 对于404错误，不显示用户提示
        if (!error.message || !error.message.includes("404")) {
          this.$showFriendlyError(error, "加入会话失败，请重试");
        }
      }
    },

    // 标记会话为已读
    async markSessionAsRead() {
      if (!this.hubConnection ||
          this.hubConnection.state !== signalR.HubConnectionState.Connected) {
        return;
      }

      if (!this.sessionStatus.sessionId) {
        return;
      }

      try {
        // 根据模板，只传递sessionId参数，第二个参数为null
        await this.hubConnection.invoke("MarkSessionAsRead",
          this.sessionStatus.sessionId,
          null
        );

        // 本地立即更新未读状态，提升用户体验
        this.updateLocalUnreadStatus();
      } catch (error) {
        console.error("标记已读失败:", error);
        // 只对非404错误显示提示
        if (!error.message || !error.message.includes("404")) {
          console.warn("标记已读失败:", error.message);
        }
      }
    },

    // 更新本地未读状态
    updateLocalUnreadStatus() {
      // 如果当前选中的会话在列表中，立即更新其未读状态
      if (this.currentChatIndex >= 0 && this.chatHistory[this.currentChatIndex]) {
        // 使用Vue.set确保响应式更新
        this.$set(this.chatHistory[this.currentChatIndex], 'hasUnreadMessages', false);

        // 检查并更新全局未读状态
        this.updateGlobalUnreadStatus();
      }
    },

    // 更新全局未读状态
    updateGlobalUnreadStatus() {
      // 检查是否还有其他未读会话
      const hasUnread = this.chatHistory.some(chat => chat.hasUnreadMessages);

      // 如果状态发生变化，更新全局未读状态
      if (this.hasUnreadSessions !== hasUnread) {
        this.hasUnreadSessions = hasUnread;

      }
    },

    // 将当前会话标记为已读
    markCurrentSessionAsRead() {
      if (this.currentChatIndex >= 0 && this.currentChatIndex < this.chatHistory.length) {
        const currentSession = this.chatHistory[this.currentChatIndex];

        if (currentSession.hasUnreadMessages) {


          // 更新当前会话的未读状态
          this.$set(this.chatHistory[this.currentChatIndex], 'hasUnreadMessages', false);

          // 更新全局未读状态
          this.updateGlobalUnreadStatus();


        }
      }
    },

    // 添加状态消息
    addStatusMessage(message) {
      this.addMessage({
        role: "system",
        content: formatOutputContent(message),
        time: this.formatTime(new Date()),
        isStatus: true,
      });
    },

    // 处理接收到的会话历史消息
    handleReceiveSessionHistory(messages) {
      if (!messages || messages.length === 0) {
        return;
      }

      // 按时间戳排序确保消息顺序正确
      const sortedMessages = messages.sort((a, b) => {
        return new Date(a.timestamp || a.creationTime) - new Date(b.timestamp || b.creationTime);
      });

      // 保存当前的欢迎消息（如果有）
      const welcomeMessages = this.messages.filter(msg => msg.isWelcomeMessage);

      // 清空当前消息列表，优先显示服务器历史消息
      this.messages = [];

      // 创建一个Set来存储已处理的消息ID，避免重复
      const processedMessageIds = new Set();

      sortedMessages.forEach((msg) => {
        // 检查消息是否已处理过（基于messageId或内容组合）
        const messageId = msg.messageId || msg.id;
        if (messageId && processedMessageIds.has(messageId)) {
          console.log("跳过重复的历史消息:", messageId);
          return;
        }

        // 如果没有messageId，使用内容组合作为唯一标识
        if (!messageId) {
          const contentKey = `${msg.textContent || msg.content}_${msg.timestamp || msg.creationTime}_${msg.senderId}_${msg.senderType}`;
          if (processedMessageIds.has(contentKey)) {
            console.log("跳过重复的历史消息（基于内容）:", contentKey);
            return;
          }
          processedMessageIds.add(contentKey);
        } else {
          processedMessageIds.add(messageId);
        }
        // 根据senderType标准枚举判断消息显示位置
        // 1 = user（用户）→ 显示在左边
        // 2 = human（人工客服）→ 显示在右边
        // 3 = assistant（AI助手）→ 显示在右边
        let messageType = 'assistant'; // 默认为助手消息（右边）

        if (msg.senderType === 1) {
          messageType = 'user'; // 用户消息显示在左边
        } else if (msg.senderType === 2) {
          messageType = 'human'; // 人工客服消息显示在右边，使用human样式
        } else if (msg.senderType === 3) {
          messageType = 'assistant'; // AI助手消息显示在右边
        } else {
          // 其他情况默认显示在右边
          messageType = 'assistant';
        }

        // 转换为Vue组件的消息格式
        const formattedMessage = {
          role: messageType,
          content: formatOutputContent(msg.textContent || msg.content || ''),
          time: this.formatTime(new Date(msg.timestamp || msg.creationTime)),
          messageId: msg.messageId || msg.id,
          senderId: msg.senderId,
          senderName: msg.senderName || (messageType === 'user' ? '用户' : '助手'),
          isRead: msg.isRead || false,
          isHistoryMessage: true, // 标记为历史消息
          senderType: msg.senderType, // 保存原始的senderType字段
        };

        this.messages.push(formattedMessage);
      });

      // 如果没有历史消息但有欢迎消息，则保留欢迎消息
      if (this.messages.length === 0 && welcomeMessages.length > 0) {
        this.messages = welcomeMessages;
      }

      // 保存到本地存储
      this.saveChatMessages();

      // 滚动到底部显示最新消息
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 获取当前用户ID（用于判断消息方向）
    getCurrentUserId() {
      try {
        const userStr = localStorage.getItem("user");
        if (userStr) {
          const user = JSON.parse(userStr);
          return user.id || user.userId || user.sub;
        }

        // 如果没有用户信息，尝试从token中解析
        const token = localStorage.getItem("token");
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.sub || payload.userId || payload.id || payload.nameid || payload.unique_name;
        }
      } catch (error) {
        console.error("获取用户ID失败:", error);
      }

      // 如果都失败了，使用连接ID作为fallback
      return this.hubConnection ? this.hubConnection.connectionId : 'unknown';
    },

    // 处理接收到的消息
    handleReceiveMessage(message) {
      this.isTyping = false;

      // 记录消息接收时间，用于检查消息先后顺序
      // 收到实时消息

      // 防止重复处理相同消息
      if (message.messageId) {
        // 使用sessionStorage暂存已处理过的消息ID，避免刷新页面后丢失
        const processedMessages =
          sessionStorage.getItem("processedMessageIds") || "[]";
        let messageIds = [];
        try {
          messageIds = JSON.parse(processedMessages);

          // 检查是否已处理过这条消息
          if (messageIds.includes(message.messageId)) {
            console.log("消息已处理过，跳过重复消息:", message.messageId);
            return;
          }

          // 添加到已处理消息列表
          messageIds.push(message.messageId);
          // 保留最近100条消息ID，避免列表无限增长
          if (messageIds.length > 100) {
            messageIds = messageIds.slice(-100);
          }
          sessionStorage.setItem(
            "processedMessageIds",
            JSON.stringify(messageIds)
          );
        } catch (error) {
          console.error("处理消息ID缓存出错:", error);
        }
      } else {
        // 如果没有messageId，使用内容+时间戳作为唯一标识
        const messageKey = `${message.textContent || message.content}_${message.timestamp || Date.now()}_${message.senderId}`;
        const processedKeys = sessionStorage.getItem("processedMessageKeys") || "[]";

        try {
          let keyList = JSON.parse(processedKeys);

          if (keyList.includes(messageKey)) {
            console.log("检测到重复消息（无ID），跳过:", messageKey);
            return;
          }

          keyList.push(messageKey);
          if (keyList.length > 50) {
            keyList = keyList.slice(-50);
          }
          sessionStorage.setItem("processedMessageKeys", JSON.stringify(keyList));
        } catch (error) {
          console.error("处理消息key缓存出错:", error);
        }
      }

      // 判断消息是否为自己发送的
      let isSelf = false;

      // 方法1：通过senderType判断（人工客服的消息类型为2）
      if (message.senderType === 2) {
        isSelf = true;
      }

      // 方法2：通过senderId判断（如果senderId包含当前用户标识）
      const currentUserId = this.getCurrentUserId();
      if (message.senderId && currentUserId &&
          (message.senderId === currentUserId ||
           message.senderId.toString().includes(currentUserId) ||
           message.senderId === `human${this.sessionStatus.sessionId}`)) {
        isSelf = true;
      }

      // 方法3：通过senderName判断（如果是人工客服发送的）
      if (message.senderName &&
          (message.senderName === this.sessionStatus.userName ||
           message.senderName === '人工客服' ||
           message.senderName.includes('客服'))) {
        isSelf = true;
      }

      if (isSelf) {
        console.log("过滤自己发送的消息:", {
          senderType: message.senderType,
          senderId: message.senderId,
          senderName: message.senderName,
          currentUserId: currentUserId
        });
        return; // 如果是自己发送的消息，直接返回不处理
      }

      // 优先检查并提取消息内容，参考模板的灵活处理方式
      let content = "";

      // 1. 优先使用 textContent 字段（新消息常用）
      if (message.textContent && typeof message.textContent === "string") {
        content = message.textContent;
      }
      // 2. 其次检查 content 字段
      else if (message.content) {
        if (typeof message.content === "string") {
          content = message.content;
        } else if (typeof message.content === "object") {
          // 如果是对象，尝试提取 text 字段
          if (message.content.text) {
            content = message.content.text;
          } else if (message.content["text"]) {
            content = message.content["text"];
          } else {
            // 尝试JSON字符串中的文本匹配
            const contentStr = JSON.stringify(message.content);
            const textMatch = contentStr.match(/"text":"(.+?)"/);
            if (textMatch && textMatch[1]) {
              content = textMatch[1];
            } else {
              content = contentStr;
            }
          }
        }
      }

              // 如果成功提取到文本内容，就显示消息
        if (content && typeof content === "string" && content.trim()) {
          // 根据senderName和senderType判断消息显示位置
          let messageRole = "assistant"; // 默认为助手消息（右边）

          if (message.senderName && message.senderName === '匿名客户') {
            messageRole = "user"; // 匿名客户的消息显示在左边
          } else if (message.senderType === 2) {
            messageRole = "human"; // 人工客服消息显示在右边
          } else {
            messageRole = "assistant"; // AI助手消息显示在右边
          }

        // 使用formatOutputContent格式化输出内容
        const formattedContent = formatOutputContent(content);

        this.addMessage({
          role: messageRole,
          content: formattedContent,
          time: this.formatTime(new Date()),
          senderId: message.senderId,
          senderName: message.senderName,
          messageId: message.messageId,
        });
      } else {
        // 如果无法提取文本内容，检查消息类型并显示相应提示
        let typeName = "未知";

        // 检查 type 或 messageType 字段
        const messageType = message.type || message.messageType;

        switch (messageType) {
          case 0:
          case "Unknown":
            typeName = "未知";
            break;
          case 1:
          case "Text":
            typeName = "文本";
            break;
          case 2:
          case "Image":
            typeName = "图片";
            break;
          case 3:
          case "Audio":
            typeName = "语音";
            break;
          case 4:
          case "Video":
            typeName = "视频";
            break;
          case 5:
          case "File":
            typeName = "文件";
            break;
          default:
            typeName = messageType ? `${messageType}类型` : "未知类型";
        }

        // 根据senderName和senderType判断消息显示位置
        let messageRole = "assistant"; // 默认为助手消息（右边）

        if (message.senderName && message.senderName === '匿名客户') {
          messageRole = "user"; // 匿名客户的消息显示在左边
        } else if (message.senderType === 2) {
          messageRole = "human"; // 人工客服消息显示在右边
        } else {
          messageRole = "assistant"; // AI助手消息显示在右边
        }

        this.addMessage({
          role: messageRole,
          content: formatOutputContent(`[${typeName}消息]`),
          time: this.formatTime(new Date()),
          senderId: message.senderId,
          senderName: message.senderName,
          messageId: message.messageId,
        });
      }

      // 接收到消息后滚动到底部并关闭输入中状态
      this.isTyping = false;
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 保存聊天消息到本地存储
    saveChatMessages() {
      if (!this.sessionStatus.sessionId) return;

      try {
        const storageKey =
          this.chatMessagesStoragePrefix + this.sessionStatus.sessionId;
        // 限制存储的消息数量，避免超出存储限制
        const messagesToSave = this.messages.slice(-200);
        localStorage.setItem(storageKey, JSON.stringify(messagesToSave));

      } catch (error) {
        console.error("保存聊天消息失败:", error);
      }
    },

    // 从本地存储加载聊天消息
    loadChatMessages(sessionId) {
      if (!sessionId) {
        sessionId = this.sessionStatus.sessionId;
      }
      if (!sessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + sessionId;
        const savedMessagesJson = localStorage.getItem(storageKey);

        if (savedMessagesJson) {
          const savedMessages = JSON.parse(savedMessagesJson);
          if (Array.isArray(savedMessages) && savedMessages.length > 0) {
            // 对历史消息内容进行格式化
            this.messages = savedMessages.map(msg => {
              return {
                ...msg,
                content: formatOutputContent(msg.content)
              };
            });


            // 加载后滚动到底部
            this.$nextTick(() => {
              this.scrollToBottom();
            });

            return true;
          }
        }

        return false;
      } catch (error) {
        console.error("加载聊天消息失败:", error);
        return false;
      }
    },

    // 清除指定会话的聊天消息缓存
    clearChatMessages(sessionId) {
      if (!sessionId) {
        sessionId = this.sessionStatus.sessionId;
      }
      if (!sessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + sessionId;
        localStorage.removeItem(storageKey);

      } catch (error) {
        console.error("清除聊天消息失败:", error);
      }
    },

    fetchAppInfo() {
      this.isLoading = true;
      try {
        if (!this.appId) {
          this.$showFriendlyError(null, "应用ID不存在");
          this.rightPanelReady = true;
          return;
        }

        // 查找左侧栏中的应用信息
        let appInfo = null;

        // 从聊天历史记录中查找匹配的应用
        for (let i = 0; i < this.chatHistory.length; i++) {
          const chat = this.chatHistory[i];
          if (chat.clientId === this.appId || chat.id === this.appId) {
            appInfo = {
              id: chat.id,
              name: chat.appName,
              description: chat.description || "智能助手为您服务",
              profilePhoto: chat.profilePhoto || "",
              introduce: chat.introduce || "",
            };
            break;
          }
        }

        // 如果在历史记录中找不到，则使用默认值
        if (!appInfo) {
          this.appInfo = {
            id: this.appId,
            name: "智能客服助手",
            description: "我是一个AI智能助手，可以帮您解答各种问题。",
            profilePhoto: "",
            introduce: "",
          };
        } else {
          this.appInfo = appInfo;
        }

        // 强制更新DOM
        this.$nextTick(() => {
          // 触发视图更新
          this.forceUpdate();
          // 标记右侧面板已准备好
          this.rightPanelReady = true;
        });

        // 设置有结果状态
        this.hasResult = true;
        this.isLoading = false;
      } catch (error) {
        console.error("获取应用信息失败:", error);
        this.$showFriendlyError(error, "获取应用信息失败");
        // 即使失败，也标记右侧面板准备完成
        this.rightPanelReady = true;
      } finally {
        this.isLoading = false;
      }
    },

    addMessage(message) {
      this.messages.push(message);

      // 保存消息到本地存储
      this.saveChatMessages();

      // 如果有选中的聊天，则更新该聊天的最后消息和时间
      if (this.currentChatIndex >= 0) {
        const chat = this.chatHistory[this.currentChatIndex];
        if (chat) {
          // 将消息添加到该聊天的消息列表中
          if (!chat.messages) {
            chat.messages = [];
          }
          chat.messages.push(message);

          // 更新最后一条消息
          if (message.content) {
            let messageContent = message.content.replace(/<[^>]*>/g, "");
            // 限制消息长度显示（与其他地方保持一致）
            if (messageContent.length > 30) {
              messageContent = messageContent.substring(0, 30) + "...";
            }
            chat.lastMessage = messageContent;
          }
          // 使用完整的时间格式，与其他地方保持一致
          chat.time = message.time;

          // 更新聊天历史
          this.$set(this.chatHistory, this.currentChatIndex, chat);
        }
      }
    },

    formatTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping) return;

      // 确保发送消息时滚动到底部
      this.shouldScrollToBottom = true;

      const message = this.inputMessage.trim();
      const sessionId = this.sessionStatus.sessionId;

      // 添加用户消息
      this.addMessage({
        role: "human",
        content: formatOutputContent(message),
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(), // 为人工消息生成唯一ID
        senderId: "human" + this.sessionStatus.sessionId, // 人工客服消息
      });

      // 清空输入框
      this.inputMessage = "";

      // 将当前会话标记为已读（用户发送消息表示已经查看了会话）
      this.markCurrentSessionAsRead();

      // 显示发送中状态
      this.isTyping = true;

      if (!sessionId) {
        this.$showFriendlyError(null, "用户未登录，无法发送消息");
        this.isTyping = false;
        return;
      }

      // 不再调用工作流执行接口，直接发送SignalR消息
      if (this.sessionStatus.isConnected && this.hubConnection) {
        try {
          // 准备发送消息所需参数
          const userName = this.sessionStatus.userName || "人工客服";
          const senderType = 2; // 人工客服

          // 构造统一消息DTO对象
          const messageDto = {
            sessionId: sessionId,
            contentType: 1, // EnumMessageContentType.Text = 1
            content: message,
            senderName: userName,
            customSenderId: null, // JWT认证用户不需要自定义ID
            senderType: senderType, // 人工客服类型
            // 文本消息不需要设置metadata
          };

          // 使用SendMessage方法发送消息DTO对象
          await this.hubConnection.invoke("SendMessage", messageDto);

          // // 直接添加模拟的系统回复消息
          // this.addMessage({
          //   role: "assistant",
          //   content: `我已收到您的消息: "${message}"`,
          //   time: this.formatTime(new Date()),
          //   messageId: this.generateGuid(), // 为系统回复生成唯一ID
          // });
        } catch (error) {
          console.error("发送SignalR消息失败:", error);

          // 添加错误提示消息
          this.addMessage({
            role: "assistant",
            content: formatOutputContent(`抱歉，发送消息失败: ${error.message || "未知错误"}`),
            time: this.formatTime(new Date()),
            messageId: this.generateGuid(),
          });
        } finally {
          // 关闭输入中状态
          this.isTyping = false;
          // 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } else {
        // 如果连接不可用，尝试重连
        this.isTyping = false;
        this.$message.warning("当前处于离线状态，正在尝试重连...");

        // 尝试重连
        this.reconnectToSignalR();

        // 添加系统提示消息
        this.addMessage({
          role: "assistant",
          content: formatOutputContent("连接已断开，正在尝试重新连接..."),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });

        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    refreshChat() {
      // 修改刷新逻辑，保留历史消息
      if (this.messages.length > 0 && confirm("确定要清除当前对话记录吗？")) {
        // 清除当前会话的消息缓存
        this.clearChatMessages();
        this.messages = [];
        // 重新加载应用信息，显示欢迎消息
        this.fetchAppInfo();
      } else if (this.messages.length === 0) {
        // 如果没有消息，直接重新加载应用信息
        this.fetchAppInfo();
      }
    },
    closeChat() {
      // 关闭连接
      this.stopConnection();
      if (window) {
        window.close();
      }
    },
    // 停止连接
    async stopConnection() {
      if (!this.hubConnection) return;

      try {
        if (this.hubConnection.state === signalR.HubConnectionState.Connected) {
          await this.hubConnection.stop();
        }

      } catch (error) {
        console.error("停止连接时出错:", error);
      } finally {
        this.sessionStatus.isConnected = false;
        this.cleanupConnection();
      }
    },

    // 停止全局通知连接
    async stopGlobalNotificationConnection() {
      if (!this.globalNotificationConnection) return;

      try {
        if (this.globalNotificationConnection.state === signalR.HubConnectionState.Connected) {
          await this.globalNotificationConnection.stop();
        }

      } catch (error) {
        console.error("停止全局通知连接时出错:", error);
      } finally {
        this.globalNotificationConnection = null;
      }
    },

    // 建立全局通知连接（只接收实时通知，不加入具体会话）
    async establishGlobalNotificationConnection() {
      try {
        // 如果已有全局通知连接，先断开
        if (this.globalNotificationConnection) {
          await this.globalNotificationConnection.stop();
          this.globalNotificationConnection = null;
        }

        // 获取认证令牌
        const token = localStorage.getItem("token");
        if (!token) {
          return;
        }

        // 使用SignalR专用的环境变量
        const signalRUrl = process.env.VUE_APP_SIGNALR_URL || 'http://localhost:5280';

        // 创建专门用于全局通知的SignalR连接（不传sessionId）
        const queryParams = new URLSearchParams({
          clientId: 'jwt-global-notification',
          sourceType: '1' // 应用来源
        });

        const connectionUrl = `${signalRUrl}/signalr-hubs/jwt?${queryParams.toString()}`;

        this.globalNotificationConnection = new signalR.HubConnectionBuilder()
          .withUrl(connectionUrl, {
            accessTokenFactory: () => token
          })
          .withAutomaticReconnect()
          .configureLogging(signalR.LogLevel.Information)
          .build();

        // 设置全局通知事件处理器
        this.setupGlobalNotificationHandlers();

        // 开始连接
        await this.globalNotificationConnection.start();

        // 连接成功后立即发送一个心跳消息来保持连接活跃
        try {
          await this.globalNotificationConnection.invoke('Ping');
        } catch (error) {
          console.warn("心跳消息发送失败，但连接可能仍然有效:", error.message);
        }

      } catch (error) {
        console.error("建立全局通知连接失败:", error);
      }
    },

    // 设置全局通知事件处理器
    setupGlobalNotificationHandlers() {
      if (!this.globalNotificationConnection) return;

      // 新会话通知
      this.globalNotificationConnection.on('NewChatSession', (sessionDto) => {
        // 直接插入新会话到列表顶部，而不是重新加载整个列表
        this.insertNewSessionToList(sessionDto);
      });

      // 未读数更新通知 - 使用统一的防抖方法
      this.globalNotificationConnection.on('UpdateUnreadCount', (data) => {
        console.log('📊 UpdateUnreadCount 收到数据:', data);
        this.debouncedUpdateUnreadCount(data);
      });

      // 会话在线状态变更
      this.globalNotificationConnection.on('SessionOnlineStatusChanged', (data) => {
        // 更新会话列表中的在线状态
        this.updateSessionListOnlineStatus(data.sessionId, data.isOnline);
      });

      // 连接建立成功事件
      this.globalNotificationConnection.on('OnConnectionEstablished', () => {
        console.log("全局通知连接已建立");
      });

      // 添加其他可能的服务器调用的方法
      this.globalNotificationConnection.on('OnError', (error) => {
        console.error("全局通知连接OnError:", error);
      });

      // 添加连接验证成功的处理
      this.globalNotificationConnection.on('OnConnected', () => {
        console.log("全局通知连接验证成功");
      });


      // 连接状态事件
      this.globalNotificationConnection.onreconnecting((error) => {
        console.warn("全局通知连接重连中:", error);
      });

      this.globalNotificationConnection.onreconnected(() => {
        console.log("全局通知连接已恢复");
      });

      this.globalNotificationConnection.onclose((error) => {
        if (error) {
          console.error("全局通知连接关闭错误:", error);

          // 如果是意外关闭，尝试重新连接
          if (error.message && !error.message.includes('Connection stopped')) {
            setTimeout(() => {
              this.establishGlobalNotificationConnection();
            }, 5000);
          }
        }
      });
    },

    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$nextTick(() => {
          this.$refs.messagesContainer.scrollTop =
            this.$refs.messagesContainer.scrollHeight;
        });
      }
    },

    // 加载更多历史消息
    loadMoreMessages() {
      if (!this.sessionStatus.sessionId || this.isLoadingMore || !this.hasMoreMessages) {
        return;
      }

      // 设置标志，加载历史消息时不自动滚动到底部
      this.shouldScrollToBottom = false;
      this.isLoadingMore = true;


      // 使用API获取历史消息
      api.mainRequest
        .getMoreSessionHistory(
          this.sessionStatus.sessionId,
          this.historyMessageParams.skipCount,
          this.historyMessageParams.maxResultCount
        )
        .then((result) => {
          if (
            result &&
            result.code === 200 &&
            result.data &&
            result.data.items &&
            Array.isArray(result.data.items)
          ) {
            const historyMessages = result.data.items;


            // 如果没有返回数据，说明没有更多历史消息
            if (historyMessages.length === 0) {
              // 标记没有更多消息，防止重复加载
              this.hasMoreMessages = false;

              // 只在第一次发现没有更多消息时显示提示
              this.$message.info("没有更多历史消息了");

              // 查询没有更多记录时，保持不滚动状态
              return;
            }

            // 将历史消息添加到消息列表前面
            const newMessages = [];
            historyMessages.forEach((msg) => {
              // 根据senderName和senderType判断消息显示位置
              let messageType = 'assistant'; // 默认为助手消息（右边）

              // 1. 优先根据senderName判断
              if (msg.senderName && msg.senderName === '匿名客户') {
                messageType = 'user'; // 匿名客户的消息显示在左边
              }
              // 2. 根据senderType判断：senderType为1且senderName为"匿名客户"的是用户消息
              else if (msg.senderType === 1 && msg.senderName === '匿名客户') {
                messageType = 'user';
              }
              // 3. 根据senderType判断：senderType为2的是人工客服消息，显示在右边
              else if (msg.senderType === 2) {
                messageType = 'human'; // 人工客服消息
              }
              // 4. 其他情况（如AI客服）默认显示在右边
              else {
                messageType = 'assistant';
              }

              newMessages.push({
                role: messageType,
                content: formatOutputContent(msg.textContent || msg.content || ''),
                time: this.formatHistoryTime(msg.timestamp),
                messageId: msg.messageId || msg.id,
                senderId: msg.senderId,
                senderName: msg.senderName || (messageType === 'user' ? '用户' : '助手'),
                isHistory: true,
                senderType: msg.senderType,
              });
            });

            // 更新分页参数，为下次加载做准备
            this.historyMessageParams.skipCount += historyMessages.length;

            // 按时间排序（如果需要）
            newMessages.sort((a, b) => new Date(a.time) - new Date(b.time));

            // 检查并去重，避免重复显示相同的消息
            const existingMessageIds = new Set(this.messages.map(msg => msg.messageId).filter(id => id));
            const uniqueNewMessages = newMessages.filter(msg => {
              // 如果消息有messageId，检查是否已存在
              if (msg.messageId) {
                return !existingMessageIds.has(msg.messageId);
              }
              // 如果没有messageId，通过内容和时间戳判断是否重复
              const messageKey = `${msg.content}_${msg.time}_${msg.senderId}_${msg.senderType}`;
              const isDuplicate = this.messages.some(existingMsg => {
                const existingKey = `${existingMsg.content}_${existingMsg.time}_${existingMsg.senderId}_${existingMsg.senderType}`;
                return existingKey === messageKey;
              });
              return !isDuplicate;
            });

            // 只添加不重复的消息到列表前面
            if (uniqueNewMessages.length > 0) {
              this.messages = [...uniqueNewMessages, ...this.messages];
              console.log(`成功加载 ${uniqueNewMessages.length} 条新的历史消息，过滤掉 ${newMessages.length - uniqueNewMessages.length} 条重复消息`);
            } else {
              console.log("没有新的历史消息，可能都是重复的");
              this.hasMoreMessages = false;
            }

            // 保存更新后的消息到本地存储
            this.saveChatMessages();

            // 不再自动滚动到底部
          } else {
            console.error("获取历史消息返回数据格式错误:", result);
            this.$message.warning("获取历史消息失败");
            this.hasMoreMessages = false;
          }
        })
        .catch((error) => {
          console.error("获取历史消息出错:", error);
          this.$showFriendlyError(error, "获取历史消息失败，请重试");
          this.hasMoreMessages = false;

          // 不恢复自动滚动
          // this.shouldScrollToBottom = true; // 删除这行，不恢复自动滚动
        })
        .finally(() => {
          this.isLoadingMore = false;
        });
    },

    // 格式化历史消息的时间
    formatHistoryTime(dateStr) {
      try {
        if (!dateStr) return "未知时间";

        const date = new Date(dateStr);
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn("无效的日期格式:", dateStr);
          return dateStr;
        }

        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        const seconds = date.getSeconds().toString().padStart(2, "0");
        return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error("格式化历史消息时间出错:", error, "原始时间:", dateStr);
        return dateStr || "未知时间";
      }
    },

    // 处理搜索
    handleSearch() {
      if (this.isSearching) {
        return;
      }

      // 如果搜索框为空，则显示所有数据
      if (!this.searchKeyword || !this.searchKeyword.trim()) {
        this.handleSearchClear();
        return;
      }


      this.isSearching = true;
      this.isRefreshingList = true; // 设置刷新状态，显示loading效果

      // 重置分页参数，重新搜索
      this.skipCount = 0;
      this.hasMore = true;
      this.chatHistory = [];

      // 调用获取会话列表，API中会自动使用searchKeyword参数
      this._fetchChatSessionsActual();
    },

    // 清除搜索
    handleSearchClear() {

      this.searchKeyword = "";
      this.isSearching = false;
      this.isRefreshingList = true; // 设置刷新状态，显示loading效果

      // 重置分页参数，重新加载所有数据
      this.skipCount = 0;
      this.hasMore = true;
      this.chatHistory = [];

      // 重新获取所有会话列表
      this._fetchChatSessionsActual();
    },

    // 处理导出命令
    handleExportCommand(command) {
      switch (command) {
        case 'exportAll':
          this.handleExportAllSessions();
          break;
        case 'exportSelected':
          this.handleExportSelectedSessions();
          break;
        default:
          console.warn('未知的导出命令:', command);
      }
    },

    // 导出全部会话
    handleExportAllSessions() {
      console.log('导出全部会话');
      this.exportType = 'all';
      this.exportDateRange = [];
      this.exportDialogVisible = true;
    },

    // 导出指定会话
    handleExportSelectedSessions() {
      console.log('导出指定会话');
      // 进入选择模式
      this.isSelectMode = true;
      this.selectedSessions = [];
    },

    // 处理会话项点击
    handleChatItemClick(chat, index) {
      if (this.isSelectMode) {
        // 选择模式下切换选中状态
        this.toggleSessionSelection(chat.sessionId);
      } else {
        // 正常模式下选择会话
        this.selectChat(index);
      }
    },

    // 切换会话选中状态
    toggleSessionSelection(sessionId) {
      const index = this.selectedSessions.indexOf(sessionId);
      if (index > -1) {
        this.selectedSessions.splice(index, 1);
      } else {
        this.selectedSessions.push(sessionId);
      }
    },

    // 全选会话
    selectAllSessions() {
      this.selectedSessions = this.filteredChats.map(chat => chat.sessionId);
    },

    // 清空选择
    clearSelection() {
      this.selectedSessions = [];
    },

    // 确认导出选中的会话
    confirmExportSelected() {
      if (this.selectedSessions.length === 0) {
        this.$message.warning('请至少选择一个会话');
        return;
      }
      console.log('确认导出选中的会话:', this.selectedSessions);
      this.exportType = 'selected';
      this.exportDateRange = [];
      this.exportDialogVisible = true;
    },

    // 退出选择模式
    exitSelectMode() {
      this.isSelectMode = false;
      this.selectedSessions = [];
    },

    // 确认导出
    async confirmExport() {
      if (!this.exportDateRange || this.exportDateRange.length !== 2) {
        this.$message.warning('请选择导出的时间范围');
        return;
      }

      const [startDate, endDate] = this.exportDateRange;
      const diffTime = Math.abs(new Date(endDate).getTime() - new Date(startDate).getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays > 30) {
        this.$message.warning('时间跨度不能超过30天');
        return;
      }

      // 构建导出请求参数
      const exportData = {
        startTime: startDate,
        endTime: endDate
      };

      // 如果是指定会话导出，添加sessionIds
      if (this.exportType === 'selected') {
        if (!this.selectedSessions || this.selectedSessions.length === 0) {
          this.$message.warning('请至少选择一个会话');
          return;
        }
        exportData.sessionIds = this.selectedSessions;
      }

            // 显示轻量级加载提示
      const message = this.$message({
        message: '正在导出会话记录，请稍候...',
        type: 'info',
        duration: 0, // 不自动关闭
        showClose: false
      });

            try {
        // 调用导出接口
        const response = await api.chat.exportRecords(exportData);

        // 关闭加载提示
        message.close();

        // 检查响应是否是blob类型（文件流）
        if (response instanceof Blob) {
          // 检查是否为Excel文件类型
          const isExcelFile = response.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                             response.type === 'application/vnd.ms-excel' ||
                             response.type === 'application/ms-excel' ||
                             response.type === 'application/octet-stream';

                              // 如果是Excel文件或文件较大，直接当作正常文件处理
          if (isExcelFile || response.size > 1024) {
            try {
              // 创建下载链接
              const url = window.URL.createObjectURL(response);
              const link = document.createElement('a');
              link.href = url;
              link.download = `会话记录_${startDate}_${endDate}.xlsx`;
              link.style.display = 'none';

              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);

              this.$message.success('会话记录导出成功');

              // 成功后的清理工作
              if (this.exportType === 'selected') {
                this.exitSelectMode();
              }
              this.exportDialogVisible = false;

            } catch (downloadError) {
              this.$showFriendlyError(downloadError, '文件下载失败，请重试');
              this.exportDialogVisible = false;
              throw downloadError;
            }
          } else {
            // 只有当文件很小且不是Excel类型时，才检查是否为错误响应
            try {
              const text = await response.text();
              const errorData = JSON.parse(text);
              this.$showFriendlyError({ message: errorData.message }, '导出失败，请重试');
              return;
            } catch (e) {
              // 解析失败，当作正常文件处理
              this.$message.success('会话记录导出成功');
              const url = window.URL.createObjectURL(response);
              const link = document.createElement('a');
              link.href = url;
              link.download = `会话记录_${startDate}_${endDate}.xlsx`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            }
          }
        } else if (response && response.data) {
          // 如果返回的是标准JSON响应格式
          if (response.code === 200) {
            this.$message.success('会话记录导出成功');

            // 如果返回的数据中包含下载链接
            if (response.data.downloadUrl) {
              const link = document.createElement('a');
              link.href = response.data.downloadUrl;
              link.download = response.data.fileName || `会话记录_${startDate}_${endDate}.xlsx`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }
          } else {
            this.$showFriendlyError(null, response.message || '导出失败，请稍后重试');
          }
        } else {
          this.$showFriendlyError(null, '导出失败，响应格式异常');
        }
      } catch (error) {
        // 确保在任何情况下都关闭加载提示
        message.close();

        // 显示错误信息
        let errorMessage = '导出失败';
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.message) {
          errorMessage = error.message;
        } else if (typeof error === 'string') {
          errorMessage = error;
        } else {
          errorMessage = '网络错误，请稍后重试';
        }

        this.$showFriendlyError(null, errorMessage);
      }
    },

    // 处理日期范围变化
    handleDateRangeChange(value) {
      // 当日期范围清空或重新选择时，清除第一个日期的记录
      if (!value || value.length === 0) {
        this.firstSelectedDate = null;
      }
    },

    // 处理新会话创建事件
    handleNewSessionCreated(sessionData) {
      // 格式化新会话数据
      const newSession = this.formatSessionData(sessionData);

      // 将新会话添加到列表顶部
      this.chatHistory.unshift(newSession);

      // 更新总数量
      this.totalCount += 1;

      // 更新全局未读状态
      this.updateGlobalUnreadStatus();

      // 显示通知
      this.$notify({
        title: '新会话',
        message: `新会话已创建: ${newSession.appName || '未知应用'}`,
        type: 'success'
      });

      // 如果当前没有选中任何会话，自动选中新会话
      if (this.currentChatIndex === -1) {
        this.selectChat(0);
      }
    },

    // 处理会话更新事件
    handleSessionUpdated(sessionData) {
      // 查找要更新的会话
      const sessionIndex = this.chatHistory.findIndex(
        chat => chat.sessionId === sessionData.sessionId || chat.id === sessionData.id
      );

      if (sessionIndex >= 0) {
        // 格式化更新的会话数据
        const updatedSession = this.formatSessionData(sessionData);

        // 更新会话数据
        this.$set(this.chatHistory, sessionIndex, updatedSession);

        // 更新全局未读状态
        this.updateGlobalUnreadStatus();

        // 如果更新的是当前选中的会话，同步更新右侧面板信息
        if (this.currentChatIndex === sessionIndex) {
          this.appInfo = {
            name: updatedSession.appName,
            id: updatedSession.id,
            profilePhoto: updatedSession.profilePhoto || "",
            introduce: updatedSession.introduce || "",
          };
        }
      }
    },

    // 刷新会话列表（不重置分页）
    refreshSessionList() {
      // 防抖处理，避免频繁刷新
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
      }

      this.refreshTimer = setTimeout(() => {
        // 保存当前选中的会话ID
        const currentSessionId = this.currentChatIndex >= 0 && this.chatHistory[this.currentChatIndex]
          ? this.chatHistory[this.currentChatIndex].sessionId
          : null;

        // 重新获取第一页数据
        this.skipCount = 0;
        this.hasMore = true;
        this.chatHistory = [];

        this._fetchChatSessionsActual().then(() => {
          // 恢复之前选中的会话
          if (currentSessionId) {
            const newIndex = this.chatHistory.findIndex(
              chat => chat.sessionId === currentSessionId
            );
            if (newIndex >= 0) {
              this.currentChatIndex = newIndex;
            }
          }

          // 更新全局未读状态
          this.updateGlobalUnreadStatus();
        }).catch(error => {
          console.error("刷新会话列表失败:", error);
        });
      }, 1000); // 1秒防抖
    },

    // 统一的防抖未读数更新方法
    debouncedUpdateUnreadCount(data) {
      // 防抖处理，避免短时间内重复处理相同数据
      const dataKey = `${data.sessionId}_${data.unreadCount}_${data.totalUnreadCount || 0}`;
      const now = Date.now();

      if (this.lastUnreadUpdate &&
          this.lastUnreadUpdate.key === dataKey &&
          now - this.lastUnreadUpdate.time < 2000) { // 2秒防抖

        return false; // 返回false表示被防抖跳过
      }

      this.lastUnreadUpdate = { key: dataKey, time: now };

      // 如果是当前选中的会话，不应该标记为未读（用户正在查看）
      if (data.sessionId === this.sessionStatus.sessionId) {
        // 当前会话的消息不标记为未读，但仍然更新全局状态
        this.updateGlobalUnreadStatus();
        return true;
      }

      // 检查是否为匿名用户发送的消息
      const isAnonymousMessage = data.senderName === '匿名客户' ||
                                data.isAnonymous === true ||
                                data.fromAnonymous === true ||
                                (!data.senderName && !data.isStaff);

              // 只有匿名用户发送的消息才更新未读状态和显示通知
        if (isAnonymousMessage) {
          // 更新会话列表中的未读数红点和最新消息信息
          this.updateSessionListUnreadCount(data.sessionId, data.unreadCount, data.lastMessage);

          // 更新全局未读状态
          this.updateGlobalUnreadStatus();

          // 只在有实际变化时显示消息
          if (data.unreadCount > 0 && !data.markedAsRead) {
            // 匿名用户新未读消息
          } else if (data.markedAsRead) {
            // 会话已标记为已读
          }

          // 如果不是当前连接的会话且有新未读消息，显示提醒
          if (data.sessionId !== this.sessionStatus.sessionId && data.unreadCount > 0 && !data.markedAsRead) {
            this.$notify({
              title: '未读消息',
              message: '您有新的未读消息',
              type: 'info'
            });
          }
        }

      return true; // 返回true表示成功处理
    },

    // 实时更新会话列表中特定会话的未读状态和最新消息信息
    updateSessionListUnreadCount(sessionId, unreadCount, lastMessageData = null) {
      try {
        // 查找对应会话的DOM元素
        const sessionIndex = this.chatHistory.findIndex(
          chat => chat.sessionId === sessionId || chat.id === sessionId
        );

        if (sessionIndex >= 0) {
          // 更新未读消息状态
          this.$set(this.chatHistory[sessionIndex], 'hasUnreadMessages', unreadCount > 0);

          // 如果有最新消息数据，更新会话的最后消息和时间
          if (lastMessageData) {
            // 更新最后一条消息内容
            if (lastMessageData.contentPreview) {
              this.$set(this.chatHistory[sessionIndex], 'lastMessage', lastMessageData.contentPreview);
            }

            // 更新时间
            if (lastMessageData.sentAt) {
              const lastMessageTime = new Date(lastMessageData.sentAt);
              const year = lastMessageTime.getFullYear();
              const month = (lastMessageTime.getMonth() + 1).toString().padStart(2, "0");
              const day = lastMessageTime.getDate().toString().padStart(2, "0");
              const hours = lastMessageTime.getHours().toString().padStart(2, "0");
              const minutes = lastMessageTime.getMinutes().toString().padStart(2, "0");
              const seconds = lastMessageTime.getSeconds().toString().padStart(2, "0");
              const timeDisplay = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
              this.$set(this.chatHistory[sessionIndex], 'time', timeDisplay);
            }
          }
        }
      } catch (error) {
        console.error('更新会话列表未读状态失败:', error);
      }
    },

    // 实时更新会话列表中特定会话的在线状态
    updateSessionListOnlineStatus(sessionId, isOnline) {
      try {
        // 查找对应会话的索引
        const sessionIndex = this.chatHistory.findIndex(
          chat => chat.sessionId === sessionId || chat.id === sessionId
        );

        if (sessionIndex >= 0) {
          // 更新内存中的数据
          this.$set(this.chatHistory[sessionIndex], 'isOnline', isOnline);


        }
      } catch (error) {
        console.error('更新会话列表在线状态失败:', error);
      }
    },

    // 直接插入新会话到列表顶部
    insertNewSessionToList(sessionDto) {
      console.log("插入新会话到列表顶部", sessionDto);
      try {
        // 处理不同的字段名（兼容sessionId和sessionID）
        const sessionId = sessionDto.sessionId || sessionDto.sessionID;
        const sessionName = sessionDto.sessionName || sessionDto.name || '新会话';

        if (!sessionId) {
          console.warn("新会话通知缺少sessionId，跳过插入");
          return;
        }

        // 检查会话是否已存在于列表中
        const existingIndex = this.chatHistory.findIndex(
          chat => chat.sessionId === sessionId || chat.id === sessionId
        );

        if (existingIndex >= 0) {
          return;
        }

        // 检查新会话是否由匿名用户发起
        const isAnonymousSession = sessionDto.senderName === '匿名客户' ||
                                  sessionDto.isAnonymous === true ||
                                  sessionDto.fromAnonymous === true ||
                                  (!sessionDto.senderName && !sessionDto.isStaff);

        const sessionDataForFormat = {
          ...sessionDto,
          sessionId: sessionId,
          sessionName: sessionName,
          hasUnreadMessages: false, // 新会话创建时不标记为未读，只有真正收到消息时才标记
          lastMessageSnippet: '',
          lastMessageSentAt: new Date().toISOString(),
          isOnline: true
        };

        const newSession = this.formatSessionData(sessionDataForFormat);

        // 在插入新会话前，保存当前选中的会话信息
        const currentSelectedSessionId = this.currentChatIndex >= 0 && this.chatHistory[this.currentChatIndex]
          ? this.chatHistory[this.currentChatIndex].sessionId
          : null;

        // 插入到列表顶部
        this.chatHistory.unshift(newSession);
        this.totalCount += 1;

        // 如果之前有选中的会话，需要调整currentChatIndex
        if (currentSelectedSessionId && this.currentChatIndex >= 0) {
          // 因为新会话插入到顶部，原来的索引需要+1
          this.currentChatIndex += 1;

          // 验证调整后的索引是否指向正确的会话
          if (this.currentChatIndex < this.chatHistory.length &&
              this.chatHistory[this.currentChatIndex].sessionId === currentSelectedSessionId) {
            console.log(`新会话插入后，成功保持选中会话: ${currentSelectedSessionId}，新索引: ${this.currentChatIndex}`);
          } else {
            // 如果索引调整后还是不匹配，尝试重新查找正确的索引
            const correctIndex = this.chatHistory.findIndex(chat =>
              chat.sessionId === currentSelectedSessionId || chat.id === currentSelectedSessionId
            );
            if (correctIndex >= 0) {
              this.currentChatIndex = correctIndex;
              console.log(`通过查找修正选中索引为: ${correctIndex}，会话: ${currentSelectedSessionId}`);
            } else {
              console.warn('无法找到之前选中的会话，重置选中状态');
              this.currentChatIndex = -1;
            }
          }
        }

        // 强制触发Vue响应式更新
        this.$forceUpdate();

        // 只有匿名用户的新会话才显示通知（但不标记为未读，等真正有消息时才标记）
        if (isAnonymousSession) {
          // 防重复通知机制
          const now = Date.now();
          const shouldShowNotification = !this.lastNotificationTime ||
                                       !this.lastNotificationSessionId ||
                                       this.lastNotificationSessionId !== sessionId ||
                                       (now - this.lastNotificationTime) > 5000; // 5秒内同一会话不重复通知

          if (shouldShowNotification) {
            // 显示通知（新会话创建通知）
            this.$notify({
              title: '新咨询',
              message: '您有一个新的咨询',
              type: 'success'
            });

            // 更新防重复记录
            this.lastNotificationTime = now;
            this.lastNotificationSessionId = sessionId;
          }
        }

      } catch (error) {
        console.error("插入新会话到列表时发生错误:", error);

        // 如果插入失败，回退到刷新整个列表
        setTimeout(() => {
          this.refreshSessionList();
        }, 1000);
      }
    },

    // 格式化会话数据的通用方法
    formatSessionData(session) {
      // 格式化时间
      let timeDisplay = "";
      if (session.lastMessageSentAt) {
        const lastMessageTime = new Date(session.lastMessageSentAt);
        const year = lastMessageTime.getFullYear();
        const month = (lastMessageTime.getMonth() + 1).toString().padStart(2, "0");
        const day = lastMessageTime.getDate().toString().padStart(2, "0");
        const hours = lastMessageTime.getHours().toString().padStart(2, "0");
        const minutes = lastMessageTime.getMinutes().toString().padStart(2, "0");
        const seconds = lastMessageTime.getSeconds().toString().padStart(2, "0");
        timeDisplay = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } else if (session.lastModificationTime) {
        const lastModificationTime = new Date(session.lastModificationTime);
        const year = lastModificationTime.getFullYear();
        const month = (lastModificationTime.getMonth() + 1).toString().padStart(2, "0");
        const day = lastModificationTime.getDate().toString().padStart(2, "0");
        const hours = lastModificationTime.getHours().toString().padStart(2, "0");
        const minutes = lastModificationTime.getMinutes().toString().padStart(2, "0");
        const seconds = lastModificationTime.getSeconds().toString().padStart(2, "0");
        timeDisplay = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } else if (session.createdAt) {
        const createdTime = new Date(session.createdAt);
        const year = createdTime.getFullYear();
        const month = (createdTime.getMonth() + 1).toString().padStart(2, "0");
        const day = createdTime.getDate().toString().padStart(2, "0");
        const hours = createdTime.getHours().toString().padStart(2, "0");
        const minutes = createdTime.getMinutes().toString().padStart(2, "0");
        const seconds = createdTime.getSeconds().toString().padStart(2, "0");
        timeDisplay = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }

      const formattedData = {
        id: session.id,
        clientId: session.clientId || session.id,
        sessionId: session.sessionID || session.sessionId,
        name: session.sessionName,
        appId: session.appId,
        avatar: "",
        profilePhoto: session.profilePhoto,
        introduce: session.introduce,
        lastMessage: session.lastMessageSnippet || "",
        time: timeDisplay,
        appName: session.appName,
        messages: [],
        channelName: session.channelName,
        hasUnreadMessages: session.hasUnreadMessages || false,
        appSourceType: session.appSourceType || 1,
        isOnline: session.isOnline || false,
      };

      return formattedData;
    },

    // ===== 语音播报、复制、清除记忆功能相关方法 =====

    // 初始化语音播报功能
    initVoiceTTS() {
      try {
        // 创建语音播报工具实例
        const voiceUtils = new VoiceUtils();
        this.voiceTTS = voiceUtils.createTTS({
          preferServer: true,
          fallbackToLocal: true,
          onStart: (messageIndex) => {
            console.log('语音朗读开始:', messageIndex);
          },
          onStop: () => {
            console.log('语音朗读结束');
          },
          onError: (error, messageIndex) => {
            console.error('语音朗读错误:', error, messageIndex);
          }
        });

        console.log('语音播报功能已初始化');
      } catch (error) {
        console.error('初始化语音播报功能失败:', error);
      }
    },

    // 语音朗读消息 - 使用公共方法
    async readMessage(content, messageIndex) {
      try {
        if (!this.voiceTTS) {
          this.$showFriendlyError(null, '语音播报功能未初始化');
          return;
        }

        // 准备TTS参数
        const ttsParams = {
          sessionID: this.sessionStatus.sessionId,
          flowType: this.getFlowType(),
          appId: this.getAppId()
        };

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg)
        };

        // 调用公共语音朗读方法
        await this.voiceTTS.readMessage(content, messageIndex, ttsParams, api, messageHandler);

      } catch (error) {
        console.error('语音朗读失败:', error);
        this.$showFriendlyError(error, '语音朗读功能出现错误');
      }
    },

    // 停止语音朗读 - 使用公共方法
    stopReading() {
      try {
        if (this.voiceTTS) {
          this.voiceTTS.stopReading();
        }
      } catch (error) {
        console.error('停止朗读失败:', error);
      }
    },

    // 获取语音朗读按钮的图标 - 使用公共方法
    getVoiceReadButtonIcon(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonIcon(messageIndex);
      }
      return 'el-icon-video-play';
    },

    // 获取语音朗读按钮的提示文字 - 使用公共方法
    getVoiceReadButtonTitle(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonTitle(messageIndex);
      }
      return '语音朗读';
    },

    // 一键复制消息内容 - 使用公共方法
    async copyMessage(content) {
      try {
        // 提取纯文本内容
        const textContent = VoiceUtils.extractTextContent(content);

        if (!textContent.trim()) {
          this.$message.warning('该消息没有可复制的文本内容');
          return;
        }

        // 消息处理函数
        const messageHandler = {
          success: (msg) => this.$message.success(msg),
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg)
        };

        // 调用公共复制方法
        await VoiceUtils.copyToClipboard(textContent, messageHandler);

      } catch (error) {
        console.error('复制消息失败:', error);
        this.$showFriendlyError(error, '复制失败');
      }
    },

    // 清除聊天记忆
    async clearChatMemory() {
      try {
        const result = await clearChatMemory({
          sessionId: this.sessionStatus.sessionId,
          api: api,
          messageHandler: {
            confirm: (message, title, options) => this.$confirm(message, title, options),
            success: (message) => this.$message.success(message),
            error: (message) => this.$showFriendlyError(null, message)
          },
          clearLocalMessages: () => {
            // 清空当前对话显示
            this.messages = [];

            // 清除本地存储的消息
            if (this.sessionStatus.sessionId) {
              this.clearChatMessages(this.sessionStatus.sessionId);
            }
          },
          stopStreaming: () => {
            // SignalR连接模式下没有流式输出需要停止
            this.isTyping = false;
          },
          stopReading: () => {
            // 停止语音朗读
            if (this.voiceTTS) {
              this.voiceTTS.stopReading();
            }
          },
          addWelcomeMessage: () => {
            // 添加新的欢迎消息
            if (this.currentChatIndex >= 0 && this.chatHistory[this.currentChatIndex]) {
              const chat = this.chatHistory[this.currentChatIndex];
              if (chat.introduce) {
                this.addMessage({
                  role: "assistant",
                  content: formatOutputContent(chat.introduce),
                  time: this.formatTime(new Date()),
                  isWelcomeMessage: true,
                });
              }
            }
          }
        });

        console.log('清除记忆操作结果:', result);
      } catch (error) {
        console.error('清除聊天记忆失败:', error);
        this.$showFriendlyError(error, '清除聊天记忆失败');
      }
    },

    // 获取工作流类型
    getFlowType() {
      if (this.currentChatIndex < 0 || !this.chatHistory[this.currentChatIndex]) {
        return 'SessionFlow'; // 默认值
      }
      const chat = this.chatHistory[this.currentChatIndex];
      return chat.appSourceType === 2 ? 'WorkFlow' : 'SessionFlow';
    },

    // 获取应用ID
    getAppId() {
      if (this.currentChatIndex < 0 || !this.chatHistory[this.currentChatIndex]) {
        return null;
      }
      const chat = this.chatHistory[this.currentChatIndex];
      return chat.appId || null;
    },
  },
};
</script>

<style lang="scss" scoped>
.execute-workflow-page {
  height: calc(100vh - 40px);
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.full-height {
  height: 100%;
}

/* 左侧聊天列表样式 */
.left-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

/* 左侧聊天列表加载状态样式 */
.chat-list-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.chat-list-loading .loading-content {
  text-align: center;
  color: #606266;
}

.chat-list-loading .loading-icon {
  font-size: 24px;
  margin-bottom: 12px;
  color: #409eff;
  animation: rotate 1s linear infinite;
}

.chat-list-loading p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px 10px;
}

.search-container {
  padding: 10px;
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-container .el-input {
  flex: 1;
}

.export-dropdown {
  flex-shrink: 0;
}

.export-btn {
  height: 32px;
  font-size: 12px;
  padding: 0 12px;
  line-height: 30px;
}

/* 选择模式操作栏 */
.select-mode-container {
  padding: 10px;
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.select-info {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.select-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.select-actions .el-button {
  height: 28px;
  font-size: 12px;
  padding: 0 10px;
}

/* 选择模式下的会话项样式 */
.chat-list-item.select-mode {
  cursor: pointer;
}

.chat-list-item.select-mode:hover {
  background-color: #f0f9ff;
}

.chat-checkbox {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.chat-preview {
  display: flex;
  align-items: center;
}

/* 导出弹框样式 */
.export-dialog-content .el-alert {
  margin-bottom: 20px;
}

/* 确保primary类型的alert显示正确的蓝色主题 */
.export-dialog-content .el-alert--primary {
  background-color: #ecf5ff !important;
  border-color: #b3d8ff !important;
}

.export-dialog-content .el-alert--primary .el-alert__icon {
  color: #409eff !important;
}

.export-dialog-content .el-alert--primary .el-alert__title {
  color: #409eff !important;
}

.date-range-section {
  margin-top: 20px;
}

.date-label {
  display: block;
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.date-label .required {
  color: #f56c6c;
  margin-left: 2px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.search-box {
  padding: 0 0 10px 0;
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
}

.chat-list-item {
  position: relative;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #ecf5ff;
    border-color: #409eff;
  }

  .chat-preview {
    display: flex;
    align-items: center;

    .chat-avatar {
      margin-right: 10px;
    }

    .chat-info {
      flex: 1;
      overflow: hidden;

      .chat-name {
        font-weight: 500;
        margin-bottom: 3px;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-last-message {
        color: #909399;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-app-info {
        margin-top: 5px;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;

        .el-tag {
          font-size: 10px;
          height: 20px;
          line-height: 18px;
          padding: 0 5px;
        }
      }

      .chat-time {
        margin-top: 5px;
        font-size: 12px;
        color: #909399;
        white-space: nowrap;
      }
    }
  }
}

.empty-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;

  i {
    font-size: 32px;
    margin-bottom: 10px;
  }

  p {
    font-size: 14px;
  }
}

/* 右侧聊天区域样式 */
.result-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    .el-tag {
      margin-left: 8px;
    }
  }

  .header-actions {
    display: flex;
    gap: 5px;
  }
}

.result-container {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f9f9fb;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f2f2f2;
    border-radius: 3px;
  }
}



.chat-message {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
  width: 100%;
}

.user-message {
  flex-direction: row;
  justify-content: flex-start;
}

.system-message {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

/* 人工客服消息样式，与系统消息类似但可以有不同的样式 */
.human-message {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f0f0f0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar {
  margin-right: 10px;
}

.system-avatar {
  margin-left: 10px;
}

.message-content {
  max-width: calc(100% - 60px);
  margin: 0 10px;
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.system-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: left;
}

.human-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: left;
}

.message-text {
  padding: 10px 12px;
  border-radius: 4px;
  word-break: break-word;
  line-height: 1.5;
}

.user-message .message-text {
  background-color: #f5f7fa;
  color: #303133;
  border-top-left-radius: 0;
}

.system-message .message-text {
  background-color: #e8f4fd;
  color: #2c3e50;
  border-top-right-radius: 0;
  border: 1px solid #d4e8fc;
}

/* 人工客服消息样式 */
.human-message .message-text {
  background-color: #f0f9ff;
  color: #1e40af;
  border-top-right-radius: 0;
  border: 1px solid #bfdbfe;
}

.message-info {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 消息操作按钮样式 */
.message-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 4px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background-color: #f5f7fa;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;

  &:hover {
    background-color: #e4e7ed;
    color: #409eff;
  }

  &:active {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background-color: #f5f7fa;
      color: #606266;
    }
  }
}

.voice-read-btn {
  &.reading {
    background-color: #409eff;
    color: #fff;

    &:hover {
      background-color: #66b1ff;
    }
  }
}

.copy-btn {
  &:hover {
    background-color: #67c23a;
    color: #fff;
  }
}

.typing-indicator {
  display: flex;
  justify-content: flex-start;
  padding: 0 16px;
  margin-bottom: 10px;
  margin-left: 60px;

  span {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 0 2px;
    background-color: #aaa;
    border-radius: 50%;
    animation: typing 1s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

.chat-input {
  padding: 16px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  align-items: flex-end;
  background-color: #fff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);

  .el-input {
    margin-right: 15px;
    flex: 1;

    ::v-deep .el-textarea__inner {
      resize: none;
      border-radius: 6px;
      border-color: #dcdfe6;
      padding: 10px 15px;
      transition: all 0.3s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .el-button {
    height: 40px;
    font-size: 15px;
    padding: 0 20px;
    border-radius: 6px;
    transition: all 0.3s;

    &:not(:disabled):hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(64, 158, 255, 0.4);
    }
  }
}

@keyframes typing {
  0% {
    transform: translateY(0);
    opacity: 0.5;
  }

  50% {
    transform: translateY(-5px);
    opacity: 1;
  }

  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
}

/* 加载更多消息样式 */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 10px 0;
  margin-bottom: 10px;
}

.load-more-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin-bottom: 10px;
  color: #909399;

  i {
    margin-right: 8px;
  }
}

/* Markdown 格式化样式 */
:deep(.markdown-content) {
  line-height: 1.6;
  color: #333;

  .md-title {
    position: relative;
    margin-bottom: 12px;
    font-weight: 600;
    color: #2c3e50;
    padding-bottom: 6px;
    border-bottom: 1px solid #eaecef;

    .md-title-icon {
      opacity: 0.5;
      margin-right: 6px;
      font-weight: 400;
    }

    &:hover .md-title-icon {
      opacity: 0.8;
    }
  }

  .md-h1 {
    font-size: 22px;
    margin-top: 20px;
  }

  .md-h2 {
    font-size: 18px;
    margin-top: 16px;
  }

  .md-h3 {
    font-size: 16px;
    margin-top: 14px;
    border-bottom: none;
  }

  .md-list {
    padding-left: 22px;
    margin: 12px 0;

    &.md-list-ordered {
      list-style-type: decimal;
    }
  }

  .md-list-item {
    margin-bottom: 8px;
  }

  .inline-code {
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
    padding: 2px 5px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 90%;
    color: #476582;
  }

  .code-container {
    margin: 14px 0;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .code-header {
      background: #f1f1f1;
      padding: 8px 12px;
      font-size: 12px;
      font-weight: 500;
      color: #666;
      border-bottom: 1px solid #ddd;
    }
  }

  pre.code-block {
    background-color: #f6f8fa;
    padding: 16px;
    margin: 0;
    overflow-x: auto;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;

    code {
      background: transparent;
      padding: 0;
      font-size: inherit;
    }
  }

  .md-blockquote {
    border-left: 4px solid #42b983;
    padding: 10px 15px;
    margin: 12px 0;
    background-color: rgba(66, 185, 131, 0.05);
    color: #454d64;
    font-style: italic;
  }

  .md-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
    margin: 20px 0;
  }

  .md-link {
    color: #3eaf7c;
    font-weight: 500;
    text-decoration: none;
    position: relative;

    &:hover {
      text-decoration: underline;
    }

    &:after {
      content: "↗";
      font-size: 80%;
      position: relative;
      top: -3px;
      opacity: 0.5;
    }
  }

  .md-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    overflow-x: auto;
    display: block;

    td {
      border: 1px solid #ddd;
      padding: 8px 12px;
    }

    tr:nth-child(1) td {
      font-weight: 600;
      background-color: #f5f7f9;
    }

    tr:nth-child(even) {
      background-color: #fafafa;
    }
  }

  strong {
    font-weight: 600;
    color: #273849;
  }

  em {
    color: #555;
  }

  strong em, em strong {
    color: #34495e;
  }
}
:deep(.el-avatar>img){
  width: 100%;
  height: 100%;
}

/* 未选择会话时的提示样式 */
.no-session-selected {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 60px 40px;

  .empty-chat-icon {
    font-size: 64px;
    color: #c0c4cc;
    margin-bottom: 24px;
  }

  h3 {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 500;
    color: #606266;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #909399;
  }
}

/* 搜索框样式 */
.search-container {
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #eaecef;
}

.search-container :deep(.el-input-group) {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
    border-right: 0;
  }

  .el-input-group__append {
    border-radius: 0 4px 4px 0;
    border-left: 0;
    background-color: #409eff;
    border-color: #409eff;

    .el-button {
      background-color: transparent;
      border: none;
      color: #fff;
      padding: 0 15px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

/* 左侧面板tabs样式 */
.tabs-container {
  padding: 10px 15px 0;
  background-color: #fff;
  border-bottom: 1px solid #eaecef;
}

.tabs-container :deep(.el-tabs) {
  .el-tabs__header {
    margin: 0 0 10px 0;
  }

  .el-tabs__nav-wrap {
    &::after {
      display: none;
    }
  }

  .el-tabs__item {
    padding: 0 15px;
    font-size: 13px;
    position: relative; /* 为红点定位提供参考 */

    &.is-active {
      color: #409eff;
      font-weight: 500;
    }
  }

  .el-tabs__active-bar {
    background-color: #409eff;
  }
}

/* 选项卡未读红点样式 */
.tab-label {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin-right: 8px; /* 为红点留出一些空间 */
}

.tab-unread-dot {
  position: absolute;
  top: 3px;
  right: -8px;
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #ff6b6b, #ff4757);
  border-radius: 50%;
  box-shadow: 0 0 0 2px #fff, 0 2px 4px rgba(255, 71, 87, 0.3);
  animation: tab-pulse 2.5s ease-in-out infinite;
}

/* 调整左侧面板样式 */
.left-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.left-panel .list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px 10px;
}

/* 瀑布流相关样式 */
.chat-list-scroll {
  height: 100%;
  overflow-y: auto;
}

.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  margin-top: 10px;
}

.load-more-btn {
  padding: 8px 20px;
  font-size: 13px;
  color: #409eff;
  border: 1px solid #409eff;
  background-color: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #409eff;
    color: #fff;
  }
}

.loading-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  color: #606266;
  font-size: 13px;
  margin-top: 10px;

  i {
    margin-right: 6px;
    animation: rotate 1s linear infinite;
  }
}

.no-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  color: #c0c4cc;
  font-size: 12px;
  margin-top: 10px;
}

/* 会话列表未读红点样式 */
.chat-preview {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.chat-unread-indicator {
  position: absolute;
  top: -3px;
  right: -3px;
  z-index: 2;
}

.chat-unread-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #ff6b6b, #ff4757);
  border-radius: 50%;
  box-shadow: 0 0 0 2px #fff, 0 2px 6px rgba(255, 71, 87, 0.4);
  animation: chat-pulse 2.5s ease-in-out infinite;
}

/* 选项卡红点脉冲动画 */
@keyframes tab-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 2px #fff, 0 2px 4px rgba(255, 71, 87, 0.3);
  }
  50% {
    transform: scale(1.3);
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(255, 71, 87, 0.2), 0 2px 8px rgba(255, 71, 87, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 2px #fff, 0 2px 4px rgba(255, 71, 87, 0.3);
  }
}

/* 会话红点脉冲动画 */
@keyframes chat-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 2px #fff, 0 2px 6px rgba(255, 71, 87, 0.4);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 0 2px #fff, 0 0 0 6px rgba(255, 71, 87, 0.3), 0 2px 8px rgba(255, 71, 87, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 2px #fff, 0 2px 6px rgba(255, 71, 87, 0.4);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
