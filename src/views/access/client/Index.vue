<template>
  <div class="access-client-page">
    <div class="page-header">
      <div class="header-left">
        <h2>客户端管理</h2>
      </div>
      <div class="header-right">
        <el-button type="primary">离线通知</el-button>
      </div>
    </div>

    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="请输入客户端名称"
        style="width: 200px"
      ></el-input>
      <el-select v-model="selectedType" placeholder="请选择渠道接入类型" style="width: 150px; margin-left: 10px">
        <el-option label="全部" value=""></el-option>
        <el-option label="网页嵌入" value="1"></el-option>
      </el-select>
      <el-select v-model="selectedStatus" placeholder="请选择应用状态" style="width: 150px; margin-left: 10px">
        <el-option label="全部" value=""></el-option>
        <el-option label="在线" value="0"></el-option>
        <el-option label="离线" value="1"></el-option>
      </el-select>
      <el-button type="primary" @click="fetchClientList" style="margin-left: 10px">
        <i class="el-icon-search"></i>
        查询
      </el-button>
    </div>

    <el-table
      :data="clientList"
      style="width: 100%"
      height="calc(100vh - 350px)"
      v-loading="loading"
    >
      <el-table-column width="50">
        <template #default>
          <div class="client-icon">
            <i class="el-icon-monitor"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="channelType" label="类型" width="120">
        <template #default="scope">
          <span>{{ getChannelTypeName(scope.row.channelType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="websiteName" label="名称" min-width="150">
        <template #default="scope">
          <span>{{ scope.row.websiteName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sourceType" label="服务类型" width="120">
        <template #default="scope">
          <span>{{ getSourceTypeName(scope.row.sourceType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="appOnlineStatus" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.appOnlineStatus == 0 ? 'success' : 'info'">
            {{ scope.row.appOnlineStatus == 0 ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastModificationTime" label="最近活跃时间" width="180">
        <template #default="scope">
          <span>{{ formatDate(scope.row.lastModificationTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" align="right">
        <template #default="scope">
          <el-button type="primary" @click="handleConfigure(scope.row)">配置</el-button>
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" class="delete-btn" @click="handleDelete(scope.row)">删除</el-button>
          <el-button type="text" class="dialog-btn" @click="handleChat(scope.row)">对话</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 编辑客户端名称弹窗 -->
    <el-dialog
      title="客户端编辑"
      :close-on-click-modal="false"
      :visible.sync="editDialogVisible"
      width="30%"
      @close="resetEditForm"
    >
      <el-form :model="editForm" label-width="100px" @submit.native.prevent>
        <el-form-item label="客户端名称:">
          <el-input
            v-model="editForm.name"
            placeholder="请输入客户端名称"
            @keydown.enter.native.prevent="submitEdit"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api/request'

export default {
  name: 'AccessClientPage',
  data() {
    return {
      searchQuery: '',
      selectedType: '',
      selectedStatus: '',
      clientList: [],
      loading: false,
      total: 0,
      pageSize: 10,
      currentPage: 0,
      // 编辑弹窗相关
      editDialogVisible: false,
      editForm: {
        id: '',
        name: ''
      },
      submitLoading: false
    }
  },
  created() {
    this.fetchClientList()
  },
  methods: {
    // 获取客户端列表
    async fetchClientList() {
      this.loading = true
      try {
        const params = {
          websiteName: this.searchQuery || undefined,
          channelType: this.selectedType || undefined,
          appOnlineStatus: this.selectedStatus || undefined,
          sorting: "1",
          skipCount: this.currentPage,
          maxResultCount: this.pageSize
        }

        const res = await api.client.getList(params)
        if (res.isSuccess) {
          this.clientList = res.data.items
          this.total = res.data.totalCount
        }
      } catch (error) {
        console.error('获取客户端列表失败', error)
        this.$showFriendlyError(error, '获取客户端列表失败')
      } finally {
        this.loading = false
      }
    },

    // 根据channelType获取类型名称
    getChannelTypeName(type) {
      const typeMap = {
        1: '网页嵌入',
        2: '移动应用'
      }
      return typeMap[type] || '未知类型'
    },

    // 根据sourceType获取服务类型名称
    getSourceTypeName(type) {
      const typeMap = {
        1: '智能客服',
        2: '自定义'
      }
      return typeMap[type] || '未知类型'
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '--'
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },

    // 配置按钮点击事件
    handleConfigure(row) {
      // 跳转到配置页面
      this.$router.push(`/access/web-embed/${row.id}`)
    },

    // 编辑按钮点击事件
    handleEdit(row) {
      this.editForm.id = row.id
      this.editForm.name = row.websiteName
      this.editDialogVisible = true
    },

    // 提交编辑
    async submitEdit() {
      if (!this.editForm.name.trim()) {
        this.$message.warning('客户端名称不能为空')
        return
      }

      this.submitLoading = true
      try {
        const res = await api.client.updateName(this.editForm.id, this.editForm.name)
        if (res.isSuccess) {
          this.$message.success('更新成功')
          this.editDialogVisible = false
          this.fetchClientList() // 刷新列表
        } else {
          this.$showFriendlyError(null, res.message || '更新失败')
        }
      } catch (error) {
        console.error('更新客户端名称失败', error)
        this.$showFriendlyError(error, '更新客户端名称失败')
      } finally {
        this.submitLoading = false
      }
    },

    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        id: '',
        name: ''
      }
    },

    // 删除按钮点击事件
    handleDelete(row) {
      this.$confirm(`确定要删除客户端 "${row.websiteName}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await api.client.delete(row.id)
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.fetchClientList()
          }
        } catch (error) {
          this.$showFriendlyError(error, '删除失败')
        }
      }).catch(() => {})
    },

    // 对话按钮点击事件
    handleChat(row) {
      // 跳转到对话页面并传递客户端ID
      this.$router.push({
        path: '/access/chat',
        query: {
          clientId: row.correspondingAppId,
          clientName: row.websiteName
        }
      })
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.fetchClientList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchClientList()
    }
  }
}
</script>

<style lang="scss" scoped>
.access-client-page {
  padding: 20px;
  height: 100%;
  background-color: #fff;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-weight: 500;
    }
  }

  .search-bar {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
  }

  .client-icon {
    font-size: 20px;
    color: #409EFF;
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  :deep(.el-table) {
    margin-bottom: 30px;
    background-color: #fff;

    .el-table__body-wrapper {
      min-height: 400px;
      background-color: #fff;
    }

    .el-table__header-wrapper {
      background-color: #fff;
      th {
        background-color: #fff !important;
      }
    }

    .delete-btn {
      color: #f56c6c;
    }

    .dialog-btn {
      color: #409EFF;
    }
  }
}
</style>
