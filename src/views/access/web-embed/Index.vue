<template>
  <div class="web-embed-config-page">
    <div class="page-header">
      <h2>网页嵌入配置</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="info" @click="resetChat">重置聊天</el-button>
        <el-button @click="handleReturn">返回</el-button>
      </div>
    </div>

    <div class="page-content">
      <el-row :gutter="20" class="full-height-row">
        <!-- 左侧预览区域 -->
        <el-col :span="10">
          <div
            class="preview-container"
            :style="{ height: containerHeight + 'px' }"
          >
            <div class="mobile-preview">
              <div
                class="mobile-header"
                :style="{ backgroundColor: configData.backgroundColor }"
              >
                <div class="mobile-title">
                  {{ configData.title || "智能客服" }}
                </div>
                <div class="mobile-subtitle">{{ configData.subtitle }}</div>
              </div>
              <div class="mobile-content">
                <div
                  class="chat-messages"
                  ref="chatMessages"
                  @click="handleImageClick"
                >
                  <!-- 欢迎消息 -->
                  <div class="message bot-message">
                    <div class="avatar">
                      <img
                        :src="
                          configData.botAvatarUrl ||
                          'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
                        "
                        alt="机器人头像"
                      />
                    </div>
                    <div class="message-content">
                      <div
                        class="message-text"
                        v-html="
                          formatOutputContent(
                            configData.welcomeMessage ||
                              '您好，我是您的智能客服，请问有什么可以帮助您的？'
                          )
                        "
                      ></div>
                    </div>
                  </div>

                  <!-- 聊天消息 -->
                  <template v-for="(message, index) in chatMessages">
                    <!-- 用户消息 -->
                    <div
                      v-if="message.role === 'user'"
                      :key="'user_' + index"
                      class="message user-message"
                    >
                      <div class="avatar user-avatar">
                        <img
                          src="https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png"
                          alt="用户头像"
                        />
                      </div>
                      <div class="message-content">
                        <!-- 显示用户发送的图片 -->
                        <div v-if="message.imageUrl" class="message-image">
                          <img
                            :src="message.imageUrl"
                            alt="用户发送的图片"
                            @click="handleImageClick"
                          />
                        </div>
                        <div class="message-text">
                          {{ message.content }}
                        </div>
                      </div>
                    </div>
                    <!-- 系统消息 -->
                    <div
                      v-else
                      :key="'bot_' + index"
                      class="message bot-message"
                    >
                      <div class="avatar">
                        <img
                          :src="
                            configData.botAvatarUrl ||
                            'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
                          "
                          alt="机器人头像"
                        />
                      </div>
                      <div class="message-content">
                        <div
                          class="message-text"
                          :class="{
                            streaming: message.isStreaming,
                            error: message.hasError,
                          }"
                          v-html="message.content"
                        ></div>
                        <!-- 知识库文件标签 -->
                        <div v-if="message.knowledgeFiles && message.knowledgeFiles.length > 0" class="knowledge-files">
                          <div class="knowledge-files-header">
                            <i class="el-icon-folder-opened"></i>
                            <span>参考资料</span>
                          </div>
                          <div class="knowledge-files-list">
                            <span
                              v-for="file in message.knowledgeFiles"
                              :key="file.fileId"
                              class="knowledge-file-tag"
                              :title="`文件ID: ${file.fileId}`"
                            >
                              <i class="el-icon-document"></i>
                              {{ file.fileName }}
                            </span>
                          </div>
                        </div>
                        <!-- 工作流执行过程展示 -->
                        <div
                          v-if="
                            message.fromWorkflow &&
                            message.executionSteps &&
                            message.executionSteps.length > 0
                          "
                          class="workflow-execution-steps"
                        >
                          <div class="execution-steps-list">
                            <div
                              v-for="(
                                step, stepIndex
                              ) in message.executionSteps"
                              :key="'step_' + stepIndex"
                              class="execution-step"
                              :class="{
                                current: step.isCurrent,
                                completed: step.isCompleted,
                              }"
                            >
                              <div class="step-indicator">
                                <i
                                  v-if="step.isCompleted"
                                  class="el-icon-check"
                                ></i>
                                <i
                                  v-else-if="step.isCurrent"
                                  class="el-icon-loading"
                                ></i>
                                <span v-else class="step-number">{{
                                  stepIndex + 1
                                }}</span>
                              </div>
                              <div class="step-content">
                                <span class="step-label">{{
                                  getStepDisplayName(step.nodeType)
                                }}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- 流式传输指示器 -->
                        <div
                          v-if="message.isStreaming"
                          class="streaming-indicator"
                        >
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                        <!-- 消息操作按钮 -->
                        <div
                          v-if="
                            !message.isStreaming &&
                            !message.hasError &&
                            (configData.isVoiceReading ||
                              configData.isOneClickCopy)
                          "
                          class="message-actions"
                        >
                          <!-- 语音朗读按钮 -->
                          <button
                            v-if="configData.isVoiceReading"
                            class="action-btn voice-read-btn"
                            @click="readMessage(message.content, index)"
                            :disabled="
                              isLoadingAudio &&
                              currentReadingMessageIndex !== index
                            "
                            :title="getVoiceReadButtonTitle(index)"
                          >
                            <i :class="getVoiceReadButtonIcon(index)"></i>
                          </button>
                          <!-- 一键复制按钮 -->
                          <button
                            v-if="configData.isOneClickCopy"
                            class="action-btn copy-btn"
                            @click="copyMessage(message.content)"
                            title="复制内容"
                          >
                            <i class="el-icon-document-copy"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </template>

                  <!-- 正在输入提示 -->
                  <div
                    v-if="isTyping && !isStreaming && !hasStreamingMessage"
                    class="message bot-message typing-message"
                  >
                    <div class="avatar">
                      <img
                        :src="
                          configData.botAvatarUrl ||
                          'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
                        "
                        alt="机器人头像"
                      />
                    </div>
                    <div class="message-content">
                      <div class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 图片预览区域 - 固定在底部 -->
                <div
                  v-if="uploadedImageUrl"
                  class="image-preview-container-bottom"
                >
                  <div class="preview-label">已选择图片：</div>
                  <div class="image-preview">
                    <img
                      :src="uploadedImageUrl"
                      alt="上传的图片"
                      class="preview-image"
                      @click="handleImageClick"
                    />
                    <button
                      class="remove-image-btn"
                      @click="removeUploadedImage"
                      title="移除图片"
                    >
                      <i class="el-icon-close"></i>
                    </button>
                  </div>
                </div>

                <div class="chat-input">
                  <input
                    type="text"
                    v-model="userMessage"
                    :placeholder="configData.inputBoxPrompt || '请输入您的问题'"
                    @keyup.enter="sendMessage"
                    :disabled="isStreaming || isRecording"
                    ref="messageInput"
                  />
                  <!-- 图片上传按钮 -->
                  <el-upload
                    class="image-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="beforeImageUpload"
                    :http-request="handleImageUpload"
                    accept="image/*"
                    :disabled="isStreaming || isRecording"
                  >
                    <el-button
                      type="text"
                      :disabled="isStreaming || isRecording"
                      class="upload-btn"
                      title="上传图片"
                    >
                      <i class="el-icon-picture"></i>
                    </el-button>
                  </el-upload>
                  <!-- 清除记忆按钮 - 当启用清除记忆功能时显示 -->
                  <button
                    v-if="configData.isCanClearRecord && !isStreaming"
                    class="clear-memory-btn"
                    @click="clearChatMemory"
                    :disabled="
                      isTyping || isStreaming || chatMessages.length === 0
                    "
                    title="清除记忆"
                  >
                    <i class="el-icon-delete"></i>
                  </button>
                  <!-- 语音按钮 - 当启用语音功能时显示 -->
                  <button
                    v-if="configData.isCanVoice && !isStreaming"
                    class="voice-btn"
                    :class="{ disabled: !voiceSupported }"
                    :style="{
                      backgroundColor: voiceSupported
                        ? configData.backgroundColor
                        : '#c0c4cc',
                    }"
                    @click="showVoiceModal"
                    :disabled="isTyping || isStreaming || !voiceSupported"
                    :title="getVoiceButtonTitle()"
                  >
                    <i class="el-icon-microphone"></i>
                  </button>
                  <!-- 停止按钮 - 流式输出时显示 -->
                  <button
                    v-if="isStreaming"
                    class="stop-btn"
                    @click="stopStreaming"
                    title="停止生成"
                  >
                    <i class="el-icon-video-pause"></i>
                  </button>
                  <!-- 发送按钮 - 非流式输出时显示 -->
                  <button
                    v-else-if="!configData.isCanVoice || !voiceSupported"
                    class="send-btn"
                    :style="{ backgroundColor: configData.backgroundColor }"
                    @click="sendMessage"
                    :disabled="isTyping || !userMessage.trim()"
                  >
                    <i class="el-icon-s-promotion"></i>
                  </button>
                  <!-- 发送按钮 - 有语音功能但不录音时显示 -->
                  <button
                    v-else-if="!isRecording"
                    class="send-btn"
                    :style="{ backgroundColor: configData.backgroundColor }"
                    @click="sendMessage"
                    :disabled="isTyping || !userMessage.trim()"
                  >
                    <i class="el-icon-s-promotion"></i>
                  </button>
                </div>

                <!-- 语音录制模态框 -->
                <div v-if="showVoiceModalFlag" class="voice-modal-overlay">
                  <div class="voice-modal">
                    <div class="voice-modal-header">
                      <span class="voice-title">点击说话</span>
                      <button class="voice-close-btn" @click="hideVoiceModal">
                        <i class="el-icon-close"></i>
                      </button>
                    </div>
                    <div class="voice-modal-content">
                      <div class="voice-circle-container">
                        <div
                          class="voice-circle"
                          :class="{ recording: isRecording }"
                        >
                          <i class="el-icon-microphone voice-icon"></i>
                        </div>
                      </div>
                      <div class="voice-action-area">
                        <button
                          class="voice-record-btn"
                          :class="{ recording: isRecording }"
                          :style="{
                            backgroundColor: isRecording
                              ? '#e6a23c'
                              : configData.backgroundColor,
                          }"
                          @mousedown="startVoiceRecord"
                          @mouseup="stopVoiceRecord"
                          @mouseleave="stopVoiceRecord"
                          @touchstart="startVoiceRecord"
                          @touchend="stopVoiceRecord"
                        >
                          <i class="el-icon-microphone"></i>
                          <span>{{
                            isRecording ? "松开结束" : "点击说话"
                          }}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 底部区域 -->
                <div
                  v-if="configData.bottomTitle || configData.websiteUrl"
                  class="mobile-footer"
                >
                  <div
                    v-if="configData.bottomTitle"
                    class="bottom-title"
                    :class="{ clickable: configData.websiteUrl }"
                    @click="handleBottomTitleClick"
                  >
                    {{ configData.bottomTitle }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 右侧配置区域 -->
        <el-col :span="14">
          <div
            class="config-container"
            :style="{ height: containerHeight + 'px', overflowY: 'auto' }"
          >
            <!-- 基础设置 -->
            <el-collapse v-model="activeCollapse" accordion>
              <el-collapse-item title="基础设置" name="basic">
                <div class="config-section">
                  <div class="form-item">
                    <div class="form-label">网站名称</div>
                    <el-input
                      v-model="configData.websiteName"
                      placeholder="请输入网站名称"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">对话应用</div>
                    <el-select
                      v-model="configData.correspondingAppId"
                      placeholder="选择你的默认应用"
                      class="full-width"
                    >
                      <el-option-group label="应用">
                        <el-option
                          v-for="item in correspondingAppIdList"
                          :key="'app_' + item.id"
                          :label="item.name"
                          :value="'app_' + item.id"
                        >
                        </el-option>
                      </el-option-group>
                      <el-option-group label="工作流">
                        <el-option
                          v-for="item in workflowList"
                          :key="'workflow_' + item.id"
                          :label="item.name"
                          :value="'workflow_' + item.id"
                        >
                        </el-option>
                      </el-option-group>
                    </el-select>
                  </div>

                  <div class="form-item">
                    <div class="form-label">窗口标题</div>
                    <el-input
                      v-model="configData.title"
                      placeholder="智能客服"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">窗口副标题</div>
                    <el-input
                      v-model="configData.subtitle"
                      placeholder="请输入"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">欢迎语</div>
                    <el-input
                      v-model="configData.welcomeMessage"
                      type="textarea"
                      :rows="3"
                      placeholder="您好！我是您的AI助手，有什么可以帮您解答的？"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">输入框提示</div>
                    <el-input
                      v-model="configData.inputBoxPrompt"
                      placeholder="请输入您的问题，Shift+回车换行"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">背景色</div>
                    <el-color-picker
                      v-model="configData.backgroundColor"
                    ></el-color-picker>
                  </div>

                  <div class="form-item">
                    <div class="form-label">组件选择</div>
                    <el-checkbox v-model="configData.isCanVoice"
                      >语音输入</el-checkbox
                    >
                    <el-checkbox v-model="configData.isVoiceReading"
                      >语音朗读</el-checkbox
                    >
                    <el-checkbox v-model="configData.isOneClickCopy"
                      >一键复制</el-checkbox
                    >
                    <el-checkbox v-model="configData.isCanClearRecord"
                      >清除记忆</el-checkbox
                    >
                  </div>
                </div>
              </el-collapse-item>

              <!-- 集成方式 -->
              <el-collapse-item title="集成方式" name="integration">
                <div class="config-section">
                  <div class="integration-method">
                    <h4>方式一：悬浮窗口嵌入</h4>
                    <p>
                      复制script标签内容，放在应用项目目标路径html文件的<code>&lt;head&gt;</code>标签中
                    </p>
                    <div class="code-block">
                      <pre><code>&lt;script
    data-name="link-bot"
    defer
    client-id="{{clientId}}"
    session-id="{{sessionId}}"
    src="{{baseUrl}}/link-bot.min.js"
&gt;&lt;/script&gt;</code></pre>
                      <el-button
                        type="primary"
                        class="copy-btn"
                        @click="copyCode('script')"
                        >复制</el-button
                      >
                    </div>
                  </div>

                  <div class="integration-method">
                    <h4>方式二：独立页面链接</h4>
                    <p>复制下面的链接，可以直接访问或分享给他人</p>
                    <div class="code-block">
                      <pre><code>{{baseUrl}}?clientId={{clientId}}&sessionId={{sessionId}}&flowDetailType={{flowDetailType}}</code></pre>
                      <!-- <pre><code>http://***************:3001?clientId={{clientId}}&sessionId={{sessionId}}</code></pre> -->
                      <el-button
                        type="primary"
                        class="copy-btn"
                        @click="copyCode('link')"
                        >复制</el-button
                      >
                    </div>
                  </div>

                  <div class="integration-method">
                    <h4>方式三：iframe嵌入</h4>
                    <p>复制iframe代码，可以嵌入到您的网页中</p>
                    <div class="code-block">
                      <pre><code>&lt;iframe frameborder="0" height="800" width="100%" allow="microphone" src="{{baseUrl}}?clientId={{clientId}}&sessionId={{sessionId}}&flowDetailType={{flowDetailType}}"&gt;&lt;/iframe&gt;</code></pre>
                      <el-button
                        type="primary"
                        class="copy-btn"
                        @click="copyCode('iframe')"
                        >复制</el-button
                      >
                    </div>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 高级设置 -->
              <el-collapse-item title="高级设置" name="advanced">
                <div class="config-section">
                  <div class="form-item">
                    <div class="form-label">机器人头像</div>
                    <div class="upload-container">
                      <el-image
                        v-if="configData.botAvatarUrl"
                        :src="configData.botAvatarUrl"
                        class="preview-icon"
                      ></el-image>
                      <el-upload
                        class="avatar-uploader"
                        action="#"
                        :show-file-list="false"
                        :before-upload="beforeAvatarUpload"
                        :http-request="handleAvatarUpload"
                      >
                        <el-button type="primary">上传文件</el-button>
                      </el-upload>
                      <span class="upload-hint"
                        >建议上传无背景图片，建议尺寸 36*36px, jpg,
                        png格式</span
                      >
                    </div>
                  </div>

                  <div class="form-item">
                    <div class="form-label">底部标语</div>
                    <el-input
                      v-model="configData.bottomTitle"
                      placeholder="请输入底部标语"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">跳转地址</div>
                    <el-input
                      v-model="configData.websiteUrl"
                      placeholder="请输入跳转地址"
                    ></el-input>
                  </div>

                  <div class="form-item">
                    <div class="form-label">转人工</div>
                    <el-switch
                      v-model="configData.enableHumanTransfer"
                    ></el-switch>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request"
import { clearChatMemory, formatOutputContent as formatOutput } from "@/utils"
import { VoiceUtils } from "@/utils/voice"

export default {
  name: "WebEmbedConfigPage",
  data() {
    return {
      activeCollapse: "basic",
      clientId: "",
      sessionId: "", // 会话ID
      correspondingAppIdList: [],
      workflowList: [],
      clientInfo: {
        sourceType: null,
        correspondingAppId: null,
      },
      configData: {
        websiteName: "",
        appName: "",
        correspondingAppId: "",
        channelType: 1,
        title: "",
        subtitle: "",
        welcomeMessage: "",
        inputBoxPrompt: "",
        theme: "",
        backgroundColor: "#409EFF",
        isCanVoice: false,
        isVoiceReading: false,
        isOneClickCopy: false,
        isCanClearRecord: false,
        voiceId: "", // 声音选项ID
        botAvatarUrl: "",
        bottomTitle: "",
        websiteUrl: "",
        enableHumanTransfer: false,
      },
      // 聊天相关数据
      chatMessages: [], // 聊天消息数组
      userMessage: "", // 用户输入消息
      isTyping: false, // 是否正在输入（机器人回复中）
      isStreaming: false, // 是否正在流式输出
      streamController: null, // 流式输出控制器

      // 语音相关数据
      voiceRecorder: null, // 语音录制器
      voiceRecognition: null, // 语音识别器
      isRecording: false, // 是否正在录音
      showVoiceModalFlag: false, // 是否显示语音模态框
      voiceSupported: false, // 浏览器是否支持语音功能
      recordingDuration: 0, // 录音时长(ms)
      recordingProgress: 0, // 录音进度(0-100)
      recordingTip: "长按说话", // 录音提示文字
      maxRecordingDuration: 60000, // 最大录音时长(ms)
      minRecordingDuration: 1000, // 最小录音时长(ms)

      // 语音播报相关 - 使用公共语音工具
      voiceTTS: null, // 语音播报工具实例

      // 图片上传相关
      uploadedImageUrl: null, // 上传的图片URL
    };
  },
  computed: {
    containerHeight() {
      // 计算容器高度，减去页头高度和页面内边距
      return window.innerHeight - 120; // 页面padding 20px，页头高度约 80px
    },
    // 计算悬停状态下的按钮颜色（略微浅一点的背景色）
    hoverBackgroundColor() {
      // 如果没有设置背景色，使用默认蓝色
      if (!this.configData.backgroundColor) {
        return "#66b1ff";
      }

      // 将hex颜色转换为rgb，然后调亮
      let hex = this.configData.backgroundColor.replace("#", "");
      let r = parseInt(hex.substr(0, 2), 16);
      let g = parseInt(hex.substr(2, 2), 16);
      let b = parseInt(hex.substr(4, 2), 16);

      // 调亮颜色 (增加亮度)
      r = Math.min(r + 25, 255);
      g = Math.min(g + 25, 255);
      b = Math.min(b + 25, 255);

      return `rgb(${r}, ${g}, ${b})`;
    },
    // 基础URL
    baseUrl() {
      // 使用环境变量
      return process.env.VUE_APP_CLIENT_URL;
    },
    // 根据选择的智能体类型获取flowDetailType
    flowDetailType() {
      if (!this.configData.correspondingAppId) {
        return "SessionFlow"; // 默认值
      }
      const appData = this.configData.correspondingAppId.split("_");
      const sourceType = appData[0] === "app" ? 1 : 2;
      return sourceType === 1 ? "SessionFlow" : "WorkFlow";
    },
    // 检查是否有流式消息
    hasStreamingMessage() {
      return this.chatMessages.some((message) => message.isStreaming);
    },

    // 获取朗读状态 - 使用公共方法
    isReading() {
      return this.voiceTTS ? this.voiceTTS.getState().isReading : false;
    },

    currentReadingMessageIndex() {
      return this.voiceTTS
        ? this.voiceTTS.getState().currentReadingMessageIndex
        : -1;
    },

    isLoadingAudio() {
      return this.voiceTTS ? this.voiceTTS.getState().isLoadingAudio : false;
    },
  },
  created() {
    // 从路由参数获取ID
    this.clientId = this.$route.params.id;
    // 生成sessionId
    this.sessionId = this.generateGuid();
    // 获取客户端详情
    this.fetchClientDetail();
    // 获取应用列表和工作流列表
    this.fetchcorrespondingAppIds();
    this.fetchWorkflows();
    // 监听窗口大小变化以更新容器高度
    window.addEventListener("resize", this.handleResize);
    // 初始化语音功能
    this.initVoiceFeatures();
    // 初始化语音播报功能
    this.initVoiceTTS();
  },
  beforeDestroy() {
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleResize);
    // 清理语音相关资源
    this.cleanupVoiceFeatures();
    // 清理语音播报资源
    if (this.voiceTTS) {
      this.voiceTTS.cleanup();
      this.voiceTTS = null;
    }
  },
  methods: {
    // 格式化输出内容函数 - 将其添加到methods中供模板使用
    formatOutputContent(text) {
      return formatOutput(text);
    },

    // 生成GUID方法
    generateGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    // 格式化时间
    formatTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
    },

    // 发送消息
    async sendMessage() {
      // 检查消息是否为空或正在输入/流式输出中
      if (!this.userMessage.trim() || this.isTyping || this.isStreaming) {
        return;
      }

      const message = this.userMessage.trim();
      const sessionId = this.sessionId;
      const imageUrl = this.uploadedImageUrl; // 保存图片URL到临时变量

      // 添加用户消息到消息列表
      this.addMessage({
        role: "user",
        content: message,
        imageUrl: imageUrl, // 使用临时变量
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(),
      });

      // 清空输入框
      this.userMessage = "";

      // 清除上传的图片
      this.uploadedImageUrl = null;

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      if (!sessionId) {
        this.$showFriendlyError(null, "会话未初始化，无法发送消息");
        return;
      }

      try {
        // 检查是否选择了对话应用
        if (!this.configData.correspondingAppId) {
          throw new Error("请先选择对话应用");
        }

        // 触发消息发送事件
        this.$emit("message-sent", {
          content: message,
          sessionId: sessionId,
          time: this.formatTime(new Date()),
        });

        // 根据智能体类型设置状态
        if (this.configData.correspondingAppId.startsWith("workflow_")) {
          this.isStreaming = true; // 工作流使用流式状态
          this.isTyping = false;
        } else {
          this.isStreaming = true; // 应用也使用流式状态
          this.isTyping = false;
        }

        const workflowResult = await this.executeWorkflow(
          message,
          sessionId,
          imageUrl
        );
        console.log("工作流接口请求成功，返回结果:", workflowResult);

        // 现在所有类型（智能体和工作流）都使用流式处理，executeWorkflow 已经处理了消息的添加和更新
        console.log("流式响应已处理完成，workflowResult:", workflowResult);
      } catch (error) {
        console.error("工作流接口请求失败:", error);
        this.addMessage({
          role: "assistant",
          content: formatOutput(`发送失败: ${error.message}`),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });
      } finally {
        // 清理状态
        this.isTyping = false;
        // 现在所有类型都使用流式处理，isStreaming 状态由流式回调处理，不需要手动清理
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 执行工作流
    async executeWorkflow(message, sessionId, imageUrl = null) {
      // 解析应用ID和类型
      const appData = this.configData.correspondingAppId.split("_");
      const sourceType = appData[0] === "app" ? 1 : 2;
      const flowDetailType = sourceType === 1 ? "SessionFlow" : "WorkFlow";
      const correspondingAppId = appData[1];

      // 根据sourceType决定调用不同的API接口
      // let result;

      if (sourceType === 1) {
        // 应用调用 - 以流式方式执行工作流
        const params = {
          sessionId: sessionId,
          flowDetailType: flowDetailType,
          flowId: correspondingAppId,
          textInput: message,
          imageInput: imageUrl || "",
          fileInput: "",
          customVariables: {},
        };

        console.log("执行应用API调用（流式）:", params);

        return new Promise((resolve, reject) => {
          let fullResponse = "";
          let isCompleted = false;
          let assistantMessage = null;

          // 创建流式控制器
          this.streamController = new AbortController();

          // 先创建一个空的助手消息并添加到界面
          assistantMessage = {
            role: "assistant",
            content: formatOutput('正在思考中...'),
            time: this.formatTime(new Date()),
            messageId: this.generateGuid(),
            fromWorkflow: true,
            isStreaming: true,
            knowledgeFiles: [] // 添加知识库文件列表
          };

          console.log("添加初始流式消息:", assistantMessage);
          this.addMessage(assistantMessage);

          api.workflow
            .stream(
              params,
              // onMessage 回调 - 实时更新消息内容
              (messageData) => {
                console.log("web-embed 收到流式消息:", messageData);

                // 处理知识库节点数据 - 检查NodeType是否包含'knowledge'
                if (messageData.NodeType && messageData.NodeType.includes('knowledge') && messageData.Output && Array.isArray(messageData.Output)) {
                  console.log('检测到知识库节点，处理文件信息:', messageData.Output);
                  console.log('当前已有文件数量:', assistantMessage.knowledgeFiles.length);

                  // 创建当前所有文件ID的Set用于去重
                  const existingFileIds = new Set(assistantMessage.knowledgeFiles.map(f => f.fileId));

                  messageData.Output.forEach((item, index) => {
                    console.log(`处理第${index + 1}个文件:`, {
                      FileId: item.FileId,
                      FileName: item.FileName,
                      exists: existingFileIds.has(item.FileId)
                    });

                    if (item.FileId && item.FileName && !existingFileIds.has(item.FileId)) {
                      const newFile = {
                        fileId: item.FileId,
                        fileName: item.FileName,
                        score: item.Score || 0,
                        ossUrl: item.OssUrl || ''
                      };
                      assistantMessage.knowledgeFiles.push(newFile);
                      existingFileIds.add(item.FileId);
                      console.log('添加新文件:', newFile);
                    } else {
                      console.log('跳过文件（已存在或无效）:', item.FileId, item.FileName);
                    }
                  });

                  console.log('更新后的知识库文件列表:', assistantMessage.knowledgeFiles);
                  console.log('文件数量:', assistantMessage.knowledgeFiles.length);

                  // 强制更新消息，确保界面显示
                  this.updateMessage(assistantMessage.messageId, {
                    knowledgeFiles: [...assistantMessage.knowledgeFiles],
                    isStreaming: true
                  });

                  // 额外触发一次Vue响应式更新
                  this.$nextTick(() => {
                    console.log('NextTick检查: 消息中的知识库文件数量:',
                      this.chatMessages.find(m => m.messageId === assistantMessage.messageId)?.knowledgeFiles?.length || 0
                    );
                  });

                  // 知识库节点不输出文本内容，直接返回，不处理后续的流式输出
                  return;
                }

                // 处理流式输出内容
                let responseContent = '';
                let isStreamComplete = false;

                // 新的流式接口格式：检查 Type 为 "streaming" 的消息
                if (messageData.Type === "streaming" && messageData.Output) {
                  responseContent = messageData.Output;
                  isStreamComplete = messageData.IsEnd === true;
                  console.log("流式消息:", {
                    content: responseContent,
                    isEnd: messageData.IsEnd,
                    type: messageData.Type
                  });
                }
                // 兼容旧格式：检查 Data 字段的内容
                else if (messageData.IsSuccess && messageData.Data) {
                  // 优先检查 Response 字段
                  if (messageData.Data.Response) {
                    responseContent = messageData.Data.Response;
                  }
                  // 当 CurrentType === 2 时，检查 Output 字段
                  else if (messageData.Data.CurrentType === 2 && messageData.Data.Output) {
                    responseContent = messageData.Data.Output;
                  }
                  isStreamComplete = messageData.Data.IsFinal === true;
                }

                if (responseContent) {
                  fullResponse += responseContent;

                  console.log("累积响应内容:", {
                    responseContent,
                    fullResponse,
                    isStreamComplete,
                  });

                  // 实时更新消息内容
                  assistantMessage.content = formatOutput(fullResponse);
                  assistantMessage.isStreaming = !isStreamComplete;

                  // 更新界面中的消息
                  this.updateMessage(
                    assistantMessage.messageId,
                    assistantMessage
                  );

                  // 如果是最后一条消息，完成处理
                  if (isStreamComplete && !isCompleted) {
                    isCompleted = true;
                    console.log("流式响应完成，最终内容:", fullResponse);
                    // 清理流式状态
                    this.isStreaming = false;
                    this.streamController = null;
                    resolve(assistantMessage);
                  }
                }
                // 处理流式结束消息
                else if (messageData.Type === "end") {
                  if (!isCompleted) {
                    isCompleted = true;
                    console.log("收到流式结束信号，最终内容:", fullResponse);
                    // 清理流式状态
                    this.isStreaming = false;
                    this.streamController = null;
                    assistantMessage.content = formatOutput(
                      fullResponse || "我已收到您的消息，但暂时无法处理。"
                    );
                    assistantMessage.isStreaming = false;
                    this.updateMessage(
                      assistantMessage.messageId,
                      assistantMessage
                    );
                    resolve(assistantMessage);
                  }
                } else if (
                  messageData.ErrorCode !== null &&
                  messageData.ErrorCode !== undefined
                ) {
                  if (!isCompleted) {
                    isCompleted = true;
                    // 更新消息为错误状态
                    assistantMessage.content = formatOutput(
                      `抱歉，处理您的请求时出现了错误：${
                        messageData.Message || "流式响应出错"
                      }`
                    );
                    assistantMessage.isStreaming = false;
                    assistantMessage.hasError = true;
                    this.updateMessage(
                      assistantMessage.messageId,
                      assistantMessage
                    );
                    reject(new Error(messageData.Message || "流式响应出错"));
                  }
                }
              },
              // onError 回调
              (error) => {
                if (!isCompleted) {
                  isCompleted = true;
                  console.error("流式响应错误:", error);
                  // 清理流式状态
                  this.isStreaming = false;
                  this.streamController = null;
                  // 更新消息为错误状态
                  assistantMessage.content =
                    formatOutput("抱歉，处理您的请求时出现了错误。");
                  assistantMessage.isStreaming = false;
                  assistantMessage.hasError = true;
                  this.updateMessage(
                    assistantMessage.messageId,
                    assistantMessage
                  );
                  reject(error);
                }
              },
              // onComplete 回调
              () => {
                // 如果没有通过 IsFinal 完成，在这里完成
                if (!isCompleted) {
                  isCompleted = true;
                  // 清理流式状态
                  this.isStreaming = false;
                  this.streamController = null;
                  assistantMessage.content = formatOutput(
                    fullResponse || "我已收到您的消息，但暂时无法处理。"
                  );
                  assistantMessage.isStreaming = false;
                  this.updateMessage(
                    assistantMessage.messageId,
                    assistantMessage
                  );
                  console.log("通过 onComplete 完成，最终内容:", fullResponse);
                  resolve(assistantMessage);
                }
              },
              // 传递AbortController的signal
              this.streamController ? this.streamController.signal : null
            )
            .catch((error) => {
              if (!isCompleted) {
                isCompleted = true;
                console.error("流式请求失败:", error);
                // 清理流式状态
                this.isStreaming = false;
                this.streamController = null;
                // 更新消息为错误状态
                assistantMessage.content =
                  formatOutput("抱歉，处理您的请求时出现了错误。");
                assistantMessage.isStreaming = false;
                assistantMessage.hasError = true;
                this.updateMessage(
                  assistantMessage.messageId,
                  assistantMessage
                );
                reject(error);
              }
            });
        });
      } else if (sourceType === 2) {
        // 工作流调用 - 使用流式执行
        const params = {
          sessionId: sessionId,
          flowDetailType: flowDetailType,
          flowId: correspondingAppId,
          textInput: message,
          imageInput: imageUrl || "",
          fileInput: "",
          customVariables: {},
        };

        console.log("执行工作流API调用（流式）:", params);

        return new Promise((resolve, reject) => {
          let fullResponse = "";
          let isCompleted = false;
          let assistantMessage = null;

          // 创建流式控制器
          this.streamController = new AbortController();

          // 先创建一个空的助手消息并添加到界面
          assistantMessage = {
            role: "assistant",
            content: formatOutput('正在思考中...'),
            time: this.formatTime(new Date()),
            messageId: this.generateGuid(),
            fromWorkflow: true,
            isStreaming: true,
            knowledgeFiles: [] // 添加知识库文件列表
          };

          console.log("添加初始流式消息:", assistantMessage);
          this.addMessage(assistantMessage);

          api.workflow
            .executeStream(
              params,
              // onMessage 回调 - 实时更新消息内容
              (messageData) => {
                console.log("工作流流式消息:", messageData);

                // 处理知识库节点数据 - 检查NodeType是否包含'knowledge'
                if (messageData.NodeType && messageData.NodeType.includes('knowledge') && messageData.Output && Array.isArray(messageData.Output)) {
                  console.log('检测到知识库节点，处理文件信息:', messageData.Output);
                  console.log('当前已有文件数量:', assistantMessage.knowledgeFiles.length);

                  // 创建当前所有文件ID的Set用于去重
                  const existingFileIds = new Set(assistantMessage.knowledgeFiles.map(f => f.fileId));

                  messageData.Output.forEach((item, index) => {
                    console.log(`处理第${index + 1}个文件:`, {
                      FileId: item.FileId,
                      FileName: item.FileName,
                      exists: existingFileIds.has(item.FileId)
                    });

                    if (item.FileId && item.FileName && !existingFileIds.has(item.FileId)) {
                      const newFile = {
                        fileId: item.FileId,
                        fileName: item.FileName,
                        score: item.Score || 0,
                        ossUrl: item.OssUrl || ''
                      };
                      assistantMessage.knowledgeFiles.push(newFile);
                      existingFileIds.add(item.FileId);
                      console.log('添加新文件:', newFile);
                    } else {
                      console.log('跳过文件（已存在或无效）:', item.FileId, item.FileName);
                    }
                  });

                  console.log('更新后的知识库文件列表:', assistantMessage.knowledgeFiles);
                  console.log('文件数量:', assistantMessage.knowledgeFiles.length);

                  // 强制更新消息，确保界面显示
                  this.updateMessage(assistantMessage.messageId, {
                    knowledgeFiles: [...assistantMessage.knowledgeFiles],
                    isStreaming: true
                  });

                  // 额外触发一次Vue响应式更新
                  this.$nextTick(() => {
                    console.log('NextTick检查: 消息中的知识库文件数量:',
                      this.chatMessages.find(m => m.messageId === assistantMessage.messageId)?.knowledgeFiles?.length || 0
                    );
                  });

                  // 知识库节点不输出文本内容，直接返回，不处理后续的流式输出
                  return;
                }

                // 处理执行步骤信息
                if (messageData.NodeType && messageData.NodeId) {
                  // 更新工作流执行步骤
                  this.updateWorkflowExecutionSteps(
                    assistantMessage.messageId,
                    messageData
                  );
                  console.log("更新工作流执行步骤:", messageData);
                }

                // 处理流式输出内容
                let responseContent = '';
                let isStreamComplete = false;

                // 检查是否是错误消息
                if (messageData.Type === "error") {
                  console.error("工作流执行错误:", messageData.Output);

                  if (!isCompleted) {
                    isCompleted = true;
                    // 更新消息为错误状态
                    assistantMessage.content = formatOutput(
                      `抱歉，处理您的请求时出现了错误：${messageData.Output}`
                    );
                    assistantMessage.isStreaming = false;
                    assistantMessage.hasError = true;
                    this.updateMessage(
                      assistantMessage.messageId,
                      assistantMessage
                    );
                    this.isStreaming = false;
                    this.streamController = null;
                    reject(new Error(messageData.Output || "工作流执行出错"));
                  }
                  return;
                }
                // 处理流式结束消息
                else if (messageData.Type === "end") {
                  if (!isCompleted) {
                    isCompleted = true;
                    console.log("收到工作流流式结束信号，最终内容:", fullResponse);
                    // 清理流式状态
                    this.isStreaming = false;
                    this.streamController = null;
                    assistantMessage.content = formatOutput(
                      fullResponse || "我已收到您的消息，但暂时无法处理。"
                    );
                    assistantMessage.isStreaming = false;
                    this.updateMessage(
                      assistantMessage.messageId,
                      assistantMessage
                    );
                    resolve(assistantMessage);
                  }
                  return;
                }
                // 新的流式接口格式：检查 Type 为 "streaming" 的消息
                else if (messageData.Type === "streaming" && messageData.Output) {
                  responseContent = messageData.Output;
                  isStreamComplete = messageData.IsEnd === true;
                  console.log("工作流流式消息:", {
                    content: responseContent,
                    isEnd: messageData.IsEnd,
                    type: messageData.Type
                  });
                }
                // 兼容旧格式：处理其他Output消息
                else if (messageData.Output) {
                  // 只有当 IsEnd 为 true 时才显示输出内容
                  if (messageData.IsEnd === true) {
                    responseContent = messageData.Output;
                    isStreamComplete = true;
                    console.log("工作流最终响应内容:", {
                      response: messageData.Output,
                    });
                  } else {
                    // 对于中间步骤的输出，不更新消息内容，保持"正在思考中..."
                    console.log(
                      "工作流中间步骤输出（不显示）:",
                      messageData.Output
                    );
                    return;
                  }
                }

                if (responseContent) {
                  fullResponse += responseContent;

                  console.log("工作流累积响应内容:", {
                    responseContent,
                    fullResponse,
                    isStreamComplete,
                  });

                  // 实时更新消息内容
                  assistantMessage.content = formatOutput(fullResponse);
                  assistantMessage.isStreaming = !isStreamComplete;

                  // 更新界面中的消息
                  this.updateMessage(
                    assistantMessage.messageId,
                    assistantMessage
                  );

                  // 如果是最后一条消息，完成处理
                  if (isStreamComplete && !isCompleted) {
                    isCompleted = true;
                    console.log("工作流流式响应完成，最终内容:", fullResponse);
                    // 清理流式状态
                    this.isStreaming = false;
                    this.streamController = null;
                    resolve(assistantMessage);
                  }
                }
              },
              // onError 回调
              (error) => {
                if (!isCompleted) {
                  isCompleted = true;
                  console.error("工作流流式响应错误:", error);
                  // 清理流式状态
                  this.isStreaming = false;
                  this.streamController = null;
                  // 更新消息为错误状态
                  assistantMessage.content =
                    formatOutput("抱歉，处理您的请求时出现了错误。");
                  assistantMessage.isStreaming = false;
                  assistantMessage.hasError = true;
                  this.updateMessage(
                    assistantMessage.messageId,
                    assistantMessage
                  );
                  reject(error);
                }
              },
              // onComplete 回调
              () => {
                // 如果没有通过其他方式完成，在这里完成
                if (!isCompleted) {
                  isCompleted = true;
                  console.log("工作流流式执行完成，最终内容:", fullResponse);

                  // 标记所有执行步骤为已完成
                  if (
                    assistantMessage.executionSteps &&
                    assistantMessage.executionSteps.length > 0
                  ) {
                    assistantMessage.executionSteps.forEach((step) => {
                      step.isCurrent = false;
                      step.isCompleted = true;
                    });
                  }

                  // 清理流式状态
                  this.isStreaming = false;
                  this.streamController = null;
                  assistantMessage.content = formatOutput(
                    fullResponse || "我已收到您的消息，但暂时无法处理。"
                  );
                  assistantMessage.isStreaming = false;
                  this.updateMessage(
                    assistantMessage.messageId,
                    assistantMessage
                  );
                  resolve(assistantMessage);
                }
              },
              // 传递AbortController的signal
              this.streamController ? this.streamController.signal : null
            )
            .catch((error) => {
              if (!isCompleted) {
                isCompleted = true;
                console.error("工作流流式请求失败:", error);
                // 清理流式状态
                this.isStreaming = false;
                this.streamController = null;
                // 更新消息为错误状态
                assistantMessage.content =
                  formatOutput("抱歉，处理您的请求时出现了错误。");
                assistantMessage.isStreaming = false;
                assistantMessage.hasError = true;
                this.updateMessage(
                  assistantMessage.messageId,
                  assistantMessage
                );
                reject(error);
              }
            });
        });
      } else {
        throw new Error(`不支持的sourceType: ${sourceType}`);
      }
    },

    // 添加消息
    addMessage(message) {
      console.log("addMessage 被调用:", message);
      this.chatMessages.push(message);
      console.log("消息已添加，当前消息列表长度:", this.chatMessages.length);
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 更新消息（用于流式响应）
    updateMessage(messageId, updatedMessage) {
      console.log("updateMessage 被调用:", { messageId, updatedMessage });
      const index = this.chatMessages.findIndex(
        (msg) => msg.messageId === messageId
      );
      console.log(
        "找到消息索引:",
        index,
        "总消息数:",
        this.chatMessages.length
      );

      if (index !== -1) {
        const oldMessage = this.chatMessages[index];
        const newMessage = { ...oldMessage, ...updatedMessage };
        console.log("更新前消息:", oldMessage);
        console.log("更新后消息:", newMessage);

        // 使用Vue.set确保响应式更新
        this.$set(this.chatMessages, index, newMessage);

        // 如果消息还在流式传输中，不需要滚动到底部
        if (!updatedMessage.isStreaming) {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } else {
        console.error("未找到要更新的消息:", messageId);
      }
    },

    // 滚动聊天窗口到底部
    scrollToBottom() {
      if (this.$refs.chatMessages) {
        this.$refs.chatMessages.scrollTop =
          this.$refs.chatMessages.scrollHeight;
      }
    },

    // 处理窗口大小变化
    handleResize() {
      // 触发计算属性更新
      this.$forceUpdate();
    },
    // 头像上传前验证
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$showFriendlyError(null, "上传头像图片只能是图片格式!");
        return false;
      }
      if (!isLt2M) {
        this.$showFriendlyError(null, "上传头像图片大小不能超过 2MB!");
        return false;
      }
      return isImage && isLt2M;
    },

    // 上传头像处理
    async handleAvatarUpload(options) {
      const file = options.file;
      try {
        // 构建上传参数
        const uploadParams = {
          file: file,
          fileType: "image",
          folder: "bot_avatars",
        };
        // 使用upload方法上传文件
        const uploadRes = await api.oss.upload(uploadParams);

        if (uploadRes.data && uploadRes.data.fileUrl) {
          // 设置头像URL
          this.configData.botAvatarUrl = uploadRes.data.fileUrl;
          this.$message.success("上传成功");
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        console.error("上传头像失败:", error);
        this.$showFriendlyError(error, "上传失败，请稍后重试");
      }
    },
    // 获取客户端详情
    fetchClientDetail() {
      // 从API获取数据
      api.client
        .getDetail(this.clientId)
        .then((res) => {
          if (res && res.isSuccess && res.data) {
            const clientData = res.data;
            // 基础设置
            this.configData.websiteName = clientData.websiteName || "";
            this.configData.title = clientData.title || "";
            this.configData.subtitle = clientData.subtitle || "";
            this.configData.welcomeMessage =
              clientData.welcomeMessage ||
              "您好！我是您的AI助手，有什么可以帮您解答的？";
            this.configData.inputBoxPrompt =
              clientData.inputBoxPrompt || "请输入您的问题，Shift+回车换行";
            this.configData.backgroundColor =
              clientData.backgroundColor || "#409EFF";
            this.configData.isCanVoice = clientData.isCanVoice ? true : false;
            this.configData.isVoiceReading = clientData.isVoiceReading
              ? true
              : false;
            this.configData.isOneClickCopy = clientData.isOneClickCopy
              ? true
              : false;
            this.configData.isCanClearRecord = clientData.isCanClearRecord
              ? true
              : false;
            this.configData.voiceId = clientData.voiceId || "";

            // 设置应用ID和类型
            if (clientData.correspondingAppId) {
              this.configData.correspondingAppId =
                (clientData.sourceType === 1 ? "app_" : "workflow_") +
                clientData.correspondingAppId;
            }

            // 高级设置
            if (clientData.advancedSetting) {
              // 底部标语和跳转地址
              this.configData.bottomTitle =
                clientData.advancedSetting.bottomTitle;
              this.configData.websiteUrl =
                clientData.advancedSetting.websiteUrl;

              // 机器人头像
              this.configData.botAvatarUrl =
                clientData.advancedSetting.botAvatarUrl || "";

              // 转人工功能
              this.configData.enableHumanTransfer = clientData.advancedSetting
                .enableHumanTransfer
                ? true
                : false;
            }

            // 记录当前客户端的一些基本信息，用于后续保存
            this.configData.appName = clientData.appName || "";
            this.configData.channelType = clientData.channelType;
            this.clientInfo = {
              sourceType: clientData.sourceType,
              correspondingAppId: clientData.correspondingAppId,
            };

            console.log("成功获取客户端配置:", clientData);
          }
        })
        .catch((error) => {
          console.error("获取客户端详情失败:", error);
          this.$showFriendlyError(error, "获取客户端详情失败，请重试");
        });
    },

    // 获取应用列表
    async fetchcorrespondingAppIds() {
      try {
        const res = await api.sessionFlow.getList();
        if (res && res.isSuccess && res.data) {
          this.correspondingAppIdList = res.data.items;
        }
      } catch (error) {
        console.error("获取应用列表失败", error);
        this.$showFriendlyError(error, "获取应用列表失败");
      }
    },

    // 获取工作流列表
    async fetchWorkflows() {
      try {
        const res = await api.workflow.getList();
        if (res && res.isSuccess && res.data) {
          this.workflowList = res.data.items;
        }
      } catch (error) {
        console.error("获取工作流列表失败", error);
      }
    },
    // 保存配置
    handleSave() {
      // 验证是否上传了机器人头像
      if (!this.configData.botAvatarUrl) {
        this.$message.warning("请在高级设置中上传机器人头像");
        this.activeCollapse = "advanced";
        return;
      }

      // 如果选择了应用，解析应用ID和类型
      let sourceType = this.clientInfo.sourceType,
        correspondingAppId = this.clientInfo.correspondingAppId;

      if (this.configData.correspondingAppId) {
        const appData = this.configData.correspondingAppId.split("_");
        sourceType = appData[0] === "app" ? 1 : 2;
        correspondingAppId = appData[1];
      }

      // 构建保存的数据
      const saveData = {
        sourceType: sourceType,
        correspondingAppId: correspondingAppId,
        shareLink: "", // API要求字段，可为空
        websiteName: this.configData.websiteName,
        appStatus: 0, // 默认在线状态
        title: this.configData.title,
        subtitle: this.configData.subtitle,
        welcomeMessage: this.configData.welcomeMessage,
        inputBoxPrompt: this.configData.inputBoxPrompt,
        backgroundColor: this.configData.backgroundColor,
        isCanVoice: this.configData.isCanVoice,
        isVoiceReading: this.configData.isVoiceReading,
        isOneClickCopy: this.configData.isOneClickCopy,
        isCanClearRecord: this.configData.isCanClearRecord,
        voiceId: this.configData.voiceId,
        disablePages: [], // 默认无禁用页面
        // 高级设置
        advancedSetting: {
          bottomMark: "", // 底部图标标识，暂时未实现上传
          bottomTitle: this.configData.bottomTitle,
          botAvatarUrl: this.configData.botAvatarUrl,
          websiteUrl: this.configData.websiteUrl,
          enableHumanTransfer: this.configData.enableHumanTransfer,
        },
      };

      // 调用API保存配置
      api.client
        .configure(this.clientId, saveData)
        .then((res) => {
          if (res && res.isSuccess) {
            this.$message.success("配置保存成功");

            // 重置会话ID
            this.sessionId = this.generateGuid();

            // 切换到集成方式标签，让用户可以看到更新后的集成代码
            this.activeCollapse = "basic";

            // 清空聊天消息
            this.chatMessages = [];
            this.userMessage = "";

            // 停止输入状态
            this.isTyping = false;

            // this.$router.push("/access/channel");
          } else {
            this.$showFriendlyError({ message: res.message }, "保存失败");
          }
        })
        .catch((error) => {
          this.$showFriendlyError(error, "保存配置失败，请重试");
        });
    },
    // 返回上一页
    handleReturn() {
      this.$router.push("/access/client");
    },
    // 复制代码
    copyCode(type) {
      let code = "";

      switch (type) {
        case "script":
          code =
            '<script data-name="link-bot" defer ' +
            'client-id="' +
            this.clientId +
            '" ' +
            'session-id="' +
            this.sessionId +
            '" ' +
            'src="' +
            this.baseUrl +
            '/link-bot.min.js"' +
            "></" +
            "script>";
          break;
        case "link":
          // code = 'http://***************:3001' + "?clientId=" + this.clientId + "&sessionId=" + this.sessionId;
          code =
            this.baseUrl +
            "?clientId=" +
            this.clientId +
            "&sessionId=" +
            this.sessionId +
            "&flowDetailType=" +
            this.flowDetailType;
          break;
        case "iframe":
          code =
            '<iframe frameborder="0" height="800" width="100%" allow="microphone" ' +
            'src="' +
            this.baseUrl +
            "?clientId=" +
            this.clientId +
            "&sessionId=" +
            this.sessionId +
            "&flowDetailType=" +
            this.flowDetailType +
            '"' +
            "></iframe>";
          break;
      }

      // 尝试使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(code)
          .then(() => {
            this.$message.success("复制成功");
          })
          .catch(() => {
            this.fallbackCopyTextToClipboard(code);
          });
      } else {
        // 使用备用复制方法
        this.fallbackCopyTextToClipboard(code);
      }
    },

    // 备用的复制方法
    fallbackCopyTextToClipboard(text) {
      try {
        // 创建临时DOM元素
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // 确保元素不可见
        textArea.style.position = "fixed";
        textArea.style.left = "-9999px";
        textArea.style.top = "-9999px";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // 尝试执行复制命令
        const successful = document.execCommand("copy");
        if (successful) {
          this.$message.success("复制成功");
        } else {
          this.$message.warning("复制失败，请手动复制");
        }

        // 清理
        document.body.removeChild(textArea);
      } catch (err) {
        this.$showFriendlyError(err, "复制失败，请手动复制");
        console.error("复制失败:", err);
      }
    },
    // 停止流式输出
    stopStreaming() {
      if (this.streamController) {
        this.streamController.abort();
        this.streamController = null;
      }
      this.isStreaming = false;
      this.isTyping = false;

      // 找到正在流式传输的消息并停止
      const streamingMessage = this.chatMessages.find((msg) => msg.isStreaming);
      if (streamingMessage) {
        streamingMessage.isStreaming = false;
        streamingMessage.content +=
          '\n\n<div style="color: #999; font-style: italic;">已停止生成</div>';
        this.updateMessage(streamingMessage.messageId, streamingMessage);
      }

      this.$message.info("已停止生成");
    },
    // 重置聊天
    resetChat() {
      this.chatMessages = [];
      this.userMessage = "";
      this.uploadedImageUrl = null; // 清除上传的图片
      this.isTyping = false;
      this.isStreaming = false;
      this.streamController = null;
      // 重新生成会话ID
      this.sessionId = this.generateGuid();
    },

    // 处理底部标语点击事件
    handleBottomTitleClick() {
      if (this.configData.websiteUrl) {
        // 在新窗口中打开链接
        window.open(this.configData.websiteUrl, "_blank");
      }
    },

    // 处理图片点击事件 - 在新窗口中打开图片
    handleImageClick(event) {
      const img = event.target;
      if (img && img.tagName === "IMG") {
        // 处理消息中的图片
        if (img.classList.contains("md-image")) {
          window.open(img.src, "_blank");
        }
        // 处理预览图片
        else if (img.classList.contains("preview-image")) {
          window.open(img.src, "_blank");
        }
      }
    },

    // ===== 语音功能相关方法 =====

    // 显示语音模态框
    showVoiceModal() {
      if (!this.voiceSupported || this.isStreaming || this.isTyping) {
        return;
      }

      this.showVoiceModalFlag = true;
    },

    // 隐藏语音模态框
    hideVoiceModal() {
      this.showVoiceModalFlag = false;

      // 如果正在录音，则停止录音
      if (this.isRecording) {
        this.stopVoiceRecord();
      }
    },

    // 初始化语音功能
    initVoiceFeatures() {
      try {
        // 检查浏览器支持情况
        const support = VoiceUtils.checkSupport();
        this.voiceSupported = support.recording;

        console.log("语音功能支持情况:", support);

        if (this.voiceSupported) {
          // 初始化语音工具
          const voiceUtils = new VoiceUtils();

          // 创建录音器
          this.voiceRecorder = voiceUtils.createRecorder({
            maxDuration: this.maxRecordingDuration,
            minDuration: this.minRecordingDuration,
            onStart: this.handleVoiceRecordStart,
            onStop: this.handleVoiceRecordStop,
            onData: this.handleVoiceRecordData,
            onError: this.handleVoiceRecordError,
            onProgress: this.handleVoiceRecordProgress,
          });

          // 如果浏览器支持语音识别，也初始化识别器
          if (support.recognition) {
            this.voiceRecognition = voiceUtils.createRecognition({
              language: "zh-CN",
              continuous: false,
              interimResults: true,
              onResult: this.handleVoiceRecognitionResult,
              onError: this.handleVoiceRecognitionError,
              onStart: this.handleVoiceRecognitionStart,
              onEnd: this.handleVoiceRecognitionEnd,
            });
          }
        }

        // 初始化语音合成功能
        this.initSpeechSynthesis();
      } catch (error) {
        console.error("初始化语音功能失败:", error);
        this.voiceSupported = false;
      }
    },

    // 初始化语音合成功能
    initSpeechSynthesis() {
      try {
        if ("speechSynthesis" in window) {
          this.speechSynthesis = window.speechSynthesis;
          console.log("语音合成功能已初始化");
        } else {
          console.warn("浏览器不支持语音合成功能");
        }
      } catch (error) {
        console.error("初始化语音合成失败:", error);
      }
    },

    // 清理语音功能资源
    cleanupVoiceFeatures() {
      try {
        if (this.voiceRecorder) {
          this.voiceRecorder.cleanup();
          this.voiceRecorder = null;
        }

        if (this.voiceRecognition) {
          this.voiceRecognition.cancelRecognition();
          this.voiceRecognition = null;
        }

        // 停止语音朗读
        this.stopReading();

        this.isRecording = false;
        this.showVoiceModalFlag = false;
        this.recordingDuration = 0;
        this.recordingProgress = 0;
      } catch (error) {
        console.error("清理语音功能资源失败:", error);
      }
    },

    // 开始语音录制
    async startVoiceRecord(event) {
      try {
        // 阻止默认行为，避免在移动设备上出现意外的触摸行为
        event.preventDefault();

        if (this.isRecording || this.isStreaming || this.isTyping) {
          return;
        }

        if (!this.voiceSupported || !this.voiceRecorder) {
          this.$showFriendlyError(null, "当前设备不支持语音功能");
          return;
        }

        console.log("开始语音录制");

        // 重置录音状态
        this.recordingDuration = 0;
        this.recordingProgress = 0;
        this.recordingTip = "正在录音中...";

        await this.voiceRecorder.startRecording();
      } catch (error) {
        console.error("开始语音录制失败:", error);
        this.handleVoiceRecordError(error);
      }
    },

    // 停止语音录制
    async stopVoiceRecord(event) {
      try {
        if (event) {
          event.preventDefault();
        }

        // 如果只是要关闭模态框而没有在录音，直接隐藏
        if (!this.isRecording) {
          this.hideVoiceModal();
          return;
        }

        if (!this.voiceRecorder) {
          this.hideVoiceModal();
          return;
        }

        console.log("停止语音录制");
        this.recordingTip = "录音完成，正在处理...";

        await this.voiceRecorder.stopRecording();
      } catch (error) {
        console.error("停止语音录制失败:", error);
        this.handleVoiceRecordError(error);
      }
    },

    // 语音录制开始回调
    handleVoiceRecordStart() {
      console.log("语音录制开始");
      this.isRecording = true;
      this.recordingTip = "正在录音中... 松开结束";

      // 振动反馈（如果设备支持）
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    },

    // 语音录制停止回调
    handleVoiceRecordStop(audioBlob, duration) {
      console.log("语音录制停止", { duration, size: audioBlob.size });
      this.isRecording = false;
      this.recordingTip = "长按说话";
      this.recordingDuration = 0;
      this.recordingProgress = 0;

      // 隐藏模态框
      this.hideVoiceModal();
    },

    // 语音录制数据回调
    async handleVoiceRecordData(audioBlob, duration) {
      try {
        console.log("收到语音录制数据:", { size: audioBlob.size, duration });

        // 显示处理提示
        this.$message.info("正在处理语音，请稍等...");

        // 调用服务器语音转文字接口
        const recognizedText = await this.uploadAudioForRecognition(audioBlob);

        if (recognizedText && recognizedText.trim()) {
          // 将识别的文字填入输入框
          this.userMessage = recognizedText.trim();

          // 自动发送消息
          this.$nextTick(() => {
            this.sendMessage();
          });
        } else {
          // 如果没有识别到内容，尝试本地语音识别或提示用户手动输入
          if (this.voiceRecognition) {
            this.startVoiceRecognition();
          } else {
            this.$message.warning("未识别到语音内容，请重新录音或手动输入");

            // 聚焦到输入框
            this.$nextTick(() => {
              if (this.$refs.messageInput) {
                this.$refs.messageInput.focus();
              }
            });
          }
        }
      } catch (error) {
        console.error("处理语音录制数据失败:", error);

        // 服务器识别失败，尝试本地语音识别
        if (this.voiceRecognition) {
          console.log("服务器识别失败，尝试本地语音识别");
          this.startVoiceRecognition();
        } else {
          this.$showFriendlyError(null, "语音识别失败，请重新录音");
        }
      }
    },

    // 语音录制错误回调
    handleVoiceRecordError(error) {
      console.error("语音录制错误:", error);
      this.isRecording = false;
      this.recordingTip = "长按说话";
      this.recordingDuration = 0;
      this.recordingProgress = 0;

      // 隐藏模态框
      this.hideVoiceModal();

      let errorMessage = "语音录制失败";

      if (
        error.message.includes("Permission denied") ||
        error.message.includes("NotAllowedError")
      ) {
        errorMessage = "请允许访问麦克风权限";
      } else if (error.message.includes("NotFoundError")) {
        errorMessage = "未找到可用的麦克风设备";
      } else if (error.message.includes("录音时长不能少于")) {
        errorMessage = "录音时间太短，请长按录音";
      }

      this.$showFriendlyError(null, errorMessage);
    },

    // 语音录制进度回调
    handleVoiceRecordProgress(duration, progress) {
      this.recordingDuration = duration;
      this.recordingProgress = progress * 100;

      // 更新提示文字
      if (progress > 0.9) {
        this.recordingTip = "即将达到最大录音时长";
      } else {
        this.recordingTip = `正在录音中... 松开结束 (${VoiceUtils.formatDuration(
          duration
        )})`;
      }
    },

    // 开始语音识别
    startVoiceRecognition() {
      try {
        if (!this.voiceRecognition) {
          console.warn("语音识别器未初始化");
          return;
        }

        console.log("开始语音识别");
        this.voiceRecognition.startRecognition();
      } catch (error) {
        console.error("开始语音识别失败:", error);
        this.$message.warning("语音识别功能不可用，请手动输入文字");
      }
    },

    // 语音识别开始回调
    handleVoiceRecognitionStart() {
      console.log("语音识别开始");
      this.recordingTip = "正在识别语音...";
    },

    // 语音识别结果回调
    handleVoiceRecognitionResult(result) {
      console.log("语音识别结果:", result);

      if (result.finalTranscript) {
        // 将识别的文字填入输入框
        this.userMessage = result.finalTranscript.trim();

        // 自动发送消息
        if (this.userMessage) {
          this.$nextTick(() => {
            this.sendMessage();
          });
        }
      } else if (result.interimTranscript) {
        // 显示临时识别结果
        this.recordingTip = `识别中: ${result.interimTranscript}`;
      }
    },

    // 语音识别错误回调
    handleVoiceRecognitionError(error) {
      console.error("语音识别错误:", error);
      this.recordingTip = "长按说话";

      let errorMessage = "语音识别失败";

      if (error.message.includes("no-speech")) {
        errorMessage = "未检测到语音，请重新录音";
      } else if (error.message.includes("audio-capture")) {
        errorMessage = "音频捕获失败，请检查麦克风";
      } else if (error.message.includes("not-allowed")) {
        errorMessage = "请允许访问麦克风权限";
      }

      this.$message.warning(errorMessage);
    },

    // 语音识别结束回调
    handleVoiceRecognitionEnd() {
      console.log("语音识别结束");
      this.recordingTip = "长按说话";
    },

    // 获取语音按钮的提示文字
    getVoiceButtonTitle() {
      if (!this.voiceSupported) {
        return "当前设备不支持语音功能";
      }

      if (this.isRecording) {
        return "松开结束录音";
      }

      return "长按说话";
    },

    // 格式化录音时长显示
    formatRecordingDuration(ms) {
      return VoiceUtils.formatDuration(ms);
    },

    // 上传音频进行语音识别
    async uploadAudioForRecognition(audioBlob) {
      try {
        console.log(
          "开始上传音频进行语音识别，音频大小:",
          audioBlob.size,
          "字节"
        );

        // 准备上传参数
        const uploadParams = {
          sessionID: this.sessionId,
          flowType: this.getFlowType(),
          appId: this.getAppId(),
        };

        console.log("语音识别参数:", uploadParams);

        // 调用语音转文字API
        const result = await api.voice.speechToText(audioBlob, uploadParams);

        console.log("语音识别结果:", result);

        if (result && result.isSuccess && result.data) {
          const recognizedText =
            result.data.text || result.data.recognizedText || result.data;
          console.log("识别到的文字:", recognizedText);
          return recognizedText;
        } else {
          console.warn("语音识别返回结果异常:", result);
          throw new Error(result?.message || "语音识别失败");
        }
      } catch (error) {
        console.error("上传音频识别失败:", error);

        // 如果是网络错误或服务器错误，抛出给上层处理
        if (error.response) {
          const errorMessage =
            error.response.data?.message ||
            error.response.data?.Message ||
            "服务器识别失败";
          throw new Error(errorMessage);
        } else if (error.message) {
          throw new Error(error.message);
        } else {
          throw new Error("语音识别服务暂时不可用");
        }
      }
    },

    // 获取工作流类型
    getFlowType() {
      if (!this.configData.correspondingAppId) {
        return "SessionFlow";
      }
      return this.configData.correspondingAppId.startsWith("workflow_")
        ? "WorkFlow"
        : "SessionFlow";
    },

    // 获取应用ID
    getAppId() {
      if (!this.configData.correspondingAppId) {
        return null;
      }
      const appData = this.configData.correspondingAppId.split("_");
      return appData[1] || null;
    },

    // 初始化语音播报功能
    initVoiceTTS() {
      try {
        // 创建语音播报工具实例
        const voiceUtils = new VoiceUtils();
        this.voiceTTS = voiceUtils.createTTS({
          preferServer: true,
          fallbackToLocal: true,
          onStart: (messageIndex) => {
            console.log("语音朗读开始:", messageIndex);
          },
          onStop: () => {
            console.log("语音朗读结束");
          },
          onError: (error, messageIndex) => {
            console.error("语音朗读错误:", error, messageIndex);
          },
        });

        console.log("语音播报功能已初始化");
      } catch (error) {
        console.error("初始化语音播报功能失败:", error);
      }
    },

    // 获取音频格式
    getAudioFormat(audioBlob) {
      // 根据audioBlob的type推断格式
      if (audioBlob.type) {
        if (audioBlob.type.includes("webm")) {
          return "webm";
        } else if (audioBlob.type.includes("mp4")) {
          return "mp4";
        } else if (audioBlob.type.includes("wav")) {
          return "wav";
        } else if (audioBlob.type.includes("ogg")) {
          return "ogg";
        }
      }
      // 默认返回webm
      return "webm";
    },

    // ===== 消息操作功能相关方法 =====

    // 语音朗读消息 - 使用公共方法
    async readMessage(content, messageIndex) {
      try {
        if (!this.voiceTTS) {
          this.$showFriendlyError(null, "语音播报功能未初始化");
          return;
        }

        // 准备TTS参数
        const ttsParams = {
          sessionID: this.sessionId,
          flowType: this.getFlowType(),
          appId: this.getAppId(),
        };

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg),
        };

        // 调用公共语音朗读方法
        await this.voiceTTS.readMessage(
          content,
          messageIndex,
          ttsParams,
          api,
          messageHandler
        );
      } catch (error) {
        console.error("语音朗读失败:", error);
        this.$showFriendlyError(error, "语音朗读功能出现错误");
      }
    },

    // 停止语音朗读 - 使用公共方法
    stopReading() {
      try {
        if (this.voiceTTS) {
          this.voiceTTS.stopReading();
        }
      } catch (error) {
        console.error("停止朗读失败:", error);
      }
    },

    // 复制消息内容 - 使用公共方法
    async copyMessage(content) {
      try {
        // 提取纯文本内容
        const textContent = VoiceUtils.extractTextContent(content);

        if (!textContent.trim()) {
          this.$message.warning("该消息没有可复制的文本内容");
          return;
        }

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg),
        };

        // 使用公共复制方法
        await VoiceUtils.copyToClipboard(textContent, messageHandler);
      } catch (error) {
        console.error("复制消息失败:", error);
        this.$showFriendlyError(error, "复制失败，请手动复制");
      }
    },

    // 清除聊天记忆
    async clearChatMemory() {
      try {
        const result = await clearChatMemory({
          sessionId: this.sessionId,
          api: api,
          messageHandler: {
            confirm: (message, title, options) =>
              this.$confirm(message, title, options),
            success: (message) => this.$message.success(message),
            error: (message) => this.$showFriendlyError(null, message),
          },
          clearLocalMessages: () => {
            // 清空聊天消息和输入框
            this.chatMessages = [];
            this.userMessage = "";

            // 重新生成会话ID，开始新会话
            this.sessionId = this.generateGuid();
          },
          stopStreaming: () => {
            // 停止流式输出（如果有）
            if (this.streamController) {
              this.streamController.abort();
              this.streamController = null;
            }
            this.isStreaming = false;
            this.isTyping = false;
          },
          stopReading: () => {
            // 停止当前的语音朗读
            this.stopReading();
          },
          addWelcomeMessage: () => {
            // web-embed不需要添加欢迎消息，因为模板中已经有固定的欢迎语显示
            // 聚焦到输入框
            this.$nextTick(() => {
              if (this.$refs.messageInput) {
                this.$refs.messageInput.focus();
              }
            });
          },
        });

        console.log("清除记忆操作结果:", result);
      } catch (error) {
        console.error("清除聊天记忆失败:", error);
        this.$showFriendlyError(error, "清除聊天记忆失败");
      }
    },

    // 获取语音朗读按钮的图标 - 使用公共方法
    getVoiceReadButtonIcon(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonIcon(messageIndex);
      }
      return "el-icon-video-play";
    },

    // 获取语音朗读按钮的提示文字 - 使用公共方法
    getVoiceReadButtonTitle(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonTitle(messageIndex);
      }
      return "语音朗读";
    },

    // 获取步骤显示名称
    getStepDisplayName(nodeType) {
      const stepNames = {
        StartNode: "开始",
        start: "开始",
        EndNode: "结束",
        end: "结束",
        LargeModeNode: "模型",
        largeModel: "模型",
        KnowledgeNode: "知识库",
        knowledge: "知识库",
        IntentBranchNode: "意图",
        intentBranch: "意图",
        LogicBranchNode: "逻辑",
        logicBranch: "逻辑",
        ApplicationNode: "应用",
        application: "应用",
        PluginNode: "插件",
        plugin: "插件",
        NewPluginNode: "插件",
        newPlugin: "插件",
        HumanTransferNode: "人工",
        humanTransfer: "人工",
        FixedContentNode: "固定",
        fixedContent: "固定",
        CodeNode: "代码",
        code: "代码",
      };
      return stepNames[nodeType] || nodeType;
    },

    // 更新工作流执行步骤
    updateWorkflowExecutionSteps(messageId, stepData) {
      const message = this.chatMessages.find(
        (msg) => msg.messageId === messageId
      );
      if (!message || !message.fromWorkflow) {
        return;
      }

      // 初始化执行步骤数组
      if (!message.executionSteps) {
        message.executionSteps = [];
      }

      // 查找是否已存在相同的步骤
      const existingStepIndex = message.executionSteps.findIndex(
        (step) => step.nodeId === stepData.NodeId
      );

      if (existingStepIndex >= 0) {
        // 更新现有步骤
        const existingStep = message.executionSteps[existingStepIndex];
        existingStep.isCurrent = stepData.CurrentType === 1;
        existingStep.isCompleted = stepData.IsEnd || stepData.CurrentType === 2;
      } else {
        // 添加新步骤
        const newStep = {
          nodeId: stepData.NodeId,
          nodeType: stepData.NodeType,
          isCurrent: stepData.CurrentType === 1,
          isCompleted: stepData.IsEnd || stepData.CurrentType === 2,
        };
        message.executionSteps.push(newStep);
      }

      // 更新所有之前的步骤为已完成状态
      if (stepData.CurrentType === 1 || stepData.IsEnd) {
        var currentStepIndex = message.executionSteps.findIndex(function (
          step
        ) {
          return step.nodeId === stepData.NodeId;
        });
        for (var i = 0; i < currentStepIndex; i++) {
          message.executionSteps[i].isCurrent = false;
          message.executionSteps[i].isCompleted = true;
        }
      }

      // 强制更新Vue组件
      this.$forceUpdate();
    },
    // 上传图片
    beforeImageUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$showFriendlyError(null, "上传图片只能是图片格式!");
        return false;
      }
      if (!isLt2M) {
        this.$showFriendlyError(null, "上传图片大小不能超过 2MB!");
        return false;
      }
      return isImage && isLt2M;
    },
    // 处理图片上传
    async handleImageUpload(options) {
      const file = options.file;
      let loadingMessage = null;

      try {
        // 显示上传中提示
        loadingMessage = this.$message({
          message: "图片上传中...",
          type: "info",
          duration: 0,
        });

        // 构建上传参数
        const uploadParams = {
          file: file,
          fileType: "image",
          folder: "bot_images",
        };

        // 使用upload方法上传文件
        const uploadRes = await api.oss.upload(uploadParams);

        // 关闭加载提示
        if (loadingMessage) {
          loadingMessage.close();
        }

        if (uploadRes.data && uploadRes.data.fileUrl) {
          // 设置图片URL
          this.uploadedImageUrl = uploadRes.data.fileUrl;
          this.$message.success("图片上传成功");
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        // 关闭加载提示
        if (loadingMessage) {
          loadingMessage.close();
        }

        console.error("上传图片失败:", error);
        this.$showFriendlyError(error, "图片上传失败，请稍后重试");
      }
    },
    // 移除图片
    removeUploadedImage() {
      this.uploadedImageUrl = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.web-embed-config-page {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-weight: 500;
    }
  }

  .page-content {
    height: calc(100% - 60px);

    .full-height-row {
      height: 100%;
    }

    .preview-container {
      background-color: #f5f7fa;
      border-radius: 8px;

      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      box-sizing: border-box;

      .mobile-preview {
        width: 100%;
        height: calc(100% - 20px); /* 减去padding */
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #dcdfe6;
        background-color: #fff;
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);

        .mobile-header {
          height: 50px;
          color: white;
          display: flex;
          align-content: center;
          justify-content: center;
          flex-direction: column;
          align-items: center;

          .mobile-title {
            font-size: 16px;
            font-weight: 500;
          }
          .mobile-subtitle {
            font-size: 12px;
            font-weight: 500;
          }
        }

        .mobile-content {
          height: calc(100% - 50px);
          display: flex;
          flex-direction: column;
          position: relative;

          .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background-color: #f9fafc;

            .message {
              margin-bottom: 15px;
              display: flex;

              .avatar {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                overflow: hidden;
                margin-right: 10px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .message-content {
                max-width: 70%;

                .message-image {
                  margin-bottom: 8px;

                  img {
                    max-width: 200px;
                    max-height: 150px;
                    border-radius: 8px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    cursor: pointer;
                    transition: all 0.3s ease;
                    object-fit: cover;

                    &:hover {
                      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                      transform: scale(1.02);
                    }
                  }
                }

                .message-actions {
                  margin-top: 8px;
                  display: flex;
                  gap: 8px;

                  .action-btn {
                    background: none;
                    border: 1px solid #e4e7ed;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 12px;
                    color: #606266;
                    cursor: pointer;
                    transition: all 0.3s;
                    display: flex;
                    align-items: center;

                    &:hover:not(:disabled) {
                      background-color: #f5f7fa;
                      border-color: #c0c4cc;
                      color: #409eff;
                    }

                    &:disabled {
                      opacity: 0.6;
                      cursor: not-allowed;
                    }

                    &.voice-read-btn {
                      &:hover:not(:disabled) {
                        color: #67c23a;
                        border-color: #67c23a;
                      }
                    }

                    &.copy-btn {
                      &:hover:not(:disabled) {
                        color: #e6a23c;
                        border-color: #e6a23c;
                      }
                    }

                    i {
                      font-size: 14px;

                      &.el-icon-loading {
                        animation: rotate 2s linear infinite;
                      }
                    }
                  }
                }

                .message-text {
                  background-color: white;
                  padding: 12px;
                  border-radius: 8px;
                  word-break: break-word;
                  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                  line-height: 1.5;

                  // 支持Markdown内容
                  :deep(.markdown-content) {
                    font-size: 14px;

                    h1,
                    h2,
                    h3,
                    h4,
                    h5,
                    h6 {
                      margin-top: 16px;
                      margin-bottom: 8px;
                      font-weight: 600;
                    }

                    .md-title-icon {
                      color: #909399;
                      margin-right: 4px;
                    }

                    p {
                      margin: 8px 0;
                    }

                    code.inline-code {
                      background-color: #f3f4f5;
                      padding: 0 4px;
                      border-radius: 3px;
                      font-family: monospace;
                      font-size: 12px;
                    }

                    .code-container {
                      margin: 12px 0;

                      .code-header {
                        background-color: #f5f7fa;
                        padding: 6px 12px;
                        font-size: 12px;
                        color: #909399;
                        border-radius: 4px 4px 0 0;
                        border: 1px solid #ebeef5;
                        border-bottom: none;
                      }

                      .code-block {
                        background-color: #f5f7fa;
                        padding: 12px;
                        border-radius: 0 0 4px 4px;
                        border: 1px solid #ebeef5;
                        overflow-x: auto;
                        font-family: monospace;
                        font-size: 12px;
                        line-height: 1.5;
                        white-space: pre-wrap;
                      }
                    }

                    ul,
                    ol {
                      padding-left: 20px;
                      margin: 12px 0;
                    }

                    blockquote {
                      margin: 12px 0;
                      padding: 8px 16px;
                      border-left: 4px solid #dcdfe6;
                      background-color: #f8f9fb;
                    }

                    .md-image-container {
                      margin: 12px 0;
                      text-align: center;
                      position: relative;
                    }

                    .md-image-loading {
                      color: #909399;
                      font-size: 12px;
                      padding: 20px;
                      background-color: #f5f7fa;
                      border-radius: 8px;
                      border: 1px dashed #dcdfe6;
                    }

                    .md-image {
                      max-width: 100%;
                      max-height: 300px;
                      border-radius: 8px;
                      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                      cursor: pointer;
                      transition: all 0.3s ease;
                      display: block;
                      margin: 0 auto;
                      object-fit: contain; // 保持图片比例

                      &:hover {
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                        transform: scale(1.02);
                      }

                      // base64图片特殊样式
                      &[alt*="base64"] {
                        background-color: #f8f9fa;
                        border: 1px solid #e9ecef;
                      }

                      // blob图片特殊样式
                      &[alt*="blob"] {
                        background-color: #fff3cd;
                        border: 1px solid #ffeaa7;
                      }
                    }

                    .image-fallback {
                      color: #f56c6c;
                      font-size: 12px;
                      word-break: break-all;
                      padding: 8px;
                      background-color: #fef0f0;
                      border-radius: 4px;
                      border: 1px solid #fbc4c4;
                      margin-top: 8px;

                      &:hover {
                        color: #f78989;
                        background-color: #fdf2f2;
                      }
                    }
                  }
                }
              }
            }

            .bot-message {
              align-self: flex-start;
            }

            .user-message {
              align-self: flex-end;
              flex-direction: row-reverse;

              .avatar {
                margin-right: 0;
                margin-left: 10px;
              }

              .message-content {
                .message-text {
                  background-color: #ecf5ff;
                  text-align: right;
                }
              }
            }

            .typing-message {
              .typing-indicator {
                display: flex;
                padding: 12px;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

                span {
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  margin: 0 2px;
                  background-color: #ddd;
                  border-radius: 50%;
                  animation: typing 1s infinite ease-in-out;

                  &:nth-child(1) {
                    animation-delay: 0s;
                  }

                  &:nth-child(2) {
                    animation-delay: 0.2s;
                  }

                  &:nth-child(3) {
                    animation-delay: 0.4s;
                  }
                }
              }
            }
          }

          .chat-input {
            height: 60px;
            border-top: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            padding: 0 15px;
            background-color: white;

            input {
              flex: 1;
              height: 40px;
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              padding: 0 15px;
              outline: none;
              transition: all 0.3s;

              &:focus {
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
              }
            }

            .image-uploader {
              margin-left: 5px;

              :deep(.el-upload) {
                display: flex;
                align-items: center;
              }

              .upload-btn {
                width: 40px;
                height: 40px;
                padding: 0;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                background-color: white;
                color: #606266;
                transition: all 0.3s;

                &:hover:not(:disabled) {
                  border-color: #409eff;
                  color: #409eff;
                }

                &:disabled {
                  background-color: #f5f7fa;
                  border-color: #e4e7ed;
                  color: #c0c4cc;
                  cursor: not-allowed;
                }

                i {
                  font-size: 18px;
                }
              }
            }

            .send-btn,
            .stop-btn,
            .voice-btn,
            .clear-memory-btn {
              width: 40px;
              height: 40px;
              border: none;
              color: white;
              border-radius: 4px;
              margin-left: 10px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s;

              &:hover {
                opacity: 0.85;
                transform: translateY(-1px);
              }

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
              }

              i {
                font-size: 20px;
              }
            }

            .stop-btn {
              background-color: #f56c6c;

              &:hover {
                background-color: #f78989;
              }
            }

            .clear-memory-btn {
              background-color: #e6a23c;

              &:hover:not(:disabled) {
                background-color: #ebb563;
              }

              &:disabled {
                background-color: #f5f7fa;
                color: #c0c4cc;
              }
            }

            .voice-btn {
              // 背景色由内联样式控制，这里不设置默认背景色

              &:hover:not(.disabled) {
                // 使用计算属性的hover背景色
                filter: brightness(1.1);
              }

              &.disabled {
                // disabled状态的背景色由内联样式控制
                cursor: not-allowed;

                &:hover {
                  opacity: 1;
                  transform: none;
                  filter: none;
                }
              }
            }
          }

          .voice-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;

            .voice-modal {
              background-color: white;
              border-radius: 12px;
              width: 320px;
              max-width: 90vw;
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
              overflow: hidden;

              .voice-modal-header {
                background-color: #4a90e2;
                color: white;
                padding: 16px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .voice-title {
                  font-size: 16px;
                  font-weight: 500;
                }

                .voice-close-btn {
                  background: none;
                  border: none;
                  color: white;
                  font-size: 18px;
                  cursor: pointer;
                  padding: 4px;
                  border-radius: 4px;
                  transition: all 0.3s;

                  &:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                  }
                }
              }

              .voice-modal-content {
                padding: 40px 20px;
                text-align: center;

                .voice-circle-container {
                  margin-bottom: 40px;

                  .voice-circle {
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background-color: #f0f0f0;
                    border: 3px solid #e0e0e0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto;
                    transition: all 0.3s;

                    &.recording {
                      background-color: #67c23a;
                      border-color: #67c23a;
                      animation: voice-recording-pulse 1.5s infinite;

                      .voice-icon {
                        color: white;
                        animation: voice-icon-bounce 0.6s infinite alternate;
                      }
                    }

                    .voice-icon {
                      font-size: 48px;
                      color: #909399;
                      transition: all 0.3s;
                    }
                  }
                }

                .voice-action-area {
                  .voice-record-btn {
                    // 背景色由内联样式控制
                    color: white;
                    border: none;
                    border-radius: 25px;
                    padding: 12px 24px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: all 0.3s;
                    min-width: 120px;

                    &:hover:not(.recording) {
                      filter: brightness(1.1);
                      transform: translateY(-1px);
                    }

                    &.recording {
                      // 录音状态的背景色由内联样式控制
                      animation: voice-btn-pulse 1s infinite;
                    }

                    i {
                      margin-right: 8px;
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }

          .mobile-footer {
            height: 18px;
            border-top: 1px solid #ebeef5;
            background-color: #f9fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px;

            .bottom-title {
              font-size: 12px;
              line-height: 12px;
              color: #909399;
              text-align: center;
              transition: all 0.3s;

              &.clickable {
                color: #409eff;
                cursor: pointer;

                &:hover {
                  color: #66b1ff;
                  opacity: 0.8;
                }
              }
            }
          }

          .image-preview-container-bottom {
            padding: 8px 15px;
            border-top: 1px solid #ebeef5;
            background-color: white;
            position: relative;

            .preview-label {
              font-size: 12px;
              color: #666;
              margin-bottom: 5px;
            }

            .image-preview {
              position: relative;
              display: inline-block;
              max-width: 80px;
              border-radius: 6px;
              overflow: hidden;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

              .preview-image {
                width: 80px;
                height: 60px;
                object-fit: cover;
                display: block;
                border-radius: 6px;
              }

              .remove-image-btn {
                position: absolute;
                top: 2px;
                right: 2px;
                width: 16px;
                height: 16px;
                border: none;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s;
                font-size: 10px;

                &:hover {
                  background-color: rgba(0, 0, 0, 0.9);
                }

                i {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }

    .config-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      overflow-y: auto;
      box-sizing: border-box;
      padding: 0;

      .config-section {
        padding: 20px;

        .form-item {
          margin-bottom: 24px;
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          align-content: center;
          justify-content: flex-start;
          align-items: center;

          .form-label {
            width: 100px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }

          .upload-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .preview-icon {
              width: 48px;
              height: 48px;
              margin-right: 10px;
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              background-color: #f5f7fa;
            }

            .upload-hint {
              margin-left: 10px;
              color: #909399;
              font-size: 12px;
            }
          }
        }

        .integration-method {
          margin-bottom: 30px;
          background-color: #fafafa;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #409eff;

          h4 {
            margin: 0 0 12px;
            font-size: 15px;
            color: #303133;
          }

          p {
            margin: 0 0 12px;
            color: #606266;
            font-size: 14px;
            line-height: 1.5;

            code {
              background-color: #f0f2f5;
              padding: 2px 6px;
              border-radius: 4px;
              color: #409eff;
              font-family: monospace;
            }
          }

          .code-block {
            position: relative;
            background-color: #f5f7fa;
            border-radius: 6px;
            padding: 20px;
            border: 1px solid #ebeef5;

            pre {
              margin: 0;
              overflow-x: auto;

              code {
                white-space: pre-wrap;
                font-family: "SFMono-Regular", Consolas, "Liberation Mono",
                  Menlo, monospace;
                color: #476582;
                font-size: 13px;
                line-height: 1.6;
              }
            }

            .copy-btn {
              position: absolute;
              top: 10px;
              right: 10px;
              transition: all 0.3s;

              &:hover {
                transform: translateY(-1px);
                opacity: 0.9;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes typing {
  0% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
}

// 流式传输指示器样式
.streaming-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;

  span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #409eff;
    margin: 0 1px;
    animation: streaming-pulse 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

@keyframes streaming-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 流式传输中的消息样式
.message-text.streaming {
  position: relative;

  &::after {
    content: "";
    display: inline-block;
    width: 2px;
    height: 1em;
    background-color: #409eff;
    margin-left: 2px;
    animation: cursor-blink 1s infinite;
  }
}

@keyframes cursor-blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

// 语音按钮脉冲动画
@keyframes voice-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 语音录制脉冲动画
@keyframes voice-recording-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(103, 194, 58, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

// 语音图标跳动动画
@keyframes voice-icon-bounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

// 语音按钮脉冲动画
@keyframes voice-btn-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 错误消息样式
.message-text.error {
  color: #f56c6c;
  border-left: 3px solid #f56c6c;
  padding-left: 8px;
}

// 旋转动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 知识库文件标签样式
.knowledge-files {
  margin-top: 8px;
  padding-left: 12px;
  border-left: 3px solid #0969da;
  background-color: rgba(9, 105, 218, 0.03);
  border-radius: 0 12px 12px 0;

  .knowledge-files-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    color: #0969da;
    font-weight: bold;

    i {
      margin-right: 4px;
      font-size: 14px;
    }
  }

  .knowledge-files-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .knowledge-file-tag {
      display: flex;
      align-items: center;
      background-color: #e1f3d8;
      border: 1px solid #a5d6a7;
      color: #4caf50;
      border-radius: 12px;
      padding: 4px 8px;
      font-size: 11px;
      font-weight: 500;
      transition: all 0.3s ease;
      cursor: default;

      &:hover {
        background-color: #c8e6c9;
        border-color: #81c784;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
      }

      i {
        margin-right: 3px;
        font-size: 12px;
      }
    }
  }
}

// 工作流执行步骤样式
.workflow-execution-steps {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;

  .execution-steps-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .execution-step {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      background-color: #ffffff;
      border-radius: 16px;
      border: 1px solid #dee2e6;
      transition: all 0.3s ease;
      font-size: 12px;

      .step-indicator {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 6px;
        font-size: 8px;
        font-weight: 500;
        flex-shrink: 0;
        border: 1px solid #dee2e6;
        background-color: #ffffff;
        color: #6c757d;
        transition: all 0.3s ease;

        .step-number {
          font-size: 8px;
          line-height: 1;
        }

        i {
          font-size: 9px;

          &.el-icon-loading {
            animation: rotate 1s linear infinite;
          }
        }
      }

      .step-content {
        .step-label {
          font-size: 11px;
          color: #495057;
          line-height: 1.2;
          font-weight: 400;
        }
      }

      // 当前执行的步骤
      &.current {
        border-color: #409eff;
        background-color: #ecf5ff;

        .step-indicator {
          border-color: #409eff;
          background-color: #409eff;
          color: #ffffff;
          animation: step-pulse 1.5s infinite ease-in-out;
        }

        .step-content .step-label {
          color: #409eff;
          font-weight: 500;
        }
      }

      // 已完成的步骤
      &.completed {
        border-color: #67c23a;
        background-color: #f0f9ff;

        .step-indicator {
          border-color: #67c23a;
          background-color: #67c23a;
          color: #ffffff;
        }

        .step-content .step-label {
          color: #67c23a;
          font-weight: 400;
        }
      }

      // 未开始的步骤
      &:not(.current):not(.completed) {
        opacity: 0.7;
      }
    }
  }
}

// 步骤脉冲动画
@keyframes step-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

:deep(.el-collapse) {
  border: none;
}

:deep(.el-collapse-item__header) {
  font-weight: 500;
  font-size: 16px;
  height: 56px;
  line-height: 56px;
  padding: 0 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f9fafc;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.is-active {
    border-bottom-color: #ebeef5;
  }
}

:deep(.el-collapse-item__wrap) {
  border-bottom: none;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}

:deep(.el-input .el-input__inner) {
  transition: all 0.3s;

  &:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  padding-left: 8px;
}

:deep(.el-color-picker) {
  vertical-align: middle;
}

:deep(.el-button--primary) {
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
}

.full-width {
  width: 100%;
}
</style>
