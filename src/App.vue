<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      isRouteRefreshing: false
    }
  },
  methods: {
    // 提供全局刷新方法
    reload() {
      this.isRouteRefreshing = true
      this.$nextTick(() => {
        this.isRouteRefreshing = false
      })
    }
  }
}
</script>

<style lang="scss">
#app {
  height: 100%;
}
</style>
