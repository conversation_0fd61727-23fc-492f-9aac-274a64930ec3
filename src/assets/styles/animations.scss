/* 卡片动画样式 */

/* 淡入上升动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬浮效果 */
.card-hover-effect {
  transition: all 0.3s ease-in-out;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--el-color-primary-light-5);
  }

  &:active {
    transform: scale(0.98);
  }
}

/* 卡片淡入动画 */
.card-fade-in {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform-origin: center;

  /* 使用 --card-index 变量来控制每个卡片的延迟 */
  &:not(.create-card) {
    animation-delay: calc(var(--card-index, 0) * 0.1s);
  }

  /* 创建卡片的延迟较长 */
  &.create-card {
    animation-delay: 0.3s;
  }
}

/* 图标旋转动画 */
.icon-rotate-effect {
  i {
    transition: all 0.3s ease;
  }

  &:hover i {
    transform: rotate(90deg);
    color: var(--el-color-primary);
  }
}

/* 内容元素的过渡效果 */
.content-transition {
  transition: color 0.3s ease, background-color 0.3s ease;
}
