<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4884519" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe724;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe724;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe725;</span>
                <div class="name">工作流</div>
                <div class="code-name">&amp;#xe725;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe726;</span>
                <div class="name">数据库</div>
                <div class="code-name">&amp;#xe726;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe727;</span>
                <div class="name">知识库</div>
                <div class="code-name">&amp;#xe727;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe728;</span>
                <div class="name">插件</div>
                <div class="code-name">&amp;#xe728;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70d;</span>
                <div class="name">shoucang</div>
                <div class="code-name">&amp;#xe70d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe723;</span>
                <div class="name">yulancopy</div>
                <div class="code-name">&amp;#xe723;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71e;</span>
                <div class="name">智能客服</div>
                <div class="code-name">&amp;#xe71e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71f;</span>
                <div class="name">工作学习</div>
                <div class="code-name">&amp;#xe71f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe720;</span>
                <div class="name">我的收藏</div>
                <div class="code-name">&amp;#xe720;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe721;</span>
                <div class="name">应用广场</div>
                <div class="code-name">&amp;#xe721;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe722;</span>
                <div class="name">模版中心</div>
                <div class="code-name">&amp;#xe722;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70e;</span>
                <div class="name">渠道接入</div>
                <div class="code-name">&amp;#xe70e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">游戏娱乐</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">图像绘画</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71a;</span>
                <div class="name">其他</div>
                <div class="code-name">&amp;#xe71a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71b;</span>
                <div class="name">数字分身</div>
                <div class="code-name">&amp;#xe71b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">文本创作</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71d;</span>
                <div class="name">生活帮手</div>
                <div class="code-name">&amp;#xe71d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">团队空间</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe713;</span>
                <div class="name">数据看板</div>
                <div class="code-name">&amp;#xe713;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe714;</span>
                <div class="name">我的账户</div>
                <div class="code-name">&amp;#xe714;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe715;</span>
                <div class="name">空间与成员管理</div>
                <div class="code-name">&amp;#xe715;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe716;</span>
                <div class="name">角色权限设置</div>
                <div class="code-name">&amp;#xe716;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">积分配额管理</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">对话管理</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe710;</span>
                <div class="name">客户端</div>
                <div class="code-name">&amp;#xe710;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">wendang</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe703;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe703;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe704;</span>
                <div class="name">shuaxin</div>
                <div class="code-name">&amp;#xe704;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe705;</span>
                <div class="name">tongzhi</div>
                <div class="code-name">&amp;#xe705;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe706;</span>
                <div class="name">对话api</div>
                <div class="code-name">&amp;#xe706;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe707;</span>
                <div class="name">微信公众号（企业）</div>
                <div class="code-name">&amp;#xe707;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe708;</span>
                <div class="name">网页嵌入</div>
                <div class="code-name">&amp;#xe708;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe709;</span>
                <div class="name">企业微信</div>
                <div class="code-name">&amp;#xe709;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70a;</span>
                <div class="name">微信</div>
                <div class="code-name">&amp;#xe70a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70b;</span>
                <div class="name">微信公众号（个人）</div>
                <div class="code-name">&amp;#xe70b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70c;</span>
                <div class="name">微信客服</div>
                <div class="code-name">&amp;#xe70c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fe;</span>
                <div class="name">yulan</div>
                <div class="code-name">&amp;#xe6fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ff;</span>
                <div class="name">查发票</div>
                <div class="code-name">&amp;#xe6ff;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe700;</span>
                <div class="name">查物流</div>
                <div class="code-name">&amp;#xe700;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe701;</span>
                <div class="name">数据库</div>
                <div class="code-name">&amp;#xe701;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe702;</span>
                <div class="name">查订单</div>
                <div class="code-name">&amp;#xe702;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fc;</span>
                <div class="name">选中</div>
                <div class="code-name">&amp;#xe6fc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">未选中</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f8;</span>
                <div class="name">网页</div>
                <div class="code-name">&amp;#xe6f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f9;</span>
                <div class="name">问答</div>
                <div class="code-name">&amp;#xe6f9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fa;</span>
                <div class="name">文档</div>
                <div class="code-name">&amp;#xe6fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fb;</span>
                <div class="name">表格</div>
                <div class="code-name">&amp;#xe6fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f7;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe6f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f6;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe6f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f5;</span>
                <div class="name">暂无数据库</div>
                <div class="code-name">&amp;#xe6f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f3;</span>
                <div class="name">图像识别</div>
                <div class="code-name">&amp;#xe6f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f4;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe6f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ea;</span>
                <div class="name">tishi</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f0;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe6f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f1;</span>
                <div class="name">绑定</div>
                <div class="code-name">&amp;#xe6f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f2;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ef;</span>
                <div class="name">创造</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ee;</span>
                <div class="name">增加</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e8;</span>
                <div class="name">fenxiang</div>
                <div class="code-name">&amp;#xe6e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e9;</span>
                <div class="name">管理</div>
                <div class="code-name">&amp;#xe6e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6eb;</span>
                <div class="name">faxian</div>
                <div class="code-name">&amp;#xe6eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ec;</span>
                <div class="name">接入</div>
                <div class="code-name">&amp;#xe6ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ed;</span>
                <div class="name">fuzhi</div>
                <div class="code-name">&amp;#xe6ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e7;</span>
                <div class="name">shezhi</div>
                <div class="code-name">&amp;#xe6e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e6;</span>
                <div class="name">工作流</div>
                <div class="code-name">&amp;#xe6e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e3;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e2;</span>
                <div class="name">插件</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e4;</span>
                <div class="name">知识库</div>
                <div class="code-name">&amp;#xe6e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">数据库</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1744251158573') format('woff2'),
       url('iconfont.woff?t=1744251158573') format('woff'),
       url('iconfont.ttf?t=1744251158573') format('truetype'),
       url('iconfont.svg?t=1744251158573#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-yingyong1"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.icon-yingyong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuoliu1"></span>
            <div class="name">
              工作流
            </div>
            <div class="code-name">.icon-gongzuoliu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujuku2"></span>
            <div class="name">
              数据库
            </div>
            <div class="code-name">.icon-shujuku2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhishiku1"></span>
            <div class="name">
              知识库
            </div>
            <div class="code-name">.icon-zhishiku1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chajian1"></span>
            <div class="name">
              插件
            </div>
            <div class="code-name">.icon-chajian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              shoucang
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yulancopy"></span>
            <div class="name">
              yulancopy
            </div>
            <div class="code-name">.icon-yulancopy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhinengkefu"></span>
            <div class="name">
              智能客服
            </div>
            <div class="code-name">.icon-zhinengkefu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuoxuexi"></span>
            <div class="name">
              工作学习
            </div>
            <div class="code-name">.icon-gongzuoxuexi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wodeshoucang"></span>
            <div class="name">
              我的收藏
            </div>
            <div class="code-name">.icon-wodeshoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingyongguangchang"></span>
            <div class="name">
              应用广场
            </div>
            <div class="code-name">.icon-yingyongguangchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mobanzhongxin"></span>
            <div class="name">
              模版中心
            </div>
            <div class="code-name">.icon-mobanzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qudaojieru"></span>
            <div class="name">
              渠道接入
            </div>
            <div class="code-name">.icon-qudaojieru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-youxiyule"></span>
            <div class="name">
              游戏娱乐
            </div>
            <div class="code-name">.icon-youxiyule
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuxianghuihua"></span>
            <div class="name">
              图像绘画
            </div>
            <div class="code-name">.icon-tuxianghuihua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qita"></span>
            <div class="name">
              其他
            </div>
            <div class="code-name">.icon-qita
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuzifenshen"></span>
            <div class="name">
              数字分身
            </div>
            <div class="code-name">.icon-shuzifenshen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenbenchuangzuo"></span>
            <div class="name">
              文本创作
            </div>
            <div class="code-name">.icon-wenbenchuangzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenghuobangshou"></span>
            <div class="name">
              生活帮手
            </div>
            <div class="code-name">.icon-shenghuobangshou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuanduikongjian"></span>
            <div class="name">
              团队空间
            </div>
            <div class="code-name">.icon-tuanduikongjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujukanban"></span>
            <div class="name">
              数据看板
            </div>
            <div class="code-name">.icon-shujukanban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wodezhanghu"></span>
            <div class="name">
              我的账户
            </div>
            <div class="code-name">.icon-wodezhanghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kongjianyuchengyuanguanli"></span>
            <div class="name">
              空间与成员管理
            </div>
            <div class="code-name">.icon-kongjianyuchengyuanguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaosequanxianshezhi"></span>
            <div class="name">
              角色权限设置
            </div>
            <div class="code-name">.icon-jiaosequanxianshezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jifenpeieguanli"></span>
            <div class="name">
              积分配额管理
            </div>
            <div class="code-name">.icon-jifenpeieguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duihuaguanli"></span>
            <div class="name">
              对话管理
            </div>
            <div class="code-name">.icon-duihuaguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kehuduan"></span>
            <div class="name">
              客户端
            </div>
            <div class="code-name">.icon-kehuduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wendang1"></span>
            <div class="name">
              wendang
            </div>
            <div class="code-name">.icon-wendang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuaxin"></span>
            <div class="name">
              shuaxin
            </div>
            <div class="code-name">.icon-shuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongzhi"></span>
            <div class="name">
              tongzhi
            </div>
            <div class="code-name">.icon-tongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duihuaapi"></span>
            <div class="name">
              对话api
            </div>
            <div class="code-name">.icon-duihuaapi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixingongzhonghaoqiye"></span>
            <div class="name">
              微信公众号（企业）
            </div>
            <div class="code-name">.icon-a-weixingongzhonghaoqiye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wangyeqianru"></span>
            <div class="name">
              网页嵌入
            </div>
            <div class="code-name">.icon-wangyeqianru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiyeweixin"></span>
            <div class="name">
              企业微信
            </div>
            <div class="code-name">.icon-qiyeweixin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixin"></span>
            <div class="name">
              微信
            </div>
            <div class="code-name">.icon-weixin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixingongzhonghaogeren"></span>
            <div class="name">
              微信公众号（个人）
            </div>
            <div class="code-name">.icon-a-weixingongzhonghaogeren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixinkefu"></span>
            <div class="name">
              微信客服
            </div>
            <div class="code-name">.icon-weixinkefu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yulan"></span>
            <div class="name">
              yulan
            </div>
            <div class="code-name">.icon-yulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chafapiao"></span>
            <div class="name">
              查发票
            </div>
            <div class="code-name">.icon-chafapiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chawuliu"></span>
            <div class="name">
              查物流
            </div>
            <div class="code-name">.icon-chawuliu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujuku1"></span>
            <div class="name">
              数据库
            </div>
            <div class="code-name">.icon-shujuku1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chadingdan"></span>
            <div class="name">
              查订单
            </div>
            <div class="code-name">.icon-chadingdan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanzhong"></span>
            <div class="name">
              选中
            </div>
            <div class="code-name">.icon-xuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixuanzhong"></span>
            <div class="name">
              未选中
            </div>
            <div class="code-name">.icon-weixuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wangye"></span>
            <div class="name">
              网页
            </div>
            <div class="code-name">.icon-wangye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wenda"></span>
            <div class="name">
              问答
            </div>
            <div class="code-name">.icon-wenda
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wendang"></span>
            <div class="name">
              文档
            </div>
            <div class="code-name">.icon-wendang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoge"></span>
            <div class="name">
              表格
            </div>
            <div class="code-name">.icon-biaoge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jian"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.icon-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jia"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.icon-jia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zanwushujuku"></span>
            <div class="name">
              暂无数据库
            </div>
            <div class="code-name">.icon-zanwushujuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuxiangshibie"></span>
            <div class="name">
              图像识别
            </div>
            <div class="code-name">.icon-tuxiangshibie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.icon-fanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tishi"></span>
            <div class="name">
              tishi
            </div>
            <div class="code-name">.icon-tishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiala"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.icon-xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bangding"></span>
            <div class="name">
              绑定
            </div>
            <div class="code-name">.icon-bangding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chuangzao1"></span>
            <div class="name">
              创造
            </div>
            <div class="code-name">.icon-chuangzao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zengjia"></span>
            <div class="name">
              增加
            </div>
            <div class="code-name">.icon-zengjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxiang"></span>
            <div class="name">
              fenxiang
            </div>
            <div class="code-name">.icon-fenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanli"></span>
            <div class="name">
              管理
            </div>
            <div class="code-name">.icon-guanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-faxian"></span>
            <div class="name">
              faxian
            </div>
            <div class="code-name">.icon-faxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jieru"></span>
            <div class="name">
              接入
            </div>
            <div class="code-name">.icon-jieru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhi"></span>
            <div class="name">
              fuzhi
            </div>
            <div class="code-name">.icon-fuzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shezhi"></span>
            <div class="name">
              shezhi
            </div>
            <div class="code-name">.icon-shezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuoliu"></span>
            <div class="name">
              工作流
            </div>
            <div class="code-name">.icon-gongzuoliu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingyong"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.icon-yingyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chajian"></span>
            <div class="name">
              插件
            </div>
            <div class="code-name">.icon-chajian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhishiku"></span>
            <div class="name">
              知识库
            </div>
            <div class="code-name">.icon-zhishiku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shujuku"></span>
            <div class="name">
              数据库
            </div>
            <div class="code-name">.icon-shujuku
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingyong1"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#icon-yingyong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuoliu1"></use>
                </svg>
                <div class="name">工作流</div>
                <div class="code-name">#icon-gongzuoliu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuku2"></use>
                </svg>
                <div class="name">数据库</div>
                <div class="code-name">#icon-shujuku2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhishiku1"></use>
                </svg>
                <div class="name">知识库</div>
                <div class="code-name">#icon-zhishiku1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chajian1"></use>
                </svg>
                <div class="name">插件</div>
                <div class="code-name">#icon-chajian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">shoucang</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yulancopy"></use>
                </svg>
                <div class="name">yulancopy</div>
                <div class="code-name">#icon-yulancopy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhinengkefu"></use>
                </svg>
                <div class="name">智能客服</div>
                <div class="code-name">#icon-zhinengkefu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuoxuexi"></use>
                </svg>
                <div class="name">工作学习</div>
                <div class="code-name">#icon-gongzuoxuexi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wodeshoucang"></use>
                </svg>
                <div class="name">我的收藏</div>
                <div class="code-name">#icon-wodeshoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingyongguangchang"></use>
                </svg>
                <div class="name">应用广场</div>
                <div class="code-name">#icon-yingyongguangchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mobanzhongxin"></use>
                </svg>
                <div class="name">模版中心</div>
                <div class="code-name">#icon-mobanzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qudaojieru"></use>
                </svg>
                <div class="name">渠道接入</div>
                <div class="code-name">#icon-qudaojieru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-youxiyule"></use>
                </svg>
                <div class="name">游戏娱乐</div>
                <div class="code-name">#icon-youxiyule</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuxianghuihua"></use>
                </svg>
                <div class="name">图像绘画</div>
                <div class="code-name">#icon-tuxianghuihua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qita"></use>
                </svg>
                <div class="name">其他</div>
                <div class="code-name">#icon-qita</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuzifenshen"></use>
                </svg>
                <div class="name">数字分身</div>
                <div class="code-name">#icon-shuzifenshen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenbenchuangzuo"></use>
                </svg>
                <div class="name">文本创作</div>
                <div class="code-name">#icon-wenbenchuangzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenghuobangshou"></use>
                </svg>
                <div class="name">生活帮手</div>
                <div class="code-name">#icon-shenghuobangshou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuanduikongjian"></use>
                </svg>
                <div class="name">团队空间</div>
                <div class="code-name">#icon-tuanduikongjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujukanban"></use>
                </svg>
                <div class="name">数据看板</div>
                <div class="code-name">#icon-shujukanban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wodezhanghu"></use>
                </svg>
                <div class="name">我的账户</div>
                <div class="code-name">#icon-wodezhanghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kongjianyuchengyuanguanli"></use>
                </svg>
                <div class="name">空间与成员管理</div>
                <div class="code-name">#icon-kongjianyuchengyuanguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaosequanxianshezhi"></use>
                </svg>
                <div class="name">角色权限设置</div>
                <div class="code-name">#icon-jiaosequanxianshezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jifenpeieguanli"></use>
                </svg>
                <div class="name">积分配额管理</div>
                <div class="code-name">#icon-jifenpeieguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duihuaguanli"></use>
                </svg>
                <div class="name">对话管理</div>
                <div class="code-name">#icon-duihuaguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kehuduan"></use>
                </svg>
                <div class="name">客户端</div>
                <div class="code-name">#icon-kehuduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wendang1"></use>
                </svg>
                <div class="name">wendang</div>
                <div class="code-name">#icon-wendang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuaxin"></use>
                </svg>
                <div class="name">shuaxin</div>
                <div class="code-name">#icon-shuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongzhi"></use>
                </svg>
                <div class="name">tongzhi</div>
                <div class="code-name">#icon-tongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duihuaapi"></use>
                </svg>
                <div class="name">对话api</div>
                <div class="code-name">#icon-duihuaapi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixingongzhonghaoqiye"></use>
                </svg>
                <div class="name">微信公众号（企业）</div>
                <div class="code-name">#icon-a-weixingongzhonghaoqiye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangyeqianru"></use>
                </svg>
                <div class="name">网页嵌入</div>
                <div class="code-name">#icon-wangyeqianru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiyeweixin"></use>
                </svg>
                <div class="name">企业微信</div>
                <div class="code-name">#icon-qiyeweixin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixin"></use>
                </svg>
                <div class="name">微信</div>
                <div class="code-name">#icon-weixin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixingongzhonghaogeren"></use>
                </svg>
                <div class="name">微信公众号（个人）</div>
                <div class="code-name">#icon-a-weixingongzhonghaogeren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixinkefu"></use>
                </svg>
                <div class="name">微信客服</div>
                <div class="code-name">#icon-weixinkefu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yulan"></use>
                </svg>
                <div class="name">yulan</div>
                <div class="code-name">#icon-yulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chafapiao"></use>
                </svg>
                <div class="name">查发票</div>
                <div class="code-name">#icon-chafapiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chawuliu"></use>
                </svg>
                <div class="name">查物流</div>
                <div class="code-name">#icon-chawuliu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuku1"></use>
                </svg>
                <div class="name">数据库</div>
                <div class="code-name">#icon-shujuku1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chadingdan"></use>
                </svg>
                <div class="name">查订单</div>
                <div class="code-name">#icon-chadingdan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanzhong"></use>
                </svg>
                <div class="name">选中</div>
                <div class="code-name">#icon-xuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixuanzhong"></use>
                </svg>
                <div class="name">未选中</div>
                <div class="code-name">#icon-weixuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wangye"></use>
                </svg>
                <div class="name">网页</div>
                <div class="code-name">#icon-wangye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wenda"></use>
                </svg>
                <div class="name">问答</div>
                <div class="code-name">#icon-wenda</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wendang"></use>
                </svg>
                <div class="name">文档</div>
                <div class="code-name">#icon-wendang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoge"></use>
                </svg>
                <div class="name">表格</div>
                <div class="code-name">#icon-biaoge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jian"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#icon-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jia"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#icon-jia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zanwushujuku"></use>
                </svg>
                <div class="name">暂无数据库</div>
                <div class="code-name">#icon-zanwushujuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuxiangshibie"></use>
                </svg>
                <div class="name">图像识别</div>
                <div class="code-name">#icon-tuxiangshibie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#icon-fanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tishi"></use>
                </svg>
                <div class="name">tishi</div>
                <div class="code-name">#icon-tishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiala"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#icon-xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bangding"></use>
                </svg>
                <div class="name">绑定</div>
                <div class="code-name">#icon-bangding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chuangzao1"></use>
                </svg>
                <div class="name">创造</div>
                <div class="code-name">#icon-chuangzao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zengjia"></use>
                </svg>
                <div class="name">增加</div>
                <div class="code-name">#icon-zengjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxiang"></use>
                </svg>
                <div class="name">fenxiang</div>
                <div class="code-name">#icon-fenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanli"></use>
                </svg>
                <div class="name">管理</div>
                <div class="code-name">#icon-guanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-faxian"></use>
                </svg>
                <div class="name">faxian</div>
                <div class="code-name">#icon-faxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jieru"></use>
                </svg>
                <div class="name">接入</div>
                <div class="code-name">#icon-jieru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhi"></use>
                </svg>
                <div class="name">fuzhi</div>
                <div class="code-name">#icon-fuzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi"></use>
                </svg>
                <div class="name">shezhi</div>
                <div class="code-name">#icon-shezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuoliu"></use>
                </svg>
                <div class="name">工作流</div>
                <div class="code-name">#icon-gongzuoliu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingyong"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#icon-yingyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chajian"></use>
                </svg>
                <div class="name">插件</div>
                <div class="code-name">#icon-chajian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhishiku"></use>
                </svg>
                <div class="name">知识库</div>
                <div class="code-name">#icon-zhishiku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shujuku"></use>
                </svg>
                <div class="name">数据库</div>
                <div class="code-name">#icon-shujuku</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
