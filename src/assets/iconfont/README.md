# Iconfont 使用说明

## 简介
项目使用 iconfont 作为图标解决方案，支持本地引入的方式。

## 使用方法

### 方法一：直接使用 CSS 类
```html
<i class="iconfont icon-user"></i>
```

### 方法二：使用辅助函数
```html
<template>
  <i :class="createIconClass('user')"></i>
</template>

<script>
import { createIconClass } from '@/assets/iconfont';

export default {
  methods: {
    createIconClass
  }
};
</script>
```

## 当前可用图标
- icon-user: 用户图标
- icon-home: 首页图标
- icon-settings: 设置图标
- icon-message: 消息图标
- icon-search: 搜索图标

## 如何更新图标库

1. 访问 [iconfont.cn](https://www.iconfont.cn/) 并登录账号
2. 添加或修改你的图标库
3. 下载图标库，选择"Font class"和"Symbol"方式
4. 解压下载的文件
5. 替换以下文件：
   - `iconfont.css`
   - `iconfont.js`
   - 所有字体文件 (`.eot`, `.woff`, `.woff2`, `.ttf`, `.svg`)
6. 更新此 README.md 文件，添加新图标的说明

## 注意事项
- 请保持图标命名的一致性
- 推荐使用语义化的命名，如 `icon-user` 而不是 `icon-01`
- 如果更新后图标显示异常，请清除浏览器缓存后重试 
