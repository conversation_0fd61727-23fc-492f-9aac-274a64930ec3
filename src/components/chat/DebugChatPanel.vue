<template>
  <div class="debug-chat-panel">
    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>正在初始化调试面板...</span>
      </div>
    </div>

    <!-- 聊天面板内容 -->
    <template v-else-if="isPanelReady">

      <!-- 聊天消息区域 -->
      <div class="chat-container">
        <div class="chat-messages" ref="messagesContainer">
          <!-- 消息列表 -->
          <div
            v-for="(message, index) in messages"
            :key="index"
            :class="[
              'chat-message',
              message.role === 'user' ? 'user-message' : 'system-message',
            ]"
          >
            <!-- 系统消息：头像在左侧 -->
            <div
              v-if="message.role !== 'user'"
              class="avatar system-avatar"
            >
              <img
                :src="(appInfo && appInfo.profilePhoto) || ''"
                onerror="this.src='https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'"
                alt="系统"
              />
            </div>
            <div v-if="message.role !== 'user'" class="message-content">
              <div class="message-wrapper">
                <!-- 消息操作按钮 - 右上角 -->
                <div v-if="!message.isStreaming && !message.hasError" class="message-actions-top">
                  <!-- 语音朗读按钮 -->
                  <button
                    class="action-btn voice-read-btn"
                    @click="readMessage(message.content, index)"
                    :disabled="isLoadingAudio && currentReadingMessageIndex !== index"
                    :title="getVoiceReadButtonTitle(index)"
                  >
                    <i :class="getVoiceReadButtonIcon(index)"></i>
                  </button>
                  <!-- 一键复制按钮 -->
                  <button
                    class="action-btn copy-btn"
                    @click="copyMessage(message.content)"
                    title="复制内容"
                  >
                    <i class="el-icon-document-copy"></i>
                  </button>
                </div>
                <div class="message-text" :class="{ 'streaming': message.isStreaming, 'error': message.hasError }">
                  <p v-html="message.content"></p>
                  <!-- 流式传输指示器 -->
                  <div v-if="message.isStreaming" class="streaming-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
                <!-- 知识库文件标签 -->
                <div v-if="message.knowledgeFiles && message.knowledgeFiles.length > 0" class="knowledge-files">
                  <div class="knowledge-files-header">
                    <i class="el-icon-folder-opened"></i>
                    <span>参考资料</span>
                  </div>
                  <div class="knowledge-files-list">
                    <el-tag
                      v-for="file in message.knowledgeFiles"
                      :key="file.fileId"
                      type="info"
                      size="small"
                      class="knowledge-file-tag"
                      :title="`文件ID: ${file.fileId}`"
                    >
                      <i class="el-icon-document"></i>
                      {{ file.fileName }}
                    </el-tag>
                  </div>
                </div>
              </div>
              <div class="message-info">
                <span>{{ message.time }}</span>
                <span v-if="message.isStreaming" class="streaming-text">正在输入...</span>
                <span v-if="message.hasError" class="error-text">发送失败</span>
              </div>
            </div>

            <!-- 用户消息：头像在右侧 -->
            <div v-if="message.role === 'user'" class="avatar user-avatar">
              <img
                src="@/assets/userAvatar.png"
                onerror="this.style.display='none'"
                alt="用户"
                @load="$event.target.style.display='block'"
                @error="$event.target.style.display='none'"
              />
              <i class="el-icon-user-solid user-icon"></i>
            </div>
            <div v-if="message.role === 'user'" class="message-content user-message-content">
              <div class="user-message-wrapper">
                <!-- 如果有上传的图片，显示图片 -->
                <div v-if="message.imageUrl" class="message-image">
                  <img :src="message.imageUrl" alt="用户发送的图片" @click="previewImage(message.imageUrl)" />
                </div>
                <div class="message-text">
                  <p v-html="message.content"></p>
                </div>
              </div>
              <div class="message-info">
                <span>{{ message.time }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入框区域 -->
        <div class="chat-input">
          <!-- 语音输入状态提示 -->
          <div v-if="voiceStatus" class="voice-status" :class="voiceStatusType">
            <i :class="voiceStatusIcon"></i>
            <span>{{ voiceStatus }}</span>
          </div>
          <!-- 已上传图片预览 -->
          <div v-if="uploadedImageUrl && pluginConfig.hasImageVision" class="uploaded-image-preview">
            <div class="image-container">
              <img :src="uploadedImageUrl" alt="上传的图片" class="preview-image" />
              <button
                type="button"
                class="remove-image-btn"
                @click="removeUploadedImage"
                title="移除图片"
              >
                <i class="el-icon-close"></i>
              </button>
            </div>
          </div>
          <!-- 输入区域和按钮区域 -->
          <div class="input-and-actions">
            <div class="input-container">
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="2"
                placeholder="输入消息进行调试..."
                @keyup.enter.native.exact="sendMessage"
                :disabled="isTyping || hasStreamingMessage"
              ></el-input>
              <!-- 图片上传按钮 - 当启用ImageVision插件时显示 -->
              <el-upload
                v-if="pluginConfig.hasImageVision"
                class="image-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="beforeImageUpload"
                :http-request="handleImageUpload"
                accept="image/*"
                :disabled="isTyping || hasStreamingMessage"
              >
                <button
                  type="button"
                  class="image-upload-btn"
                  :disabled="isTyping || hasStreamingMessage"
                  title="上传图片"
                >
                  <i class="el-icon-picture"></i>
                </button>
              </el-upload>
              <!-- 语音输入按钮 -->
              <button
                type="button"
                class="voice-input-btn"
                :class="{ 'recording': isRecording }"
                @mousedown="handleVoiceMouseDown"
                @mouseup="handleVoiceMouseUp"
                @mouseleave="handleVoiceMouseUp"
                @touchstart="handleVoiceTouchStart"
                @touchend="handleVoiceTouchEnd"
                :disabled="isTyping || hasStreamingMessage || !canRecord"
                :title="getVoiceInputTitle()"
              >
                <i :class="getVoiceInputIcon()"></i>
              </button>
            </div>
            <div class="input-actions">
              <!-- 停止生成按钮 - 流式输出时显示 -->
              <el-button
                v-if="hasStreamingMessage"
                type="danger"
                @click="stopStreaming"
                title="停止生成"
              >
                <i class="el-icon-video-pause"></i>
                停止
              </el-button>
              <!-- 发送按钮 - 非流式输出时显示 -->
              <el-button
                v-else
                type="primary"
                :disabled="!inputMessage.trim() || isTyping"
                @click="sendMessage"
              >
                发送
              </el-button>
              <!-- 清除记忆按钮 -->
              <el-button
                v-if="!hasStreamingMessage"
                type="warning"
                size="small"
                @click="clearChatMemory"
                :disabled="isTyping || hasStreamingMessage || messages.length === 0"
                title="清除记忆"
              >
                <i class="el-icon-delete"></i>
                清除记忆
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 初始化失败状态 -->
    <div v-else class="error-container">
      <div class="error-content">
        <i class="el-icon-warning"></i>
        <p>调试面板初始化失败</p>
        <el-button type="primary" @click="initializePanel">重新初始化</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { api } from '@/api/request'
import { clearChatMemory, formatOutputContent } from '@/utils/index'
import { VoiceInputManager, VoiceUtils } from '@/utils/voice'

export default {
  name: "DebugChatPanel",
  props: {
    // 应用信息
    appInfo: {
      type: Object,
      default: () => ({})
    },
    // 应用ID
    appId: {
      type: [String, Number],
      required: true
    },
    // 来源类型：1-应用，2-工作流
    sourceType: {
      type: Number,
      default: 1
    },
    // 调试模式类型
    debugMode: {
      type: String,
      default: 'basic' // basic, advanced
    },
    // 插件配置信息
    pluginConfig: {
      type: Object,
      default: () => ({
        hasImageVision: false,
        enabledPlugins: []
      })
    }
  },
  data() {
    return {
      // 消息列表
      messages: [],
      // 输入消息
      inputMessage: "",
      // 是否正在输入
      isTyping: false,
      // 是否正在流式输出
      isStreaming: false,
      // 流式输出控制器
      streamController: null,
      // 是否正在加载
      isLoading: false,
      // 面板是否准备好
      isPanelReady: false,
      // 当前会话ID
      currentSessionId: null,
      // 聊天消息缓存键名前缀
      chatMessagesStoragePrefix: "debug_chat_messages_",

      // ============ 语音相关状态 ============
      // 语音TTS实例
      voiceTTS: null,
      // 语音输入管理器
      voiceInputManager: null,
      // 是否正在录音
      isRecording: false,
      // 是否可以录音
      canRecord: false,
      // 语音状态提示
      voiceStatus: '',
      voiceStatusType: '',
      voiceStatusIcon: '',
      // 语音朗读状态
      isLoadingAudio: false,
      currentReadingMessageIndex: -1,
      // 录音开始时间
      recordingStartTime: null,

      // ============ 图片上传相关状态 ============
      uploadedImageUrl: null, // 上传的图片URL
    };
  },
  computed: {
    // 检查是否有流式消息
    hasStreamingMessage() {
      return this.messages.length > 0 && this.messages.some(message => message.isStreaming === true);
    }
  },
  watch: {
    // 监听应用ID变化
    appId: {
      handler(newAppId) {
        if (newAppId) {
          this.initializePanel();
        }
      },
      immediate: true
    },
    // 监听应用信息变化
    appInfo: {
      handler(newAppInfo) {
        // 如果应用信息更新了，并且当前没有消息或只有默认欢迎消息，则更新欢迎消息
        if (newAppInfo && newAppInfo.introduce && this.isPanelReady) {
          // 检查是否需要更新欢迎消息
          if (this.messages.length === 0) {
            this.addWelcomeMessage();
          } else if (this.messages.length === 1 && this.messages[0].role === 'assistant') {
            // 如果只有一条助手消息（可能是默认欢迎消息），则替换为新的开场介绍
            const firstMessage = this.messages[0];
            const defaultMessages = [
              "欢迎使用高级配置调试面板！您可以在这里测试应用的高级功能。",
              "欢迎使用基础配置调试面板！您可以在这里测试应用的基本对话功能。"
            ];

            // 检查第一条消息是否是默认消息
            const isDefaultMessage = defaultMessages.some(msg =>
              firstMessage.content.includes(msg)
            );

            if (isDefaultMessage) {
              // 更新第一条消息为开场介绍
              this.updateMessage(firstMessage.messageId, {
                content: formatOutputContent(newAppInfo.introduce)
              });
            }
          }
        }
      },
      deep: true
    },

    // 监听插件配置变化
    pluginConfig: {
      handler(newConfig) {
        console.log('插件配置更新:', newConfig);
        // 如果图像视觉插件被禁用，清除已上传的图片
        if (!newConfig.hasImageVision && this.uploadedImageUrl) {
          this.uploadedImageUrl = null;
          console.log('ImageVision插件已禁用，清除上传的图片');
        }
      },
      deep: true
    }
  },
  mounted() {
    this.scrollToBottom();
    this.initVoiceFeatures();
  },
  updated() {
    this.scrollToBottom();
  },
  beforeDestroy() {
    // 清理流式控制器
    if (this.streamController) {
      this.streamController.abort();
      this.streamController = null;
    }
    // 清理语音功能资源
    this.cleanupVoiceFeatures();
  },
  created() {
    // 生成调试会话ID
    this.currentSessionId = this.generateDebugSessionId();
    // 清理本地存储中的重复消息
    this.cleanupDuplicateMessages();
  },
  methods: {
    // 初始化面板
    initializePanel() {
      if (!this.appId) {
        console.warn("DebugChatPanel: appId 未提供");
        return;
      }

      console.log("DebugChatPanel 初始化:", {
        appId: this.appId,
        sourceType: this.sourceType,
        debugMode: this.debugMode
      });

      this.isLoading = true;

      // 模拟初始化过程
      setTimeout(() => {
        this.isPanelReady = true;
        this.isLoading = false;

        // 加载历史消息或添加欢迎消息
        const hasLoadedMessages = this.loadChatMessages();
        if (!hasLoadedMessages) {
          this.addWelcomeMessage();
        }
      }, 500);
    },

    // 生成调试会话ID
    generateDebugSessionId() {
      return this.generateGuid();
    },

    // 添加欢迎消息
    addWelcomeMessage() {
      // 优先使用应用的开场介绍
      let welcomeMessage = "";

      if (this.appInfo && this.appInfo.introduce && this.appInfo.introduce.trim()) {
        welcomeMessage = this.appInfo.introduce;
      } else {
        // 如果没有开场介绍，使用默认消息
        welcomeMessage = this.debugMode === 'advanced'
          ? "欢迎使用高级配置调试面板！您可以在这里测试应用的高级功能。"
          : "欢迎使用基础配置调试面板！您可以在这里测试应用的基本对话功能。";
      }

      this.addMessage({
        role: "assistant",
        content: formatOutputContent(welcomeMessage),
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(),
      });
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping || this.hasStreamingMessage) return;

      const message = this.inputMessage.trim();
      const sessionId = this.currentSessionId;
      const imageUrl = this.uploadedImageUrl; // 保存图片URL到临时变量

      // 添加用户消息（如果有图片，也添加到消息中用于显示）
      const userMessage = {
        role: "user",
        content: formatOutputContent(message),
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(),
      };

      // 如果有上传的图片，添加图片URL到用户消息中用于显示
      if (imageUrl) {
        userMessage.imageUrl = imageUrl;
      }

      this.addMessage(userMessage);

      // 清空输入框和已上传的图片
      this.inputMessage = "";
      this.uploadedImageUrl = null; // 发送消息后清除图片

      // 设置状态
      if (this.sourceType === 1) {
        this.isStreaming = true;
        this.isTyping = false;
      } else {
        this.isTyping = true;
        this.isStreaming = false;
      }

      try {
        // 执行调试请求，传递图片URL
        const result = await this.executeDebugRequest(message, sessionId, imageUrl);
        console.log("调试请求执行成功", result);

        // 对于非流式响应，手动添加消息
        if (result && this.sourceType === 2) {
          this.addMessage(result);
        }

      } catch (error) {
        console.error("调试请求失败:", error);
        this.addMessage({
          role: "assistant",
          content: formatOutputContent(`调试请求失败: ${error.message}`),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
          hasError: true
        });
      } finally {
        this.isTyping = false;
        this.isStreaming = false;
      }
    },

    // 执行调试请求
    async executeDebugRequest(message, sessionId, imageUrl = null) {
      if (this.sourceType === 1) {
        // 应用调试 - 使用流式API
        return await this.executeAppDebug(message, sessionId, imageUrl);
      } else if (this.sourceType === 2) {
        // 工作流调试 - 使用工作流API
        return await this.executeWorkflowDebug(message, sessionId, imageUrl);
      } else {
        throw new Error(`不支持的sourceType: ${this.sourceType}`);
      }
    },

    // 执行应用调试（流式）
    async executeAppDebug(message, sessionId, imageUrl = null) {
      const params = {
        sessionId: sessionId,
        flowDetailType: "SessionFlow",
        flowId: this.appId,
        textInput: message,
        imageInput: imageUrl || "",
        fileInput: "",
        customVariables: {},
      };

      console.log("执行应用调试API调用:", params);
      console.log("图片URL传递给API:", imageUrl);

      // 创建流式响应消息
      const responseMessage = {
        role: "assistant",
        content: "",
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(),
        isStreaming: true,
        knowledgeFiles: [] // 添加知识库文件列表
      };

      this.addMessage(responseMessage);

      // 创建AbortController用于取消请求
      this.streamController = new AbortController();

      try {
        // 使用workflow.stream方法进行流式响应
        await api.workflow.stream(
          params,
          // onMessage 回调
          (messageData) => {
            console.log('收到流式消息:', messageData);

            let responseContent = '';

            // 处理知识库节点数据 - 检查NodeType是否包含'knowledge'
            if (messageData.NodeType && messageData.NodeType.includes('knowledge') && messageData.Output && Array.isArray(messageData.Output)) {
              console.log('检测到知识库节点，处理文件信息:', messageData.Output);
              console.log('当前已有文件数量:', responseMessage.knowledgeFiles.length);

              // 创建当前所有文件ID的Set用于去重
              const existingFileIds = new Set(responseMessage.knowledgeFiles.map(f => f.fileId));

              messageData.Output.forEach((item, index) => {
                console.log(`处理第${index + 1}个文件:`, {
                  FileId: item.FileId,
                  FileName: item.FileName,
                  exists: existingFileIds.has(item.FileId)
                });

                if (item.FileId && item.FileName && !existingFileIds.has(item.FileId)) {
                  const newFile = {
                    fileId: item.FileId,
                    fileName: item.FileName,
                    score: item.Score || 0,
                    ossUrl: item.OssUrl || ''
                  };
                  responseMessage.knowledgeFiles.push(newFile);
                  existingFileIds.add(item.FileId);
                  console.log('添加新文件:', newFile);
                } else {
                  console.log('跳过文件（已存在或无效）:', item.FileId, item.FileName);
                }
              });

              console.log('更新后的知识库文件列表:', responseMessage.knowledgeFiles);
              console.log('文件数量:', responseMessage.knowledgeFiles.length);

              // 强制更新消息，确保界面显示
              this.updateMessage(responseMessage.messageId, {
                knowledgeFiles: [...responseMessage.knowledgeFiles],
                isStreaming: true
              });

              // 额外触发一次Vue响应式更新
              this.$nextTick(() => {
                console.log('NextTick检查: 消息中的知识库文件数量:',
                  this.messages.find(m => m.messageId === responseMessage.messageId)?.knowledgeFiles?.length || 0
                );
              });

              // 知识库节点不输出文本内容，直接返回
              return;
            }

            // 新的流式接口格式：直接检查 Output 字段
            if (messageData.Output) {
              responseContent = messageData.Output;
            }
            // 兼容旧格式：检查 Data.Response 和 Data.Output 字段
            else if (messageData.Data) {
              // 优先检查 Response 字段
              if (messageData.Data.Response) {
                responseContent = messageData.Data.Response;
              }
              // 当 CurrentType === 2 时，检查 Output 字段
              else if (messageData.Data.CurrentType === 2 && messageData.Data.Output) {
                responseContent = messageData.Data.Output;
              }
              }

              if (responseContent) {
                // 更新最后一条助手消息的内容
                responseMessage.content += responseContent;
                this.updateMessage(responseMessage.messageId, {
                  content: formatOutputContent(responseMessage.content),
                  isStreaming: true
                });
            }
          },
          // onError 回调
          (error) => {
            console.error('流式响应错误:', error);
            this.updateMessage(responseMessage.messageId, {
              content: formatOutputContent(`调试请求失败: ${error.message}`),
              isStreaming: false,
              hasError: true
            });
          },
          // onComplete 回调
          () => {
            console.log('流式响应完成');
            this.updateMessage(responseMessage.messageId, {
              isStreaming: false
            });
          },
          // signal 参数
          this.streamController.signal
        );

      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('流式请求被取消');
        } else {
          console.error('流式请求失败:', error);
          this.updateMessage(responseMessage.messageId, {
            content: formatOutputContent(`调试请求失败: ${error.message}`),
            isStreaming: false,
            hasError: true
          });
        }
      } finally {
        this.streamController = null;
      }
    },

    // 执行工作流调试
    async executeWorkflowDebug(message, sessionId, imageUrl = null) {
      const params = {
        sessionId: sessionId,
        flowDetailType: "WorkFlow",
        flowId: this.appId,
        textInput: message,
        imageInput: imageUrl || "",
        fileInput: "",
        customVariables: {},
      };

      console.log("执行工作流调试API调用:", params);
      console.log("图片URL传递给API:", imageUrl);
      const result = await api.workflow.execute(params);

      if (result && result.code === 200 && result.data) {
        return {
          role: "assistant",
          content: formatOutputContent(result.data.output || "工作流执行完成，但未返回输出内容。"),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
          fromWorkflow: true
        };
      } else {
        throw new Error(result?.message || "工作流调试执行失败");
      }
    },

    // 添加消息
    addMessage(message) {
      // 检查是否已存在相同的消息
      if (message.messageId && this.messages.some(msg => msg.messageId === message.messageId)) {
        console.warn("消息已存在，跳过添加:", message.messageId);
        return;
      }

      console.log("addMessage 被调用:", message);
      this.messages.push(message);

      // 检查是否需要保存到localStorage
      const shouldSave = !message.isStreaming &&
                        !message.hasError &&
                        !this.isErrorMessage(message);

      if (shouldSave) {
        this.saveChatMessages();
      }

      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 更新消息（用于流式响应）
    updateMessage(messageId, updatedMessage) {
      const index = this.messages.findIndex(msg => msg.messageId === messageId);

      if (index !== -1) {
        const oldMessage = this.messages[index];
        const newMessage = { ...oldMessage, ...updatedMessage };

        // 使用Vue.set确保响应式更新
        this.$set(this.messages, index, newMessage);

        // 如果流式传输完成且不是错误消息，保存到localStorage
        if (!updatedMessage.isStreaming) {
          const shouldSave = !newMessage.hasError && !this.isErrorMessage(newMessage);
          if (shouldSave) {
            this.saveChatMessages();
          }
        }

        if (!updatedMessage.isStreaming) {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } else {
        console.error("未找到要更新的消息:", messageId);
      }
    },

    // 检查是否为错误消息
    isErrorMessage(message) {
      if (!message || !message.content) return false;

      const errorKeywords = [
        '调试请求失败',
        '发送失败',
        '处理消息时出现了错误',
        '工作流执行失败',
        '流式响应出错'
      ];

      const content = message.content.toLowerCase();
      return errorKeywords.some(keyword => content.includes(keyword.toLowerCase()));
    },

    // 保存聊天消息到本地存储
    saveChatMessages() {
      if (!this.currentSessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + this.currentSessionId;

        // 过滤消息：去重处理 + 排除错误消息
        const validMessages = [];
        const seenMessageIds = new Set();

        for (const message of this.messages) {
          // 跳过重复消息
          if (message.messageId && seenMessageIds.has(message.messageId)) {
            continue;
          }

          // 跳过错误消息
          if (message.hasError || this.isErrorMessage(message)) {
            continue;
          }

          if (message.messageId) {
            seenMessageIds.add(message.messageId);
          }
          validMessages.push(message);
        }

        // 只保存最新的50条有效消息（调试面板不需要太多历史）
        const messagesToSave = validMessages.slice(-50);
        localStorage.setItem(storageKey, JSON.stringify(messagesToSave));

      } catch (error) {
        console.error("保存调试消息失败:", error);
      }
    },

    // 从本地存储加载聊天消息
    loadChatMessages() {
      if (!this.currentSessionId) return false;

      try {
        const storageKey = this.chatMessagesStoragePrefix + this.currentSessionId;
        const savedMessagesJson = localStorage.getItem(storageKey);

        if (savedMessagesJson) {
          const savedMessages = JSON.parse(savedMessagesJson);
          if (Array.isArray(savedMessages) && savedMessages.length > 0) {
            this.messages = savedMessages.map(msg => ({
              ...msg,
              content: formatOutputContent(msg.content),
              // 清除历史消息的流式状态
              isStreaming: false,
              // 确保知识库文件信息被保留
              knowledgeFiles: msg.knowledgeFiles || []
            }));

            this.$nextTick(() => {
              this.scrollToBottom();
            });
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error("加载调试消息失败:", error);
        return false;
      }
    },

    // 清除聊天消息缓存
    clearChatMessages() {
      if (!this.currentSessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + this.currentSessionId;
        localStorage.removeItem(storageKey);
      } catch (error) {
        console.error("清除调试消息失败:", error);
      }
    },

    // 刷新聊天
    refreshChat() {
      if (this.messages.length > 0 && confirm("确定要清除当前调试对话记录吗？")) {
        this.clearChat();
      } else if (this.messages.length === 0) {
        this.addWelcomeMessage();
      }
    },

    // 清空聊天
    clearChat() {
      this.clearChatMessages();
      this.messages = [];
      this.inputMessage = "";

      // 停止流式输出（如果有）
      if (this.streamController) {
        this.streamController.abort();
        this.streamController = null;
      }
      this.isStreaming = false;
      this.isTyping = false;

      // 重新生成会话ID
      this.currentSessionId = this.generateDebugSessionId();

      this.addWelcomeMessage();
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },

    // 格式化时间
    formatTime(date) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    // 生成GUID
    generateGuid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    // 清理重复消息
    cleanupDuplicateMessages() {
      // 清理所有调试相关的本地存储
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.chatMessagesStoragePrefix)) {
          try {
            const messages = JSON.parse(localStorage.getItem(key));
            if (Array.isArray(messages)) {
              // 去重处理
              const uniqueMessages = [];
              const seenIds = new Set();

              messages.forEach(msg => {
                if (!msg.messageId || !seenIds.has(msg.messageId)) {
                  if (msg.messageId) seenIds.add(msg.messageId);
                  uniqueMessages.push(msg);
                }
              });

              localStorage.setItem(key, JSON.stringify(uniqueMessages));
            }
          } catch (error) {
            console.error("清理重复消息失败:", error);
          }
        }
      });
    },

    // ============ 语音功能相关方法 ============

    // 初始化语音功能
    async initVoiceFeatures() {
      try {
        console.log('正在初始化语音功能...');

        // 初始化语音TTS
        const voiceUtils = new VoiceUtils();
        this.voiceTTS = voiceUtils.createTTS({
          preferServer: true,
          fallbackToLocal: true,
          onStart: (messageIndex) => {
            this.currentReadingMessageIndex = messageIndex;
            this.isLoadingAudio = false;
          },
          onStop: () => {
            this.currentReadingMessageIndex = -1;
            this.isLoadingAudio = false;
          },
          onError: (error) => {
            console.error('语音朗读错误:', error);
            this.currentReadingMessageIndex = -1;
            this.isLoadingAudio = false;
          }
        });

        // 初始化语音输入管理器
        this.voiceInputManager = new VoiceInputManager({
          sessionId: this.currentSessionId,
          appId: this.appId,
          api: api,
          onStatusChange: (status) => {
            this.voiceStatus = status.status;
            this.voiceStatusType = status.type;
            this.voiceStatusIcon = status.icon;
          },
          onTextResult: (text) => {
            // 将识别到的文本设置到输入框
            this.inputMessage = text;
          },
          onAutoSend: async () => {
            // 自动发送消息
            await this.sendMessage();
          }
        });

        await this.voiceInputManager.initialize();
        this.canRecord = this.voiceInputManager.canRecord;
        console.log('语音功能初始化成功');
      } catch (error) {
        console.error('语音功能初始化失败:', error);
        this.canRecord = false;
      }
    },

    // 清理语音功能资源
    cleanupVoiceFeatures() {
      try {
        if (this.voiceTTS) {
          this.voiceTTS.cleanup();
          this.voiceTTS = null;
        }

        if (this.voiceInputManager) {
          this.voiceInputManager.cleanup();
          this.voiceInputManager = null;
        }

        this.isRecording = false;
        this.canRecord = false;
        this.voiceStatus = '';
        this.voiceStatusType = '';
        this.voiceStatusIcon = '';
            this.isLoadingAudio = false;
    this.currentReadingMessageIndex = -1;
    this.recordingStartTime = null;
    this.uploadedImageUrl = null;
      } catch (error) {
        console.error('清理语音功能资源失败:', error);
      }
    },

    // 语音朗读消息 - 使用公共方法
    async readMessage(content, messageIndex) {
      try {
        if (!this.voiceTTS) {
          this.$showFriendlyError(null, '语音播报功能未初始化');
          return;
        }

        // 准备TTS参数
        const ttsParams = {
          sessionID: this.currentSessionId,
          flowType: 'SessionFlow',
          appId: this.appId
        };

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg)
        };

        // 调用公共语音朗读方法
        await this.voiceTTS.readMessage(content, messageIndex, ttsParams, api, messageHandler);

      } catch (error) {
        console.error('语音朗读失败:', error);
        this.$showFriendlyError(error, '语音朗读功能出现错误');
      }
    },

    // 停止语音朗读 - 使用公共方法
    stopReading() {
      try {
        if (this.voiceTTS) {
          this.voiceTTS.stopReading();
        }
      } catch (error) {
        console.error('停止朗读失败:', error);
      }
    },

    // 获取语音朗读按钮的图标 - 使用公共方法
    getVoiceReadButtonIcon(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonIcon(messageIndex);
      }
      return 'el-icon-video-play';
    },

    // 获取语音朗读按钮的提示文字 - 使用公共方法
    getVoiceReadButtonTitle(messageIndex) {
      if (this.voiceTTS) {
        return this.voiceTTS.getVoiceReadButtonTitle(messageIndex);
      }
      return '语音朗读';
    },

    // 复制消息内容 - 使用公共方法
    async copyMessage(content) {
      try {
        // 提取纯文本内容
        const textContent = VoiceUtils.extractTextContent(content);

        if (!textContent.trim()) {
          this.$message.warning('该消息没有可复制的文本内容');
          return;
        }

        // 消息处理函数
        const messageHandler = {
          warning: (msg) => this.$message.warning(msg),
          error: (msg) => this.$showFriendlyError(null, msg),
          success: (msg) => this.$message.success(msg)
        };

        // 使用公共复制方法
        await VoiceUtils.copyToClipboard(textContent, messageHandler);

      } catch (error) {
        console.error('复制消息失败:', error);
        this.$showFriendlyError(error, '复制失败，请手动复制');
      }
    },

    // 清除聊天记忆
    async clearChatMemory() {
      try {
        const result = await clearChatMemory({
          sessionId: this.currentSessionId,
          api: api,
          messageHandler: {
            confirm: () => Promise.resolve(true), // 直接返回true，跳过确认对话框
            success: (message) => this.$message.success(message),
            error: (message) => this.$showFriendlyError(null, message)
          },
          clearLocalMessages: () => {
            // 不清空聊天消息，只保留现有消息
            // this.messages = [];
            // this.inputMessage = "";

            // 清除上传的图片
            this.uploadedImageUrl = null;

            // 不清除本地存储的消息
            // if (this.currentSessionId) {
            //   const storageKey = this.chatMessagesStoragePrefix + this.currentSessionId;
            //   localStorage.removeItem(storageKey);
            // }
          },
          stopStreaming: () => {
            // 停止流式输出（如果有）
            if (this.streamController) {
              this.streamController.abort();
              this.streamController = null;
            }
            this.isStreaming = false;
            this.isTyping = false;
          },
          stopReading: () => {
            // 停止当前的语音朗读
            this.stopReading();
          },
          addWelcomeMessage: () => {
            // 不添加新的欢迎消息，保持现有对话
            // this.addWelcomeMessage();
          }
        });

        console.log('清除记忆操作结果:', result);
      } catch (error) {
        console.error('清除聊天记忆失败:', error);
        this.$showFriendlyError(error, '清除聊天记忆失败');
      }
    },

    // ============ 语音输入相关方法 ============

    // 语音按钮鼠标按下事件
    handleVoiceMouseDown() {
      this.startRecording();
    },

    // 语音按钮鼠标松开事件
    handleVoiceMouseUp() {
      this.stopRecording();
    },

    // 语音按钮触摸开始事件
    handleVoiceTouchStart(event) {
      event.preventDefault();
      this.startRecording();
    },

    // 语音按钮触摸结束事件
    handleVoiceTouchEnd(event) {
      event.preventDefault();
      this.stopRecording();
    },

    // 开始录音
    startRecording() {
      if (!this.voiceInputManager || this.isTyping || this.hasStreamingMessage) return;

      // 记录录音开始时间
      this.recordingStartTime = Date.now();

      // 更新配置
      this.voiceInputManager.updateOptions({
        sessionId: this.currentSessionId,
        appId: this.appId
      });

      this.isRecording = this.voiceInputManager.startRecording();
    },

        // 停止录音
    stopRecording() {
      if (!this.voiceInputManager) return;

            // 检查录音时间间隔
      if (this.recordingStartTime) {
                const recordingDuration = Date.now() - this.recordingStartTime;
        const minRecordingTime = 1000; // 最小录音时间1秒

        if (recordingDuration < minRecordingTime) {
          // 重置录音状态
          this.isRecording = false;
          this.recordingStartTime = null;

          // 设置标志阻止语音识别处理，然后停止录音
          try {
            if (this.voiceInputManager) {
              // 设置标志表示这是一个太短的录音，不应该处理
              this.voiceInputManager.shouldIgnoreResult = true;

              if (this.voiceInputManager.isRecording) {
                this.voiceInputManager.stopRecording();
              }
            }
          } catch (error) {
            console.error('停止录音时出错:', error);
          }

          // 立即清除所有语音状态，防止显示"正在识别"
          this.voiceStatus = '';
          this.voiceStatusType = '';
          this.voiceStatusIcon = '';

          // 短暂延迟后显示错误提示，确保状态已清除
          setTimeout(() => {
            this.voiceStatus = '请长按录音，识别语音太短';
            this.voiceStatusType = 'error';
            this.voiceStatusIcon = 'el-icon-warning';

            // 3秒后清除提示
            setTimeout(() => {
              this.voiceStatus = '';
              this.voiceStatusType = '';
              this.voiceStatusIcon = '';
            }, 3000);
          }, 50); // 减少延迟时间

          return; // 直接返回，不执行后续的语音识别处理
        }
      }

      // 正常停止录音并处理语音识别
      this.voiceInputManager.stopRecording();
      this.isRecording = false;
      this.recordingStartTime = null;
    },

    // 获取语音输入按钮标题
    getVoiceInputTitle() {
      if (!this.voiceInputManager) {
        return '语音功能未初始化';
      }
      return this.voiceInputManager.getInputTitle(this.isTyping || this.hasStreamingMessage);
    },

    // 获取语音输入按钮图标
    getVoiceInputIcon() {
      if (!this.voiceInputManager) {
        return 'el-icon-microphone-off';
      }
      return this.voiceInputManager.getInputIcon();
    },

    // 停止流式输出
    stopStreaming() {
      if (this.streamController) {
        this.streamController.abort();
        this.streamController = null;
      }
      this.isStreaming = false;
      this.isTyping = false;

      // 将最后一个流式消息标记为完成
      if (this.messages.length > 0) {
        const lastMessage = this.messages[this.messages.length - 1];
        if (lastMessage.isStreaming) {
          lastMessage.isStreaming = false;
          if (!lastMessage.content.trim()) {
            lastMessage.content = '输出已停止';
            lastMessage.hasError = true;
          }
        }
      }
    },

    // ============ 图片上传相关方法 ============

    // 检查上传的文件是否符合要求
    beforeImageUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$showFriendlyError(null, "上传图片只能是图片格式!");
        return false;
      }
      if (!isLt2M) {
        this.$showFriendlyError(null, "上传图片大小不能超过 2MB!");
        return false;
      }
      return isImage && isLt2M;
    },

    // 处理图片上传
    async handleImageUpload(options) {
      const file = options.file;
      let loadingMessage = null;

      try {
        // 显示上传中提示
        loadingMessage = this.$message({
          message: "图片上传中...",
          type: "info",
          duration: 0,
        });

        // 构建上传参数
        const uploadParams = {
          file: file,
          fileType: "image",
          folder: "debug_images",
        };

        // 使用upload方法上传文件
        const uploadRes = await api.oss.upload(uploadParams);

        // 关闭加载提示
        if (loadingMessage) {
          loadingMessage.close();
        }

        if (uploadRes.data && uploadRes.data.fileUrl) {
          // 设置图片URL
          this.uploadedImageUrl = uploadRes.data.fileUrl;
          this.$message.success("图片上传成功");
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      } catch (error) {
        // 关闭加载提示
        if (loadingMessage) {
          loadingMessage.close();
        }

        console.error("上传图片失败:", error);
        this.$showFriendlyError(error, "图片上传失败，请稍后重试");
      }
    },

    // 移除已上传的图片
    removeUploadedImage() {
      this.uploadedImageUrl = null;
      this.$message.info("已移除上传的图片");
    },

    // 预览图片
    previewImage(imageUrl) {
      // 创建一个简单的图片预览弹窗
      this.$msgbox({
        title: '图片预览',
        message: `<img src="${imageUrl}" style="max-width: 100%; max-height: 400px; object-fit: contain;" />`,
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        confirmButtonText: '关闭'
      }).catch(() => {
        // 用户点击关闭按钮
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.debug-chat-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  .loading-container, .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #666;

    .loading-spinner, .error-content {
      text-align: center;

      i {
        font-size: 24px;
        margin-bottom: 10px;
        display: block;
      }
    }
  }

  .panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #1f2937;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 16px;

      .chat-message {
        display: flex;
        margin-bottom: 16px;
        gap: 12px;

        &.user-message {
          align-self: flex-end;
          flex-direction: row-reverse;

          .avatar {
            margin-right: 0;
            margin-left: 12px;
          }

          .message-content {
            text-align: right;

            .message-text {
              color: white;
            }
          }
        }

        &.system-message {
          .message-text {
            color: #303133;

            &.streaming {
              background: #e6f7ff;
              border-left: 3px solid #409eff;
            }

            &.error {
              background: #fef0f0;
              border-left: 3px solid #f56c6c;
              color: #f56c6c;
            }
          }
        }

        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          &.system-avatar {
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
            }
          }

          &.user-avatar {
            background: #409eff;
            color: white;
            font-size: 16px;
            position: relative;

            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
              z-index: 2;
              position: relative;
            }

            .user-icon {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 18px;
              z-index: 1;
            }
          }
        }

        .message-content {
          flex: 1;
          max-width: calc(100% - 44px);

          .message-wrapper {
            position: relative;
            border-radius: 12px;
            padding: 16px 20px;
            background-color: #ffffff;
            border: 1px solid #f0f0f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;

            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }

            .message-actions-top {
              position: absolute;
              top: 12px;
              right: 12px;
              display: flex;
              gap: 6px;
              z-index: 1;
              opacity: 1;

              .action-btn {
                background: #ffffff;
                border: 1px solid #e8e8e8;
                border-radius: 6px;
                padding: 6px 8px;
                font-size: 12px;
                color: #666666;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

                &:hover:not(:disabled) {
                  background-color: #f8f9fa;
                  border-color: #d0d7de;
                  color: #0969da;
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                &:disabled {
                  opacity: 0.5;
                  cursor: not-allowed;
                }

                &.voice-read-btn {
                  &:hover:not(:disabled) {
                    color: #28a745;
                    border-color: #28a745;
                  }
                }

                &.copy-btn {
                  &:hover:not(:disabled) {
                    color: #fd7e14;
                    border-color: #fd7e14;
                  }
                }

                i {
                  font-size: 13px;

                  &.el-icon-loading {
                    animation: rotate 2s linear infinite;
                  }
                }
              }
            }

            .message-text {
              position: relative;
              padding-right: 90px; // 为右上角按钮留出空间

              p {
                margin: 0;
                line-height: 1.6;
                word-wrap: break-word;
                color: #24292f;
                font-size: 14px;
              }

              // 流式传输状态样式
              &.streaming {
                border-left: 3px solid #0969da;
                padding-left: 12px;
                background-color: rgba(9, 105, 218, 0.03);
                border-radius: 0 12px 12px 0;
              }

              // 错误状态样式
              &.error {
                border-left: 3px solid #d1242f;
                padding-left: 12px;
                background-color: rgba(209, 36, 47, 0.03);
                border-radius: 0 12px 12px 0;
              }

              // 流式传输指示器
              .streaming-indicator {
                display: inline-flex;
                align-items: center;
                margin-left: 8px;

                span {
                  width: 4px;
                  height: 4px;
                  border-radius: 50%;
                  background-color: #0969da;
                  margin-right: 3px;
                  animation: streamingDot 1.5s infinite;

                  &:nth-child(2) {
                    animation-delay: 0.3s;
                  }

                  &:nth-child(3) {
                    animation-delay: 0.6s;
                  }
                }
              }
            }
                         // 知识库文件标签样式
             .knowledge-files {
               margin-top: 12px;
               padding: 8px 12px;
               background-color: #f8f9fa;
               border: 1px solid #e9ecef;
               border-radius: 6px;
               font-size: 12px;

               .knowledge-files-header {
                 display: flex;
                 align-items: center;
                 gap: 6px;
                 margin-bottom: 8px;
                 font-weight: 500;
                 color: #495057;

                 i {
                   font-size: 14px;
                   color: #6c757d;
                 }
               }

               .knowledge-files-list {
                 display: flex;
                 flex-wrap: wrap;
                 gap: 6px;

                 :deep(.knowledge-file-tag) {
                   display: inline-flex;
                   align-items: center;
                   gap: 4px;
                   background-color: #e3f2fd;
                   color: #1565c0;
                   border: 1px solid #bbdefb;
                   border-radius: 4px;
                   padding: 4px 8px;
                   font-size: 11px;
                   line-height: 1.2;
                   cursor: pointer;
                   transition: all 0.2s ease;

                   &:hover {
                     background-color: #bbdefb;
                     border-color: #90caf9;
                     transform: translateY(-1px);
                     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                   }

                   i {
                     font-size: 12px;
                   }
                 }
               }
             }
          }

          // 用户消息样式
          &.user-message-content {
            max-width: 70%;
            display: flex;
            flex-direction: column;
            align-items: flex-end;

                      .user-message-wrapper {
            background-color: #ecf5ff;
            border-radius: 12px;
            padding: 16px 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;

            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }

            .message-image {
              margin-bottom: 8px;

              img {
                max-width: 200px;
                max-height: 150px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  transform: scale(1.02);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                }
              }
            }

            .message-text {
              padding-right: 0;

              p {
                margin: 0;
                line-height: 1.6;
                word-wrap: break-word;
                color: #303133;
                font-size: 14px;
              }
            }
          }
          }

          .message-info {
            margin-top: 8px;
            font-size: 11px;
            opacity: 0.6;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 4px;
            color: #656d76;

            .streaming-text {
              color: #0969da;
              font-style: italic;
            }

            .error-text {
              color: #d1242f;
              font-style: italic;
            }
          }
        }
      }
    }

    .chat-input {
      padding: 12px 16px;
      border-top: 1px solid #e1e4e8;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .input-and-actions {
        display: flex;
        align-items: flex-end;
        gap: 10px;
        min-height: 44px;
      }

      .input-container {
        display: flex;
        align-items: flex-end;
        gap: 10px;
        position: relative;
        flex: 1;

        .el-input {
          flex: 1;

          :deep(.el-textarea__inner) {
            border-radius: 8px;
            border: 1px solid #d0d7de;
            padding: 10px 14px;
            font-size: 14px;
            line-height: 1.4;
            resize: none;
            transition: all 0.2s ease;
            min-height: 44px;
            max-height: 120px;
            box-sizing: border-box;
            background-color: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:focus {
              border-color: #0969da;
              box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
              outline: none;
            }

            &:hover:not(:focus) {
              border-color: #8c959f;
            }

            &::placeholder {
              color: #656d76;
            }
          }
        }

        // 图片上传按钮样式
        .image-uploader {
          display: inline-block;

          :deep(.el-upload) {
            display: inline-block;
          }

          .image-upload-btn {
            width: 44px;
            height: 44px;
            border-radius: 8px;
            border: 1px solid #d0d7de;
            background: #ffffff;
            color: #656d76;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            user-select: none;

            &:hover:not(:disabled) {
              background-color: #f8f9fa;
              border-color: #0969da;
              color: #0969da;
              box-shadow: 0 2px 8px rgba(9, 105, 218, 0.15);
            }

            &:active:not(:disabled) {
              transform: scale(0.98);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
              background-color: #f5f5f5;
              border-color: #e1e4e8;
              color: #999999;

              &:hover {
                transform: none;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              }
            }

            i {
              font-size: 16px;
            }
          }
        }

        .voice-input-btn {
          width: 44px;
          height: 44px;
          border-radius: 8px;
          border: 1px solid #d0d7de;
          background: #ffffff;
          color: #656d76;
          font-size: 18px;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
          user-select: none;
          flex-shrink: 0;

          &:hover:not(:disabled) {
            background-color: #f8f9fa;
            border-color: #0969da;
            color: #0969da;
            box-shadow: 0 2px 8px rgba(9, 105, 218, 0.15);
          }

          &:active:not(:disabled) {
            transform: scale(0.98);
          }

          &.recording {
            background: linear-gradient(135deg, #ff3838, #c0392b);
            border-color: #ff3838;
            color: white;
            animation: pulse 1.5s infinite;
            box-shadow: 0 2px 12px rgba(255, 56, 56, 0.3);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f5f5f5;
            border-color: #e1e4e8;
            color: #999999;

            &:hover {
              transform: none;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }
          }

          i {
            font-size: 16px;
          }
        }
      }

      .voice-status {
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 2px;
        animation: fadeIn 0.3s ease;

        i {
          font-size: 14px;

          &.el-icon-loading {
            animation: rotate 2s linear infinite;
          }
        }

        &.success {
          background-color: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }

        &.error {
          background-color: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }

        &.loading {
          background-color: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
        }
      }

      // 已上传图片预览样式
      .uploaded-image-preview {
        margin-bottom: 8px;
        animation: fadeIn 0.3s ease;

        .image-container {
          position: relative;
          display: inline-block;
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid #e1e4e8;

          .preview-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            display: block;
          }

          .remove-image-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: background-color 0.2s ease;

            &:hover {
              background-color: rgba(0, 0, 0, 0.8);
            }

            i {
              font-size: 10px;
            }
          }
        }
      }

      .input-actions {
        display: flex;
        align-items: flex-end;
        gap: 10px;
        flex-shrink: 0;

        .el-button {
          height: 44px;
          padding: 0 18px;
          transition: all 0.2s ease;
          white-space: nowrap;
          border-radius: 8px;
          font-weight: 500;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: auto;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
          }

          i {
            margin-right: 4px;
            font-size: 14px;
          }

          // 只有图标的按钮样式调整
          &.icon-only {
            min-width: 44px;
            padding: 0;

            i {
              margin-right: 0;
            }
          }
        }

        // 停止生成按钮样式
        .el-button--danger {
          background-color: #d1242f;
          border-color: #d1242f;
          color: white;

          &:hover {
            background-color: #a21e2a;
            border-color: #a21e2a;
            box-shadow: 0 3px 12px rgba(209, 36, 47, 0.25);
          }
        }

        // 发送按钮增强样式
        .el-button--primary {
          background: linear-gradient(135deg, #0969da, #0860ca);
          border-color: #0969da;
          min-width: 80px;
          font-weight: 600;

          &:hover {
            background: linear-gradient(135deg, #0860ca, #0757b9);
            border-color: #0860ca;
            box-shadow: 0 4px 12px rgba(9, 105, 218, 0.3);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }

          &:disabled {
            opacity: 0.6;
            background-color: #94a3b8;
            border-color: #94a3b8;

            &:hover {
              transform: none;
              box-shadow: none;
            }
          }
        }

        // 清除记忆按钮样式
        .el-button--warning {
          background-color: #fd7e14;
          border-color: #fd7e14;

          &:hover {
            background-color: #e8590c;
            border-color: #e8590c;
            box-shadow: 0 3px 12px rgba(253, 126, 20, 0.25);
          }

          &:disabled {
            opacity: 0.6;
            background-color: #94a3b8;
            border-color: #94a3b8;

            &:hover {
              transform: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 70%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  35% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

@keyframes streamingDot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;

  &:hover {
    background: rgba(0, 0, 0, 0.2);
  }
}
</style>
