<template>
  <div class="chat-panel">
    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>正在加载聊天信息...</span>
      </div>
    </div>

    <!-- 聊天面板内容 -->
    <template v-else-if="isPanelReady">
      <!-- 面板头部 -->
      <div class="panel-header">
        <h3>
          {{ (appInfo && appInfo.name) || "智能客服助手" }}
          <el-tag v-if="appInfo && sessionStatus.sessionId" type="info">
            ID: {{ sessionStatus.sessionId }}
          </el-tag>
        </h3>
        <div v-if="showControls" class="header-actions">
          <el-button
            v-if="!sessionStatus.isConnected"
            type="warning"
            @click="reconnect"
          >
            重连
          </el-button>
          <el-button
            size="mini"
            icon="el-icon-refresh"
            circle
            @click="refreshChat"
          ></el-button>
          <!-- <el-button
            v-if="showCloseButton"
            size="mini"
            icon="el-icon-back"
            circle
            @click="closeChat"
          ></el-button> -->
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-container">
        <div class="chat-messages" ref="messagesContainer">
          <!-- 消息列表 -->
          <div
            v-for="(message, index) in messages"
            :key="index"
            :class="[
              'chat-message',
              message.role === 'user' ? 'user-message' : 'system-message',
            ]"
          >
            <!-- 系统消息：头像在左侧 -->
            <div
              v-if="message.role !== 'user'"
              class="avatar system-avatar"
            >
              <img
                :src="(appInfo && appInfo.profilePhoto) || ''"
                onerror="this.src='https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'"
                alt="系统"
              />
            </div>
            <div v-if="message.role !== 'user'" class="message-content">
              <div class="message-text" :class="{ 'streaming': message.isStreaming, 'error': message.hasError }">
                <p v-html="message.content"></p>
                <!-- 流式传输指示器 -->
                <div v-if="message.isStreaming" class="streaming-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              <div class="message-info">
                <span>{{ message.time }}</span>
                <span v-if="message.isStreaming" class="streaming-text">正在输入...</span>
                <span v-if="message.hasError" class="error-text">发送失败</span>
              </div>
            </div>

            <!-- 用户消息：头像在右侧 -->
            <div v-if="message.role === 'user'" class="message-content">
              <div class="message-text">
                <p v-html="message.content"></p>
              </div>
              <div class="message-info">
                <span>{{ message.time }}</span>
              </div>
            </div>
            <div v-if="message.role === 'user'" class="avatar user-avatar">
              <img
                src="@/assets/userAvatar.png"
                onerror="this.src='https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'"
                alt="用户"
              />
            </div>
          </div>

          <!-- 正在输入提示 -->
          <div class="typing-indicator" v-if="isTyping">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <!-- 输入框区域 -->
        <div class="chat-input">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="2"
            placeholder="请输入消息..."
            @keyup.enter.native.exact="sendMessage"
            :disabled="isTyping || hasStreamingMessage"
          ></el-input>
          <!-- 停止生成按钮 - 流式输出时显示 -->
          <el-button
            v-if="hasStreamingMessage"
            type="danger"
            @click="stopStreaming"
            title="停止生成"
            style="margin-left: 10px; height: 100%"
          >
            <i class="el-icon-video-pause"></i>
            停止
          </el-button>
          <!-- 发送按钮 - 非流式输出时显示 -->
          <el-button
            v-else
            type="primary"
            :disabled="
              !inputMessage.trim() || isTyping || !sessionStatus.isConnected
            "
            @click="sendMessage"
            :title="!sessionStatus.isConnected ? '当前处于离线状态' : ''"
            style="margin-left: 10px; height: 100%"
          >
            发送
          </el-button>
        </div>
      </div>
    </template>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <i class="el-icon-chat-line-square"></i>
      <p>请选择一个会话开始聊天</p>
    </div>
  </div>
</template>

<script>
import { formatOutputContent } from '@/utils';
import signalRMixin from './mixins/signalRMixin.js';

export default {
  name: "ChatPanel",
  mixins: [signalRMixin],
  props: {
    // 应用信息
    appInfo: {
      type: Object,
      default: () => ({})
    },
    // 会话ID
    sessionId: {
      type: String,
      default: null
    },
    // 应用ID
    appId: {
      type: [String, Number],
      default: null
    },
    // 来源类型
    sourceType: {
      type: Number,
      default: 1
    },
    // 是否自动连接
    autoConnect: {
      type: Boolean,
      default: true
    },
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      default: true
    },
    // 消息发送模式：'api-first' | 'signalr-only'
    messageMode: {
      type: String,
      default: 'api-first',
      validator: (value) => ['api-first', 'signalr-only'].includes(value)
    }
  },
  data() {
    return {
      // 消息列表
      messages: [],
      // 输入消息
      inputMessage: "",
      // 是否正在输入
      isTyping: false,
      // 是否正在流式输出
      isStreaming: false,
      // 流式输出控制器
      streamController: null,
      // 是否正在加载
      isLoading: false,
      // 面板是否准备好
      isPanelReady: false,
      // 聊天消息缓存键名前缀
      chatMessagesStoragePrefix: "chat_messages_",
    };
  },
  computed: {
    // 检查是否有流式消息
    hasStreamingMessage() {
      return this.messages.length > 0 && this.messages.some(message => message.isStreaming === true);
    }
  },
  watch: {
    // 监听会话ID变化
    sessionId: {
      handler(newSessionId, oldSessionId) {
        if (newSessionId && newSessionId !== oldSessionId) {
          console.log("会话ID变化:", oldSessionId, "->", newSessionId);
          this.handleSessionChange(newSessionId);
        }
      },
      immediate: true
    },
    // 监听应用ID变化
    appId: {
      handler(newAppId) {
        if (newAppId) {
          this.initializePanel();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.scrollToBottom();
  },
  updated() {
    this.scrollToBottom();
  },
  beforeDestroy() {
    // 清理流式控制器
    if (this.streamController) {
      this.streamController.abort();
      this.streamController = null;
    }
  },
  created() {
    // 清理本地存储中的重复消息（一次性清理）
    this.cleanupDuplicateMessages();
  },
  methods: {
    // 初始化面板
    initializePanel() {
      if (!this.appId) {
        console.warn("ChatPanel: appId 未提供");
        return;
      }

      console.log("ChatPanel 初始化:", {
        appId: this.appId,
        sessionId: this.sessionId,
        sourceType: this.sourceType,
        autoConnect: this.autoConnect
      });

      this.isPanelReady = true;

      // 如果设置了自动连接，则建立连接
      if (this.autoConnect && this.sessionId) {
        this.initializeConnection();
      }
    },

    // 处理会话变化
    handleSessionChange(newSessionId) {
      // 停止旧连接
      this.stopConnection();

      // 清空消息列表
      this.messages = [];

      // 设置新的会话ID
      this.sessionStatus.sessionId = newSessionId;

      // 加载历史消息
      const hasLoadedMessages = this.loadChatMessages(newSessionId);

      // 只有在没有加载到历史消息时才添加欢迎消息
      if (!hasLoadedMessages) {
        this.addWelcomeMessage();
      }

      // 如果启用自动连接，建立新连接
      if (this.autoConnect) {
        this.initializeConnection();
      }
    },

    // 初始化连接
    initializeConnection() {
      if (!this.sessionId || !this.appId) {
        console.warn("ChatPanel: 缺少必要参数，无法建立连接", {
          sessionId: this.sessionId,
          appId: this.appId
        });
        return;
      }

      this.sessionStatus.sessionId = this.sessionId;
      this.checkLoginAndJoinChat();
    },

    // 添加欢迎消息
    addWelcomeMessage() {
      if (this.appInfo && (this.appInfo.introduce || this.appInfo.description)) {
        this.addMessage({
          role: "assistant",
          content: formatOutputContent(this.appInfo.introduce || this.appInfo.description),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });
      } else {
        this.addMessage({
          role: "assistant",
          content: formatOutputContent("有什么可以帮助您的？"),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });
      }
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping || this.hasStreamingMessage) return;

      const message = this.inputMessage.trim();
      const sessionId = this.sessionStatus.sessionId;

      // 添加用户消息
      this.addMessage({
        role: "user",
        content: formatOutputContent(message),
        time: this.formatTime(new Date()),
        messageId: this.generateGuid(),
      });

      // 清空输入框
      this.inputMessage = "";

              // 根据智能体类型设置状态
      if (this.sourceType === 1) {
        // 智能体类型使用流式输出
        this.isStreaming = true;
        this.isTyping = false;
      } else {
        // 工作流类型使用传统的输入状态
        this.isTyping = true;
        this.isStreaming = false;
      }

      if (!sessionId) {
        this.$showFriendlyError(null, "会话未初始化，无法发送消息");
        this.isTyping = false;
        this.isStreaming = false;
        return;
      }

      try {
        // 触发消息发送事件
        this.$emit('message-sent', {
          content: message,
          sessionId: sessionId,
          time: this.formatTime(new Date())
        });

        // 根据消息模式决定发送方式
        if (this.messageMode === 'signalr-only') {
          // 对话管理模式：仅发送SignalR消息
          if (this.sessionStatus.isConnected && this.hubConnection) {
            try {
              await this.sendMessageToSignalR(message, sessionId);
            } catch (error) {
              console.error("SignalR消息发送失败:", error);
              this.addMessage({
                role: "assistant",
                content: formatOutputContent(`发送失败: ${error.message}`),
                time: this.formatTime(new Date()),
                messageId: this.generateGuid(),
              });
            }
          } else {
            this.addMessage({
              role: "assistant",
              content: formatOutputContent("连接未建立，无法发送消息"),
              time: this.formatTime(new Date()),
              messageId: this.generateGuid(),
            });
          }
        } else {
          // 普通模式：先发送工作流接口请求，再发送SignalR
          try {
            const workflowResult = await this.executeWorkflow(message, sessionId);

            // 接口请求成功后，再发送SignalR消息
            if (this.sessionStatus.isConnected && this.hubConnection) {
              try {
                await this.sendMessageToSignalR(message, sessionId);
              } catch (error) {
                console.warn("SignalR消息发送失败:", error);
              }
            }

            // 对于流式响应，executeWorkflow 已经处理了消息的添加和更新
            // 对于非流式响应（sourceType === 2），才需要手动添加消息
            if (workflowResult && this.sourceType === 2) {
              this.addMessage(workflowResult);
            }

          } catch (error) {
            console.error("工作流接口请求失败:", error);

            // 检查是否为用户主动中止的AbortError
            if (error.name === 'AbortError' || error.message.includes('aborted')) {
              return; // 直接返回，不添加错误消息
            }

            this.addMessage({
              role: "assistant",
              content: formatOutputContent(`发送失败，请重试`),
              time: this.formatTime(new Date()),
              messageId: this.generateGuid(),
              hasError: true
            });
          }
        }

      } catch (error) {
        console.error("发送消息失败:", error);

        // 检查是否为用户主动中止的AbortError
        if (error.name === 'AbortError' || error.message.includes('aborted')) {
          return; // 直接返回，不添加错误消息
        }

        this.addMessage({
          role: "assistant",
          content: formatOutputContent(`发送失败，请重试`),
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
        });
      } finally {
        // 只清理 isTyping 状态，isStreaming 状态由流式回调处理
        this.isTyping = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },

    // 添加消息
    addMessage(message) {
      // 检查是否已存在相同的消息（基于messageId）
      if (message.messageId && this.messages.some(msg => msg.messageId === message.messageId)) {
        return;
      }

      this.messages.push(message);

      // 检查是否需要保存到localStorage
      // 不保存以下情况的消息：
      // 1. 流式消息且还在传输中
      // 2. 包含错误的消息（hasError=true或content包含错误关键词）
      const shouldSave = !message.isStreaming &&
                        !message.hasError &&
                        !this.isErrorMessage(message);

      if (shouldSave) {
        this.saveChatMessages();
      }

      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    // 检查是否为错误消息
    isErrorMessage(message) {
      if (!message || !message.content) return false;

      const errorKeywords = [
        '抱歉，处理您的请求时出现了错误',
        '发送失败',
        'BodyStreamBuffer was aborted',
        '连接未建立',
        '会话未初始化',
        '无法发送消息',
        '处理消息时出现了错误',
        '工作流执行失败',
        '流式响应出错'
      ];

      const content = message.content.toLowerCase();
      return errorKeywords.some(keyword => content.includes(keyword.toLowerCase()));
    },

    // 更新消息（用于流式响应）
    updateMessage(messageId, updatedMessage) {
      const index = this.messages.findIndex(msg => msg.messageId === messageId);

      if (index !== -1) {
        const oldMessage = this.messages[index];
        const newMessage = { ...oldMessage, ...updatedMessage };

        // 使用Vue.set确保响应式更新
        this.$set(this.messages, index, newMessage);

        // 如果流式传输完成且不是错误消息，保存到localStorage
        if (!updatedMessage.isStreaming) {
          const shouldSave = !newMessage.hasError && !this.isErrorMessage(newMessage);
          if (shouldSave) {
            this.saveChatMessages();
          }
        }

        // 如果消息还在流式传输中，不需要滚动到底部
        if (!updatedMessage.isStreaming) {
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } else {
        console.error("未找到要更新的消息:", messageId);
      }
    },

    // 保存聊天消息到本地存储
    saveChatMessages() {
      if (!this.sessionStatus.sessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + this.sessionStatus.sessionId;

        // 过滤消息：去重处理 + 排除错误消息
        const validMessages = [];
        const seenMessageIds = new Set();

        for (const message of this.messages) {
          // 跳过重复消息
          if (message.messageId && seenMessageIds.has(message.messageId)) {
            continue;
          }

          // 跳过错误消息
          if (message.hasError || this.isErrorMessage(message)) {
            continue;
          }

          if (message.messageId) {
            seenMessageIds.add(message.messageId);
          }
          validMessages.push(message);
        }

        // 只保存最新的200条有效消息
        const messagesToSave = validMessages.slice(-200);
        localStorage.setItem(storageKey, JSON.stringify(messagesToSave));

        // 注意：这里不更新当前消息列表，因为错误消息在界面上仍需要显示
        // 只是不保存到localStorage而已
      } catch (error) {
        console.error("保存聊天消息失败:", error);
      }
    },

    // 从本地存储加载聊天消息
    loadChatMessages(sessionId) {
      if (!sessionId) {
        sessionId = this.sessionStatus.sessionId;
      }
      if (!sessionId) return false;

      try {
        const storageKey = this.chatMessagesStoragePrefix + sessionId;
        const savedMessagesJson = localStorage.getItem(storageKey);

        if (savedMessagesJson) {
          const savedMessages = JSON.parse(savedMessagesJson);
          if (Array.isArray(savedMessages) && savedMessages.length > 0) {
            this.messages = savedMessages.map(msg => ({
              ...msg,
              content: formatOutputContent(msg.content),
              // 清除历史消息的流式状态，避免界面状态混乱
              isStreaming: false
            }));

            this.$nextTick(() => {
              this.scrollToBottom();
            });
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error("加载聊天消息失败:", error);
        return false;
      }
    },

    // 清除指定会话的聊天消息缓存
    clearChatMessages(sessionId) {
      if (!sessionId) {
        sessionId = this.sessionStatus.sessionId;
      }
      if (!sessionId) return;

      try {
        const storageKey = this.chatMessagesStoragePrefix + sessionId;
        localStorage.removeItem(storageKey);
      } catch (error) {
        console.error("清除聊天消息失败:", error);
      }
    },

    // 刷新聊天
    refreshChat() {
      if (this.messages.length > 0 && confirm("确定要清除当前对话记录吗？")) {
        this.clearChatMessages();
        this.messages = [];
        this.addWelcomeMessage();
      } else if (this.messages.length === 0) {
        this.addWelcomeMessage();
      }
    },

    // 关闭聊天
    closeChat() {
      this.stopConnection();
      this.$emit('close-chat');
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const messagesContainer = this.$refs.messagesContainer;
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
      });
    },

    // 停止流式输出
    stopStreaming() {
      // 中止流式请求
      if (this.streamController) {
        this.streamController.abort();
        this.streamController = null;
      }

      // 重置流式状态
      this.isStreaming = false;
      this.isTyping = false;

      // 找到正在流式传输的消息并停止
      const streamingMessage = this.messages.find(msg => msg.isStreaming);
      if (streamingMessage) {
        streamingMessage.isStreaming = false;
        // 在内容末尾添加停止标记
        if (!streamingMessage.content.includes('已停止生成')) {
          streamingMessage.content += '\n\n<div style="color: #999; font-style: italic;">已停止生成</div>';
        }
        this.updateMessage(streamingMessage.messageId, streamingMessage);
      }

      this.$message.info('已停止生成');
    },

    // 清理本地存储中的重复消息
    cleanupDuplicateMessages() {
      const prefix = this.chatMessagesStoragePrefix;

      try {
        // 遍历所有localStorage的键
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key && key.startsWith(prefix)) {
            const savedData = localStorage.getItem(key);
            if (savedData) {
              try {
                const messages = JSON.parse(savedData);
                if (Array.isArray(messages)) {
                  // 去重处理
                  const uniqueMessages = [];
                  const seenMessageIds = new Set();

                  for (const message of messages) {
                    if (message.messageId && seenMessageIds.has(message.messageId)) {
                      continue;
                    }
                    if (message.messageId) {
                      seenMessageIds.add(message.messageId);
                    }
                    uniqueMessages.push(message);
                  }

                  // 如果发现重复消息，更新存储
                  if (uniqueMessages.length !== messages.length) {
                    localStorage.setItem(key, JSON.stringify(uniqueMessages));
                  }
                }
              } catch (error) {
                console.error(`清理会话 ${key} 的消息时出错:`, error);
              }
            }
          }
        }
      } catch (error) {
        console.error("清理重复消息失败:", error);
      }
    },

  },
};
</script>

<style lang="scss" scoped>
.chat-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #606266;

  i {
    font-size: 24px;
    margin-bottom: 10px;
    animation: rotate 2s linear infinite;
  }

  span {
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;

  .chat-message {
    display: flex;
    margin-bottom: 20px;

    &.user-message {
      justify-content: flex-end;

      .message-content {
        background-color: #409eff;
        color: white;
        margin-right: 10px;
      }
    }

    &.system-message {
      justify-content: flex-start;

      .message-content {
        background-color: #f5f5f5;
        color: #303133;
        margin-left: 10px;
      }
    }
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-content {
    max-width: 60%;
    border-radius: 8px;
    padding: 10px 15px;

    .message-text {
      position: relative;

      p {
        margin: 0;
        line-height: 1.5;
        word-wrap: break-word;
      }

      // 流式传输状态样式
      &.streaming {
        border-left: 3px solid #409eff;
        padding-left: 10px;
        background-color: rgba(64, 158, 255, 0.05);
      }

      // 错误状态样式
      &.error {
        border-left: 3px solid #f56c6c;
        padding-left: 10px;
        background-color: rgba(245, 108, 108, 0.05);
      }

      // 流式传输指示器
      .streaming-indicator {
        display: inline-flex;
        align-items: center;
        margin-left: 8px;

        span {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background-color: #409eff;
          margin-right: 2px;
          animation: streamingDot 1.5s infinite;

          &:nth-child(2) {
            animation-delay: 0.3s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }

    .message-info {
      margin-top: 5px;
      font-size: 12px;
      opacity: 0.7;
      display: flex;
      align-items: center;
      gap: 8px;

      .streaming-text {
        color: #409eff;
        font-style: italic;
      }

      .error-text {
        color: #f56c6c;
        font-style: italic;
      }
    }
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 10px 20px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #999;
    margin-right: 5px;
    animation: typing 1.5s infinite;

    &:nth-child(2) {
      animation-delay: 0.5s;
    }

    &:nth-child(3) {
      animation-delay: 1s;
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes streamingDot {
  0%, 60%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.chat-input {
  padding: 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  align-items: flex-end;
  gap: 10px;

  // 停止生成按钮样式
  .el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: white;
    transition: all 0.3s;

    &:hover {
      background-color: #f78989;
      border-color: #f78989;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    i {
      margin-right: 4px;
      font-size: 14px;
    }
  }

  // 发送按钮增强样式
  .el-button--primary {
    transition: all 0.3s;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;

  i {
    font-size: 64px;
    margin-bottom: 20px;
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}
</style>
