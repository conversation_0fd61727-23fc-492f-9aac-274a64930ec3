// SignalR连接管理混入
import { api } from "@/api/request"
import { formatOutputContent } from '@/utils'
import * as signalR from "@microsoft/signalr"

export default {
  data() {
    return {
      // SignalR连接对象
      hubConnection: null,
      // 会话状态
      sessionStatus: {
        isConnected: false,
        sessionId: null,
        userName: "",
      },
      // 连接配置
      connectionConfig: {
        timeout: 30000,
        keepAliveInterval: 15000,
        serverTimeout: 30000,
        reconnectDelays: [0, 2000, 10000, 30000]
      }
    };
  },

  beforeDestroy() {
    // 组件销毁前关闭连接
    this.stopConnection();
  },

  methods: {
    // 生成GUID方法
    generateGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    // 检查登录并加入聊天组
    checkLoginAndJoinChat() {
      try {
        // 获取用户信息
        const userStr = localStorage.getItem("user");
        if (!userStr) {
          this.$message.warning("用户未登录，部分功能可能受限");
          return;
        }

        const user = JSON.parse(userStr);
        this.sessionStatus.userName = user.name || "用户";

        // 创建连接
        this.createConnection();

      } catch (error) {
        console.error("检查登录状态失败:", error);
        this.$message.warning("初始化聊天失败，请刷新页面重试");
      }
    },

    // 创建与服务器的SignalR连接
    createConnection() {
      try {
        // 如果已经有连接且正在连接或已连接，则不重复创建
        if (this.hubConnection &&
            (this.hubConnection.state === signalR.HubConnectionState.Connected ||
             this.hubConnection.state === signalR.HubConnectionState.Connecting)) {
          console.log("SignalR连接已存在，跳过创建");
          return;
        }

        // 使用SignalR专用的环境变量
        const signalRUrl = process.env.VUE_APP_SIGNALR_URL || 'http://localhost:5280';

        // 构建查询参数
        const queryParams = new URLSearchParams({
          sessionId: this.sessionStatus.sessionId || '',
          clientId: this.appId || '',
          sourceType: this.sourceType || 1
        });

        // 修正SignalR端点路径 - 使用标准的JWT认证端点
        const serverUrl = `${signalRUrl}/signalr-hubs/jwt?${queryParams.toString()}`;

        // 获取认证令牌
        const token = localStorage.getItem("token");

        if (!token) {
          this.$message.warning("用户未登录，无法建立聊天连接");
          return;
        }

        // 先清理旧的连接
        this.cleanupConnection();

        // 构建连接参数
        const connectionOptions = {
          accessTokenFactory: () => token,
          transport: signalR.HttpTransportType.WebSockets |
                    signalR.HttpTransportType.ServerSentEvents |
                    signalR.HttpTransportType.LongPolling,
          skipNegotiation: false,
          timeout: this.connectionConfig.timeout,
          keepAliveIntervalInMilliseconds: this.connectionConfig.keepAliveInterval,
          serverTimeoutInMilliseconds: this.connectionConfig.serverTimeout
        };

        // 创建连接
        this.hubConnection = new signalR.HubConnectionBuilder()
          .withUrl(serverUrl, connectionOptions)
          .withAutomaticReconnect(this.connectionConfig.reconnectDelays)
          .configureLogging(signalR.LogLevel.Warning) // 改为Warning级别，减少日志
          .build();

        // 设置连接事件处理器
        this.setupConnectionEvents();

        // 启动连接
        this.startConnection();

      } catch (error) {
        console.error("创建连接失败:", error);
        this.$showFriendlyError(error, "连接服务器失败，请稍后重试");
      }
    },

    // 清理旧连接
    cleanupConnection() {
      if (this.hubConnection) {
        console.log("清理旧连接...");
        try {
          this.hubConnection.off("ReceiveMessage");
          this.hubConnection.off("UserStatusChanged");
          this.hubConnection.off("SystemNotification");
          this.hubConnection.off("ReceiveSystemNotification");
          this.hubConnection.off("ReceiveSessionHistory");
          this.hubConnection.off("OnError");
        } catch (error) {
          console.warn("清理旧连接事件监听器出错:", error);
        }
        this.hubConnection = null;
      }
    },

    // 设置连接事件处理器
    setupConnectionEvents() {
      if (!this.hubConnection) return;

      // 连接关闭事件
      this.hubConnection.onclose((error) => {
        this.sessionStatus.isConnected = false;
        if (error) {
          console.error("连接意外关闭:", error.message);
        }
        this.$emit('connection-changed', { isConnected: false, error });
      });

      // 重连中事件
      this.hubConnection.onreconnecting(() => {
        this.sessionStatus.isConnected = false;
        this.$emit('connection-changed', { isConnected: false, reconnecting: true });
      });

      // 重连成功事件
      this.hubConnection.onreconnected((connectionId) => {
        this.sessionStatus.isConnected = true;
        this.$emit('connection-changed', { isConnected: true, reconnected: true, connectionId });
      });

      // 注册消息接收事件
      this.hubConnection.on("ReceiveMessage", (message) => {
        this.handleReceiveMessage(message);
      });

      // 接收会话历史
      this.hubConnection.on("ReceiveSessionHistory", (messages) => {
        if (Array.isArray(messages) && messages.length > 0) {
          this.handleSessionHistory(messages);
        }
      });

      // 注册系统通知事件（静默处理）
      this.hubConnection.on("SystemNotification", () => {
        // 系统通知静默处理，不打印到控制台
      });

      // 统一的系统通知处理（静默处理）
      this.hubConnection.on("ReceiveSystemNotification", () => {
        // 系统通知静默处理，不打印到控制台
      });

      // 用户状态变更事件（静默处理）
      this.hubConnection.on("UserStatusChanged", () => {
        // 用户状态变更静默处理，不打印到控制台
      });

      // 错误处理事件
      this.hubConnection.on("OnError", (error) => {
        console.error("SignalR服务器错误:", error);
      });
    },

    // 启动连接
    async startConnection() {
      if (!this.hubConnection) return;

      if (this.hubConnection.state === signalR.HubConnectionState.Connected) {
        this.sessionStatus.isConnected = true;
        return;
      }

      try {
        await this.hubConnection.start();
        this.sessionStatus.isConnected = true;
        console.log("SignalR连接成功");
        this.$emit('connection-changed', { isConnected: true });
        this.$emit('session-ready', { sessionId: this.sessionStatus.sessionId });
      } catch (error) {
        console.error("连接失败:", error);
        this.sessionStatus.isConnected = false;
        this.$emit('connection-changed', { isConnected: false, error });
      }
    },

    // 发送消息
    async sendMessageToSignalR(messageText, sessionId = null, senderName = null) {
      if (!this.sessionStatus.isConnected || !this.hubConnection) {
        throw new Error("未连接到服务器");
      }

      const targetSessionId = sessionId || this.sessionStatus.sessionId;
      const userName = senderName || this.sessionStatus.userName || "用户";

      if (!targetSessionId || !messageText.trim()) {
        throw new Error("会话ID或消息内容不能为空");
      }

            try {
        // 构造统一消息DTO对象
        const messageDto = {
          sessionId: targetSessionId,
          contentType: 1, // EnumMessageContentType.Text = 1
          content: messageText.trim(),
          senderName: userName || "人工客服",
          customSenderId: null, // JWT认证用户不需要自定义ID
          senderType: 2, // 人工客服类型
          // 文本消息不需要设置metadata
        };

        await this.hubConnection.invoke("SendMessage", messageDto);
        return true;
      } catch (error) {
        console.error("发送SignalR消息失败:", error);
        throw error;
      }
    },

    // 停止连接
    async stopConnection() {
      if (!this.hubConnection) return;

      try {
        // 不调用 LeaveSession，直接关闭连接
        if (this.hubConnection.state === signalR.HubConnectionState.Connected) {
          await this.hubConnection.stop();
        }
      } catch (error) {
        console.error("停止连接时出错:", error);
      } finally {
        this.sessionStatus.isConnected = false;
        this.cleanupConnection();
        this.$emit('connection-changed', { isConnected: false, stopped: true });
      }
    },

    // 处理接收到的消息
    handleReceiveMessage(message) {
      if (this.isOwnMessage(message)) {
        return;
      }

      const content = this.extractMessageContent(message);
      if (content) {
        const messageData = {
          role: "assistant",
          content: formatOutputContent(content),
          time: this.formatTime(new Date()),
          senderId: message.senderId,
          senderName: message.senderName,
          messageId: message.messageId,
        };

        // 直接调用组件的addMessage方法，而不是发送事件
        if (typeof this.addMessage === 'function') {
          this.isTyping = false;
          this.addMessage(messageData);
        }

        // 发送给父组件的事件
        this.$emit('message-received', messageData);
      }
    },

    // 处理会话历史
    handleSessionHistory(messages) {
      const historyMessages = messages
        .filter(msg => !this.isOwnMessage(msg))
        .map(msg => ({
          role: "assistant",
          content: formatOutputContent(this.extractMessageContent(msg)),
          time: this.formatTime(new Date(msg.timestamp || Date.now())),
          senderId: msg.senderId,
          senderName: msg.senderName,
          messageId: msg.messageId,
          isHistory: true
        }));

      if (historyMessages.length > 0) {
        // 直接调用组件的addMessage方法
        if (typeof this.addMessage === 'function') {
          historyMessages.forEach(msg => {
            this.addMessage(msg);
          });
        }

        // 发送给父组件的事件
        this.$emit('session-history-loaded', historyMessages);
      }
    },

    // 检查是否是自己发送的消息
    isOwnMessage(message) {
      const currentSenderId = this.getCurrentUserId();
      return message.senderId === currentSenderId ||
             message.sessionId === currentSenderId ||
             message.customSenderId === currentSenderId;
    },

    // 获取当前用户ID
    getCurrentUserId() {
      try {
        // 直接从localStorage的user对象中获取用户ID
        const userStr = localStorage.getItem("user");
        console.log("尝试获取用户ID，user数据:", userStr ? "存在" : "不存在");

        if (userStr) {
          const user = JSON.parse(userStr);
          console.log('用户对象:', user);

          const userId = user.id || user.userId || user.uid;
          console.log('提取的用户ID:', userId);

          return userId;
        }

        console.warn('localStorage中没有用户信息');

      } catch (error) {
        console.error('解析用户信息失败:', error);
      }

      // 降级方案：使用连接ID
      const connectionId = this.hubConnection ? this.hubConnection.connectionId : null;
      console.log('使用连接ID作为用户ID:', connectionId);
      return connectionId;
    },

    // 提取消息内容
    extractMessageContent(message) {
      if (message.type === "Text" || message.type === 1 || !message.type) {
        return message.textContent || message.content || message.text || "";
      }

      // 对于图片类型消息，不返回文本标识，让UI组件直接处理图片显示
      if (message.type === 2 || message.type === "Image") {
        return message.textContent || message.content || message.text || "";
      }

      const typeNames = { 0: "未知", 1: "文本", 2: "图片", 3: "语音", 4: "视频", 5: "文件" };
      return `[${typeNames[message.type] || "未知类型"}消息]`;
    },

    // 格式化时间
    formatTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${year}.${month}.${day} ${hours}:${minutes}:${seconds}`;
    },

    // 重连
    async reconnect() {
      await this.stopConnection();
      setTimeout(() => this.createConnection(), 1000);
    },

    // 执行工作流
    async executeWorkflow(message, sessionId) {
      if (!this.appId) {
        throw new Error("应用ID不存在");
      }

      // 根据sourceType决定调用不同的API接口
      if (this.sourceType === 1) {
        // 应用调用 - 以流式方式执行工作流
        const params = {
          sessionId: sessionId,
          flowDetailType: "SessionFlow",
          flowId: this.appId,
          textInput: message,
          imageInput: "",
          fileInput: "",
          customVariables: {},
        };

        console.log("执行应用API调用（流式）:", params);

        // 创建一个助手消息对象用于实时更新
        const assistantMessage = {
          role: "assistant",
          content: '<div class="markdown-content">正在思考中...</div>', // 给一个初始内容
          time: this.formatTime(new Date()),
          messageId: this.generateGuid(),
          fromWorkflow: true,
          isStreaming: true
        };

        // 先添加空的助手消息到界面
        if (typeof this.addMessage === 'function') {
          console.log("添加初始助手消息:", assistantMessage);
          this.addMessage(assistantMessage);
        } else {
          console.error("addMessage 方法不存在");
        }

                return new Promise((resolve, reject) => {
          let fullResponse = "";
          let hasError = false;
          let isCompleted = false;

          console.log("开始调用 api.workflow.stream");

          // 创建流式控制器
          if (this.streamController) {
            this.streamController.abort(); // 中止之前的请求
          }
          this.streamController = new AbortController();

          // 设置超时保护，防止流式响应卡住
          const timeoutId = setTimeout(() => {
            if (!hasError && !isCompleted) {
              console.warn("流式响应超时，强制完成");
              isCompleted = true;
              assistantMessage.content = formatOutputContent(fullResponse || "响应超时，请重试。");
              assistantMessage.isStreaming = false;

              if (typeof this.updateMessage === 'function') {
                this.updateMessage(assistantMessage.messageId, assistantMessage);
              }

              // 清理控制器
              this.streamController = null;
              resolve(assistantMessage);
            }
          }, 60000); // 60秒超时

          const streamPromise = api.workflow.stream(
            params,
            // onMessage 回调 - 处理每个流式消息
            (messageData) => {
              try {
                console.log("收到流式消息:", messageData);

                if (messageData.IsSuccess && messageData.Data) {
                  let responseContent = '';
                  const isFinal = messageData.Data.IsFinal || false;

                  // 优先检查 Response 字段
                  if (messageData.Data.Response) {
                    responseContent = messageData.Data.Response;
                  }
                  // 当 CurrentType === 2 时，检查 Output 字段
                  else if (messageData.Data.CurrentType === 2 && messageData.Data.Output) {
                    responseContent = messageData.Data.Output;
                  }

                  console.log("收到流式数据:", {
                    responseContent,
                    isFinal,
                    responseLength: responseContent.length,
                    messageData: messageData
                  });

                  // 累积响应内容
                  fullResponse += responseContent;
                  console.log("累积响应内容:", { responseContent, fullResponse, isFinal });

                  // 实时更新消息内容
                  assistantMessage.content = formatOutputContent(fullResponse);
                  assistantMessage.isStreaming = !isFinal;

                  console.log("更新消息内容:", {
                    messageId: assistantMessage.messageId,
                    content: assistantMessage.content,
                    isStreaming: assistantMessage.isStreaming
                  });

                  // 更新界面显示
                  if (typeof this.updateMessage === 'function') {
                    this.updateMessage(assistantMessage.messageId, assistantMessage);
                    console.log("调用 updateMessage 成功");
                  } else if (typeof this.addMessage === 'function') {
                    // 如果没有updateMessage方法，重新添加消息
                    console.log("updateMessage 不存在，使用 addMessage");
                    this.addMessage(assistantMessage);
                  } else {
                    console.error("updateMessage 和 addMessage 方法都不存在");
                  }

                  // 发送消息更新事件
                  this.$emit('message-updated', assistantMessage);

                  // 如果是最后一条消息，标记完成
                  if (isFinal && !isCompleted) {
                    isCompleted = true;
                    clearTimeout(timeoutId);
                    console.log("流式响应完成，最终内容:", fullResponse);
                    // 清理控制器
                    this.streamController = null;
                    resolve(assistantMessage);
                  }

                } else if (messageData.ErrorCode !== null && messageData.ErrorCode !== undefined) {
                  // 处理错误响应
                  if (!hasError && !isCompleted) {
                    hasError = true;
                    clearTimeout(timeoutId);
                    const errorMessage = messageData.Message || "流式响应出错";
                    console.error("流式响应错误:", messageData);

                    assistantMessage.content = `抱歉，处理您的请求时出现了错误，请重试`;
                    assistantMessage.isStreaming = false;
                    assistantMessage.hasError = true;

                    if (typeof this.updateMessage === 'function') {
                      this.updateMessage(assistantMessage.messageId, assistantMessage);
                    }

                    // 清理控制器
                    this.streamController = null;
                    reject(new Error(errorMessage));
                  }
                }
              } catch (error) {
                if (!hasError && !isCompleted) {
                  console.error("处理流式消息时出错:", error);

                  // 检查是否为用户主动中止的AbortError
                  if (error.name === 'AbortError' || error.message.includes('aborted')) {
                    console.log("流式请求被用户主动中止 (onError)");
                    // 用户主动中止，不显示错误信息，直接停止
                    hasError = true;
                    isCompleted = true;
                    clearTimeout(timeoutId);
                    this.streamController = null;
                    resolve(assistantMessage);
                    return;
                  }

                  hasError = true;
                  clearTimeout(timeoutId);

                  assistantMessage.content = "抱歉，处理消息时出现了错误。";
                  assistantMessage.isStreaming = false;
                  assistantMessage.hasError = true;

                  if (typeof this.updateMessage === 'function') {
                    this.updateMessage(assistantMessage.messageId, assistantMessage);
                  }

                  // 清理控制器
                  this.streamController = null;
                  reject(error);
                }
              }
            },
            // onError 回调
            (error) => {
              if (!hasError && !isCompleted) {
                console.error("流式请求出错:", error);

                // 检查是否为用户主动中止的AbortError
                if (error.name === 'AbortError' || error.message.includes('aborted')) {
                  console.log("流式请求被用户主动中止 (onError)");
                  // 用户主动中止，不显示错误信息，直接停止
                  hasError = true;
                  isCompleted = true;
                  clearTimeout(timeoutId);
                  this.streamController = null;
                  resolve(assistantMessage);
                  return;
                }

                hasError = true;
                clearTimeout(timeoutId);
                assistantMessage.content = "抱歉，处理您的请求时出现了错误。";
                assistantMessage.isStreaming = false;
                assistantMessage.hasError = true;

                if (typeof this.updateMessage === 'function') {
                  this.updateMessage(assistantMessage.messageId, assistantMessage);
                }

                // 清理控制器
                this.streamController = null;
                reject(error);
              }
            },
            // onComplete 回调
            () => {
              if (!hasError && !isCompleted) {
                isCompleted = true;
                clearTimeout(timeoutId);
                console.log("流式响应完成，最终内容:", fullResponse);
                assistantMessage.content = formatOutputContent(fullResponse || "我已收到您的消息，但暂时无法处理。");
                assistantMessage.isStreaming = false;

                if (typeof this.updateMessage === 'function') {
                  this.updateMessage(assistantMessage.messageId, assistantMessage);
                }

                // 清理控制器
                this.streamController = null;
                resolve(assistantMessage);
              }
            },
            // 传递AbortController的signal
            this.streamController ? this.streamController.signal : null
          );

          // 处理 stream Promise 的结果 - 主要用于错误处理
          streamPromise.then((result) => {
            console.log("api.workflow.stream Promise resolved:", result);
            // stream Promise 成功完成，但不在这里处理 resolve
            // 让 onMessage 或 onComplete 回调处理完成逻辑
          }).catch((error) => {
            console.error("api.workflow.stream Promise rejected:", error);
            if (!hasError && !isCompleted) {

              // 检查是否为用户主动中止的AbortError
              if (error.name === 'AbortError' || error.message.includes('aborted')) {
                console.log("流式请求被用户主动中止 (Promise.catch)");
                // 用户主动中止，不显示错误信息，直接停止
                hasError = true;
                isCompleted = true;
                clearTimeout(timeoutId);
                this.streamController = null;
                resolve(assistantMessage);
                return;
              }

              hasError = true;
              clearTimeout(timeoutId);
              assistantMessage.content = `抱歉，处理您的请求时出现了错误，请重试`;
              assistantMessage.isStreaming = false;
              assistantMessage.hasError = true;

              if (typeof this.updateMessage === 'function') {
                this.updateMessage(assistantMessage.messageId, assistantMessage);
              }

              // 清理控制器
              this.streamController = null;
              reject(error);
            }
          });
        });

      } else if (this.sourceType === 2) {
        // 工作流调用 - 执行工作流（非流式）
        const params = {
          sessionId: sessionId,
          flowDetailType: "WorkFlow",
          flowId: this.appId,
          textInput: message,
          imageInput: "",
          fileInput: "",
          customVariables: {},
        };

        console.log("执行工作流API调用:", params);
        const result = await api.workflow.execute(params);

        if (result && result.code === 200 && result.data) {
          return {
            role: "assistant",
            content: formatOutputContent(result.data.output || "我已收到您的消息，但暂时无法处理。"),
            time: this.formatTime(new Date()),
            messageId: this.generateGuid(),
            fromWorkflow: true
          };
        } else {
          throw new Error(result?.message || "工作流执行失败");
        }

      } else {
        throw new Error(`不支持的sourceType: ${this.sourceType}`);
      }
    }
  }
};
