<template>
  <div class="left-panel">
    <!-- 搜索框 -->
    <div v-if="searchable" class="search-box">
      <el-input
        v-model="searchQuery"
        placeholder="搜索聊天记录..."
        size="small"
        prefix-icon="el-icon-search"
        clearable
      ></el-input>
    </div>

    <!-- 聊天列表容器 -->
    <div class="list-container" ref="listContainer" @scroll="handleScroll">
      <!-- 初次加载状态 -->
      <div v-if="isLoading && chatHistory.length === 0" class="loading-container">
        <div class="loading-spinner">
          <i class="el-icon-loading"></i>
          <span>正在加载聊天列表...</span>
        </div>
      </div>

      <!-- 聊天列表 -->
      <div v-else-if="filteredChats.length > 0" class="chat-list">
        <div
          v-for="(chat, index) in filteredChats"
          :key="chat.id || index"
          :class="['chat-list-item', { active: currentChatIndex === index }]"
          @click="selectChat(index)"
        >
          <div class="chat-preview">
            <el-avatar
              class="chat-avatar"
              :size="40"
              :src="chat.profilePhoto"
              icon="el-icon-user-solid"
            ></el-avatar>

            <div class="chat-info">
              <div class="chat-name">
                {{ chat.name || `${chat.appName || "应用"} - ${chat.channelName || "渠道"}` }}
              </div>
              <div class="chat-last-message">
                {{ chat.lastMessage || "暂无消息" }}
              </div>
              <div class="chat-time-container">
                <span class="chat-time">{{ chat.time }}</span>
                <el-tag
                  :type="getChannelTagType(chat.channelName)"
                  size="mini"
                  class="channel-tag"
                >
                  {{ chat.channelName || "未知渠道" }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载更多按钮 -->
        <div v-if="!searchQuery && hasMore && !isLoadingMore" class="load-more-container">
          <el-button
            type="text"
            @click="loadMore"
            class="load-more-btn"
          >
            加载更多
          </el-button>
        </div>

        <!-- 加载更多状态 -->
        <div v-if="isLoadingMore" class="loading-more-container">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>

        <!-- 没有更多数据提示 -->
        <div v-if="!searchQuery && !hasMore && chatHistory.length > 0" class="no-more-container">
          <span>没有更多数据了</span>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="el-icon-chat-dot-round"></i>
        <p>暂无聊天记录</p>
      </div>
    </div>
  </div>
</template>

<script>
import { api } from "@/api/request";

export default {
  name: "ChatHistory",
  props: {
    // 应用来源类型
    sourceType: {
      type: Number,
      required: true
    },
    // 当前会话ID
    currentSessionId: {
      type: String,
      default: null
    },
    // 应用ID（用于自动选中）
    appId: {
      type: [String, Number],
      default: null
    },
    // 是否显示搜索框
    searchable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 会话列表数据
      chatHistory: [],
      // 当前选中的会话索引
      currentChatIndex: -1,
      // 加载状态
      isLoading: false,
      // 搜索查询
      searchQuery: "",
      // 防抖相关
      lastUpdateTime: 0,
      updateTimer: null,

      // 瀑布流加载相关
      skipCount: 0,           // 跳过的数量
      maxResultCount: 10,     // 每次加载的数量
      totalCount: 0,          // 总数量
      hasMore: true,          // 是否还有更多数据
      isLoadingMore: false,   // 是否正在加载更多
    };
  },
  computed: {
    // 过滤后的聊天列表
    filteredChats() {
      if (!this.searchQuery) {
        return this.chatHistory;
      }

      const query = this.searchQuery.toLowerCase();
      return this.chatHistory.filter(
        (chat) =>
          (chat.sessionName &&
            chat.sessionName.toLowerCase().includes(query)) ||
          (chat.description &&
            chat.description.toLowerCase().includes(query)) ||
          (chat.appName && chat.appName.toLowerCase().includes(query)) ||
          (chat.channelName && chat.channelName.toLowerCase().includes(query))
      );
    }
  },
  watch: {
    // 监听sourceType变化，重新加载数据
    sourceType: {
      handler(newSourceType) {
        if (newSourceType) {
          this.fetchChatSessions(true); // 重新开始分页
        }
      },
      immediate: true
    },
    // 监听currentSessionId变化，更新选中状态
    currentSessionId: {
      handler(newSessionId) {
        this.updateSelectedSession(newSessionId);
      },
      immediate: true
    },
    // 监听appId变化，更新选中状态
    appId: {
      handler() {
        this.updateSelectedSession(this.currentSessionId);
      },
      immediate: true
    }
  },
  methods: {
    // 获取聊天会话列表
    fetchChatSessions(isRefresh = false) {
      // 如果是刷新，重置分页参数
      if (isRefresh) {
        this.skipCount = 0;
        this.hasMore = true;
        this.chatHistory = [];
      }

      // 防抖处理
      if (this.isLoading && !this.isLoadingMore) {
        return;
      }

      // 防抖逻辑只在首次加载时生效
      if (!this.isLoadingMore) {
        const currentTime = new Date().getTime();
        if (currentTime - this.lastUpdateTime < 2000) {
          if (this.updateTimer) {
            clearTimeout(this.updateTimer);
          }
          this.updateTimer = setTimeout(() => {
            this._fetchChatSessionsActual();
          }, 2000);
          return;
        }
      }

      this._fetchChatSessionsActual();
    },

    // 实际获取聊天列表的方法
    _fetchChatSessionsActual() {
      this.lastUpdateTime = new Date().getTime();

      // 设置加载状态
      if (!this.isLoadingMore) {
        this.isLoading = true;
        this.$emit("loading-changed", true);
      }

      // 获取认证令牌
      const token = localStorage.getItem("token");
      if (!token) {
        this.$message.warning("用户未登录，无法获取聊天历史");
        this.isLoading = false;
        this.isLoadingMore = false;
        this.$emit("loading-changed", false);
        return;
      }

      // 发起API请求获取聊天会话列表，传入分页参数
      api.chat.getSessions({
        skipCount: this.skipCount,
        maxResultCount: this.maxResultCount,
        SourceType: this.sourceType,
        IsTestSession: true
      })
        .then((result) => {
          if (
            result &&
            result.code === 200 &&
            result.data &&
            result.data.items
          ) {
            // 处理返回的聊天会话数据
            const newChatHistory = result.data.items.map((session) => {
              // 转换日期格式为显示时间
              const creationTime = new Date(session.creationTime);
              const now = new Date();
              let timeDisplay = "";

              // 格式化显示时间: 今天显示小时:分钟，其他日期显示月-日
              if (creationTime.toDateString() === now.toDateString()) {
                timeDisplay = `${creationTime
                  .getHours()
                  .toString()
                  .padStart(2, "0")}:${creationTime
                  .getMinutes()
                  .toString()
                  .padStart(2, "0")}`;
              } else {
                timeDisplay = `${(creationTime.getMonth() + 1)
                  .toString()
                  .padStart(2, "0")}-${creationTime
                  .getDate()
                  .toString()
                  .padStart(2, "0")}`;
              }

              return {
                id: session.id,
                clientId: session.clientId || session.id,
                sessionId: session.sessionID,
                name: session.sessionName,
                avatar: "",
                profilePhoto: session.profilePhoto,
                introduce: session.introduce,
                lastMessage: session.description || "无消息",
                time: timeDisplay,
                appName: session.appName,
                messages: [],
                channelName: session.channelName,
              };
            });

            // 更新分页信息
            this.totalCount = result.data.totalCount || 0;
            this.hasMore = (this.skipCount + this.maxResultCount) < this.totalCount;

            // 保存现有当前选中的会话ID
            const currentSelectedSessionId =
              this.currentChatIndex >= 0 &&
              this.chatHistory[this.currentChatIndex]
                ? this.chatHistory[this.currentChatIndex].sessionId
                : null;

            // 如果是加载更多，追加到现有列表；如果是刷新，替换列表
            if (this.isLoadingMore) {
              this.chatHistory = [...this.chatHistory, ...newChatHistory];
            } else {
              this.chatHistory = newChatHistory;
            }

            // 恢复之前选中的会话
            if (currentSelectedSessionId && !this.isLoadingMore) {
              const newIndex = this.chatHistory.findIndex(
                (chat) => chat.sessionId === currentSelectedSessionId
              );
              if (newIndex >= 0) {
                this.currentChatIndex = newIndex;
              }
            }

            // 根据传入的currentSessionId更新选中状态（只在首次加载时处理）
            if (!this.isLoadingMore) {
              this.updateSelectedSession(this.currentSessionId, true);
              this.$emit("sessions-loaded", this.chatHistory);
            }
          } else {
            console.error("获取聊天历史返回数据格式错误:", result);
          }
        })
        .catch((error) => {
          console.error("获取聊天历史失败:", error);
        })
        .finally(() => {
          this.isLoading = false;
          this.isLoadingMore = false;
          this.$emit("loading-changed", false);
        });
    },

    // 选择聊天会话
    selectChat(index) {
      if (this.currentChatIndex === index) {
        return;
      }

      this.currentChatIndex = index;
      const chat = this.chatHistory[index];

      if (chat) {
        this.$emit("session-selected", chat);
      }
    },

    // 更新选中的会话（根据sessionId）
    updateSelectedSession(sessionId, shouldEmitEvent = false) {
      if (!sessionId && !this.appId) {
        return;
      }

      if (this.chatHistory.length === 0) {
        return;
      }

      let foundIndex = -1;

      // 首先尝试通过sessionId匹配（优先级更高，因为sessionId更精确）
      if (sessionId) {
        foundIndex = this.chatHistory.findIndex(
          (chat) => chat.sessionId === sessionId
        );
      }

      // 如果通过sessionId没找到，再尝试通过appId匹配
      if (foundIndex < 0 && this.appId) {
        foundIndex = this.chatHistory.findIndex(
          (chat) => {
            const matchClientId = chat.clientId && (chat.clientId.toString() === this.appId.toString());
            const matchId = chat.id && (chat.id.toString() === this.appId.toString());
            return matchClientId || matchId;
          }
        );
      }

      // 总是更新选中状态（UI同步）
      if (foundIndex >= 0 && this.currentChatIndex !== foundIndex) {
        this.currentChatIndex = foundIndex;
      }

      // 只有在明确需要触发事件时才触发（避免重复事件）
      if (shouldEmitEvent && foundIndex >= 0) {
        const selectedChat = this.chatHistory[foundIndex];
        if (selectedChat) {
          this.$emit("session-selected", selectedChat);
        }
      }
    },

    // 刷新聊天列表
    refresh() {
      this.fetchChatSessions(true); // 传入true表示刷新，重置分页
    },

    // 获取选中的会话数据
    getSelectedSession() {
      if (this.currentChatIndex >= 0 && this.chatHistory[this.currentChatIndex]) {
        return this.chatHistory[this.currentChatIndex];
      }
      return null;
    },

    // 加载更多
    loadMore() {
      if (this.isLoadingMore || !this.hasMore) {
        return;
      }

      this.isLoadingMore = true;
      this.skipCount += this.maxResultCount;
      this._fetchChatSessionsActual();
    },

    // 处理滚动事件
    handleScroll() {
      const container = this.$refs.listContainer;
      if (container) {
        const scrollTop = container.scrollTop;
        const scrollHeight = container.scrollHeight;
        const clientHeight = container.clientHeight;

        // 距离底部100px时触发加载更多
        if (scrollTop + clientHeight >= scrollHeight - 100 && this.hasMore && !this.isLoadingMore) {
          this.loadMore();
        }
      }
    },

    // 根据channelName获取标签类型
    getChannelTagType(channelName) {
      if (!channelName) return '';

      const lowerChannelName = channelName.toLowerCase();

      // 根据渠道名称设置不同的标签颜色
      if (lowerChannelName.includes('app') || lowerChannelName.includes('应用')) {
        return 'primary'; // 蓝色
      } else if (lowerChannelName.includes('workflow') || lowerChannelName.includes('工作流')) {
        return 'success'; // 绿色
      } else if (lowerChannelName.includes('api')) {
        return 'warning'; // 橙色
      } else if (lowerChannelName.includes('web') || lowerChannelName.includes('网页')) {
        return 'info'; // 灰色
      } else {
        return ''; // 默认颜色
      }
    }
  },
  beforeDestroy() {
    // 清理计时器
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
    }
  }
};
</script>

<style lang="scss" scoped>
/* 左侧聊天列表样式 */
.left-panel {
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.search-box {
  padding: 10px;
  border-bottom: 1px solid #eaeaea;
}

.list-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #606266;

  i {
    font-size: 24px;
    margin-bottom: 10px;
    animation: rotate 2s linear infinite;
  }

  span {
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.chat-list-item {
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    background-color: #ecf5ff;
    border-color: #409eff;
  }

  .chat-preview {
    display: flex;
    align-items: center;

    .chat-avatar {
      margin-right: 10px;
    }

    .chat-info {
      flex: 1;
      overflow: hidden;

      .chat-name {
        font-weight: 500;
        margin-bottom: 3px;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-last-message {
        color: #909399;
        font-size: 12px;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chat-time-container {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .chat-time {
          color: #c0c4cc;
          font-size: 11px;
        }

        .channel-tag {
          flex-shrink: 0;
          font-size: 10px;
          height: 16px;
          line-height: 16px;
          padding: 0 4px;
          margin-left: 8px;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  color: #909399;
  margin-top: 50px;

  i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
  }

  p {
    font-size: 14px;
    margin: 0;
  }
}

.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  margin-top: 10px;
}

.load-more-btn {
  padding: 8px 20px;
  font-size: 13px;
  color: #409eff;
  border: 1px solid #409eff;
  background-color: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #409eff;
    color: #fff;
  }
}

.loading-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  color: #606266;
  font-size: 13px;
  margin-top: 10px;

  i {
    margin-right: 6px;
    animation: rotate 1s linear infinite;
  }
}

.no-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  color: #c0c4cc;
  font-size: 12px;
  margin-top: 10px;
}
</style>
