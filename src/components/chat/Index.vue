<template>
  <div class="chat-container">
    <!-- 双栏布局：显示会话历史 + 聊天面板 -->
    <el-row :gutter="10" class="full-height">
      <!-- 左侧聊天历史列表 -->
      <el-col :span="4" class="full-height">
        <ChatHistory
          :source-type="sourceType"
          :current-session-id="currentSessionId"
          :app-id="appId"
          :searchable="true"
          @session-selected="handleSessionSelected"
          @sessions-loaded="handleSessionsLoaded"
          @loading-changed="handleHistoryLoadingChanged"
        />
      </el-col>

      <!-- 右侧聊天面板 -->
      <el-col :span="20" class="full-height">
        <ChatPanel
          :app-info="appInfo"
          :session-id="currentSessionId"
          :app-id="appId"
          :source-type="sourceType"
          :auto-connect="true"
          :show-controls="true"
          :show-close-button="true"
          :message-mode="messageMode"
          @message-sent="handleMessageSent"
          @message-received="handleMessageReceived"
          @connection-changed="handleConnectionChanged"
          @session-ready="handleSessionReady"
          @close-chat="handleCloseChat"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import ChatHistory from './ChatHistory.vue';
import ChatPanel from './ChatPanel.vue';

export default {
  name: "ChatIndex",
  components: {
    ChatHistory,
    ChatPanel
  },
  props: {
    // 消息发送模式：'api-first' | 'signalr-only'
    messageMode: {
      type: String,
      default: 'api-first',
      validator: (value) => ['api-first', 'signalr-only'].includes(value)
    }
  },
  data() {
    return {
      // 路由参数
      appId: null,
      sourceType: null,
      routeSessionId: null, // 新增：保存路由中的sessionId，用于后续匹配

      // 当前状态
      currentSessionId: null,
      appInfo: {},

      // 会话列表
      chatSessions: [],

      // 状态标志
      isHistoryLoading: false,
      isConnected: false,
      isInitializing: true, // 新增：标记是否正在初始化

      // sourceType配置映射（仅用于显示名称）
      sourceTypeConfig: {
        1: { name: '应用聊天' },      // 应用
        2: { name: '工作流聊天' },    // 工作流
      }
    };
  },
  computed: {
    // 当前配置信息
    currentConfig() {
      return this.sourceTypeConfig[this.sourceType] || { name: '默认聊天' };
    }
  },
  created() {
    // 从路由获取参数
    this.appId = this.$route.params.id;
    this.sourceType = parseInt(this.$route.query.sourceType) || 1;
    this.routeSessionId = this.$route.query.sessionId; // 保存路由sessionId，等会话列表加载后再处理

    // 等待会话列表加载完成后在 handleSessionsLoaded 中处理
  },
  methods: {
    // 设置默认应用信息
    setDefaultAppInfo() {
      this.appInfo = {
        id: this.appId,
        name: this.currentConfig.name,
        description: "我是一个AI智能助手，可以帮您解答各种问题。",
        profilePhoto: "",
        introduce: "有什么可以帮助您的？",
      };
    },

    // 初始化会话
    initializeSession() {
      // 尝试从本地存储恢复会话ID
      const routeKey = `${this.appId}_${this.sourceType}`;
      const savedSessionData = localStorage.getItem("chatSessionData");

      if (savedSessionData) {
        try {
          const sessionData = JSON.parse(savedSessionData);
          if (sessionData.routeKey === routeKey) {
            this.currentSessionId = sessionData.sessionId;
            return;
          }
        } catch (error) {
          console.error("解析本地会话数据失败:", error);
        }
      }

      // 生成新的会话ID
      this.currentSessionId = this.generateSessionId();

      // 保存到本地存储
      this.saveSessionData();
    },

    // 生成会话ID
    generateSessionId() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    // 保存会话数据到本地存储
    saveSessionData() {
      if (!this.currentSessionId) return;

      try {
        const routeKey = `${this.appId}_${this.sourceType}`;
        const sessionData = {
          routeKey: routeKey,
          sessionId: this.currentSessionId,
          timestamp: new Date().getTime(),
        };
        localStorage.setItem("chatSessionData", JSON.stringify(sessionData));
      } catch (error) {
        console.error("保存会话数据失败:", error);
      }
    },

    // 处理会话选择
    handleSessionSelected(sessionData) {
      // 更新当前会话ID
      this.currentSessionId = sessionData.sessionId;

      // 更新应用信息
      this.appInfo = {
        id: sessionData.id || sessionData.clientId,
        name: `${sessionData.appName || "应用"} - ${sessionData.channelName || "渠道"}`,
        profilePhoto: sessionData.profilePhoto,
        introduce: sessionData.introduce,
        description: sessionData.lastMessage || sessionData.description,
      };

      // 更新appId（从聊天历史中选择的可能不同于路由参数）
      if (sessionData.clientId) {
        this.appId = sessionData.clientId;
      } else if (sessionData.id) {
        this.appId = sessionData.id;
      }

      // 保存会话数据
      this.saveSessionData();
    },

    // 处理会话列表加载完成
    handleSessionsLoaded(sessions) {
      this.chatSessions = sessions;

      // 正在初始化时才处理会话选择
      if (this.isInitializing) {
        this.isInitializing = false;

        if (this.routeSessionId) {
          // 根据路由sessionId查找对应会话
          const foundSession = sessions.find(session => session.sessionId === this.routeSessionId);

          if (foundSession) {
            this.handleSessionSelected(foundSession);
          } else {
            // 如果没找到匹配的会话，尝试通过appId自动选择
            this.selectCurrentSessionInList();
          }
        } else {
          // 没有routeSessionId，尝试通过appId自动选择会话
          this.selectCurrentSessionInList();
        }
      }
    },

    // 在列表中选中当前会话
    selectCurrentSessionInList() {
      if (!this.appId || this.chatSessions.length === 0) {
        // 设置默认应用信息
        this.setDefaultAppInfo();
        // 如果没有routeSessionId，生成新的会话ID
        if (!this.currentSessionId) {
          this.initializeSession();
        }
        return;
      }

      // 根据路由参数ID查找匹配的列表项
      let foundSession = null;

      // 首先尝试通过路由ID匹配clientId或id字段
      foundSession = this.chatSessions.find(
        (chat) => {
          const matchClientId = chat.clientId && (chat.clientId.toString() === this.appId.toString());
          const matchId = chat.id && (chat.id.toString() === this.appId.toString());
          return matchClientId || matchId;
        }
      );

      // 如果找到了对应的会话，则选中它
      if (foundSession) {
        this.handleSessionSelected(foundSession);
      } else {
        // 设置默认应用信息
        this.setDefaultAppInfo();
        // 如果没有currentSessionId，生成新的会话ID
        if (!this.currentSessionId) {
          this.initializeSession();
        }
      }
    },

    // 处理历史加载状态变化
    handleHistoryLoadingChanged(isLoading) {
      this.isHistoryLoading = isLoading;
    },

    // 处理消息发送
    handleMessageSent() {
      // 可以在这里添加发送统计或其他逻辑
    },

    // 处理消息接收
    handleMessageReceived() {
      // 可以在这里添加接收统计或其他逻辑
    },

    // 处理连接状态变化
    handleConnectionChanged(connectionInfo) {
      this.isConnected = connectionInfo.isConnected;

      if (!connectionInfo.isConnected && connectionInfo.error) {
        console.error("连接错误:", connectionInfo.error);
      }
    },

    // 处理会话就绪
    handleSessionReady() {
      // 可以在这里添加会话就绪后的逻辑
    },

    // 处理关闭聊天
    handleCloseChat() {
      // 可以添加关闭聊天的处理逻辑，比如返回上一页
      if (this.$router) {
        this.$router.go(-1);
      } else if (window) {
        window.close();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100%;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.full-height {
  height: 100%;
}

.el-row {
  height: 100%;
}

.el-col {
  height: 100%;
}
</style>
