<template>
  <div></div>
</template>

<script>
/**
 * 文件预览公共组件
 * 支持图片、视频、音频、PDF、文本等多种文件类型的预览
 */
export default {
  name: 'FilePreview',
  methods: {
    /**
     * 预览文件
     * @param {Object} file - 文件对象
     * @param {string} file.name - 文件名
     * @param {string} file.url - 文件URL
     * @param {string} file.type - 文件类型 (图片/视频/音频/文件)
     */
    preview(file) {
      if (!file || !file.url) {
        this.$message.warning('文件链接不存在');
        return;
      }

      const fileExtension = this.getFileExtension(file.name);
      const fileType = this.getFileTypeByExtension(fileExtension);

      switch (fileType) {
        case 'image':
          this.previewImage(file);
          break;
        case 'video':
          this.previewVideo(file);
          break;
        case 'audio':
          this.previewAudio(file);
          break;
        case 'pdf':
          this.previewPdf(file);
          break;
        case 'text':
          this.previewText(file);
          break;
        case 'doc':
          this.previewDocument(file);
          break;
        default:
          this.downloadFile(file);
          break;
      }
    },

    /**
     * 获取文件扩展名
     */
    getFileExtension(fileName) {
      if (!fileName) return '';
      const lastDotIndex = fileName.lastIndexOf('.');
      return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
    },

    /**
     * 根据文件扩展名判断文件类型
     */
    getFileTypeByExtension(extension) {
      const typeMap = {
        // 图片
        'png': 'image',
        'jpg': 'image',
        'jpeg': 'image',
        'gif': 'image',
        'bmp': 'image',
        'webp': 'image',
        'svg': 'image',
        // 视频
        'mp4': 'video',
        'avi': 'video',
        'mov': 'video',
        'wmv': 'video',
        'flv': 'video',
        'webm': 'video',
        // 音频
        'mp3': 'audio',
        'wav': 'audio',
        'ogg': 'audio',
        'aac': 'audio',
        'flac': 'audio',
        // PDF
        'pdf': 'pdf',
        // 文本
        'txt': 'text',
        'md': 'text',
        'json': 'text',
        'xml': 'text',
        'csv': 'text',
        // 文档
        'doc': 'doc',
        'docx': 'doc',
        'xls': 'doc',
        'xlsx': 'doc',
        'ppt': 'doc',
        'pptx': 'doc'
      };
      return typeMap[extension] || 'unknown';
    },

    /**
     * 预览图片
     */
    previewImage(file) {
      const imageHtml = `
        <div style="text-align: center; max-width: 100%; max-height: 70vh; overflow: auto;">
          <img src="${file.url}" alt="${file.name}" style="max-width: 100%; max-height: 60vh; object-fit: contain;" />
          <div style="margin-top: 10px; color: #666; font-size: 14px;">${file.name}</div>
        </div>
      `;

      this.$msgbox({
        title: '图片预览',
        message: imageHtml,
        dangerouslyUseHTMLString: true,
        showConfirmButton: true,
        confirmButtonText: '关闭',
        showClose: false,  // 不显示右上角关闭按钮
        closeOnClickModal: false,
        closeOnPressEscape: false,  // 禁用ESC键关闭
        customClass: 'file-preview-dialog image-preview'
      });
    },

    /**
     * 预览视频
     */
    previewVideo(file) {
      const videoId = `video-${Date.now()}`; // 生成唯一的视频ID
      const videoHtml = `
        <div style="text-align: center; max-width: 100%;">
          <video id="${videoId}" controls style="max-width: 100%; max-height: 60vh;" preload="metadata">
            <source src="${file.url}" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
          <div style="margin-top: 10px; color: #666; font-size: 14px;">${file.name}</div>
        </div>
      `;

      this.$msgbox({
        title: '视频预览',
        message: videoHtml,
        dangerouslyUseHTMLString: true,
        showConfirmButton: true,
        confirmButtonText: '关闭',
        showClose: false,  // 不显示右上角关闭按钮
        closeOnClickModal: false,
        closeOnPressEscape: false,  // 禁用ESC键关闭
        customClass: 'file-preview-dialog video-preview',
        beforeClose: (action, instance, done) => {
          // 在弹窗关闭前暂停视频播放
          const videoElement = document.getElementById(videoId);
          if (videoElement) {
            videoElement.pause();
            videoElement.currentTime = 0; // 重置播放进度
          }
          // 确保done是函数再调用
          if (typeof done === 'function') {
            done();
          }
        }
      });
    },

    /**
     * 预览音频
     */
    previewAudio(file) {
      const audioId = `audio-${Date.now()}`; // 生成唯一的音频ID
      const audioHtml = `
        <div style="text-align: center; padding: 20px;">
          <div style="margin-bottom: 20px; font-size: 16px; color: #333;">${file.name}</div>
          <audio id="${audioId}" controls style="width: 100%;" preload="metadata">
            <source src="${file.url}" type="audio/mpeg">
            您的浏览器不支持音频播放
          </audio>
        </div>
      `;

      this.$msgbox({
        title: '音频预览',
        message: audioHtml,
        dangerouslyUseHTMLString: true,
        showConfirmButton: true,
        confirmButtonText: '关闭',
        showClose: false,  // 不显示右上角关闭按钮
        closeOnClickModal: false,
        closeOnPressEscape: false,  // 禁用ESC键关闭
        customClass: 'file-preview-dialog audio-preview',
        beforeClose: (action, instance, done) => {
          // 在弹窗关闭前暂停音频播放
          const audioElement = document.getElementById(audioId);
          if (audioElement) {
            audioElement.pause();
            audioElement.currentTime = 0; // 重置播放进度
          }
          // 确保done是函数再调用
          if (typeof done === 'function') {
            done();
          }
        }
      });
    },

    /**
     * 预览PDF
     */
    previewPdf(file) {
      // 在新窗口中打开PDF
      const newWindow = window.open(file.url, '_blank');
      if (!newWindow) {
        this.$confirm('无法直接预览PDF文件，是否下载查看？', '提示', {
          confirmButtonText: '下载',
          cancelButtonText: '取消',
          type: 'info',
          showClose: false,  // 不显示右上角关闭按钮
          closeOnClickModal: false
        }).then(() => {
          this.downloadFile(file);
        }).catch(() => {});
      }
    },

    /**
     * 预览文本文件
     */
    async previewText(file) {
      try {
        const response = await fetch(file.url);
        if (!response.ok) {
          throw new Error('文件加载失败');
        }
        const text = await response.text();

        const textHtml = `
          <div style="text-align: left; max-height: 60vh; overflow: auto;">
            <div style="margin-bottom: 10px; font-weight: bold; color: #333;">${file.name}</div>
            <pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; font-size: 12px; line-height: 1.4; white-space: pre-wrap; word-wrap: break-word;">${text}</pre>
          </div>
        `;

        this.$msgbox({
          title: '文本预览',
          message: textHtml,
          dangerouslyUseHTMLString: true,
          showConfirmButton: true,
          confirmButtonText: '关闭',
          showClose: false,  // 不显示右上角关闭按钮
          closeOnClickModal: false,
          closeOnPressEscape: false,  // 禁用ESC键关闭
          customClass: 'file-preview-dialog text-preview'
        });
      } catch (error) {
        this.$message.warning('文本文件预览失败，请下载后查看');
      }
    },

    /**
     * 预览文档文件（Word、Excel、PPT等）
     */
    previewDocument(file) {
      this.$confirm('该文件类型需要下载后使用相应软件打开，是否下载？', '提示', {
        confirmButtonText: '下载',
        cancelButtonText: '取消',
        type: 'info',
        showClose: false,  // 不显示右上角关闭按钮
        closeOnClickModal: false
      }).then(() => {
        this.downloadFile(file);
      }).catch(() => {});
    },

    /**
     * 下载文件
     */
    downloadFile(file) {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};
</script>

<style lang="scss" scoped>
// 文件预览对话框样式
:deep(.file-preview-dialog) {
  .el-message-box {
    width: auto !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
  }

  .el-message-box__content {
    max-height: 75vh !important;
    overflow: auto !important;
  }

  .el-message-box__header {
    .el-message-box__close {
      display: none !important; // 隐藏右上角关闭按钮
    }
  }

  &.image-preview .el-message-box {
    width: auto !important;
    min-width: 400px !important;
  }

  &.video-preview .el-message-box {
    width: auto !important;
    min-width: 600px !important;
  }

  &.audio-preview .el-message-box {
    width: 500px !important;
  }

  &.text-preview .el-message-box {
    width: 700px !important;
  }
}
</style>
