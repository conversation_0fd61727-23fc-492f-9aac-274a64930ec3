import { translateErrorMessage } from '@/utils/errorHandler'
import { isInErrorHandlingWhitelist } from '@/utils/errorWhitelist'
import axios from 'axios'
import { Message } from 'element-ui'

// API配置
const API_CONFIG = {
  auth: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/Auth']
  },
  signalR: {
    baseURL: process.env.VUE_APP_SIGNALR_URL,
    paths: ['/api/v1/chat-session']
  },
  sms: {
    baseURL: process.env.VUE_APP_BASE_API_SYS,
    paths: ['/api/Sms']
  },
  captcha: {
    baseURL: process.env.VUE_APP_BASE_API_SYS,
    paths: ['/api/Captcha']
  },
  user: {
    baseURL: process.env.VUE_APP_BASE_API_USER,
    paths: ['/api/User']
  },
  sessionFlow: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/SessionFlow']
  },
  oss: {
    baseURL: 'http://***************:6275',
    paths: ['/upload']
  },
  subscription: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/Subscription']
  },
  apiPlugin: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/ApiPlugin']
  },
  plugin: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/Plugin']
  },
  workflow: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/workflow']
  },
  rag: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/rag/upload', '/api/v1/FileService/documents/preview', '/rag/confirm', '/kb/create', '/api/v1/KnowledgeBaseService', '/api/v1/FileService/files', '/rag/retrieve']
  },
  fileService: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/FileService']
  },
  material: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/MaterialService', '/api/v1/MaterialService', '/material/delete']
  },
  imChat: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/IMChat']
  },
  client: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/SessionFlowBase']
  },
  points: {
    baseURL: process.env.VUE_APP_BASE_API_MAIN,
    paths: ['/api/v1/Points']
  },
  mainRequest: {
    baseURL: process.env.VUE_APP_BASE_API_MAIN,
    paths: ['/api/v1/MainRequest']
  },
  userAdmin: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/UserAdmin']
  },
  team: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/UserTenantAccount']
  },
  chatHistory: {
    baseURL: process.env.VUE_APP_SIGNALR_URL,
    paths: ['/api/chat-history']
  },
  voice: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/voice']
  },
  mcpService: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/McpService']
  },
  schemaStore: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/SchemaStore']
  },
  llmService: {
    baseURL: process.env.VUE_APP_BASE_API,
    paths: ['/api/v1/LlmService']
  }
}

// 开发环境下输出配置信息
if (process.env.NODE_ENV === 'development') {
  console.log('API配置信息:', {
    环境: process.env.NODE_ENV,
    接口服务: process.env.VUE_APP_BASE_API,
    客户端地址: process.env.VUE_APP_CLIENT_URL,
    页面地址: process.env.VUE_APP_BASE,
    验证相关接口地址: process.env.VUE_APP_BASE_API_SYS,
    客户管理信息地址: process.env.VUE_APP_BASE_API_MAIN
  })
}

// 根据请求路径获取对应的baseURL
function getBaseURLByPath(path) {
  // 检查路径是否匹配验证码服务
  if (path.startsWith('/api/Captcha')) {
    return API_CONFIG.captcha.baseURL
  }
  // 检查路径是否匹配SMS服务
  if (path.startsWith('/api/Sms')) {
    return API_CONFIG.sms.baseURL
  }
  // 检查路径是否匹配SMS服务
  if (path.startsWith('/api/User')) {
    return API_CONFIG.user.baseURL
  }
  // 检查路径是否匹配认证服务
  if (path.startsWith('/api/v1/Auth')) {
    return API_CONFIG.auth.baseURL
  }
  // 检查路径是否匹配会话流服务
  if (path.startsWith('/api/v1/SessionFlow')) {
    return API_CONFIG.sessionFlow.baseURL
  }
  // 检查路径是否匹配OSS服务
  if (path.startsWith('/upload')) {
    return API_CONFIG.oss.baseURL
  }
  // 检查路径是否匹配订阅服务
  if (path.startsWith('/api/v1/Subscription')) {
    return API_CONFIG.subscription.baseURL
  }
  // 检查路径是否匹配插件服务
  if (path.startsWith('/api/v1/ApiPlugin')) {
    return API_CONFIG.apiPlugin.baseURL
  }
  // 检查路径是否匹配Plugin服务
  if (path.startsWith('/api/v1/Plugin')) {
    return API_CONFIG.plugin.baseURL
  }
  // 检查路径是否匹配工作流服务
  if (path.startsWith('/api/workflow')) {
    return API_CONFIG.workflow.baseURL
  }
  // 检查路径是否匹配RAG服务
  if (path.startsWith('/rag/') || path.startsWith('/kb/')) {
    return API_CONFIG.rag.baseURL
  }
  // 检查路径是否匹配素材服务
  if (path.startsWith('/material/')) {
    return API_CONFIG.material.baseURL
  }
  // 检查路径是否匹配文件服务
  if (path.startsWith('/api/v1/FileService')) {
    return API_CONFIG.fileService.baseURL
  }
  // 检查路径是否匹配IM聊天服务
  if (path.startsWith('/api/v1/IMChat')) {
    return API_CONFIG.imChat.baseURL
  }
  // 检查路径是否匹配聊天会话服务
  if (path.startsWith('/api/v1/chat-session')) {
    return API_CONFIG.signalR.baseURL // 聊天会话服务使用认证服务的baseURL
  }
  // 检查路径是否匹配聊天导出服务
  if (path.startsWith('/api/v1/chat-export')) {
    return API_CONFIG.signalR.baseURL // 聊天导出服务使用相同的baseURL
  }
  // 检查路径是否匹配客户端服务
  if (path.startsWith('/api/v1/SessionFlowBase')) {
    return API_CONFIG.client.baseURL
  }
  // 检查路径是否匹配积分服务
  if (path.startsWith('/api/v1/Points')) {
    return API_CONFIG.points.baseURL
  }
  // 检查路径是否匹配使用记录服务
  if (path.startsWith('/api/v1/MainRequest')) {
    return API_CONFIG.mainRequest.baseURL
  }
  // 检查路径是否匹配用户管理服务
  if (path.startsWith('/api/v1/UserAdmin')) {
    return API_CONFIG.userAdmin.baseURL
  }
  // 检查路径是否匹配团队管理服务
  if (path.startsWith('/api/v1/UserTenantAccount')) {
    return API_CONFIG.team.baseURL
  }
  // 检查路径是否匹配聊天历史消息服务
  if (path.startsWith('/api/chat-history')) {
    return API_CONFIG.chatHistory.baseURL
  }
  // 检查路径是否匹配声音服务
  if (path.startsWith('/api/voice')) {
    return API_CONFIG.voice.baseURL
  }
  // 检查路径是否匹配MCP服务
  if (path.startsWith('/api/v1/McpService')) {
    return API_CONFIG.mcpService.baseURL
  }
  // 检查路径是否匹配SchemaStore服务
  if (path.startsWith('/api/v1/SchemaStore')) {
    return API_CONFIG.schemaStore.baseURL
  }
  // 检查路径是否匹配LlmService服务
  if (path.startsWith('/api/v1/LlmService')) {
    return API_CONFIG.llmService.baseURL
  }
  // 默认使用认证服务
  return API_CONFIG.auth.baseURL
}

// 创建axios实例
const instance = axios.create({
  timeout: 100000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 根据请求路径设置不同的baseURL
    config.baseURL = getBaseURLByPath(config.url)

    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 从localStorage获取租户ID
    const tenantId = localStorage.getItem('tenantId')
    if (tenantId) {
      config.headers['__tenant'] = tenantId
    }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  response => {
    const res = response.data
    const url = response.config.url
    const method = response.config.method

    // 滑动验证码相关接口特殊处理
    if (url.includes('/api/Captcha/slide')) {
      if (url.includes('/verify')) {
        // 验证接口直接返回整个响应
        return res
      } else {
        // 获取验证码接口返回data部分
        if ( res.isSuccess ||res.success||res.is_success) {
          return res.data
        }
      }
    }

    // 【新增】检查是否在错误处理白名单中
    const isWhitelisted = isInErrorHandlingWhitelist(url, method, res.errorCode, response.status)

    // 【新增】优先检查 errorCode - 统一异常处理
    if (res.errorCode !== null && res.errorCode !== undefined) {
      // 如果在白名单中，跳过统一处理，直接抛出原始错误
      if (isWhitelisted) {
        return Promise.reject({
          errorCode: res.errorCode,
          message: res.message || '请求失败',
          originalResponse: res,
          isWhitelisted: true  // 标记为白名单接口，组件可以根据此标记进行特殊处理
        })
      }

      // 不在白名单中，执行统一错误处理
      const rawMessage = res.message || '请求失败'
      const friendlyMessage = translateErrorMessage(rawMessage)
      Message({
        message: friendlyMessage,
        type: 'error',
        duration: 3000
      })
      return Promise.reject({
        errorCode: res.errorCode,
        message: friendlyMessage,
        originalResponse: res,
        alreadyHandled: true  // 标记已在拦截器中处理过
      })
    }

    // 其他接口的通用处理
    if ( res.isSuccess ||res.success||res.is_success) {
      return res
    } else {
      // 检查是否在白名单中（针对业务错误码）
      if (isWhitelisted) {
        // 白名单接口直接抛出原始错误，不进行统一处理
        res.isWhitelisted = true
        return Promise.reject(res)
      }

      // 只有在没有 errorCode 的情况下，才使用原有的 code 逻辑
      switch (res.code) {
        case 400:
          Message({
            message: translateErrorMessage(res.message || '请求参数有误'),
            type: 'warning',
            duration: 3000
          })
          // 标记已处理，避免组件中重复显示
          res.alreadyHandled = true
          break
        case 401: // 未登录或token过期
          Message({
            message: '登录已过期，请重新登录',
            type: 'warning',
            duration: 3000
          })
          localStorage.removeItem('token')
          localStorage.removeItem('tenantId')
          localStorage.removeItem('tenantInfo')
          localStorage.removeItem('user')
          setTimeout(() => {
            window.location.href = '/login'
          }, 1500)
          // 标记已处理
          res.alreadyHandled = true
          break
        case 403: // 权限不足
          break
        case 404: // 资源不存在
          break
        // 可以添加其他状态码处理
      }

      return Promise.reject(res)
    }
  },
  error => {
    console.error('Response Error:', error)

    // 检查是否在白名单中（针对网络错误）
    const url = error.config?.url || ''
    const method = error.config?.method || 'GET'
    const statusCode = error.response?.status || null
    const isWhitelisted = isInErrorHandlingWhitelist(url, method, null, statusCode)

    if (isWhitelisted) {
      // 白名单接口直接抛出原始错误
      error.isWhitelisted = true
      return Promise.reject(error)
    }

    // 处理401状态码
    if (error.response && error.response.status === 401) {
      Message({
        message: '登录已过期，请重新登录',
        type: 'warning',
        duration: 3000
      })
      localStorage.removeItem('token')
      localStorage.removeItem('tenantId')
      localStorage.removeItem('tenantInfo')
      localStorage.removeItem('user')
      setTimeout(() => {
        window.location.href = '/login'
      }, 1500)
      return Promise.reject(error)
    }

    // 其他错误处理
    // Message({
    //   message: '服务暂时繁忙，请稍后尝试....',
    //   type: 'error',
    //   duration: 3000
    // })
    return Promise.reject(error)
  }
)

// RESTful 风格的请求方法
export const request = {
  /**
   * GET 请求
   * @param {string} url - 请求地址
   * @param {object} params - 查询参数
   * @param {object} config - 额外配置
   */
  get(url, params = {}, config = {}) {
    return instance.get(url, { ...config, params })
  },

  /**
   * POST 请求
   * @param {string} url - 请求地址
   * @param {object} data - 请求体数据
   * @param {object} config - 额外配置
   */
  post(url, data = {}, config = {}) {
    return instance.post(url, data, config)
  },

  /**
   * PUT 请求
   * @param {string} url - 请求地址
   * @param {object} data - 请求体数据
   * @param {object} config - 额外配置
   */
  put(url, data = {}, config = {}) {
    return instance.put(url, data, config)
  },

  /**
   * DELETE 请求
   * @param {string} url - 请求地址
   * @param {object} params - 查询参数
   * @param {object} config - 额外配置
   */
  delete(url, params = {}, config = {}) {
    return instance.delete(url, { ...config, params })
  },

  /**
   * PATCH 请求
   * @param {string} url - 请求地址
   * @param {object} data - 请求体数据
   * @param {object} config - 额外配置
   */
  patch(url, data = {}, config = {}) {
    return instance.patch(url, data, config)
  },

  /**
   * 文件上传
   * @param {string} url - 上传地址
   * @param {File|FormData} file - 文件对象或FormData
   * @param {object} config - 额外配置
   */
  upload(url, file, config = {}) {
    const formData = file instanceof FormData ? file : new FormData()
    if (!(file instanceof FormData)) {
      formData.append('file', file)
    }
    return instance.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      }
    })
  },

  /**
   * 文件下载
   * @param {string} url - 下载地址
   * @param {object} params - 查询参数
   * @param {string} fileName - 文件名（可选）
   */
  download(url, params = {}, fileName = '') {
    return instance.get(url, {
      params,
      responseType: 'blob'
    }).then(response => {
      console.log("response",response)
      // response.data 已经是一个 Blob 对象，不需要再次创建
      const blob = response.data

      // 如果没有提供文件名，尝试从响应头获取
      let downloadFileName = fileName
      if (!downloadFileName) {
        const contentDisposition = response.headers['content-disposition']
        if (contentDisposition) {
          const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
          if (matches && matches[1]) {
            downloadFileName = matches[1].replace(/['"]/g, '')
          }
        }
      }

      // 创建下载链接
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = downloadFileName || 'download'
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(link.href)

      return response
    })
  },

  /**
   * 使用multipart/form-data上传文档
   * @param {FormData} formData - 包含文档数据的FormData对象
   */
  uploadForm: (formData) => {
    return instance.post('/api/v1/FileService/documents/preview', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// API接口定义
export const api = {
  auth: {
    // 密码登录
    tokenByPassword: (data) => request.post('/api/v1/Auth/token-by-password', data),
    // 验证码登录
    tokenBySms: (data) => request.post('/api/v1/Auth/token-by-phone', data),
    // 用户注册
    register: (data) => request.post('/api/v1/UserAdmin/user-by-phone', data),
    // 验证短信验证码 (用于注册和登录)
    verifySmsCode: (data) => request.post('/api/v1/Auth/verify-sms-code', data),
    // 获取当前用户信息
    getCurrentUser: () => request.get('/api/v1/UserAdmin/current-user'),
    // 忘记密码
    forgotPassword: (data) => request.post('/api/v1/UserAdmin/forgot-password', data),
    // 获取租户列表
    getTenants: () => request.get('/api/v1/UserTenantAccount/tenants')
  },
  user: {
    // 发送短信验证码，支持不同类型：register, login, resetPassword
    sendSms: (data) => request.post('/api/Sms/send', data),
    // 验证用户是否存在
    checkUserExists: (username) => request.get(`/api/User/is-exists/${username}`)
  },
  captcha: {
    // 获取验证码
    get: (data) => request.get('/api/Captcha/slide', { params: data }),
    // 验证
    verify: (data) => request.post('/api/Captcha/slide/verify', data)
  },
  sessionFlow: {
    // 获取应用列表
    getList: (params = {}) => request.get('/api/v1/SessionFlow/list', params),
    // 创建智能体
    create: (data) => request.post('/api/v1/SessionFlow', data),
    // 获取应用详情
    getDetail: (id) => request.get(`/api/v1/SessionFlow/${id}`),
    // 更新应用
    update: (data) => request.put(`/api/v1/SessionFlow/${data.id}`, data),
    // 删除应用
    deleteApp: (id) => request.delete(`/api/v1/SessionFlow/${id}`)
  },
  oss: {
    // 获取OSS上传签名 (这个接口似乎没用到，可以保留或移除)
    // getSignature: (data) => request.post('/upload', data),

    /**
     * 上传文件到OSS
     * @param {object} params - 参数对象
     * @param {File} params.file - 文件对象
     * @param {string} params.fileType - 文件类型 (e.g., 'Auto')
     * @param {string} params.folder - 上传的目标文件夹
     * @param {object} config - 额外的axios配置
     */
    upload: (params, config = {}) => {
      const { file, fileType, folder } = params;
      if (!file) {
        return Promise.reject(new Error('未提供上传文件'));
      }
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileType', fileType || 'Auto');
      formData.append('folder', folder || 'default');

      // 调用 instance.post 时，在 config 中显式移除 Content-Type
      return instance.post('/api/Oss/upload', formData, {
        ...config, // 保留其他传入的 config
        headers: {
          ...config.headers, // 保留其他传入的 headers
          'Content-Type': undefined // 强制 axios 自动设置 multipart/form-data
        },
      });
    },
  },
  subscription: {
    // 获取订阅计划列表
    getList: () => request.get('/api/v1/Subscription'),
    // 获取当前订阅信息
    getCurrent: () => request.get('/api/v1/Subscription/current'),
    // 获取有效的订阅信息
    getValid: () => request.get('/api/v1/UserSubscription/valid'),
    // 更新订阅
    update: (data) => request.put('/api/v1/Subscription', data)
  },
  apiPlugin: {
    // 获取插件列表
    getList: (params = {}) => request.get('/api/v1/ApiPlugin/paged', params),
    // 创建插件
    create: (data) => request.post('/api/v1/ApiPlugin', data),
    // 更新插件
    update: (id, data) => request.put(`/api/v1/ApiPlugin/${id}`, data),
    // 获取插件详情
    getDetail: (id) => request.get(`/api/v1/ApiPlugin/${id}`),
    // 获取MCP插件详情
    getMcpDetail: (id) => request.get(`/api/v1/Plugin/${id}`),
    // 解析响应结构
    parseResponseSchema: (data) => request.post('/api/v1/ApiPlugin/parse-response-schema', data),
    // 获取MCP工具列表
    getMcpTools: (pluginId) => request.get(`/api/v1/Plugin/mcp/tools/${pluginId}`),
    // 运行MCP工具
    executeMcpTool: (data) => request.post('/api/v1/Plugin/mcp/tools/execute', data)
  },

  // Plugin接口
  plugin: {
    // 获取插件列表
    getList: (params = {}) => request.get('/api/v1/Plugin/list', params)
  },
  rag: {
    // 上传文档到RAG系统
    upload: (params) => {
      // 如果是文件上传，使用FormData
      if (params.document_files && params.document_files[0].content instanceof ArrayBuffer) {
        const formData = new FormData();

        // 添加文件
        params.document_files.forEach((file) => {
          formData.append('files', new Blob([file.content]), file.file_name);
        });

        // 添加其他参数
        formData.append('knowledge_base_id', params.knowledge_base_id);
        formData.append('userid', params.userid);
        formData.append('enhance_parsing', params.enhance_parsing);
        if (params.segment_length) {
          formData.append('segment_length', params.segment_length);
        }
        return instance.post('/rag/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
      }
      // 普通请求
      return request.post('/rag/upload', params);
    },
    // 使用multipart/form-data上传文档
    uploadForm: (formData) => {
      return instance.post('/api/v1/FileService/documents/preview', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    // 问答文件预览
    qaPreview: (formData) => {
      return instance.post('/api/v1/FileService/qa/preview', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    // 表格文件预览
    tablePreview: (formData) => {
      return instance.post('/api/v1/FileService/tables/preview', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    // 网页预览
    webPreview: (params) => {
      return request.post('/api/v1/FileService/web/preview', params);
    },
    // 确认导入知识库
    confirm: (data) => request.post('/api/v1/FileService/documents/confirm', {
      knowledgeBaseId: data.knowledge_base_id || data.knowledgeBaseId,
      fileIds: data.file_ids || data.fileIds
    }),
    // 问答导入确认 - 专用接口
    qaConfirm: (data) => request.post('/api/v1/FileService/qa/confirm', {
      knowledgeBaseId: data.knowledge_base_id || data.knowledgeBaseId,
      fileIds: data.file_ids || data.fileIds
    }),
    // 表格导入确认 - 专用接口
    tableConfirm: (data) => request.post('/api/v1/FileService/tables/confirm', {
      knowledgeBaseId: data.knowledge_base_id || data.knowledgeBaseId,
      fileIds: data.file_ids || data.fileIds
    }),
    // 创建知识库
    createKnowledge: (params) => request.post('/api/v1/KnowledgeBaseService', params),
    // 获取知识库列表
    getKnowledgeList: (params) => request.get('/api/v1/KnowledgeBaseService', params),
    // 更新知识库
    updateKnowledge: (id, params) => request.put(`/api/v1/KnowledgeBaseService/${id}`, params),
    // 删除知识库
    deleteKnowledge: (id) => request.delete(`/api/v1/KnowledgeBaseService/${id}`),
    // 获取RAG文件列表
    getRagFileList: (params) => request.get('/api/v1/FileService/files', params),
    // 删除文件
    deleteFile: (fileId) => request.delete(`/api/v1/FileService/files/${fileId}`),
    // 检索知识库内容
    retrieve: (data) => request.post('/rag/retrieve', data),
    // 获取知识库容量信息
    getCapacity: (knowledgeBaseId) => request.get(`/api/v1/KnowledgeBaseService/${knowledgeBaseId}/capacity`),
  },
  material: {
    // 上传素材
    upload: async (params) => {
      try {
        // 创建FormData对象
        const formData = new FormData();

        // 如果params包含file对象（文件上传场景）
        if (params.file instanceof File) {
          formData.append('file', params.file);
        }
        // 如果是Base64内容，需要转换为Blob
        else if (typeof params.file === 'string' && params.file.includes('base64')) {
          // 将Base64字符串转换为Blob对象
          const byteString = atob(params.file.split(',')[1] || params.file);
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }
          const blob = new Blob([ab], { type: params.fileType || 'application/octet-stream' });
          formData.append('file', blob, params.fileName || 'file');
        }

        // 添加其他参数
        if (params.knowledgeBaseId) {
          formData.append('knowledgeBaseId', params.knowledgeBaseId);
        }
        if (params.type) {
          formData.append('type', params.type);
        }

        // 发送multipart/form-data请求
        const response = await instance.post('/api/v1/MaterialService', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        return response;
      } catch (error) {
        console.error('Material upload error:', error);
        Message({
          message: error.message || '素材上传失败',
          type: 'error',
          duration: 3000
        });
        throw error;
      }
    },
    // 获取素材列表
    getList: (params) => request.get('/api/v1/MaterialService', params),
    // 删除素材
    delete: (params) => request.delete(`/api/v1/MaterialService/${params.id}`, { KnowledgeBaseId: params.knowledgeBaseId || params.knowledge_base_id }),
    // 更新素材
    update: (data) => request.put(`/api/v1/MaterialService/material/${data.id}/rename`, {
      newName: data.name
    }),
  },
  workflow: {
    // 获取工作流列表
    getList(data) {
      return request.get('/api/workflow',data)
    },

    // 创建工作流
    create(data) {
      return request.post('/api/workflow', data)
    },

    // 删除工作流
    delete(id) {
      return request.delete(`/api/workflow/${id}`)
    },

    // 获取工作流详情
    getDetail(id) {
      return request.get(`/api/workflow/${id}`)
    },

    // 复制工作流
    copy(id) {
      return request.post(`/api/workflow/CopyWorkFlow/${id}`)
    },

    // 执行工作流（工作流调用）
    execute(data) {
      return request.post('/api/v1/FlowDetail', data)
    },

    // 流式执行工作流（新的流式接口）- SSE流式响应
    executeStream(data, onMessage, onError, onComplete, signal = null) {
      return new Promise((resolve, reject) => {
        // 获取baseURL
        const baseURL = getBaseURLByPath('/api/v1/FlowDetail/execute-stream')
        const url = `${baseURL}/api/v1/FlowDetail/execute-stream`

        // 创建EventSource不支持POST，所以使用fetch
        const token = localStorage.getItem('token')
        const tenantId = localStorage.getItem('tenantId')

        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }

        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }

        if (tenantId) {
          headers['__tenant'] = tenantId
        }

        let isCompleted = false
        let hasError = false

        // 构建fetch选项，支持AbortController
        const fetchOptions = {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(data)
        }

        // 如果传入了signal，添加到fetch选项中
        if (signal) {
          fetchOptions.signal = signal
        }

        fetch(url, fetchOptions).then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          const reader = response.body.getReader()
          const decoder = new TextDecoder()
          let buffer = ''

          function readStream() {
            return reader.read().then(({ done, value }) => {
              // 检查是否被取消
              if (signal && signal.aborted) {
                isCompleted = true
                if (onError) onError(new Error('请求已被取消'))
                reject(new Error('请求已被取消'))
                return
              }

              if (done) {
                // 流读取完成，但只有在还没有通过 IsFinal 完成时才处理
                if (!hasError && !isCompleted) {
                  isCompleted = true
                  console.log('流读取完成，但没有收到 IsFinal 标记')
                  if (onComplete) onComplete()
                  // 不在这里 resolve，让回调处理
                }
                return
              }

              // 将新数据添加到缓冲区
              buffer += decoder.decode(value, { stream: true })

              // 处理缓冲区中的完整消息
              const lines = buffer.split('\n')
              buffer = lines.pop() || '' // 保留最后一个不完整的行

              for (const line of lines) {
                if (line.trim() === '') continue

                if (line.startsWith('data: ')) {
                  try {
                    const jsonStr = line.substring(6) // 移除 'data: ' 前缀
                    const messageData = JSON.parse(jsonStr)

                    // 检查是否有错误
                    if (messageData.ErrorCode !== null && messageData.ErrorCode !== undefined) {
                      hasError = true
                      const errorMessage = messageData.Message || '流式响应出错'
                      if (onError) onError(new Error(errorMessage))
                      reject(new Error(errorMessage))
                      return
                    }

                    // 解码 Unicode 转义序列
                    if (messageData.Data && messageData.Data.Response) {
                      try {
                        // 更安全的 Unicode 解码方法
                        const response = messageData.Data.Response;
                        // 只有当字符串包含 Unicode 转义序列时才进行解码
                        if (response.includes('\\u')) {
                          // 使用正则表达式替换 Unicode 转义序列
                          const decoded = response.replace(/\\u[\dA-Fa-f]{4}/g, function (match) {
                            return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                          });
                          messageData.Data.Response = decoded;
                        }
                        // 如果没有 Unicode 转义序列，保持原样
                      } catch (decodeError) {
                        // 如果解码失败，保持原始字符串
                        console.warn('Unicode 解码失败，使用原始字符串:', decodeError)
                      }
                    }

                    // 当 CurrentType === 2 时，对 Output 字段进行 Unicode 解码
                    if (messageData.Data && messageData.Data.CurrentType === 2 && messageData.Data.Output) {
                      try {
                        const output = messageData.Data.Output;
                        // 只有当字符串包含 Unicode 转义序列时才进行解码
                        if (output.includes('\\u')) {
                          // 使用正则表达式替换 Unicode 转义序列
                          const decoded = output.replace(/\\u[\dA-Fa-f]{4}/g, function (match) {
                            return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                          });
                          messageData.Data.Output = decoded;
                        }
                        // 如果没有 Unicode 转义序列，保持原样
                      } catch (decodeError) {
                        // 如果解码失败，保持原始字符串
                        console.warn('Output Unicode 解码失败，使用原始字符串:', decodeError)
                      }
                    }

                    // 调用消息处理回调
                    if (onMessage) {
                      onMessage(messageData)
                    }

                    // 检查是否是最后一条消息 (针对新接口格式)
                    if (messageData.Type === 'end' || (messageData.Data && messageData.Data.IsFinal)) {
                      isCompleted = true
                      if (onComplete) onComplete()
                      // 不在这里 resolve，让 onMessage 回调处理完成
                      return
                    }
                  } catch (error) {
                    console.error('解析SSE消息失败:', error, 'Line:', line)
                    // 解析错误不应该中断整个流程，继续处理下一条消息
                  }
                }
              }

              // 继续读取下一块数据
              return readStream()
            }).catch(error => {
              if (!hasError && !isCompleted) {
                hasError = true
                console.error('读取流数据失败:', error)
                if (onError) onError(error)
                reject(error)
              }
            })
          }

          return readStream()
        }).catch(error => {
          if (!hasError && !isCompleted) {
            hasError = true
            console.error('SSE流式请求失败:', error)
            if (onError) onError(error)
            reject(error)
          }
        })
      })
    },

        // 以流式方式执行工作流（应用调用）- SSE流式响应
    stream(data, onMessage, onError, onComplete, signal = null) {
      // 直接调用新的流式接口方法，保持接口一致性
      return this.executeStream(data, onMessage, onError, onComplete, signal)
    }
  },
  fileService: {
    // 检索接口
    retrieve(params) {
      return instance.get('/api/v1/FileService/retrieve', { params })
    },

    // 获取文件内容
    getFileContent(fileId, params) {
      return request.get(`/api/v1/FileService/files/${fileId}/content`, params)
    },

    // 删除文件内容
    deleteFileContent(fileId, chunkId) {
      return request.delete(`/api/v1/FileService/chunks/${chunkId}`)
    },

    // 更新文件内容
    updateFileContent(fileId, chunkId, data) {
      return request.put(`/api/v1/FileService/files/${fileId}/content/${chunkId}`, data)
    },

    // 添加文件内容
    addFileContent(fileId, data) {
      return request.post(`/api/v1/FileService/files/${fileId}/content`, data)
    },

    // 重命名文件
    rename(data) {
      return request.put(`/api/v1/FileService/files/${data.id}/rename`, {
        newName: data.name
      })
    }
  },
  imChat: {
    // 处理消息，用于加入聊天组、发送消息等
    handleMessage: (sessionId, content, appId) => {
      return request.post(
        `/api/v1/IMChat/send-text?sessionId=${sessionId}&content=${encodeURIComponent(content)}&appId=${appId}`
      )
    }
  },
  client: {
    // 获取客户端列表
    getList: (params) => request.get('/api/v1/SessionFlowBase', params ),
    // 获取客户端详情
    getDetail: (id) => request.get(`/api/v1/SessionFlowBase/${id}`),
    // 创建客户端
    create: (data) => request.post('/api/v1/SessionFlowBase/create', data),
    // 更新客户端
    update: (id, data) => request.put(`/api/v1/SessionFlowBase/${id}`, data),
    // 配置客户端（网页嵌入配置）
    configure: (id, data) => request.put(`/api/v1/SessionFlowBase/${id}/configure`, data),
    // 更新客户端名称
    updateName: (id, newName) => request.put(`/api/v1/SessionFlowBase/${id}/client-name`, { newName }),
    // 删除客户端
    delete: (id) => request.delete(`/api/v1/SessionFlowBase/${id}`)
  },
  points: {
    // 获取用户积分
    getPoints: () => request.get('/api/v1/Points'),
    // 获取积分记录列表
    getRecordList: (params) => request.get('/api/v1/Points/GetPointsRevenue', params),
    // 获取订单记录列表
    getOrderRecordList: (params) => request.get('/api/v1/Points/GetOrderRecordList', params),
    // 获取积分购买规则列表
    getPointPurchaseRulesList: () => request.get('/api/v1/Points/PointPurchaseRulesModel'),
    // 获取积分计费规则列表
    getPointCostRulesList: () => request.get('/api/v1/Points/GetPointCostRulesList'),
    // 充值积分
    recharge: (data) => request.post('/api/v1/Points/Recharge', data)
  },
  chat: {
    // 创建聊天会话
    createSession: (data) => request.post('/api/v1/chat-sessions/create', data),
    // 获取聊天会话列表
    getSessions: (params = {}) => {
      // 确保必需的分页参数存在
      const requestParams = {
        ...params // 展开其他可选参数
      };
      return request.get('/api/v1/chat-sessions', requestParams);
    },
    // 获取会话详情
    getSessionDetail: (id) => request.get(`/api/v1/im-chat-session/${id}`),
    // 获取会话历史消息 - 使用新的接口
    getSessionMessages: (sessionId, params = {}) => {
      const requestData = {
        sessionId: sessionId,
        maxResultCount: params.maxResultCount || 50,
        skipCount: params.skipCount || 0,
        sorting: params.sorting || "",
        customUserId: params.customUserId || "",
        beforeMessageId: params.beforeMessageId || "",
        includeDeleted: params.includeDeleted || false
      };
      return request.post('/api/chat-history/authenticated', requestData);
    },
    // 导出会话记录
    exportRecords: (data) => {
      // 创建专用的axios实例，避免响应拦截器干扰Blob数据处理
      const exportInstance = axios.create({
        timeout: 100000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      // 添加请求拦截器（用于认证和路由）
      exportInstance.interceptors.request.use(
        config => {
          config.baseURL = getBaseURLByPath(config.url);
          const token = localStorage.getItem('token');
          if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
          }
          const tenantId = localStorage.getItem('tenantId');
          if (tenantId) {
            config.headers['__tenant'] = tenantId;
          }
          return config;
        },
        error => Promise.reject(error)
      );

      return exportInstance.post('/api/v1/chat-export/records', data, {
        responseType: 'blob'
      }).then(response => response.data);
    },
    // 删除会话
    deleteHistory: (sessionId) => request.delete(`/api/v1/chat-sessions/${sessionId}`)
  },
  mainRequest: {
    // 获取使用记录列表
    getList: (params) => request.get('/api/v1/MainRequest/GetList', params),
    // 获取使用记录详情
    getDetail: (id) => request.get(`/api/v1/MainRequest/${id}`),
    // 获取会话历史消息
    getSessionHistory: (sessionId, params) => {
      if (!sessionId) {
        return Promise.reject(new Error('会话ID不能为空'));
      }
      return request.get(`/api/v1/MainRequest/session-history/${sessionId}`, params)
        .then(response => {
          // 确保返回结果的格式正确
          if (!response || !response.data) {
            console.warn('获取会话历史返回结果异常:', response);
            // 返回一个带空数组的标准格式结果，保持API一致性
            return {
              isSuccess: true,
              code: 200,
              message: "无历史消息",
              data: []
            };
          }
          return response;
        })
        .catch(error => {
          console.error('获取会话历史出错:', error);
          // 出错时也返回一个带空数组的标准格式结果
          return {
            isSuccess: false,
            code: 500,
            message: error.message || "获取历史消息失败",
            data: []
          };
        });
    },
    // 获取更多会话历史消息 (分页) - 使用新的接口
    getMoreSessionHistory: (sessionId, skipCount = 0, maxResultCount = 10) => {
      const requestData = {
        sessionId: sessionId,
        maxResultCount: maxResultCount,
        skipCount: skipCount,
        sorting: "",
        customUserId: "",
        beforeMessageId: "",
        includeDeleted: false
      };
      return request.post('/api/chat-history/authenticated', requestData);
    }
  },
  userAdmin: {
    // 修改密码
    changePassword(data) {
      return instance.post('/api/v1/UserAdmin/change-password', data)
    }
  },
  team: {
    // 获取团队成员列表（租户列表）
    getMembers: () => request.get('/api/v1/UserTenantAccount/team-members'),
    // 生成邀请码
    generateInviteCode: () => request.post('/api/v1/UserTenantAccount/generate-invite-code'),
    // 更新团队空间名称
    updateTeamSpaceName: (name) => request.post(`/api/v1/UserTenantAccount/update-team-space-name?name=${encodeURIComponent(name)}`),
    // 邀请新成员（生成邀请链接）
    inviteMember: (data) => request.post('/api/v1/UserTenantAccount/invite', data),
    // 移除团队成员
    removeMember: (userId) => request.post(`/api/v1/UserTenantAccount/remove-user?userId=${encodeURIComponent(userId)}`),
    // 获取当前用户角色
    getUserRole: () => request.get('/api/v1/UserTenantAccount/is-main'),
    // 加入团队（通过邀请码）
    joinTeam: (inviteCode) => request.post(`/api/v1/UserTenantAccount/add-sub-account?inviteCode=${encodeURIComponent(inviteCode)}`)
  },
  voice: {
    // 获取声音列表
    getVoices: () => request.get('/api/voice/voices'),

        // 语音转文字接口（完全按照demo实现）
    speechToText: async (audioFile, params = {}) => {
      try {
        // 完全按照demo的方式实现，使用原生fetch避免axios拦截器干扰
        const baseURL = getBaseURLByPath('/api/voice/speech-to-text');
        const url = `${baseURL}/api/voice/speech-to-text`;

        // 获取认证信息
        const token = localStorage.getItem('token');
        const tenantId = localStorage.getItem('tenantId');

        // 构建headers，完全按照demo的方式
        const headers = {
          'sessionID': params.sessionID || 'temp-session',
          'appID': params.appId || 'temp-app', // demo中使用的是 appID
          'flowType': params.flowType || 'SessionFlow',
          'language': params.language || 'zh-CN',
          'audioFormat': params.audioFormat || 'webm'
        };

        // 添加认证头
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
        if (tenantId) {
          headers['__tenant'] = tenantId;
        }

        // 添加可选参数
        if (params.sampleRate) {
          headers['sampleRate'] = params.sampleRate.toString();
        }

        console.log('发送语音识别请求，headers:', headers);
        console.log('请求URL:', url);
        console.log('音频文件大小:', audioFile.size, 'bytes');

        // 使用原生fetch，完全按照demo的方式
        const response = await fetch(url, {
          method: 'POST',
          headers: headers,
          body: audioFile
        });

        const data = await response.json();
        console.log('语音识别响应:', data);

        // 转换为统一的响应格式
        if (response.ok && data.isSuccess) {
          return data;
        } else {
          throw new Error(data.message || data.errorMessage || '语音识别失败');
        }
      } catch (error) {
        console.error('语音识别请求失败:', error);
        throw error;
      }
    },

    // 文字转语音接口
    textToSpeech: (params = {}) => {
      const requestData = {
        text: params.text,
        sessionID: params.sessionID,
        flowType: params.flowType || 'SessionFlow'
      };

      // 添加appId参数（如果提供）
      if (params.appId) {
        requestData.appId = params.appId;
      }

      return instance.post('/api/voice/text-to-speech', requestData, {
        headers: {
          'Content-Type': 'application/json',
          'accept': 'text/plain'
        }
      });
    }
  },
  mcpService: {
    // 获取工具列表
    getTools: (data) => request.get('/api/v1/McpService/tools', data),
    // 运行工具
    runTool: (data) => request.post('/api/v1/McpService/runTool', data)
  },
  schemaStore: {
    // 获取数据库列表
    getDatabases: (params = {}) => request.get('/api/v1/SchemaStore/databases', params),
    // 创建数据库
    createDatabase: (data) => request.post('/api/v1/SchemaStore/create-database', data),
    // 获取数据库详情（包含表格列表）
    getDatabaseDetail: (databaseId) => request.get(`/api/v1/SchemaStore/database/${databaseId}`),
    // 更新数据库信息
    updateDatabase: (databaseId, data) => request.put(`/api/v1/SchemaStore/database/${databaseId}`, data),
    // 删除数据库
    deleteDatabase: (databaseId) => request.delete(`/api/v1/SchemaStore/database/${databaseId}`),
    // 查询表数据 (不包含列信息)
    getTables: (params = {}) => request.get('/api/v1/SchemaStore/tables', params),
    // 查询表字段配置信息
    getTableColumns: (tableId) => request.get(`/api/v1/SchemaStore/table/${tableId}/columns`),
    // 查询表数据记录
    getDataRecords: (params = {}) => request.get('/api/v1/SchemaStore/data-records', params),
    // 创建数据记录
    createDataRecord: (data) => request.post('/api/v1/SchemaStore/data-record', data),

    // 更新数据记录
    updateDataRecord: (recordId, data) => request.put(`/api/v1/SchemaStore/data-record/${recordId}`, data),

    // 删除数据记录
    deleteDataRecord: (recordId, params = {}) => request.delete(`/api/v1/SchemaStore/data-record/${recordId}`, params),
    // 创建数据表
    createTable: (data) => request.post('/api/v1/SchemaStore/create-table', data),

    // 更新数据表信息
    updateTable: (data) => request.put('/api/v1/SchemaStore/update-table', data),
    // 删除数据表
    deleteTable: (tableId) => request.delete(`/api/v1/SchemaStore/table/${tableId}`),
    // 新增表字段
    createColumn: (data) => request.post('/api/v1/SchemaStore/column', data),
    // 修改表字段
    updateColumn: (columnId, data) => request.put(`/api/v1/SchemaStore/column/${columnId}`, data),
    // 删除表字段
    deleteColumn: (columnId) => request.delete(`/api/v1/SchemaStore/column/${columnId}`),
    // 导出表字段模板
    exportTableColumnsTemplate: (tableId) => {
      // 创建专用的axios实例，参考exportRecords的实现
      const exportInstance = axios.create({
        timeout: 100000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      // 添加请求拦截器（用于认证和路由）
      exportInstance.interceptors.request.use(
        config => {
          config.baseURL = getBaseURLByPath(config.url);
          const token = localStorage.getItem('token');
          if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
          }
          const tenantId = localStorage.getItem('tenantId');
          if (tenantId) {
            config.headers['__tenant'] = tenantId;
          }
          return config;
        },
        error => Promise.reject(error)
      );

      return exportInstance.get(`/api/v1/SchemaStore/export-table-columns-template/${tableId}`, {
        responseType: 'blob'
      }).then(response => response.data);
    },
    // 从Excel导入数据记录
    importDataRecordsFromExcel: (data) => {
      const formData = new FormData();
      formData.append('excelFile', data.excelFile);
      formData.append('virtualTableId', data.virtualTableId);
      formData.append('hasHeader', data.hasHeader);

      return instance.post('/api/v1/SchemaStore/import-data-records-from-excel', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    },
    // 导出数据记录到Excel
    exportDataRecordsToExcel: (virtualTableId, tableName) => {
      // 创建专用的axios实例，参考exportRecords的实现
      const exportInstance = axios.create({
        timeout: 100000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      // 添加请求拦截器（用于认证和路由）
      exportInstance.interceptors.request.use(
        config => {
          config.baseURL = getBaseURLByPath(config.url);
          const token = localStorage.getItem('token');
          if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
          }
          const tenantId = localStorage.getItem('tenantId');
          if (tenantId) {
            config.headers['__tenant'] = tenantId;
          }
          return config;
        },
        error => Promise.reject(error)
      );

      return exportInstance.get('/api/v1/SchemaStore/export-data-records-to-excel', {
        params: {
          virtualTableId: virtualTableId,
          tableName: tableName
        },
        responseType: 'blob'
      }).then(response => response.data);
    },
    // 从Excel导入表结构
    importExcelColumns: (data) => {
      const formData = new FormData();
      formData.append('excelFile', data.excelFile);
      formData.append('virtualDatabaseId', data.virtualDatabaseId);

      return instance.post('/api/v1/SchemaStore/preview-excel-columns', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
    }
  },
  agentService: {
    // AI优化智能体描述（无超时限制）
    optimizeAgentDescription: (data) => {
      return instance.post('/api/v1/AgentService/optimize-agent-description', data, {
        timeout: 0 // 禁用超时
      });
    },
    // AI优化开场介绍
    optimizeAgentIntroduction: (data) => {
      return request.post('/api/v1/AgentService/optimize-agent-introduction', data);
    },
    // AI优化智能体设定
    optimizeAgentSettings: (data) => {
      return request.post('/api/v1/AgentService/optimize-agent-settings', data);
    }
  },
  llmService: {
    // 清除会话记忆
    clearMemory: (data) => {
      return request.post('/api/v1/LlmService/clear-memory', data);
    },
    // 搜索模型列表
    searchModels: (data) => {
      return request.post('/api/v1/LlmService/models/search', data);
    }
  }
}

export default instance
