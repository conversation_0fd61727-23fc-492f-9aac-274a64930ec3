import { routerPush, routerReplace } from '@/utils/router'

export default {
  methods: {
    /**
     * 路由跳转并刷新页面
     * @param {String|Object} location - 目标路由路径或对象
     * @param {Boolean} forceRefresh - 是否强制刷新组件
     */
    goWithRefresh(location, forceRefresh = true) {
      return routerPush(this.$router, location, forceRefresh)
    },

    /**
     * 路由替换并刷新页面
     * @param {String|Object} location - 目标路由路径或对象
     * @param {Boolean} forceRefresh - 是否强制刷新组件
     */
    replaceWithRefresh(location, forceRefresh = true) {
      return routerReplace(this.$router, location, forceRefresh)
    }
  },

  // 处理同一路由不同参数的情况
  beforeRouteUpdate(to, from, next) {
    // 检查是否需要强制刷新
    if (to.params.forceRefresh || to.meta.forceRefresh) {
      // 这里可以添加组件刷新的逻辑，例如调用组件上的刷新方法
      if (typeof this.refreshData === 'function') {
        this.$nextTick(() => {
          this.refreshData()
        })
      }
    }
    next()
  }
}
