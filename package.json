{"name": "user-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "pro": "vue-cli-service serve --mode production", "local": "vue-cli-service serve --mode local", "build": "vue-cli-service build --mode development", "build-pro": "vue-cli-service build --mode production", "build-local": "vue-cli-service build --mode local", "preview-pro": "npm run build-pro && npx serve -s dist", "preview-local": "npm run build-local && npx serve -s dist", "lint": "vue-cli-service lint"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-json": "^6.0.2", "@codemirror/language": "^6.11.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.37.2", "@microsoft/signalr": "^8.0.7", "ali-oss": "^6.17.1", "axios": "^1.8.4", "codemirror": "^6.0.2", "core-js": "^3.8.3", "echarts": "^5.6.0", "element-ui": "^2.15.14", "qrcode": "^1.5.4", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"no-debugger": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}