<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滑动拼图验证码API演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f7f9fc;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px 30px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .slide-container {
            position: relative;
            margin: 25px auto;
            overflow: hidden;
            border-radius: 8px;
            width: 280px;
            height: auto;
            padding: 15px;
            background-color: #fff;
            border: 1px solid #e0e0e0;
            box-sizing: border-box;
        }
        #slide-background {
            width: 100%;
            display: block;
            border-radius: 4px;
            background: linear-gradient(135deg, #fafbfd 0%, #f8f9fc 100%);
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            vertical-align: middle;
        }
        #slide-puzzle {
            position: absolute;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
            border-radius: 3px;
            box-sizing: border-box;
            z-index: 5;
            pointer-events: none;
            margin: 0;
            padding: 0;
            transform-origin: center;
            will-change: transform;
            top: 0;
            left: 0;
        }
        .slider {
            width: 100%;
            height: 40px;
            background-color: #f5f7fa;
            position: relative;
            margin-top: 8px;
            border: 1px solid #e8eef7;
            border-radius: 20px;
            display: flex;
            align-items: center;
        }
        .slider-button {
            width: 40px;
            height: 38px;
            background: #fff;
            position: absolute;
            left: 0;
            top: 0.5px;
            cursor: pointer;
            border: 1px solid #e8e8e8;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            transition: all 0.2s;
        }
        .slider-button:hover {
            background-color: #f0f8ff;
        }
        .slider-button:active {
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        .slider-button::after {
            content: '→';
            color: #2196F3;
            font-size: 16px;
        }
        .slider-track {
            height: 100%;
            width: 0;
            background: linear-gradient(90deg, rgba(187, 222, 251, 0.4), rgba(144, 202, 249, 0.5));
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            border-radius: 20px;
            transition: width 0.2s;
        }
        .slider-hint {
            color: #7a8ba9;
            font-size: 13px;
            margin-left: 50px;
            user-select: none;
            font-weight: 500;
        }
        .result {
            margin-top: 10px;
            text-align: center;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
            display: none;
        }
        .result.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            display: block;
        }
        .result.error {
            background-color: #ffebee;
            color: #c62828;
            display: block;
        }
        .loading {
            text-align: center;
            padding: 15px;
            display: none;
        }
        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #e0e0e0;
            border-top: 3px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #1976D2;
        }
        button:disabled {
            background-color: #bbdefb;
            cursor: not-allowed;
        }
        #slide-distance {
            margin-top: 10px;
            font-size: 13px;
            color: #757575;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>滑动拼图验证码API演示</h1>

        <div class="card">
            <h2 class="card-title">滑动验证码</h2>
            <div class="card-content">
                <p class="description">点击下方按钮生成一个新的滑动验证码，通过调用API接口获取并验证。</p>
                <button id="generate-captcha">生成验证码</button>
                <div id="slide-captcha" class="slide-container" style="display: none;">
                    <img id="slide-background" src="" alt="背景图片">
                    <img id="slide-puzzle" src="" alt="滑块">
                    <div class="slider">
                        <div class="slider-button" id="slider-button"></div>
                        <div class="slider-track" id="slider-track"></div>
                        <div class="slider-hint">向右拖动滑块填充拼图</div>
                    </div>
                    <div id="slide-distance"></div>
                    <div id="slide-result" class="result"></div>
                </div>
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>处理中...</p>
                </div>
            </div>
        </div>
    </div>
    <script>
        // 全局变量
        let captchaId = null;
        let isDragging = false;
        let startX = 0;
        let currentX = 0;
        let slideDisabled = false;
        let trajectory = [];
        let captchaData = null; // 存储验证码数据的全局变量

        // DOM 元素
        const generateButton = document.getElementById('generate-captcha');
        const captchaContainer = document.getElementById('slide-captcha');
        const backgroundImage = document.getElementById('slide-background');
        const sliderImage = document.getElementById('slide-puzzle');
        const sliderButton = document.getElementById('slider-button');
        const sliderTrack = document.getElementById('slider-track');
        const resultElement = document.getElementById('slide-result');
        const loadingElement = document.getElementById('loading');
        const slideDistanceElement = document.getElementById('slide-distance');

        // 获取容器内边距
        function getContainerPadding() {
            const container = document.querySelector('.slide-container');
            const style = window.getComputedStyle(container);
            return {
                left: parseInt(style.paddingLeft) || 15,
                top: parseInt(style.paddingTop) || 15
            };
        }

        // 生成验证码
        generateButton.addEventListener('click', generateCaptcha);

        // 滑块事件
        sliderButton.addEventListener('mousedown', dragStartHandler);
        document.addEventListener('mousemove', dragMoveHandler);
        document.addEventListener('mouseup', dragEndHandler);

        // 触摸设备支持
        sliderButton.addEventListener('touchstart', touchStartHandler);
        document.addEventListener('touchmove', touchMoveHandler);
        document.addEventListener('touchend', touchEndHandler);

        // 记录时间戳函数
        function now() {
            return new Date().getTime();
        }

        // 生成验证码 - 调用API接口
        async function generateCaptcha() {
            try {
                // 重置状态
                resetSlider();
                showLoading(true);
                generateButton.disabled = true;
                resultElement.className = 'result';
                resultElement.textContent = '';
                captchaContainer.style.display = 'none';

                // 调用API获取验证码
                const response = await fetch('http://192.168.199.123:5174/api/captcha/slide?width=280&height=140');
                if (!response.ok) {
                    throw new Error('获取验证码失败');
                }

                captchaData = await response.json(); // 存储到全局变量
                captchaId = captchaData.id;

                // 设置图片
                backgroundImage.onload = function() {
                    console.log('背景图片加载完成，尺寸:', this.width, 'x', this.height);

                    sliderImage.onload = function() {
                        console.log('滑块图片加载完成，尺寸:', this.width, 'x', this.height);

                        const padding = getContainerPadding();

                        // 设置滑块初始位置，考虑容器内边距
                        const initialTop = (backgroundImage.height - sliderImage.height) / 2;
                        sliderImage.style.top = `${initialTop}px`;
                        sliderImage.style.left = `${padding.left}px`;

                        console.log('初始位置计算:', {
                            container: {
                                padding: padding
                            },
                            background: {
                                height: backgroundImage.height,
                                width: backgroundImage.width
                            },
                            slider: {
                                height: sliderImage.height,
                                width: sliderImage.width,
                                position: {
                                    top: initialTop,
                                    left: padding.left
                                }
                            },
                            目标位置: captchaData?.targetX
                        });

                        // 显示验证码容器并隐藏加载中
                        captchaContainer.style.display = 'block';
                        showLoading(false);
                        generateButton.disabled = false;
                    };

                    sliderImage.src = captchaData.sliderImage;
                };

                backgroundImage.src = captchaData.backgroundImage;

                // 显示提示信息
                document.querySelector('.slider-hint').textContent = captchaData.hint || "向右拖动滑块填充拼图";
            } catch (error) {
                showError('获取验证码失败：' + error.message);
                console.error('获取验证码错误:', error);
                showLoading(false);
                generateButton.disabled = false;
            }
        }

        // 拖动开始处理
        function dragStartHandler(e) {
            if (slideDisabled) return;
            e.preventDefault();
            isDragging = true;
            startX = e.clientX || e.touches[0].clientX;

            // 记录滑动轨迹的起点，考虑内边距
            trajectory = [{
                x: 0,
                y: 0,
                timestamp: now()
            }];
        }

        // 拖动移动处理
        function dragMoveHandler(e) {
            if (!isDragging) return;
            e.preventDefault();

            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const moveX = clientX - startX;
            const padding = getContainerPadding();

            // 限制滑动范围
            const maxX = backgroundImage.width - sliderImage.width;
            currentX = Math.max(0, Math.min(moveX, maxX));

            // 更新滑块按钮位置
            sliderButton.style.left = currentX + 'px';
            sliderTrack.style.width = (currentX + sliderButton.offsetWidth / 2) + 'px';

            // 更新拼图滑块位置，考虑内边距
            const sliderX = padding.left + currentX;
            sliderImage.style.left = `${sliderX}px`;

            // 记录实际滑动距离
            slideDistanceElement.textContent = `滑动距离: ${Math.round(currentX)}px`;

            // 记录轨迹点
            trajectory.push({
                x: Math.round(currentX),
                y: 0,
                timestamp: now()
            });
        }

        // 拖动结束处理 - 调用API验证
        async function dragEndHandler(e) {
            if (!isDragging) return;
            isDragging = false;

            if (currentX > 0) {
                try {
                    slideDisabled = true;
                    showLoading(true);

                    const padding = getContainerPadding();
                    // 计算实际验证距离（加上内边距，与服务端计算保持一致）
                    const verifyX = currentX + padding.left;

                    console.log('验证信息:', {
                        原始滑动距离: currentX,
                        验证距离: verifyX,
                        内边距: padding.left,
                        轨迹点数: trajectory.length,
                        起始位置: trajectory[0],
                        结束位置: trajectory[trajectory.length - 1],
                        目标位置: captchaData?.targetX
                    });

                    // 调用API验证
                    const verifyResponse = await fetch('http://192.168.199.123:5174/api/captcha/slide/verify', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            id: captchaId,
                            x: Math.round(verifyX),
                            trajectory: trajectory
                        })
                    });

                    showLoading(false);

                    if (verifyResponse.ok) {
                        showSuccess('验证成功！');

                        // 添加成功动画，保持滑块在当前位置
                        sliderImage.style.transition = 'all 0.3s ease';
                        sliderImage.style.transform = 'scale(1.05)';
                        setTimeout(() => {
                            sliderImage.style.transform = 'scale(1)';
                        }, 300);
                    } else {
                        // 获取错误详情
                        const errorData = await verifyResponse.json();
                        showError(errorData.message || '验证失败，请重试');

                        // 添加失败动画
                        sliderImage.style.transition = 'all 0.1s ease';
                        sliderImage.style.transform = 'translateX(-5px)';
                        setTimeout(() => {
                            sliderImage.style.transform = 'translateX(5px)';
                            setTimeout(() => {
                                sliderImage.style.transform = 'translateX(0)';
                                sliderImage.style.transition = '';
                            }, 100);
                        }, 100);

                        // 如果需要刷新验证码
                        if (errorData.needRefresh) {
                            setTimeout(generateCaptcha, 1000);
                        } else {
                            resetSlider();
                        }
                    }
                } catch (error) {
                    showLoading(false);
                    showError('验证请求失败：' + error.message);
                    console.error('验证请求错误:', error);
                    resetSlider();
                }
            } else {
                resetSlider();
            }
        }

        // 触摸事件处理
        function touchStartHandler(e) {
            dragStartHandler(e);
        }

        function touchMoveHandler(e) {
            dragMoveHandler(e);
        }

        function touchEndHandler(e) {
            dragEndHandler(e);
        }

        // 重置滑块位置
        function resetSlider() {
            isDragging = false;
            startX = 0;
            currentX = 0;

            sliderButton.style.left = '0px';
            sliderTrack.style.width = '0px';

            if (captchaId && captchaData) {
                const padding = getContainerPadding();
                sliderImage.style.transition = 'all 0.3s ease';
                sliderImage.style.left = `${padding.left}px`;
                sliderImage.style.top = `${(backgroundImage.height - sliderImage.height) / 2}px`;
                sliderImage.style.transform = 'none';
            }

            // 清除轨迹记录
            trajectory = [];
            slideDisabled = false;
            slideDistanceElement.textContent = '';

            // 延迟移除过渡效果
            setTimeout(() => {
                sliderImage.style.transition = '';
            }, 300);
        }

        // 显示成功消息
        function showSuccess(message) {
            resultElement.textContent = message;
            resultElement.className = 'result success';
        }

        // 显示错误消息
        function showError(message) {
            resultElement.textContent = message;
            resultElement.className = 'result error';
        }

        // 隐藏结果消息
        function hideResult() {
            resultElement.style.display = 'none';
            resultElement.className = 'result';
        }

        // 显示/隐藏加载中
        function showLoading(show) {
            loadingElement.style.display = show ? 'block' : 'none';
        }

        // 页面加载时触发一次验证码生成
        window.addEventListener('DOMContentLoaded', function() {
            // 自动生成验证码
            generateCaptcha();
        });
    </script>
</body>
</html>